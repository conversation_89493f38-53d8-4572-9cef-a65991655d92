import React from 'react';
import { IComData } from '../core/types';
import BaseRenderStore from './store';
import { RenderProps, Resolver } from './types';

export type RenderContextType<T = IComData, Store = BaseRenderStore> = {
  resolver?: Resolver;
  renderProps?: RenderProps<T>;
  store: Store;
};

export const InternalRenderContext = React.createContext<RenderContextType | null>(null);

export const InternalRenderProvider = InternalRenderContext.Provider;

export default {};
