import { CorsComponent, utils } from '@weapp/ui';
import { eventEmitter } from '@weapp/utils';
import {
  useCallback, useEffect, useRef, useState,
} from 'react';
import ReactDOM from 'react-dom';
import { Route, withRouter } from 'react-router-dom';
import CorsRouter from './CorsRouter';
import RightLayout from './RightLayout';
import { RoutePageViewProps } from './types';

const hasBindJumpLinkEvent = () => {
  const eventNames = eventEmitter.eventNames();

  return eventNames.includes('@weapp/ebdcoms/jumplink.link');
};

function RoutePageView(props: RoutePageViewProps) {
  const {
    servicePath, terminalType, pageId, routeWrapDom, instanceId, layoutType, events,
  } = props;
  const path = utils.formatParentPath(props);
  const [shouldRenderDialog, renderDialogs] = useState(false);
  const linkDataRef = useRef<any[]>(null);

  const handleMount = useCallback((controller: any) => {
    if (linkDataRef.current && !hasBindJumpLinkEvent()) {
      controller.openDialog(...linkDataRef.current);
    }
  }, []);

  const renderComponet = useCallback(
    () => (
      <CorsComponent
        weId={`${props.weId || ''}_ak9ead`}
        app="@weapp/ebdpage"
        compName="RoutePageView"
        clientType={terminalType || 'PC'}
        servicePath={servicePath}
        isRenderPage={false}
        isRenderUniqueDialogs={false}
        isPreview
      />
    ),
    [],
  );

  const setEbPageParams = () => {
    const { history, match } = props;

    window.__ebpage_history__ = { history, match };
  };

  useEffect(() => {
    setEbPageParams();
  }, [pageId]);

  useEffect(() => {
    if (!routeWrapDom || hasBindJumpLinkEvent()) {
      return;
    }

    const handleJumpLink = (...args: any[]) => {
      (linkDataRef as any).current = args;

      // 弹窗初始化完毕后事件可以清除，弹窗内部有初始化相应的事件
      // 此处得先卸载监听，renderDialogs会导致组件状态更新导致重新渲染
      // 这个卸载事件就会被挂载起来，引发ebdpage仓库那边先再注册了一份'jumplink.link'事件
      // 然后就会导致hasBindJumpLinkEvent方法判断出问题
      eventEmitter.off('@weapp/ebdcoms', 'jumplink.link', handleJumpLink);

      renderDialogs(true);
    };

    eventEmitter.on('@weapp/ebdcoms', 'jumplink.link', handleJumpLink);

    return () => {
      eventEmitter.off('@weapp/ebdcoms', 'jumplink.link', handleJumpLink);
    };
  }, []);

  if (!routeWrapDom) return null;

  return ReactDOM.createPortal(
    <>
      <Route
        weId={`${props.weId || ''}_ppxukp`}
        path={`${path}/:type/:id`}
        component={renderComponet}
      />
      <RightLayout
        weId={`${props.weId || ''}_ln6k46`}
        events={events}
        routeWrapDom={routeWrapDom}
        instanceId={instanceId}
        layoutType={layoutType}
      />
      <CorsRouter weId={`${props.weId || ''}_xtrek9`} />
      {shouldRenderDialog ? (
        <>
          <CorsComponent
            weId={`${props.weId || ''}_bvj07y`}
            compName="UniqueDialogs"
            app="@weapp/ebdpage"
            onMount={handleMount}
          />
        </>
      ) : null}
    </>,
    routeWrapDom,
  );
}

export default withRouter(RoutePageView);
