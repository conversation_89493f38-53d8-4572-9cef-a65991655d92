@import (reference) '../style/prefix.less';

.@{prefix}-render-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.@{prefix}-render-mask-loading {
  position: absolute;
  top: 0;
  left: 0;
  background-color: inherit;
  z-index: 100;

  &+div {
    visibility: hidden !important;
  }
}

body[page-rtl='true'] {

  // 页面运行区域不镜像处理，组件内容做镜像处理
  .ebpage {
    transform: scaleX(-1) !important;

    &>.ebcoms-empty-noData {
      .ui-empty-title {
        transform: none;
      }
    }
  }

  .ebcom-view:not(.demo-com) {
    transform: scaleX(-1) !important;
  }
}