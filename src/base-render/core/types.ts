import { ReactElement, ReactNode } from 'react';
import { IComData, Page } from '../../core';
import EventEmitter from '../../core/utils/event';
import { ViewProps } from '../../core/view/types';

export interface ComViewProps extends Omit<ViewProps, 'mounted' | 'comServicePath'> {
  /** 后端服务路径 */
  servicePath?: string;
  /** 事件总线，作为实现组件间联动的方案之一 */
  events?: EventEmitter;
  /** 页面信息 */
  page?: Page;
  /** 自定义子组件的渲染视图，包含所有层级的子组件 */
  renderChild?: (childView: ReactElement) => ReactElement | ReactNode;
  /** 是否需要根据组件数据自定义渲染视图 */
  shouldRenderWithView?: (data: IComData) => boolean;
  /** 组件加载完成回调，返回组件实例 */
  mount?: (comInstance: any) => void;
  /** 是否分片处理组件，默认为true */
  splitLongTask?: boolean;
}
