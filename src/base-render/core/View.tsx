import { isEqual, uniq } from '@weapp/utils';
import { PureComponent } from 'react';
import { View } from '../../core/view';
import { InternalRenderContext, RenderContextType } from '../Context';
import { getCompSDK } from '../utils';
import { LOG } from '../utils/log';
import { ComViewProps } from './types';

interface ComViewStates {
  lazy: boolean;
  preRenderComs: string[];
}

export default class ComView extends PureComponent<ComViewProps, ComViewStates> {
  static contextType = InternalRenderContext;

  context!: RenderContextType;

  constructor(props: ComViewProps) {
    super(props);

    const { __lazy = false } = props.data.config;

    this.state = {
      lazy: __lazy,
      preRenderComs: [],
    };

    this.bindEvents();
  }

  componentWillUnmount() {
    const { data, events } = this.props;
    const { __preRenderComs = [] } = data.config;
    __preRenderComs.forEach((id: any) => {
      events?.off?.('lazyrender', id);
    });
  }

  bindEvents = () => {
    const { data, events } = this.props;
    const { __preRenderComs = [] } = data.config;
    const { preRenderComs } = this.state;

    __preRenderComs.forEach((id: any) => {
      events?.on?.('lazyrender', id, () => {
        const _preRenderComs = uniq([...preRenderComs, id]).sort();

        const lazy = !isEqual(_preRenderComs, __preRenderComs.sort());

        this.setState({
          lazy,
          preRenderComs: _preRenderComs,
        });
      });
    });
  };

  getCompSDK() {
    const { data, page } = this.props;
    const comId = data.id;
    const pageId = page?.id;

    return getCompSDK(comId, pageId);
  }

  render() {
    const { lazy } = this.state;

    const events = this.props?.events || this.context?.store.events;

    const {
      data, children, servicePath, ...restProps
    } = this.props;
    if (lazy) {
      // 记录日志便于后期定位，日志含义：组件视图延迟加载
      LOG?.set({ data, logType: 'view-lazy' });
      return null;
    }

    const compSDK = this.getCompSDK();

    return (
      <View
        {...restProps}
        weId={`${this.props.weId || ''}_79849n`}
        data={data}
        comServicePath={servicePath}
        events={events}
        onMount={compSDK?.initView}
        onContentMount={compSDK?.initContent}
      >
        {children}
      </View>
    );
  }
}
