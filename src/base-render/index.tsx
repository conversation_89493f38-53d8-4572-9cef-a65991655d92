import { ComponentType } from 'react';
import Loadable from '../common/loadable';
import { BaseRenderProps, DroppableViewProps } from './types';

const preloadBaseRender = () => import(
  /* webpackChunkName: "de_base_render" */
  /* webpackMode: "eager" */
  './Render'
);

export const BaseRender = Loadable({
  name: 'BaseRender',
  loader: preloadBaseRender,
  lazyLoader: false,
  middleware: false,
}) as ComponentType<BaseRenderProps> & { preload?: any };

BaseRender.displayName = 'BaseRender';
BaseRender.preload = preloadBaseRender;

export const DroppableView = Loadable({
  name: 'DroppableView',
  lazyLoader: false,
  middleware: false,
  loader: () => import(
    /* webpackChunkName: "de_base_render" */
    /* webpackMode: "eager" */
    './resolver/Droppable'
  ),
}) as ComponentType<DroppableViewProps>;

DroppableView.displayName = 'DroppableView';

export default {};
