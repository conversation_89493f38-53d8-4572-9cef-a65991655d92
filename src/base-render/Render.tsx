import { Spin } from '@weapp/ui';
import React, { ComponentType, PureComponent } from 'react';
import { prefixCls } from '../constants';
import { InternalRenderProvider } from './Context';
import { FilterPlugin, LazyRenderPlugin } from './event-plugins';
import RoutePageView from './RoutePageView';
import BaseRenderStore from './store';
import { BaseRenderProps } from './types';
import { LOG } from './utils/log';
import { initSourceCode, isEcodeUsable } from './utils/sourceCode';

interface BaseRenderStates {
  loading: boolean;
  pageId: string;
}

class BaseRender extends PureComponent<BaseRenderProps, BaseRenderStates> {
  ref = React.createRef();

  store = this.props.store || new BaseRenderStore();

  preLoadPage: boolean = false;

  constructor(props: BaseRenderProps) {
    super(props);

    const { renderProps } = props;
    const { pageId, config, extraSourceCodeDatas = [] } = renderProps.data;

    this.state = {
      loading: this.shouldLoadEcodeFile(props),
      pageId,
    };

    this.initSourceCode(props);

    // 页面源码只有css资源的时候，可以提前渲染eb页面
    this.preLoadPage = !extraSourceCodeDatas.length && !(config?.ebCodeFileTypes || ['js', 'css']).includes('js');
  }

  componentDidMount() {
    this.props.renderProps.onMount?.(this.store);
  }

  static getDerivedStateFromProps(
    nextProps: Readonly<BaseRenderProps>,
    prevState: BaseRenderStates,
  ) {
    const { pageId } = nextProps.renderProps.data;

    if (pageId !== prevState.pageId) {
      return { pageId, loading: true };
    }

    return null;
  }

  componentDidUpdate(prevProps: Readonly<BaseRenderProps>): void {
    // pageId更换后需要重新初始化新页面sourceCode
    if (this.props.renderProps.data.pageId !== prevProps.renderProps.data.pageId) {
      this.initSourceCode();
    }
  }

  initSourceCode = async (props: BaseRenderProps = this.props) => {
    const { data, onSourceCodeLoadCallback, onEcodeMount } = props.renderProps;
    const {
      pageId,
      id,
      ecodeId,
      extraSourceCodeDatas = [],
      cssCacheEcodeId,
      ecodeVersion,
      ecodeCacheVersion,
      comps,
      module: moduleType,
      terminalType,
      config,
      pageVersion,
      instanceId,
      appid,
      layoutType,
      name,
    } = data;

    this.store.events.registerPlugins({ LazyRenderPlugin, FilterPlugin });

    (this.store.events as any)?.initPlugins?.([
      { name: 'lazyrender', type: 'LazyRenderPlugin' },
      {
        name: 'filter',
        type: 'FilterPlugin',
      },
    ]);

    /**
     * ecode_version
     * 1: 代表旧的路径规则 index
     * 2: 代表路径规则 index-pageId-clientType
     * 3: 代表路径规则 index-pageId-clientType 以及跳过本地缓存以及接口内容判断，直接加载代码文件
     * 4: 代表路径规则 index 以及跳过本地缓存以及接口内容判断，直接加载代码文件
     */
    const path = ['2', '3'].includes(String(ecodeVersion))
      ? `index-${pageId || id}-${terminalType || 'PC'}`
      : 'index';

    initSourceCode(pageId || id, comps, {
      events: this.store.events,
      path,
      terminalType,
      extraSourceCodeDatas,
      cssCacheEcode: {
        ecodeId: cssCacheEcodeId || '',
        version: pageVersion,
      },
      ecode: {
        ecodeId: ecodeId || '',
        version: ecodeCacheVersion,
      },
      skipLocalCache: ['3', '4'].includes(String(ecodeVersion)),
      fileTypes: config?.ebCodeFileTypes || ['js', 'css'],
      sourceCodeLoadCallback: onSourceCodeLoadCallback,
      cb: () => {
        onEcodeMount?.();
        this.store.events.emit('ecodemount');

        // 记录日志便于后期定位，日志含义：loading结束
        LOG?.set({ logType: 'designer-render-loading-off' });
        this.setState({ loading: false });
      },
      moduleType,
      instanceId: instanceId || this.store.instanceId,
      appid,
      layoutType,
      pageName: name,
    });
  };

  shouldLoadEcodeFile = (props: BaseRenderProps) => {
    const { data } = props.renderProps;
    const { ecodeId, extraSourceCodeDatas = [] } = data;

    return isEcodeUsable() && !!(ecodeId || extraSourceCodeDatas.length);
  };

  componentWillUnmount(): void {
    const { renderProps, store } = this.props;
    const { pageId } = renderProps.data;

    window.ebuilderSDK?.destory?.({
      instanceId: renderProps?.data?.instanceId || store!.instanceId,
      pageId: renderProps?.data?.pageId,
    });

    this.store.events.emit('pageUnload', { page: { id: pageId } });
  }

  render() {
    const { resolver, renderProps, store } = this.props;
    const {
      data, servicePath, routeWrapDom, loadingEnabled = true,
    } = renderProps;
    const { instanceId } = store!;
    const { loading } = this.state;

    if (loading && !this.preLoadPage) {
      return loadingEnabled ? (
        <Spin
          weId={`${this.props.weId || ''}_17fv16`}
          className={`${prefixCls}-render-loading`}
          size="large"
        />
      ) : null;
    }

    return (
      <InternalRenderProvider
        weId={`${this.props.weId || ''}_kwf5te`}
        value={{ resolver, renderProps, store: this.store }}
      >
        {loading && loadingEnabled ? (
          <Spin
            weId={`${this.props.weId || ''}_17fv16`}
            className={`${prefixCls}-render-loading ${prefixCls}-render-mask-loading`}
            size="large"
          />
        ) : null}
        {this.props.children}
        <RoutePageView
          weId={`${this.props.weId || ''}_sjcmzx`}
          pageId={data.pageId}
          terminalType={data.terminalType}
          layoutType={data?.layoutType}
          instanceId={data?.instanceId || instanceId}
          servicePath={servicePath}
          routeWrapDom={routeWrapDom}
          events={this.store.events}
        />
      </InternalRenderProvider>
    );
  }
}

export default BaseRender as unknown as ComponentType<BaseRenderProps>;
