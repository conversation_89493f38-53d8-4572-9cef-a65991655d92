import { CorsComponent } from '@weapp/ui';
import { eventEmitter } from '@weapp/utils';
import React, {
  useCallback, useEffect, useRef, useState,
} from 'react';

const CorsRouter: React.FC<React.Attributes> = (props) => {
  const ref = useRef<any>();
  const [shouldRenderCorsRouter, renderCorsRouter] = useState(false);

  const registerCorsRouter = useCallback((path: string, otherProps: any = {}) => {
    renderCorsRouter(true);

    ref.current = { path, otherProps };
    eventEmitter.off('@weapp/ebdcoms', 'jumplink.router', registerCorsRouter);
  }, []);

  const onMount = useCallback((_registerCorsRouter: any) => {
    _registerCorsRouter(ref.current.path, ref.current.otherProps);
  }, []);

  useEffect(() => {
    eventEmitter.on('@weapp/ebdcoms', 'jumplink.router', registerCorsRouter);
  }, []);

  if (!shouldRenderCorsRouter) return null;

  return (
    <CorsComponent
      weId={`${props.weId || ''}_stm6zq`}
      compName="CorsRouter"
      app="@weapp/ebdpage"
      onMount={onMount}
    />
  );
};

export default CorsRouter;
