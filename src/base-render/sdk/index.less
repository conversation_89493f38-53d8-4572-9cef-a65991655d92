@ebSdkElPrefix: ebsdk;

.@{ebSdkElPrefix}-spin-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  & > div {
    height: 100%;
  }

  .@{ebSdkElPrefix}-spin-content {
    background: var(--base-white);
    width: 100vw;
    height: 100vh;
  }
}

.@{ebSdkElPrefix}-drop-wrapper {
  position: absolute;
  background: var(--base-white);
  overflow-y: auto;
  border-radius: var(--border-radius-xs);
  box-shadow: var(--box-shadow);
  border: var(--border-width) solid var(--diviling-line-color);
  z-index: var(--dialog-route-layout-zindex);

  & > .ui-menu {
    width: auto;
    border: none;
  }

  .ui-menu-tab-top-container {
    right: 0;
  }

  .ui-menu-nav-container {
    display: block;
  }

  .ui-menu-nav-scroll {
    height: 100%;
    width: 100%;
    overflow: hidden;
  }

  .ui-menu-nav {
    width: 100%;
  }

  .ui-menu-list-item {
    padding: 0 8px;
    border: none;
  }

  .ui-menu-list-item:hover {
    background: var(--menu-item-hover-bc);
  }
}
