import { ClientType, IComData, LayoutType } from '../../types';
import Base, { JSApi } from './Base';
import CompSDK from './CompSDK';
import { pageApis } from './page';

export type PageSDKOptsType = {
  pageId: string;
  layoutType: LayoutType;
  // eb页面运行的唯一id，两个相同的eb页面也会存在不同的instanceId
  instanceId?: string;
  appid?: string;
  pageName?: string;
  coms: IComData[];
  events: any;
  clientType: ClientType;
};

export class PageSDK extends Base {
  private compSDKs: Record<string, CompSDK> = {};

  /**
   * pageSDK 新增event 用于 pageSdk.on 事件绑定
   */
  events: any;

  pageId: string = '';

  appid: string = '';

  instanceId: string = '';

  layoutType: LayoutType = 'GRID';

  clientType: ClientType = 'PC';

  pageName: string = '';

  /**
   * ecode导出的方法和变量
   */
  ecodeExports: Record<string, any> = {};

  coms: IComData[] = [];

  /** 表单组件store */
  formStore: any;

  constructor(opts: PageSDKOptsType) {
    super();

    const {
      pageId,
      events,
      coms,
      instanceId = '',
      appid = '',
      layoutType = 'GRID',
      pageName = '',
      clientType = 'PC',
    } = opts;

    pageApis.forEach((jsApi: JSApi) => {
      (this as any)[jsApi.name] = jsApi.fn.bind(this);
    });

    this.coms = coms;
    this.pageId = pageId;
    this.events = events;
    this.instanceId = instanceId;
    this.appid = appid;
    this.layoutType = layoutType;
    this.pageName = pageName;
    this.clientType = clientType;

    coms.forEach((com) => {
      this.compSDKs[com.id] = new CompSDK();
    });
  }

  update(opts: PageSDKOptsType) {
    const { coms, events } = opts;
    this.coms = [...this.coms, ...coms];
    this.events = events;
    coms.forEach((com) => {
      this.compSDKs[com.id] = new CompSDK();
    });
  }

  /**
   * @weapp/designer的Render有使用该方法
   */
  getCompSDK(comId: string) {
    return this.compSDKs[comId];
  }

  getCompInstance(comId: string, fn?: Function) {
    const comSDK = this.getCompSDK(comId);
    const comInstance = comSDK?.getInstance();

    fn?.(comInstance);

    return comInstance;
  }

  getCom = (comId: string) => this.coms.find((com) => com.id === comId);

  setExports(args = {}) {
    this.ecodeExports = {
      ...args,
    };
  }

  // 允许外部注册pageSDK api
  registerApis = (jsApis: JSApi[]) => {
    jsApis.forEach((jsApi) => {
      const { name, fn } = jsApi;

      (this as any)[name] = fn.bind(this);
    });
  };
}

export default {};
