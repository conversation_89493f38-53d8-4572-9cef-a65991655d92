import { MenuItemData } from '@weapp/ui';

export interface MenuItemInfo extends MenuItemData {
  text?: any;
}

export type FooterMenusItem = {
  id: string;
  text: string;
  icon: string;
  callback: () => void;
  [key: string]: any;
};

export interface FooterMenus {
  items: FooterMenusItem[];
  layout?: 'menu' | 'panel';
  iconPosition?: 'left' | 'right';
  column?: number;
  /** 敏感词上报入口展示控制，默认不展示，组件库默认展示 */
  showSensitive?: boolean;
}

export type SdkDestoryOpts = {
  pageId: string;
  instanceId: string;
};

export default {};
