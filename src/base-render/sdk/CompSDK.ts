export default class CompSDK {
  /** createView的组件实例 */
  private viewInstance: any;

  /** 组件的上下文对象 */
  private context: any;

  /** 组件的dom节点 */
  private dom: HTMLElement | null = null;

  get comDom() {
    return this.dom;
  }

  set comDom(dom: HTMLElement | null) {
    this.dom = dom;
  }

  /**
   * 初始化createView高阶组件相关实例信息
   * @param instance view的组件实例，不向外部提供，作为内部备用
   */
  initView = (instance: any) => {
    this.viewInstance = instance;
  };

  /**
   * 初始化Eb组件的内容(Content)部分，即createView中的Component组件
   * @param dom Content的dom节点
   * @param context 组件抛出来的上下文对象，用于外部sdk调用
   */
  initContent = (context: any) => {
    this.context = context;
  };

  /* eb组件暴露给外部的上下文对象，外部通过源码触发组件内部支持的动作 */
  getInstance() {
    /** 避免外部调用出错 */
    return {
      /**
       * 这里兼容老的getInstance(返回的是内部组件的实例)
       * TODO: 统一之后需要移除
       */
      show: this.viewInstance?.show,
      hide: this.viewInstance?.hide,
      toggle: this.viewInstance?.toggle,
      ...this.viewInstance?.comRef.current,
      ...this.context,
    };
  }
}
