import { eventEmitter, findKey, isString } from '@weapp/utils';
import Base, { JSApi } from './Base';
import { ebuilderApis } from './ebuilder';
import './index.less';
import { PageSDK, PageSDKOptsType } from './PageSDK';
import { SdkDestoryOpts } from './types';

export default class EbuilderSDK extends Base {
  pageSDKs: Record<string, PageSDK> = {};

  pageInstances: Record<string, number> = {};

  // 记录页面运行的唯一id 和 pageId 的对应关系
  pageInstanceIdMaps: Record<string, string> = {};

  static getInstance(pageId: string) {
    if (!window.ebuilderSDK) {
      window.ebuilderSDK = new EbuilderSDK(ebuilderApis, pageId);
    }

    return window.ebuilderSDK;
  }

  constructor(jsApis: JSApi[], pageId: string) {
    super();

    jsApis.forEach((jsApi: JSApi) => {
      (this as any)[jsApi.name] = jsApi.fn.bind(this);
    });

    if (!window.ebuilderSDK) {
      window.ebuilderSDK = this;
    }

    this.lastPageInstanceId = pageId;

    eventEmitter.on('@weapp/ebdpage', 'change.activePage', this.changeActivePage);

    return window.ebuilderSDK;
  }

  destory = (opts: SdkDestoryOpts) => {
    const { pageId, instanceId } = opts || {};
    const { pageInstances } = this;
    const pageInstanceId = instanceId || pageId;

    if (pageInstances[pageInstanceId]) {
      pageInstances[pageInstanceId] -= 1;
    }
    if (instanceId) {
      if (this.pageSDKs[instanceId]) {
        delete this.pageSDKs[instanceId];
      }
      if (this.pageInstanceIdMaps[instanceId]) {
        delete this.pageInstanceIdMaps[instanceId];
      }
    }
    const noEmpty = Object.keys(pageInstances).find((_pageId) => !!pageInstances[_pageId]);
    if (!noEmpty) {
      eventEmitter.off('@weapp/ebdpage', 'change.activePage', this.changeActivePage);
      window.ebuilderSDK = null;
    }
  };

  changeActivePage = (changedPageId: string) => {
    if (changedPageId && changedPageId !== this.lastPageInstanceId) {
      this.lastPageInstanceId = changedPageId;
    }
  };

  lastPageInstanceId: string = '';

  createPageSDK = (opts: PageSDKOptsType) => {
    const { pageId, instanceId } = opts;
    this.lastPageInstanceId = instanceId || pageId;

    if (!this.pageInstances[this.lastPageInstanceId]) {
      this.pageInstances[this.lastPageInstanceId] = 0;
    }

    if (instanceId) {
      this.pageInstanceIdMaps[instanceId] = pageId;
    }

    this.pageInstances[this.lastPageInstanceId] += 1;

    if (this.pageSDKs[this.lastPageInstanceId]) {
      const pageSDK = this.pageSDKs[this.lastPageInstanceId];
      pageSDK.update(opts);
    } else {
      const pageSDK = new PageSDK(opts);

      this.pageSDKs[this.lastPageInstanceId] = pageSDK;
    }
  };

  getPageSDK = (lastPageInstanceId?: string) => {
    lastPageInstanceId = lastPageInstanceId && isString(lastPageInstanceId)
      ? lastPageInstanceId
      : this.lastPageInstanceId;

    // 兼容处理: 获取pageSDK的时候，可能会存在用instanceId 注册的，但是还是用pageId 获取的情况
    if (this.pageSDKs[lastPageInstanceId]) {
      return this.pageSDKs[lastPageInstanceId];
    }

    const pageInstanceId = findKey(
      this.pageInstanceIdMaps,
      (pageId) => pageId === lastPageInstanceId,
    )!;

    return this.pageSDKs[pageInstanceId];
  };
}
