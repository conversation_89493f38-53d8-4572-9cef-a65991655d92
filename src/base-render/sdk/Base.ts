import { doRequest } from './api';

export type JSApi = {
  name: string;
  fn: Function;
};

export interface RegCustomApisOtherParams {
  pageId?: string;
}

const importEcode = (params: {
  ecodeId: string;
  path: string;
  skipLocalCache?: boolean;
  // ecode 内容版本，用于浏览器缓存加载ecode资源
  ecodeContentVersion?: string;
}) => {
  const {
    ecodeId, path, skipLocalCache = true, ecodeContentVersion,
  } = params;

  return skipLocalCache
    ? window.weappEcodesdk?.asyncCustAppImport?.('func', ecodeId, path, ['js'], ecodeContentVersion)
    : window.weappEcodesdk?.asyncImport?.(ecodeId, path, false, ['js']);
};

const asyncImportEcodeFunc = (data: {
  ecodeId: string;
  ecodeVersion?: string;
  ecodeContentVersion?: string;
}) => {
  const { ecodeId, ecodeVersion = '1', ecodeContentVersion } = data;
  const skipLocalCache = String(ecodeVersion) === '4';

  return new Promise((resolve) => {
    let _skipLocalCache = skipLocalCache;

    importEcode({
      ecodeId,
      path: 'entry',
      skipLocalCache: _skipLocalCache,
      ecodeContentVersion,
    })
      .then((mod: any) => {
        resolve(mod);
      })
      .catch(() => {
        if (_skipLocalCache === skipLocalCache) {
          _skipLocalCache = !skipLocalCache;

          importEcode({
            ecodeId,
            path: 'entry',
            skipLocalCache: _skipLocalCache,
            ecodeContentVersion,
          })
            .then((mod: any) => {
              resolve(mod);
            })
            .catch(() => {
              resolve([]);
            });
        } else {
          resolve([]);
        }
      });
  });
};

export default class Base {
  private settled = false;

  private readied = false;

  private registered = false;

  private mountedCallback: Array<Function> = [];

  asyncApis: Record<string, any> = {};

  getCustomApis = (pageId?: string, moduleType?: string) => doRequest('functionCategory', {
    params: { module: moduleType },
    ebBusinessId: pageId || '10000000000000000',
  }).then((data) => {
    if (data?.length) {
      return data.find?.((item: any) => item.id === '-1')?.functions || [];
    }
    return [];
  });

  readyCustomApis(apisPromise: Promise<any>) {
    apisPromise.then((apis) => {
      if (apis?.length) {
        const promises = apis.map((item: any) => {
          if (window.weappEcodesdk && item.ecodeId) {
            return asyncImportEcodeFunc(item).then((mod: any) => {
              if (Object.keys(mod)?.length) {
                Object.keys(mod).forEach((key: string) => {
                  if (key === 'default') {
                    (this as any)[item.name] = mod?.default;
                  } else {
                    (this as any)[`${item.name}_${key}`] = mod[key];
                  }
                });
              }
            });
          }
          return null;
        });
        Promise.allSettled(promises).then(() => {
          this.settled = true;
          this.mountedCallback.forEach((callback) => callback?.call?.(this));
        });
      } else {
        this.settled = true;
        this.mountedCallback.forEach((callback) => callback?.call?.(this));
      }
    });
  }

  registerCustomApis(pageId?: string, moduleType?: string) {
    /**
     * 解决增加独立部署前台 ready 函数未定义的问题
     * - EB_CUSTOM_PAGE - 独立部署前台
     */
    const allowedMoudleTypes = ['EB_CUSTOM_PAGE', 'EB_PAGE'];
    if (!moduleType || !allowedMoudleTypes.includes(moduleType) || this.registered) return;
    this.registered = true;

    // const apisPromise = this.getCustomApis(pageId, moduleType);
    // apisPromise.then((apis) => {
    //   if (apis?.length) {
    //     apis.forEach((api: any) => {
    //       this.asyncApis[api.name] = (...args: any) => {
    //         if (window.weappEcodesdk && api.ecodeId) {
    //           return window.weappEcodesdk.asyncImport(api.ecodeId, 'entry').then((mod: any) => {
    //             if (typeof mod?.default === 'function') {
    //               return mod?.default.apply(this, args);
    //             }
    //             return mod?.default;
    //           });
    //         }
    //         return Promise.resolve(null);
    //       };
    //     });
    //   }
    // });
    (this as any).ready = (callback: () => void) => {
      // 是否完成资源全部的加载
      if (this.settled) {
        callback.call(this);
      } else {
        this.mountedCallback.push(callback);
        // 是否加载过资源
        if (this.readied) return;
        this.readied = true;
        const apisPromise = this.getCustomApis(pageId, moduleType);
        this.readyCustomApis(apisPromise);
      }
    };
  }
}
