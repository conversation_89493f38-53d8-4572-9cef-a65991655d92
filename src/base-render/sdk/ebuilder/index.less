@import (reference) '../../../style/prefix.less';

.@{ebpagePrefix}-m-alter {
  .ui-m-dialog-content {
    width: 60%;
    border-radius: 5px;
  }

  .ui-m-dialog-footer-btn {
    background-color: rgb(240, 240, 240);
    color: var(--m-dialog-footer-btn-primary-color);
  }
}

// 底部菜单
.ebsdk-footer-wrapper {
  .ui-m-action-sheet-button-list-item,
  .ui-m-action-sheet-grid-list-item {
    cursor: pointer;
  }
}

// 底部菜单
.ebsdk-footer-menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ebsdk-promt-content {
  min-width: 170px;
}

.ebsdk-promt-content > div:last-of-type {
  display: flex;
  align-items: center;

  .ebsdk-promt-content-require {
    display: none;
    margin-left: 3px;

    &.show {
      display: inline-block;
    }
  }
}

.ebsdk-promt-content > div:first-of-type {
  margin-bottom: 10px;
}

.ebsdk-spin-wrapper {
  z-index: var(--dialog-message-zindex);
}
