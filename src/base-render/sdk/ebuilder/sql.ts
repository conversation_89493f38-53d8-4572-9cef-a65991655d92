import { AnyObj } from '@weapp/ui';
import ebdcoms from '../../../utils/ebdcoms';

function SQL(this: any, dataSetId: string, params?: AnyObj, cb?: Function) {
  const { ajax } = ebdcoms.get();
  const { pageId } = this.getPageSDK() || {};

  const callback = (result = []) => {
    if (params && typeof params === 'function') {
      return params(result);
    }
    if (cb && typeof cb === 'function') {
      return cb(result);
    }
  };

  if (!dataSetId || !(typeof dataSetId === 'string')) {
    return callback();
  }

  const isJson = params && Object.prototype.toString.call(params) === '[object Object]';
  const reqParams = isJson ? params : {};
  const formdata = new FormData();
  formdata.append('ebBusinessId', pageId);
  formdata.append('datasetId', dataSetId);
  formdata.append('params', JSON.stringify(reqParams));

  ajax({
    url: '/api/ebuilder/coms/dataset/sql/getData',
    method: 'POST',
    data: formdata,
    error: (res: any) => {
      const { code } = res;
      if (code === 500) {
        callback();
      }
    },
  }).then((res: any) => {
    if (res) {
      callback(res);
    } else {
      callback();
    }
  });
}

export default { SQL };
