import base from './base';
import date from './date';
import dom from './dom';

const popupAsync = () => import(
  /* webpackChunkName: "de_sdkfuns_popup"  */
  './popup'
);

const pageJumpAsync = () => import(
  /* webpackChunkName: "de_sdkfuns_pagejump"  */
  './pageJump'
);

const nativeAsync = () => import(
  /* webpackChunkName: "de_sdkfuns_native"  */
  './native'
);

const otherAsync = () => import(
  /* webpackChunkName: "de_sdkfuns_other"  */
  './other'
);

const sqlAsync = () => import(
  /* webpackChunkName: "de_sdkfuns_other"  */
  './sql'
);

const popup = [
  'msg',
  'alert',
  'confirm',
  'prompt',
  'footerMenu',
  'dropDownMenu',
  'showLoader',
  'hideLoader',
].map((fnName) => ({
  name: fnName,
  fn: (...args: any) => popupAsync().then((res: any) => res.default[fnName]?.(...args)),
}));

const pageJump = ['openPage', 'backPage', 'backToHomepage', 'openWebView'].map((fnName) => ({
  name: fnName,
  fn: (...args: any) => {
    window._event = window.event;

    return pageJumpAsync().then((res: any) => res.default[fnName]?.(...args));
  },
}));

const native = [
  'scanQRCode',
  'getCurrPosition',
  'rotatePage',
  'getWechatUserInfo',
  'callMobile',
  'sendMessage',
  'openItemChart',
].map((fnName) => ({
  name: fnName,
  fn: (...args: any) => nativeAsync().then((res: any) => res.default[fnName]?.(...args)),
}));

const other = ['callEsbFlow', 'openHrmCombineResource'].map((fnName) => ({
  name: fnName,
  fn: (...args: any) => otherAsync().then((res: any) => res.default[fnName]?.(...args)),
}));

const sql = ['SQL'].map((fnName) => ({
  name: fnName,
  fn(...args: any) {
    sqlAsync().then((res: any) => res.default[fnName]?.apply(this, args));
  },
}));

/**
 * 如果之前代码块中可使用const data = fun(xxx)，则不适用以上的异步引入，不要做调整
 * 以上的异步引入仅适用于非同步方式的api调用
 */
export const ebuilderApis = [
  ...base,
  ...popup,
  ...pageJump,
  ...native,
  ...date,
  ...other,
  ...sql,
  ...dom,
];

// 异步引入方法兼容Safari浏览器
if (/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)) {
  pageJumpAsync();
}

export default {};
