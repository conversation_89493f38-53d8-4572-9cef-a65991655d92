import { CorsComponent, Dialog, utils } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React from 'react';
import ReactDOM from 'react-dom';
import { BrowserRouter as Router } from 'react-router-dom';
import ebdcoms from '../../../utils/ebdcoms';
import { EbSdkElPrefix } from '../constants';
import { runtime } from '../Utils';

const { message } = Dialog;

const hrmCombineWrapper = `${EbSdkElPrefix}-hrm-combine-resource`;
let hrmCombineVal: any[] = [];

async function callEsbFlow(esbFlowId: string, mainParams: any = {}, detailParams: any[] = []) {
  const params: any = {
    esbFlowId,
    moduleSource: 'ebuilder',
    customParams: {
      mainTable: {},
    },
  };
  if (!utils.isEmpty(mainParams)) {
    params.customParams.mainTable = {
      ...mainParams,
    };
  }
  detailParams.forEach((el, i) => {
    const detailKey = `detail${i + 1}`;
    params.customParams.mainTable[detailKey] = el;
  });

  try {
    const res: any = await ebdcoms.asyncExcu('ajax', {
      customHandleError: true,
      cancelLimit: false,
      url: '/api/esb/server/event/triggerActionFlow',
      method: 'post',
      data: params,
      resultRootData: true,
    });

    if (res?.resultCode === 200 && res.actionData) {
      let result: any = {};
      Object.keys(res.actionData).forEach((k) => {
        if (k === 'responseData') {
          result = {
            ...result,
            ...res.actionData.responseData?.customData,
          };
        } else {
          result[k] = res.actionData[k];
        }
      });
      return Promise.resolve(result);
    }
    message({
      type: 'error',
      content: res?.resultMsg,
    });
    return Promise.reject(res?.resultMsg);
  } catch (err: any) {
    message({
      type: 'error',
      content: err?.msg || getLabel('59896', '程序异常'),
    });
  }
}

const removeHrmCombineResouce = () => {
  if (document.getElementsByClassName(hrmCombineWrapper).length) {
    document.getElementsByClassName(hrmCombineWrapper)[0].remove();
  }
};

const onClose = (onCancel: () => void) => () => {
  onCancel?.();
  removeHrmCombineResouce();
};

const handleHrmCombineChange = (onOk: (val: any[]) => void) => (value: any[]) => {
  hrmCombineVal = value;
  onOk?.(hrmCombineVal);
  removeHrmCombineResouce();
};

function openHrmCombineResource(onOk: (val: any[]) => void, onCancel: () => void) {
  const isMobile = runtime.isMobile();

  if (document.getElementsByClassName(hrmCombineWrapper).length) {
    removeHrmCombineResouce();
  }

  const divDom = document.createElement('div');
  divDom.className = hrmCombineWrapper;
  document.body.appendChild(divDom);

  ReactDOM.render(
    <Router weId="lgeleo">
      <CorsComponent
        app="@weapp/ebdpage"
        compName="HrmCombine"
        weId="2oh68o"
        client={isMobile ? 'MOBILE' : 'PC'}
        value={hrmCombineVal}
        onChange={handleHrmCombineChange(onOk)}
        onClose={onClose(onCancel)}
      />
    </Router>,
    divDom,
  );
}

export default { callEsbFlow, openHrmCombineResource };
