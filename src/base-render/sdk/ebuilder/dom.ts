export type WaitForElementOpts = {
  delay?: number;
  maxCount?: number;
  isMultiple?: boolean;
  alternativeSelector?: string;
  // 监听dom被移除，移除后，重新获取dom
  observeElRemoved?: boolean;
  // 父节点
  parentNode?: HTMLElement | Document;
};

type ObserveDomRemovedOpts = {
  selector: string;
  alternativeSelector: string;
  isMultiple: boolean;
  parentNode: HTMLElement | Document;
  callback: Function;
};

function observeDomRemoved(targetNode: any, options: ObserveDomRemovedOpts) {
  // 目标节点本身就不存在，不做任何处理，此处只监听dom一开始存在，后来被移除的情况
  if (!targetNode || targetNode.length === 0) return;

  const {
    isMultiple, selector, alternativeSelector, callback, parentNode,
  } = options;

  let count = 0;

  const parentEl = parentNode || document;

  const interval = setInterval(() => {
    // 最多查找20次
    if (count === 20) {
      clearInterval(interval);
      return;
    }

    let dom: any = null;

    if (isMultiple) {
      dom = alternativeSelector
        ? parentEl.querySelectorAll(selector) || parentEl.querySelectorAll(alternativeSelector)
        : parentEl.querySelectorAll(selector);
    } else {
      dom = alternativeSelector
        ? parentEl.querySelector(selector) || parentEl.querySelector(alternativeSelector)
        : parentEl.querySelector(selector);
    }

    // 没有获取到dom，表示dom被移除了，执行callback，重新获取dom
    if (!isMultiple && !dom) {
      callback();
      clearInterval(interval);
      return;
    }

    // 多选情况，如果获取的dom长度是0，表示没有获取到dom
    if (isMultiple && dom.length === 0) {
      callback();
      clearInterval(interval);
      return;
    }

    count++;
  }, 200);
}

export function waitForElement(selector: string, callback?: Function, opts?: WaitForElementOpts) {
  const {
    delay = 100,
    maxCount = 20,
    isMultiple = false,
    alternativeSelector = '',
    observeElRemoved = false,
    parentNode,
  } = opts || {};
  let count: number = 0;
  let timer: number | null = null;

  const parentEl = parentNode || document;

  function getDom() {
    let dom: any = null;
    if (isMultiple) {
      dom = alternativeSelector
        ? parentEl.querySelectorAll(selector) || parentEl.querySelectorAll(alternativeSelector)
        : parentEl.querySelectorAll(selector);
    } else {
      dom = alternativeSelector
        ? parentEl.querySelector(selector) || parentEl.querySelector(alternativeSelector)
        : parentEl.querySelector(selector);
    }

    if (timer) {
      clearTimeout(timer);
      timer = null;
    }

    if ((isMultiple && dom.length) || (!isMultiple && dom) || count === maxCount) {
      if (observeElRemoved) {
        observeDomRemoved(dom, {
          isMultiple,
          selector,
          parentNode: parentEl,
          alternativeSelector,
          callback: getDom,
        });
      }
      count = 0;
      callback?.(dom);
    } else {
      count += 1;
      timer = window.setTimeout(() => {
        getDom();
      }, delay);
    }
  }

  getDom();
}

export default [{ name: 'waitForElement', fn: waitForElement }];
