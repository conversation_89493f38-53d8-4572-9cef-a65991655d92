import { AnyObj, Dialog, MapUtil } from '@weapp/ui';
import {
  corsImport, getLabel, ua, weappSDK,
} from '@weapp/utils';
import ebdcoms from '../../../utils/ebdcoms';
import { itemChartFn, nativeRun, runtime } from '../Utils';

/** 事项群聊必填参数 */
type ItemChartData = {
  /** 事项所属的模块名称。模块名称取值为：模块名称参数(模块) */
  module?: string;
  /** 事项所属模块的类型ID。模块类型id的取值为：模块类型ID(模块) */
  moduleId?: string;
  /** 需要创建或打开群聊的事项数据ID */
  entityId: string;
  /** 事项详情 */
  entityInfo: any;
  /** 应用ID */
  appId?: string;
};

/** 事项群聊选填参数 */
type ItemChartOptions = {
  /** 事项名称，将作为事项群名称使用 */
  name?: string;
  /** 打开群聊成功回调（仅在该群聊已存在且加入时触发） */
  onOpenChatSuccess?: (res: any) => void;
  /** 打开群聊失败回调（仅在该群聊已存在且加入时触发） */
  onOpenChatFail?: (error: any) => void;
  /** 创建群聊成功回调（仅在该群聊不存在时触发） */
  onCreateEntityGroupSuccess?: (res: any) => void;
  /** 创建群聊失败回调（仅在该群聊不存在时触发） */
  onCreateEntityGroupFail?: (error: any) => void;
  /** 加入群聊成功回调（仅在该群聊已存在但未加入时触发） */
  onJoinEntityGroupSuccess?: (res: any) => void;
  /** 加入群聊失败回调（仅在该群聊已存在但未加入时触发） */
  onJoinEntityGroupFail?: (error: any) => void;
};

// 企业微信的用户信息，公众号的话会多一个 openId 的参数
type CurrentUser = {
  id: string;
  /** 第三方 userId */
  thirdUserId: string;
  /** 服务号openId  */
  openId?: string;
};

const { message } = Dialog;

/** 改用weappSDK.exec调用 */
function scanQRCode(cb?: Function) {
  weappSDK.exec('scanQRCode', { needResult: 1 }).then((res: any) => {
    cb?.(res?.resultStr);
  });
}

function getCurrPosition(cb?: Function) {
  const { getCurrentPosition } = MapUtil;
  getCurrentPosition('gaode')
    .then((res: any) => {
      // 默认使用高德定位
      cb?.(res);
    })
    .catch((err) => {
      console.error(err);
    });
}
function rotatePage(orientation?: number) {
  nativeRun('changeOrientation', {
    orientation: orientation || 0, // 0,竖屏(默认)，1，横屏，2，跟随屏幕转动
  });
}
function getWechatUserInfo(callback?: (res: any) => void) {
  const user = window?.TEAMS?.currentUser || {};
  const userInfo = {
    openid: user.openUserId,
    nickname: user.wechatUserid,
    avataruser: user.avatar?.url,
  };

  // 云桥getCurrentUser方法支持的客户端类型;
  const isSupportBrowser = [
    'wxwork',
    'MicroMessenger',
    'DingTalk',
    'ZhengWuDing',
    'Welink',
    'YunZhiJia',
    'Lark',
    /** 微信小程序 */
    'miniProgram',
  ].includes(ua.browser);

  // 云桥提供的getCurrentUser方法获取用户的openId等信息
  if (isSupportBrowser && weappSDK.customSDK) {
    weappSDK.customSDK.invoke('getCurrentUser', {
      success: (currentUser: CurrentUser) => {
        callback?.({
          ...userInfo,
          ...(currentUser || {}),
          openid: currentUser?.openId || userInfo.openid || '',
        });
      },
      fail: () => {
        callback?.(userInfo);
      },
    });
  } else {
    callback?.(userInfo);
  }
}
function callMobile(tel: string, type: string = 'tel') {
  // todo
  if (type === 'tel') {
    nativeRun('tel', {
      tel,
      fail: () => {
        location.href = `tel:${tel}`;
      },
    });
  } else if (type === 'sms') {
    nativeRun('textMessage', {
      mobile: tel,
      fail: () => {
        location.href = `sms:${tel}`;
      },
    });
  }
}
function sendMessage(
  target: Record<string, any>,
  msg: any[],
  success: () => void = () => {},
  options?: Record<string, any>,
) {
  const isMobile = runtime.isMobile();
  const params = {
    target,
    msgList: msg || [],
    success,
    ...options,
  };
  if (isMobile) {
    weappSDK
      .checkApi('sendMessage')
      .then(() => {
        nativeRun('sendMessage', params);
      })
      .catch(() => {
        // 验证不通过 调用h5方法
        corsImport('@weapp/em').then((em) => {
          em.sendMessage({ ...params, isHide: true });
        });
      });
  } else {
    corsImport('@weapp/em').then((em) => {
      em.sendMessage(params);
    });
  }
}

/** 根据appId获取应用中心：自定义消息的模块信息 */
async function getModuleInfo(appId: string) {
  if (appId) {
    return ebdcoms.asyncExcu('ajax', {
      url: '/api/mc/handle/customModule/getEbMessageModule',
      method: 'post',
      data: {
        dataIds: [appId],
      },
    });
  }

  return new Promise((res) => res([]));
}

async function openItemChart(itemData: ItemChartData, opts?: ItemChartOptions) {
  const isMobile = runtime.isMobile();
  const {
    moduleId, entityId, entityInfo, appId,
  } = itemData || {};
  const {
    name,
    onOpenChatSuccess,
    onOpenChatFail,
    onCreateEntityGroupSuccess,
    onCreateEntityGroupFail,
    onJoinEntityGroupSuccess,
    onJoinEntityGroupFail,
  } = opts || {};

  // 存在appId时，module为ebuilder，moduleId为156
  const module = appId ? 'ebuilder' : itemData?.module;
  const [moduleInfo] = ((await getModuleInfo(appId || '')) || []) as AnyObj[];
  const { yqPcUrl = '', yqH5Url = '' } = moduleInfo || {};
  // imCode为0代表应用自定义消息模块未开启，默认使用ebuilder
  const imCode = !moduleInfo?.imCode ? '156' : String(moduleInfo?.imCode);

  itemChartFn(
    'checkEntityGroup',
    {
      entityId, // 事项id
      module, // 模块名称
      join: 0, // 非必填 若群已经存在且该用户未加入群时的处理办法：0:直接加群 1:不加群, 默认 1
      success(res: any) {
        const status = String(res?.status) || '';

        switch (status) {
          // 0  ： 存在事项群，且已经加入该群;
          case '0':
            // 若创建过且当前用户在群聊中，则直接打开群聊
            itemChartFn(
              'openChat',
              {
                type: 2, // 打开的目标类型，1表示普通内部人员，2表示群组，3表示外部联系人
                gid: res.groupId, // 群组ID，当type值为2时使用
                group_type: 0, // 当type值为2时使用 1表示为客户群组 由小助手生成，其他为普通群组
                success: (successRes: any) => {
                  onOpenChatSuccess?.(successRes);
                },
                fail: (error: any) => {
                  if (onOpenChatFail) {
                    onOpenChatFail(error);
                  } else {
                    message({ type: 'info', content: getLabel('186652', '打开群聊失败') });
                  }
                },
              },
              isMobile,
            );
            break;

          // 100： 该事项没有事项群;
          case '100': {
            // 若未创建过群聊，则需要先创建群聊（创建时页面会弹窗选择群聊人员），创建成功后打开群聊并将当前事项发送到群聊中
            const _entityInfo = JSON.stringify(
              moduleInfo
                ? {
                  ...entityInfo,
                  // imCode：自定义消息模块的typeId
                  imType: (appId && imCode) || entityInfo.imType,
                  pu: entityInfo.pu || yqPcUrl,
                  hu: entityInfo.hu || yqH5Url,
                  module: entityInfo?.module || module,
                }
                : {
                  ...entityInfo,
                  // 通过appId打开的事项群聊
                  // 如果该应用未开启 自定义消息模块 的话，默认按照ebuilder模块显示
                  imType: appId ? '156' : entityInfo?.imType,
                },
            );
            const options = {
              module, // 事项 module
              // 通过appId打开的事项群聊，module默认为ebuilder: 156
              moduleId: appId ? '156' : moduleId, // 事项 模块id
              entityId, // 事项 id
              entityInfo: _entityInfo, // json 字符串，事项详情
              name, // 事项名称,将作为事项群名称使用  [非必填]
              openChat: 1, // 是否打开该会话 0：不打开 1：打开  默认1[打开]
              success: (createEntityGroupRes: any) => {
                // code: 0 表示成功， 1 创建群失败
                // groupId: // 事项群聊 id
                switch (createEntityGroupRes?.code) {
                  case 0:
                    onCreateEntityGroupSuccess?.(createEntityGroupRes);
                    break;

                  case 1:
                  default:
                    if (onCreateEntityGroupFail) {
                      onCreateEntityGroupFail(createEntityGroupRes);
                    } else {
                      message({ type: 'info', content: getLabel('186653', '创建群聊失败') });
                    }
                    break;
                }
              },
              fail: (error: any) => {
                onCreateEntityGroupFail?.(error);
              },
            };

            if (isMobile) {
              weappSDK
                .checkApi('createEntityGroup')
                .then(() => {
                  // app端需自行实现跳转选择人员页面
                  // 通过registerBroadcast回调获取用户配置的人员信息
                  weappSDK.invoke('registerBroadcast', {
                    name: 'weappEmCreateEntityGroupParams', // 广播名称
                    fail: () => console.error('广播(weappEmCreateEntityGroupParams)注册失败'),
                    callback: (param: any) => {
                      nativeRun('createEntityGroup', {
                        ...options,
                        rules: param?.param?.rules,
                        users: param?.param?.uids,
                      });
                    },
                  });

                  // invoke模式
                  weappSDK.invoke('openLink', {
                    url: `/mobile/em/createEntityGroupSelect?module=${module}&entityId=${entityId}`,
                  });
                })
                .catch(() => {
                  // h5端调用createEntityGroupByIM方法实现
                  itemChartFn('createEntityGroupByIM', options);
                });
            } else {
              itemChartFn('createEntityGroupByIM', options);
            }
            break;
          }

          // 101： 存在事项群，但未加入该群
          case '101':
            // 若创建过但当前用户不在群聊中，则将用户添加到群聊后打开群聊
            itemChartFn(
              'joinEntityGroup',
              {
                module, // 事项 module
                entityId, // 事项 id
                openChat: 1, // 是否打开该会话 0：不打开 1：打开  默认1[打开]
                success: (joinEntityGroupRes: any) => {
                  // code: 0 表示成功， 2 加入群失败
                  // groupId: 事项群聊 id
                  switch (joinEntityGroupRes?.code) {
                    case 0:
                      onJoinEntityGroupSuccess?.(joinEntityGroupRes);
                      break;

                    case 2:
                    default:
                      if (onJoinEntityGroupFail) {
                        onJoinEntityGroupFail(joinEntityGroupRes);
                      } else {
                        message({ type: 'info', content: getLabel('186654', '加入群聊失败') });
                      }
                      break;
                  }
                },
                fail: (error: any) => {
                  onJoinEntityGroupFail?.(error);
                },
              },
              isMobile,
            );
            break;

          default:
            break;
        }
      },
    },
    isMobile,
  );
}

export default {
  scanQRCode,
  getCurrPosition,
  rotatePage,
  getWechatUserInfo,
  callMobile,
  sendMessage,
  openItemChart,
};
