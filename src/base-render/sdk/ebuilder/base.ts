import { request, RequestOptionConfig } from '@weapp/utils';
import ebdcoms from '../../../utils/ebdcoms';

type CurrUserOptions = {
  /** 是否获取最新的用户数据 */
  noCache: boolean;
  /** 模块标识 */
  servicePath?: string;
};

function getCurrUser(opts?: CurrUserOptions) {
  const { noCache, servicePath = 'ebuilder/common' } = opts || {};

  const currUser = window.TEAMS?.currentUser;

  if (noCache) {
    const userInfo = {
      loginaccount: '{loginaccount}',
      mobile: '{mobile}',
    };

    return ebdcoms
      .asyncExcu('ajax', {
        url: `/api/${servicePath}/ebVariable/replace`,
        method: 'post',
        data: JSON.stringify(userInfo),
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then((data) => {
        try {
          return { ...currUser, ...(JSON.parse(data) || {}) };
        } catch (error) {
          return currUser;
        }
      });
  }

  return currUser || {};
}
function requestFun(options: RequestOptionConfig) {
  return request({
    customHandleError: true,
    cancelLimit: false,
    ...options,
  });
}

export default [
  { name: 'getCurrUser', fn: getCurrUser },
  { name: 'request', fn: requestFun },
];
