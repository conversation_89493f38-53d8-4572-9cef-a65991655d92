import { LinkInfo } from '@weapp/ebdcoms/lib/common/page-link/types';
import { eventEmitter, qs } from '@weapp/utils';
import ebdcoms from '../../../utils/ebdcoms';
import {
  getCustomLinkInfo,
  getSystemPageLink,
  nativeRun,
  runtime,
  transformUrlToRouteJumpLinkInfo,
} from '../Utils';

const root = window.publicUrlweappUi || window.publicUrlstatic || window.publicUrl || '';
function getPath(url: string) {
  if (/^\d+$/.test(url)) {
    const isMobile = runtime.isMobile();
    const rootRouter = isMobile ? '/mobile' : '/sp';
    return `${root}${rootRouter}/ebdpage/view/${url}/page/${url}`;
  }
  return url;
}
function openWebView(url: string, options?: any, fali?: Function) {
  const fail = fali
    || function () {
      location.href = url;
    };
  nativeRun('openLink', {
    url,
    ...options,
    fail,
  });
}
const getQueryParams = (search: string) => {
  if (!search) {
    return {};
  }

  const hrefArr = search.split('?');
  const queryParams = hrefArr.length > 1 ? hrefArr[hrefArr.length - 1] : '';

  return qs.parse(queryParams) || {};
};

export enum OpenMode {
  Default = '4', // 当前页面
  New = '0', // 新窗口
  Modal = '5', // 弹出窗口
  SlideLeftFromRight = '1', // 右侧滑出
  Replace = '8', // 重定向,
  RightLayout = '7', // 右侧推出
  Layout = '6', // 当前布局
  MFloatDialog = '11', // 浮层页面
  WebView = '10', // 新的 webview 窗口
  RouterJump = '12', // 路由跳转
}

const openModeVals = {
  1: OpenMode.Default, // 当前页面
  2: OpenMode.New, // 新窗口
  3: OpenMode.Modal, // 弹出窗口
  4: OpenMode.SlideLeftFromRight, // 右侧滑出
  5: OpenMode.Replace, // 重定向
  6: OpenMode.RightLayout, // 右侧推出
  7: OpenMode.Layout, // 当前布局
  8: OpenMode.MFloatDialog, // 浮层页面
  9: OpenMode.WebView, // 新的 webview 窗口
  10: OpenMode.RouterJump, // 路由跳转
};
export interface IOpenPageOptions {
  openType: number;
  layoutType: string;
  compId: string;
  pageId: string;
  // 支持直接传入linkInfo默认使用linkInfo数据跳转
  linkInfo?: LinkInfo;
  isBlankClose?: boolean /** 是否空白处关闭弹窗 */;
  modalWidth?: string /** 弹窗宽度 */;
  modalHeight?: string /** 弹窗高度 */;
  pageIcon?: string | Record<string, any> /** 页面图标 */;
  pageTitle?: string | Record<string, any> /** 页面标题 */;
  currentPageId?: string; // 跳转前页面的pageid
  instanceId?: string; // 页面运行时唯一键
  appid?: string;
}

/**
 *
 * @param url 跳转链接，默认18位数字为EB页面，在PC端可以触发各种打开方式
 * @param params 跳转地址的链接参数
 * @param options 可选参数，包括打开方式、选择布局
 * @param onSave 可选参数，保存后的回调
 * @returns
 */
function openPage(
  url: string,
  params?: any,
  options?: number | IOpenPageOptions,
  onSave?: Function,
) {
  let paramsTrans = params;
  let optionsTrans = options;
  const opts = !!optionsTrans && typeof optionsTrans !== 'number'
    ? optionsTrans
    : {
      compId: '',
      pageId: '',
      isBlankClose: true,
      modalHeight: '',
      modalWidth: '',
      pageIcon: '',
      pageTitle: '',
      instanceId: '',
      appid: '',
    };
  /**
   * 针对第二个参数是数字，同时没有第三个参数的处理
   * ? 当第二个参数传了数字，同时没有传第三个参数时，视为把 params 当成了 options
   */
  if (typeof params === 'number' && options === undefined) {
    // 认为没传 params
    paramsTrans = undefined;
    // 认为传入的 params 指的是 options
    optionsTrans = params;
  }

  const openType = typeof optionsTrans === 'number' ? optionsTrans : optionsTrans?.openType;
  const openMode = openModeVals[openType as keyof typeof openModeVals] || '4';
  let inputLinkInfo = typeof options === 'object' && options?.linkInfo;

  const isEbPageId = /^\d+$/.test(url);
  const isLayoutUrl = url.includes('/sp/ebdfpage/card') || url.includes('/sp/ebdfpage/list');
  const systempageLink = getSystemPageLink(url, { openMode, params: paramsTrans });

  const isCustomUrl = !isEbPageId && !isLayoutUrl && !systempageLink && !inputLinkInfo;

  // 自定义链接支持多种打开方式
  if (isCustomUrl && !runtime.isMobile()) {
    inputLinkInfo = getCustomLinkInfo(url, { openMode, params: paramsTrans, options });
  }

  if (
    ((isEbPageId || isLayoutUrl) && !runtime.isMobile())
    || inputLinkInfo
    || systempageLink
    // 浮层页面、路由跳转的方式，通过jumplink 的方式打开
    || [OpenMode.MFloatDialog, OpenMode.RouterJump].includes(openMode)
  ) {
    const pageId = url;
    const { JumpLink } = ebdcoms.get();
    // 中间栏：main；左边栏：leftSider；右边栏：rightSider；上边栏：topSider；下边栏：bottomSider；
    const layoutType = typeof optionsTrans === 'number' ? undefined : optionsTrans?.layoutType;

    let linkInfo = inputLinkInfo || {
      page: {
        pageName: '',
        pageId,
        pageType: isLayoutUrl ? 'LINK' : 'PAGE',
        openMode,
        terminalScopes: 'PC,MOBILE',
      },

      openMode,
      winSize: '800px',
      params: [],
      name: '',
      layoutType,
    };

    // 表单iframe地址转组件
    if (
      /\/sp\/ebdfpage\/(card|list)\//.test(url)
      && (openMode === OpenMode.Modal || openMode === OpenMode.SlideLeftFromRight)
    ) {
      let pageIdTemp = pageId;
      let paramsTemp: any = [];
      let pageType = 'LAYOUT';
      if (/\/sp\/ebdfpage\/card\//.test(url)) {
        let path: any = pageId.split('/sp/ebdfpage/card');
        path = path[1]?.split('/');

        if (path.length >= 3) {
          pageIdTemp = `${path[1]}/${path[2]}`;
        }

        if (path.length >= 4) {
          const qId = path[3] ? path[3] : '';
          paramsTemp = [
            {
              name: 'id',
              type: 'fixed',
              value: qId,
            },
          ];
        }
      } else {
        const listId = /\/sp\/ebdfpage\/list\/(\d*)/.exec(url)?.[1];
        if (listId) {
          pageIdTemp = listId;
          pageType = 'SEARCH';
        }
      }

      linkInfo = {
        page: {
          pageName: '',
          pageId: pageIdTemp,
          pageType,
          openMode,
        },
        openMode,
        params: paramsTemp,
        name: '',
        layoutType,
      } as any;
    }
    if (systempageLink) {
      linkInfo = systempageLink as any;
    }

    if (openMode === OpenMode.Layout) {
      const transParams = Object.keys(paramsTrans).map((key) => ({
        name: key,
        value: paramsTrans[key],
        type: 'fixed',
      }));
      let _linkInfo = { ...linkInfo, params: transParams };

      let urlLink = pageId;
      // urlLinkType - 0: 选择链接; 1: 选择字段; 2: 选择菜单
      const isLink = isLayoutUrl || isCustomUrl;
      /** 是否为内部链接 */
      const isInnerLink = urlLink.startsWith('/sp/') || (urlLink.startsWith('/second/') && urlLink.includes('/sp/'));
      // 通常以sp后的节点为模块名
      const moduleName = urlLink.split('/sp/')[1]?.match(/[^/]+/)?.[0];

      if (moduleName === 'ebdpage') {
        _linkInfo = {
          ...linkInfo,
          page: {
            ...linkInfo.page,
            pageId: pageId?.match(/\/(preview|view)\/(\d+)/)?.[2] || pageId,
            pageType: 'PAGE',
            openMode: '6',
          },
        } as any;
      }

      // 自定义链接地址和根据字段值渲染当前布局：
      // 如果url内包含/sp/标识认定为内部链接，需将后续路由追加到当前页面的url上
      // renderLayout区域会根据url加载对应模块的主路由RouterMain，实现内容渲染
      // ebdpage 本模块的单页不跳转url
      if (isLink && isInnerLink && moduleName !== 'ebdpage') {
        // eslint-disable-next-line prefer-destructuring
        urlLink = urlLink.split('/sp')[1]; // 内部链接需要去除/sp然后追加到当前页面的url上

        const { pathname, search } = window.location;
        const { getFreeParams } = ebdcoms.get();
        // 收集参数
        const newSearch = getFreeParams(urlLink, {});
        const newQueryParams = getQueryParams(newSearch);

        const hrefArr = urlLink.split('?');
        // 内部链接上如果有xxx?a=b，其中的参数已经收集在newQueryParams内，追加时需要去掉?a=b
        urlLink = hrefArr.length > 1 ? hrefArr[0] : urlLink;

        /** 当前布局的链接 */
        const preEbLink = (getQueryParams(search)?.ebLink as string) || '';

        const urlParams = { ...(paramsTrans || {}) };
        const pathParams = { ...urlParams, ...newQueryParams, ebLink: urlLink };

        const searchParams = `?${qs.stringify({
          ...newQueryParams,
          ...pathParams,
        })}`;

        // 如果上一个页面也是当前布局渲染内部链接的话，需要将之前追加的url去掉
        const _pathname = search.includes('ebLink') ? pathname.split(preEbLink)[0] : pathname;

        window.__ebpage_history__?.history?.push(`${_pathname}${urlLink}${searchParams}`);
      }
      const renderLayoutInfo = {
        ..._linkInfo,
        currentPageId: typeof options === 'object' ? options?.currentPageId : '',
      };
      eventEmitter.emit('@weapp/designer', 'grid.renderLayout', renderLayoutInfo);
    } else if (openMode === OpenMode.RouterJump) {
      // 路由跳转支持部分url
      const pageInfo = transformUrlToRouteJumpLinkInfo(url);

      if (pageInfo) {
        const _linkInfo = {
          ...linkInfo,
          page: {
            ...linkInfo.page,
            ...pageInfo.page,
            appid: opts.appid || pageInfo.page.appid,
          },
        };

        JumpLink(
          {
            ..._linkInfo,
          },
          {
            params: { ...paramsTrans, ...pageInfo.params },
            clientType: runtime.isMobile() ? 'MOBILE' : 'PC',
            compId: opts!.compId,
            pageId: pageInfo.page.pageId,
            instanceId: opts?.instanceId,
            ...window.__ebpage_history__,
          },
        );

        return;
      }
    } else {
      // 若 pathIcon 有值，且值类型为 string，则将其转换为 { path: value } 的格式
      let pageIcon = opts?.pageIcon;
      let pageTitle = opts?.pageTitle;
      if (pageIcon) {
        if (typeof pageIcon === 'string') {
          pageIcon = { path: pageIcon };
        }
        const { path } = pageIcon;
        // 系统图标库内的图标 {path: `#iconName`}
        if (path && /^Icon-.*/.test(path)) {
          pageIcon.path = `#${path}`;
        }
      }
      // 若 pageTitle 有值，且值类型为 string，则将其转换为多语言 { nameAlias: value } 的格式
      if (pageTitle && typeof pageTitle === 'string') {
        pageTitle = { nameAlias: pageTitle };
      }

      JumpLink(
        {
          ...linkInfo,
          isBlankClose: opts?.isBlankClose,
          modalWidth: opts?.modalWidth, // 弹出窗口时弹窗宽度的设置
          winSizeNew: opts?.modalWidth, // 右侧滑出、右侧推出时窗口宽度的设置
          modalHeight: opts?.modalHeight,
          pageIcon,
          pageTitle,
        },
        {
          params: paramsTrans,
          clientType: runtime.isMobile() ? 'MOBILE' : 'PC',
          compId: opts!.compId,
          pageId: opts!.pageId,
          onSave,
          instanceId: opts?.instanceId,
          ...window.__ebpage_history__,
        },
      );
    }

    return;
  }
  let webViewPath = getPath(url);
  let pagePath = url;
  if (paramsTrans) {
    pagePath = url.indexOf('?') !== -1
      ? `${url}&${qs.stringify(paramsTrans)}`
      : `${url}?${qs.stringify(paramsTrans)}`;
    webViewPath = webViewPath.indexOf('?') !== -1
      ? `${webViewPath}&${qs.stringify(paramsTrans)}`
      : `${webViewPath}?${qs.stringify(paramsTrans)}`;
  }
  openWebView(webViewPath, { openType: openType || 1 }, () => {
    // 移动端不区分跳转类型，PC根据 openType 来进行页面跳转
    if (runtime.isMobile()) {
      location.href = webViewPath;
    } else if (openType === 2) {
      window.open(pagePath);
    } else if (openType === 5) {
      location.replace(pagePath);
    } else {
      location.href = pagePath;
    }
  });
}
function backPage() {
  history.go(-1);
}
function backToHomepage() {
  // todo
  const isEbdApp = location.pathname.indexOf('/ebdapp/view/') !== -1;
  const isPreview = location.pathname.startsWith('/sp/ebddesigner/preview');
  const isMobile = location.pathname.indexOf('/mobile') !== -1;
  const pathName = location.pathname.split('/');

  if (isEbdApp) {
    // 获取pageId在pathName中的索引
    const index = isMobile ? 5 : 4;

    if (pathName.length > index) {
      location.href = pathName.slice(0, index).join('/');
    }
  }
  if (isPreview) {
    const homePageId = window.EBPageList[0].id;

    pathName[5] = homePageId;
    location.replace(pathName.slice(0, 6).join('/'));
  }
}

export default {
  openPage,
  backPage,
  backToHomepage,
  openWebView,
};
