import {
  <PERSON><PERSON>,
  CorsComponent,
  Dialog,
  Icon,
  Input,
  InputProps,
  MActionSheet,
  MDialog,
  Menu,
  MenuItemData,
  Spin,
  utils,
} from '@weapp/ui';
import {
  classnames, getLabel, has, isEmpty,
} from '@weapp/utils';
import React, {
  ReactText, useCallback, useEffect, useState,
} from 'react';
import ReactDOM from 'react-dom';
import ebdcoms from '../../../utils/ebdcoms';
import { clsPrefix, EbSdkElPrefix } from '../constants';
import { FooterMenus } from '../types';
import { formatterMenuData, getShowSensitive, runtime } from '../Utils';
import './index.less';

interface ModalProps extends React.Attributes {}

interface MenuPanelProps extends React.Attributes {
  data: MenuItemData[];
  onChange?: (item: MenuItemData, value: string) => void;
}

interface DialogPromtProps extends React.Attributes {
  // 同一页面同时打开多个prompt弹窗
  id: string;
  message: string;
  defaultValue?: string;
  onSure?: Function;
  inputConfig?: InputProps;
}

type PromptOptions = {
  defValue?: string;
  btns?: string[];
  onOk: Function;
  inputConfig?: InputProps & { required?: boolean };
};

export const ModalPanel: React.FC<ModalProps> = () => <div />;

export const MenuPanel: React.FC<MenuPanelProps> = (props) => {
  const { onChange, data } = props;

  const removeMenu = useCallback(() => {
    const dropWapper = `${EbSdkElPrefix}-drop-wrapper`;
    if (document.getElementsByClassName(dropWapper).length) {
      const dom = document.getElementsByClassName(dropWapper)[0];

      // 卸载点击事件
      document.body.removeEventListener('click', removeMenu, true);
      dom.parentElement?.removeChild?.(dom);
    }
  }, []);

  const onMenuClick = useCallback((val: string, item: any) => {
    removeMenu();

    onChange?.(item, val);
  }, []);

  useEffect(() => {
    const dropWapper = `${EbSdkElPrefix}-drop-wrapper`;
    const dom = document.getElementsByClassName(dropWapper)[0];

    if (dom) {
      document.body.addEventListener('click', removeMenu, true);
    }
  }, []);

  const menuData = formatterMenuData(data);

  return (
    <Menu
      weId={`${props.weId || ''}_pi2a7q`}
      data={menuData}
      position="left"
      cover
      onChange={onMenuClick}
    />
  );
};

const DialogPromt = React.forwardRef((props: DialogPromtProps, promtRef: any) => {
  const { message, defaultValue, inputConfig = {} } = props;
  const [value, setValue] = useState<ReactText>(String(defaultValue || ''));

  const callback = useCallback(() => {
    const { onSure } = props;

    onSure?.(value);
  }, [value]);

  promtRef.current = { callback, value };

  return (
    <div className={`${EbSdkElPrefix}-promt-content`}>
      <div>{message}</div>
      <div>
        <Input
          weId={`${props.weId || ''}_5v7k4y`}
          onChange={setValue}
          value={value}
          {...inputConfig}
        />
        <Icon
          weId={`${props.weId || ''}_m6r1i4`}
          className={classnames(`${EbSdkElPrefix}-promt-content-require`, {
            show: inputConfig.required && !value,
          })}
          name="Icon-Required"
          size="xxs"
        />
      </div>
    </div>
  );
});

const formatConfirmProps = (options: any) => {
  let confirmOpts;

  if (typeof options === 'function') {
    confirmOpts = {
      onOk: () => options && options(),
    };
  } else if (Array.isArray(options)) {
    confirmOpts = {
      okText: options.length > 0 ? options[0] : '',
      cancelText: options.length > 1 ? options[1] : '',
    };
  } else if (utils.isString(options)) {
    confirmOpts = {
      okText: options,
    };
  } else {
    confirmOpts = options;
  }

  return confirmOpts;
};

const formatDialogBtnText = (options: any) => {
  if (utils.isString(options)) {
    return options;
  }
  if (options?.okText) {
    return options?.okText;
  }
};

/**
 * 返回icon结构，如果是string格式，则不做处理（ecode源码处调用时）
 * 如果是object格式，需转换成AssetsItem组件（事件动作-显示菜单处配置时）
 * 如果是底部菜单-纵向排列时，配置icon通过showActionSheetWithOptions是不会显示出来的，需要通过itiem.content去渲染图标
 */
const renderIconContent = (icon: any, content?: string) => {
  if (isEmpty(icon)) return content || '';

  if (utils.isString(icon)) {
    return content || icon;
  }

  if (utils.isObject(icon)) {
    const iconDom = (
      <CorsComponent
        weId="oaelk2"
        app="@weapp/ebdcoms"
        compName="AssetsItem"
        path={icon.path}
        style={icon.style}
      />
    );

    return content ? (
      <div className={`${EbSdkElPrefix}-footer-menu-icon`}>
        {iconDom}
        {content}
      </div>
    ) : (
      iconDom
    );
  }
};

function msg(content: string, delay?: number, type?: 'info' | 'error' | 'success') {
  if (runtime.isMobile()) {
    let newType: any = type;
    if (type === 'error') {
      newType = 'fail';
    }
    MDialog.toast({
      content,
      delay: delay === 0 ? 0 : delay || 2000,
      type: newType || ('info' as any),
    });
  } else {
    Dialog.message({
      content,
      delay: delay === 0 ? 0 : delay || 2000,
      type: type || 'info',
    });
  }
}

function alert(
  content: string,
  options?: {
    title?: string;
    okText?: string;
  },
  cb?: Function,
) {
  let confirmIns: any;

  if (runtime.isMobile()) {
    const close = () => {
      confirmIns?.onClose();
    };
    const btnText = formatDialogBtnText(options);
    MDialog.prompt({
      ...options,
      className: `${clsPrefix}-m-alter`,
      message: content,
      maskClosable: true,
      onClose: () => cb && cb(),
      onOk: () => cb && cb(),
      prompt: false,
      getInstance: (ins) => {
        confirmIns = ins;
      },
      footer: (btnText
        ? [
          {
            key: 'cancel',
            text: btnText,
            onPress: close,
          },
        ]
        : []) as any,
    });
  } else {
    const close = () => {
      confirmIns?.onOk();
    };
    Dialog.confirm({
      content,
      footer: (
        <Button weId="_3k0p3p" type="primary" {...{ onClick: close }}>
          {formatDialogBtnText(options) || getLabel('55010', '关闭')}
        </Button>
      ),
      getInstance: (ins) => {
        confirmIns = ins;
      },
      onOk: () => cb && cb(),
      ...formatConfirmProps(options),
    });
  }
}

function confirm(
  content: string,
  options?: {
    title?: string;
    okText?: string;
  },
  cb?: Function,
) {
  if (runtime.isMobile()) {
    MDialog.prompt({
      message: content,
      maskClosable: true,
      onOk: () => cb && cb(),
      prompt: false,
      ...formatConfirmProps(options),
    });
  } else {
    Dialog.confirm({
      content,
      onOk: () => cb && cb(),
      ...formatConfirmProps(options),
    });
  }
}

function prompt(content: string, options?: PromptOptions) {
  const {
    defValue = '', onOk, btns = [], inputConfig = {},
  } = options || {};
  const id = ebdcoms.excu('UUID');
  let instanceRef: any;
  const ref = React.createRef<{ callback: Function; value: ReactText; state?: any }>();

  const getInstance = (_instanceRef: any) => {
    instanceRef = _instanceRef;
  };

  const handleOk = () => {
    if (instanceRef) {
      // 校验必填
      if (inputConfig.required && !ref.current?.value) {
        Dialog.message({ content: getLabel('317036', '输入框必填！'), type: 'info' });

        return;
      }

      instanceRef.onOk();
    }
  };

  const handleMobileOk = () => {
    if (instanceRef) {
      // 校验必填
      if (inputConfig.required && !ref.current?.state?.value) {
        MDialog.toast({ content: getLabel('317036', '输入框必填！') });

        return;
      }

      instanceRef.mDialogWarpRef.onOk();
    }
  };

  const handleCancel = () => {
    if (instanceRef) {
      instanceRef.onClose();
    }
  };

  if (runtime.isMobile()) {
    MDialog.prompt({
      message: content,
      maskClosable: true,
      promptProps: { ...inputConfig, defaultValue: defValue, ref },
      onOk: (val) => onOk && onOk(val),
      getInstance,
      footer: ['cancel', 'ok'].map((btn: string) => ({
        key: btn,
        text:
          btn === 'ok'
            ? btns[0] || getLabel('55437', '确认')
            : btns[1] || getLabel('53937', '取消'),
        type: btn === 'ok' ? 'primary' : 'default',
        onPress: btn === 'ok' ? handleMobileOk : handleCancel,
      })),
    });
  } else {
    Dialog.confirm({
      content: (
        <DialogPromt
          weId="m5vtw5"
          ref={ref}
          key={id}
          id={id}
          message={content}
          defaultValue={defValue}
          onSure={onOk}
          inputConfig={options?.inputConfig || {}}
        />
      ),
      onOk: () => {
        ref.current?.callback?.();
      },
      getInstance,
      footer: (
        <div>
          <Button key="primary" weId="ca9cft" type="primary" onClick={handleOk as any}>
            {btns[0] || getLabel('55437', '确认')}
          </Button>
          <Button key="cancel" weId="hm9g92" type="default" onClick={handleCancel as any}>
            {btns[1] || getLabel('53937', '取消')}
          </Button>
        </div>
      ),
    });
  }
}

function footerMenu(footerMenus: FooterMenus) {
  const menu = Array.isArray(footerMenus) ? { layout: 'menu', items: footerMenus } : footerMenus;
  const data = menu.items.map((item) => {
    const { text: showText } = item;
    if (typeof showText === 'object' && has(showText, 'nameAlias')) {
      item.text = showText?.nameAlias || '';
    }
    const content = menu?.layout === 'panel' ? item.text : renderIconContent(item.icon, item.text);

    return {
      key: item.id,
      id: item.id,
      content,
      icon: renderIconContent(item.icon),
      onClick: item.callback,
    };
  });

  data.push({ content: getLabel('53937', '取消') } as any);

  /**
   * 敏感词上报入口展示控制，组件库默认展示 -- true
   * - 改为默认不展示，调用时可配置
   */
  const showSensitive = getShowSensitive(footerMenus);

  if (menu?.layout === 'panel') {
    MActionSheet.showActionSheetWithGridOptions({
      options: data,
      cancelButtonIndex: data.length - 1,

      maskClosable: true,
      column: footerMenus?.column ?? 5,
      showSensitive,
      className: `${EbSdkElPrefix}-footer-wrapper`,
    });
  } else {
    MActionSheet.showActionSheetWithOptions({
      options: data,
      cancelButtonIndex: data.length - 1,
      maskClosable: true,
      showSensitive,
      className: `${EbSdkElPrefix}-footer-wrapper`,
    });
  }
}

function dropDownMenu(
  menus: { icon?: string; text?: string; id?: string }[],
  onChange?: (item: MenuItemData, val: string) => void,
  menuPosition?: string,
  externalBindDom?: HTMLElement,
) {
  const data = menus.map((item, index: number) => ({
    ...item,
    id: item?.id || String(index),
    content: item.text,
    icon: renderIconContent(item.icon),
    index,
  }));

  const dropWapper = `${EbSdkElPrefix}-drop-wrapper`;
  if (document.getElementsByClassName(dropWapper).length) {
    const dom = document.getElementsByClassName(dropWapper)[0];

    dom.parentElement?.removeChild?.(dom);
    return;
  }

  const bindDom = externalBindDom || document.getElementsByClassName('ebsdk-bind-dom')?.[0];

  if (!bindDom) return;

  const {
    left, top, height, bottom, width, right,
  } = bindDom && bindDom?.getBoundingClientRect();
  const browserWindowWidth = document.documentElement.clientWidth || document.body.clientWidth;
  const browserWindowHeight = document.documentElement.clientHeight || document.body.clientHeight;

  const overlayLayer = document.createElement('div');

  if (menuPosition === 'bottom') {
    overlayLayer.style.top = `${top + height + 4}px`;
    overlayLayer.style.left = browserWindowWidth - left < 120 ? `${right - 120}px` : `${left}px`;
    overlayLayer.style.maxHeight = '150px';
  } else if (menuPosition === 'right') {
    overlayLayer.style.top = `${top}px`;
    overlayLayer.style.left = `${left + width + 4}px`;
    overlayLayer.style.maxHeight = '150px';
    overlayLayer.style.zIndex = '99';
    // 悬浮按钮展示在右侧时，判断右侧、底部是否会超出可视区，超出时向左、向上移动
    if (browserWindowWidth - right < 120) {
      overlayLayer.style.left = `${browserWindowWidth - 120 - 4}px`;
      overlayLayer.style.top = `${bottom + 4}px`;
    }
    const menuCurHeight = menus?.length * 35 < 150 ? menus?.length * 35 : 150;
    if (browserWindowHeight - bottom < menuCurHeight) {
      overlayLayer.style.top = 'auto';
      overlayLayer.style.bottom = `${browserWindowHeight - top + 4}px`;
    }
  } else {
    overlayLayer.style.top = `${top + height + 4}px`;
    const leftNum = left + width - 60 > 0 ? left + width - 60 : 0;
    overlayLayer.style.left = `${leftNum}px`;
    overlayLayer.style.maxHeight = `${browserWindowHeight - bottom - 8}px`;
  }
  overlayLayer.className = dropWapper;
  overlayLayer.style.width = `${120}px`;

  document.body.appendChild(overlayLayer);

  ReactDOM.render(<MenuPanel weId="ieliml" data={data} onChange={onChange} />, overlayLayer);
}

function showLoader(text: string, selector?: string) {
  const SpinCom = Spin;
  const spinWapper = `${EbSdkElPrefix}-spin-wrapper`;

  if (selector) {
    const dom = document.body.querySelector(selector);

    if (dom) {
      const spinDom = document.createElement('div');

      spinDom.className = spinWapper;
      dom.appendChild(spinDom);

      ReactDOM.render(
        <SpinCom weId="4ihu65" text={text || ''}>
          <div className={`${EbSdkElPrefix}-spin-content`} />
        </SpinCom>,
        spinDom,
      );
    }

    return;
  }

  if (document.getElementsByClassName(spinWapper).length) {
    return;
  }
  const divNew = document.createElement('div');
  divNew.className = spinWapper;
  document.body.appendChild(divNew);

  ReactDOM.render(
    <SpinCom weId="4ihu65" text={text || ''}>
      <div className={`${EbSdkElPrefix}-spin-content`} />
    </SpinCom>,
    divNew,
  );
}

function hideLoader() {
  const spinWapper = `${EbSdkElPrefix}-spin-wrapper`;
  if (document.getElementsByClassName(spinWapper).length) {
    const dom = document.getElementsByClassName(spinWapper)[0];

    dom.parentElement?.removeChild?.(dom);
  }
}
export default {
  msg,
  alert,
  confirm,
  prompt,
  footerMenu,
  dropDownMenu,
  showLoader,
  hideLoader,
};
