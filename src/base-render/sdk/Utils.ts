import { corsImport, last, weappSDK } from '@weapp/utils';
import ebdcoms from '../../utils/ebdcoms';
import { OpenMode } from './ebuilder/pageJump';
import { FooterMenus, MenuItemInfo } from './types';

const ua = navigator.userAgent || navigator.vendor || window.opera;

export const runtime = {
  isIpad() {
    return ua.match(/(iPad).*OS\s([\d_]+)/);
  },
  isIphone() {
    return !this.isIpad() && ua.match(/(iPhone\sOS)\s([\d_]+)/);
  },
  isAndroid() {
    return ua.match(/(Android)\s+([\d.]+)/);
  },
  isMobile() {
    return (
      this.isIphone()
      || this.isAndroid() // 通过参数控制是否显示移动端样式，如设计器中预览页面
      || location.pathname?.indexOf('/mobile/') !== -1
    );
  },
};

// eslint-disable-next-line max-len
export const nativeRun = (api: string, options?: any) => weappSDK
  .checkApi(api)
  .then(() => weappSDK
    .invoke(api, { ...options })
    .then((res) => {
      // invoke方法返回 promise 对象，通过 resolve 与 reject 方法处理成功与失败的情况
      if (options?.success) {
        options?.success(res);
      }
    })
    .catch((err) => {
      console.log(err);
      if (options?.fail) {
        options?.fail();
      }
    }))
  .catch(() => {
    if (options?.fail) {
      options?.fail();
    }
  });

/**
 * 获取 敏感词上报入口展示 配置
 */
export const getShowSensitive = (footerMenus: FooterMenus) => {
  try {
    if (Array.isArray(footerMenus)) {
      const targetMenu = footerMenus?.find((menu) => 'showSensitive' in menu) || {};

      return targetMenu?.showSensitive || false;
    }

    return footerMenus?.showSensitive || false;
  } catch (error) {
    console.error(error);
    return false;
  }
};

/**
 * 从 pageParams 中获取上一个页面的 pageId
 */
export const getPrevPageId = () => {
  const defaultPageId = last(Object.keys(window.ebuilderSDK?.pageSDKs || {})) || '';
  try {
    const pageParams = typeof window?.EBUILDER?.pageParams === 'string'
      ? JSON.parse(window?.EBUILDER?.pageParams)
      : {};

    if (!pageParams) {
      return defaultPageId;
    }

    // 获取页面访问顺序
    const pageIdList = Object.keys(pageParams).sort((pageA: string, pageB: string) => {
      const pageAHistoryIdx = pageParams[pageA]?._historyIndex || 0;
      const pageBHistoryIdx = pageParams[pageB]?._historyIndex || 0;
      return pageAHistoryIdx - pageBHistoryIdx;
    });

    const pageListLength = pageIdList?.length;

    if (pageListLength >= 2) {
      // 返回上一个访问的页面
      return pageIdList?.[pageListLength - 2] || defaultPageId;
    }

    return pageIdList[0] || defaultPageId;
  } catch (error) {
    return defaultPageId;
  }
};

export const itemChartFn = (name: string, options: any, isMobile?: boolean) => {
  if (!name) return;

  if (isMobile) {
    weappSDK
      .checkApi(name)
      .then(() => {
        nativeRun(name, options);
      })
      .catch(() => {
        // 验证不通过 调用h5方法
        corsImport('@weapp/em').then((em) => {
          em[name](options);
        });
      });
  } else {
    corsImport('@weapp/em').then((em) => {
      em[name](options);
    });
  }
};

export const formatterMenuData = (data: MenuItemInfo[]): MenuItemInfo[] => {
  if (!Array.isArray(data)) {
    return data;
  }

  const { getLocaleValue } = ebdcoms.get() || {};

  return data.map((item) => {
    const { content, text, ...restData } = item;

    return {
      ...restData,
      text: getLocaleValue(text),
      content: getLocaleValue(content),
    };
  });
};

/**
 * 获取系统页面link
 * @param url 地址 /sp/systempage/xxx
 * @param opts
 * @returns
 */
export const getSystemPageLink = (url: string, opts: any) => {
  const { openMode, params = {} } = opts || {};
  const isSystemUrl = url.startsWith('systempage/');
  let linkInfo = null;
  if (isSystemUrl) {
    const { SystemPageType = {} } = ebdcoms.get() || {};
    const urlSplits = url.split('/');
    const pageType = 'SYSTEMPAGE';
    const urlLast = urlSplits[urlSplits.length - 1];
    const linkParams = Object.keys(params).map((key) => ({
      name: key,
      type: 'fixed',
      value: params[key],
    }));
    let pageId = '';

    Object.keys(SystemPageType).forEach((key) => {
      if (SystemPageType[key].toLocaleLowerCase() === urlLast) {
        pageId = SystemPageType[key];
      }
    });

    if (pageId) {
      linkInfo = {
        page: {
          pageId,
          pageType,
          openMode,
        },
        openMode,
        winSize: '800px',
        params: linkParams,
      };
    }
  }

  return linkInfo;
};

export const getCustomLinkInfo = (url: string, opts: any) => {
  const { openMode, params = {}, options = {} } = opts || {};
  const linkParams = Object.keys(params).map((key) => ({
    name: key,
    type: 'fixed',
    value: params[key],
  }));

  return {
    ...options,
    page: {
      pageId: url,
      pageName: url,
      pageType: 'LINK' as any,
      openMode,
    },
    options: {
      ...window.__ebpage_history__,
      clientType: runtime.isMobile() ? 'MOBILE' : 'PC',
    },
    openMode,
    winSize: '800px',
    params: linkParams,
  };
};

/**
 * 移动端路由跳转 根据url转化成page的配置
 */
const urlToRouteJumpLinkRules = [
  {
    // eb页面
    match: (url: string) => /^\d+$/.test(url) || url.includes('/mobile/ebdpage/view'),
    getPageInfo: (url: string) => {
      let pageId = /^\d+$/.test(url) ? url : url.split('/mobile/ebdpage/view/')[1]?.split('/')?.[0];
      let appid = '';

      if (pageId.includes('_')) {
        [appid, pageId] = pageId.split('_');
      }

      return {
        page: {
          pageId,
          pageType: 'PAGE',
          appid,
        },
        openMode: OpenMode.RouterJump,
      };
    },
  },
  {
    // 表单新建、编辑、显示
    match: (url: string) => url.includes('/mobile/ebdfpage/card'),
    getPageInfo: (url: string) => {
      const path = url.split('/mobile/ebdfpage/card/')[1]?.split('/');
      let pageId = `${path[0]}/${path[1]}`;
      const params = { id: path[2] };
      let appid = '';

      if (pageId.includes('_')) {
        [pageId, appid] = pageId.split('/');
        const [_appid, _pageId] = appid.split('_');

        appid = _appid;
        pageId = `${pageId}/${_pageId}`;
      }

      return {
        page: {
          pageId,
          pageType: 'LAYOUT',
          appid,
        },
        openMode: OpenMode.RouterJump,
        params,
      };
    },
  },
  {
    // 表单表格
    match: (url: string) => url.includes('/sp/ebdfpage/list'),
    getPageInfo: (url: string) => {
      let pageId = url.split('/sp/ebdfpage/list/')[1];
      let appid = '';

      if (pageId.includes('_')) {
        [appid, pageId] = pageId.split('_');
      }

      return {
        page: {
          pageId,
          pageType: 'SEARCH',
          appid,
        },
        openMode: OpenMode.RouterJump,
      };
    },
  },
  {
    // 表单日历 看板 甘特图
    match: (url: string) => url.includes('/sp/ebdfpage/viewport'),
    getPageInfo: (url: string) => {
      let pageId = url.split('/sp/ebdfpage/viewport/')[1];
      let appid = '';

      if (pageId.includes('_')) {
        [appid, pageId] = pageId.split('_');
      }

      return {
        page: {
          pageId,
          pageType: 'VIEWPORT',
          appid,
        },
        openMode: OpenMode.RouterJump,
      };
    },
  },
  {
    // eb报表
    match: (url: string) => url.includes('/sp/edc/report/view'),
    getPageInfo: (url: string) => {
      let pageId = url.split('/sp/edc/report/view/')[1];
      let appid = '';

      if (pageId.includes('_')) {
        [appid, pageId] = pageId.split('_');
      }

      return {
        page: {
          pageId,
          pageType: 'REPORT',
          appid,
        },
        openMode: OpenMode.RouterJump,
      };
    },
  },
  {
    // 会议的会议室使用情况
    match: (url: string) => url.includes('/mobile/meeting/roomUsage'),
    getPageInfo: () => ({
      page: {
        pageId: 'MeetingRoomInfo',
        pageType: 'SYSTEMPAGE',
      },
      openMode: OpenMode.RouterJump,
    }),
  },
];

export const transformUrlToRouteJumpLinkInfo = (url: string) => {
  let pageInfo: any = null;

  urlToRouteJumpLinkRules.forEach((urlRule) => {
    if (urlRule.match(url)) {
      pageInfo = urlRule.getPageInfo(url);
    }
  });

  return pageInfo;
};

export default {};
