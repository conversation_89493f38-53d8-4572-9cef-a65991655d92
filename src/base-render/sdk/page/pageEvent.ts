import { PageSDK } from '../PageSDK';

type DisplayType = 'show' | 'hide' | 'switchover';

type DisplayComType = {
  id: string;
  display: DisplayType;
  [key: string]: any;
};

type ShowComOpts = {
  display?: DisplayType;
  children?: DisplayComType[];
};

function pageEventOn(this: PageSDK, evtName: string, comId?: string, cb?: Function) {
  this.events.on(evtName, comId, cb);
}

function pageEventEmit(this: PageSDK, evtName: string, ...args: any[]) {
  this.events.emit(evtName, ...args);
}

function pageEventReady(this: PageSDK, evtName: string, comId?: string, cb?: Function) {
  this.events.ready(evtName, comId, cb);
}

function showCom(this: PageSDK, comId: string, opts: ShowComOpts = {}) {
  const { display, ...restOpts } = opts;
  this.events.emit('event.displayCom', comId, {
    id: comId,
    display: display || 'show',
    ...restOpts,
  });
}

function hideCom(this: PageSDK, comId: string) {
  this.events.emit('event.displayCom', comId, {
    id: comId,
    display: 'hide',
  });
}

export default [
  { name: 'on', fn: pageEventOn },
  { name: 'ready', fn: pageEventReady },
  { name: 'emit', fn: pageEventEmit },
  { name: 'showCom', fn: showCom },
  { name: 'hideCom', fn: hideCom },
];
