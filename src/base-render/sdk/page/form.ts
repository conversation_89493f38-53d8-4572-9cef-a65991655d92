import { PageSDK } from '../PageSDK';

function getFormInstance(_this: PageSDK, comId: string) {
  const com = _this.coms.find((c) => c.id === comId);

  if (com) {
    const { formId } = com.config;
    // 中间表组件
    const formCom = _this.coms.find((c) => c.config.fieldId === formId);

    if (formCom) {
      const comInstance = _this.getCompInstance(formCom.id);

      return comInstance;
    }
  }
}

function getFormFieldVal(this: PageSDK, comId: string) {
  return getFormInstance(this, comId)?.getFormFieldVal(this, comId);
}

function setFormFieldVal(this: PageSDK, comId: string, value: any) {
  return getFormInstance(this, comId)?.setFormFieldVal(this, comId, value);
}

function computedFormFields(this: PageSDK, comId: string, expression: string) {
  return getFormInstance(this, comId)?.computedFormFields(this, comId, expression);
}

function listenFieldChange(this: PageSDK, comId: string, cb?: Function) {
  return getFormInstance(this, comId)?.listenFieldChange(this, comId, cb);
}

/** formId：支持传中间表id获取数据，也支持传明细表的表单id获取数据 */
function getFormDataDetail(this: PageSDK, formId: string) {
  const comInstance = this.getCompInstance(formId);

  if (comInstance?.getFormDataDetail) {
    return comInstance.getFormDataDetail();
  }

  /** 明细表组件 */
  const detailTableCom = this.coms.find((c) => c.config.subFormId === formId);

  if (detailTableCom) {
    return getFormInstance(this, detailTableCom.id)?.getFormDataDetail();
  }

  return [];
}

export default [
  { name: 'getFormFieldVal', fn: getFormFieldVal },
  { name: 'setFormFieldVal', fn: setFormFieldVal },
  { name: 'computedFormFields', fn: computedFormFields },
  { name: 'listenFieldChange', fn: listenFieldChange },
  { name: 'getFormDataDetail', fn: getFormDataDetail },
];
