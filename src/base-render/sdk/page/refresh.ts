import { has, isPlainObject } from '@weapp/utils';
import ebdcoms from '../../../utils/ebdcoms';
import { PageSDK } from '../PageSDK';
import { getPrevPageId } from '../Utils';

function refresh(this: PageSDK, comId: string, condition?: any, fn?: Function) {
  const comInstance = this.getCompInstance(comId);

  if (isPlainObject(condition) && !has(condition, 'type') && !has(condition, 'filter')) {
    /** 如果有条件，会在coms ajax直接传递，统一处理 */
    window.pageRefreshConditions = {
      ...window.pageRefreshConditions,
      [comId]: condition,
    };
  } else if (
    window.pageRefreshConditions
    && Object.hasOwnProperty.call(window.pageRefreshConditions, comId)
  ) {
    delete window.pageRefreshConditions[comId];
  }

  if (comInstance?.refresh) {
    this.events.emit('refresh.sysParams', { compId: comId });

    return comInstance?.refresh?.(comId, condition, fn);
  }

  this.events.emit('operation.btn', comId, null, {
    event: {
      to: 'REFRESHCOM',
    },
  });

  fn?.();
}

function reload(this: PageSDK) {
  this.events.emit('reload', this.pageId);
}

/**
 * 刷新前一个页面上的组件
 * - 找到上一个页面的 pageSdk，然后调用它的 refresh 方法
 */
function refreshPrevPageComp(this: PageSDK, comId: string, condition?: any, fn?: Function) {
  const pageId = getPrevPageId();
  const pageSdk = window.ebuilderSDK?.pageSDKs?.[pageId];

  if (pageSdk) {
    return Promise.resolve(pageSdk?.refresh?.bind(pageSdk)?.(comId, condition, fn));
  }

  return Promise.resolve();
}

/**
 * 刷新当前页面数据集
 */
function refreshDataSet(this: PageSDK, cb?: Function) {
  const { getEbParams, UUID } = ebdcoms.get();
  const pageParams = getEbParams('pageParams', this.instanceId || this.pageId);
  if (pageParams?.datasetVals?.length) {
    sessionStorage.setItem('ebdcoms_dataset_params_refresh_key', UUID());
    cb?.();
  }
}

/**
 * 刷新当前页面系统参数
 */
function refreshSysParams(this: PageSDK, pageId: string) {
  this.events.emit('refresh.sysParams', { pageId: pageId || this.pageId });
}

export default {
  refresh,
  reload,
  refreshPrevPageComp,
  refreshDataSet,
  refreshSysParams,
};
