import { isPlainObject } from '@weapp/utils';
import { formatValue } from '../../../core/event-action/setDynamicFieldValue';
import ebdcoms from '../../../utils/ebdcoms';
import { PageSDK } from '../PageSDK';

function getPageId(this: PageSDK) {
  return this.pageId;
}

function getParameter(this: PageSDK, name: string) {
  const { getUrlParams } = ebdcoms.get();
  const pageParams = getUrlParams(this.instanceId);
  return pageParams?.[name] || '';
}

function getAllParameter(this: PageSDK) {
  const { getUrlParams } = ebdcoms.get();
  const pageParams = getUrlParams(this.instanceId);
  return pageParams || {};
}

function getBelongAppId(this: PageSDK) {
  const { getEbParams } = ebdcoms.get();
  const pageInfo = getEbParams('pageInfo');
  return pageInfo?.appid || '';
}

function getDataSet(this: <PERSON>SDK, name: string, cb?: Function) {
  const { getEbParams, ajax, getUrlParams } = ebdcoms.get();
  const pageParams = getEbParams('pageParams', this.pageId);
  const currDataset = pageParams?.datasetVals?.find((d: any) => d.name === name);
  if (currDataset) {
    ajax({
      url: '/api/ebuilder/coms/dataset/getData',
      method: 'POST',
      params: { datasetId: currDataset.id, pageId: this.pageId, ...getUrlParams(this.pageId) },
      ebBusinessId: this.pageId,
    }).then((res: any) => {
      if (res) {
        if (Array.isArray(res) && res.length) {
          cb?.(res[0]);
        } else {
          cb?.(res);
        }
      } else {
        cb?.({});
      }
    });
  } else {
    cb?.({});
  }
}

function getDataSets(this: PageSDK, names: string[], cb?: (datas: any[]) => void) {
  const self = this as any;

  const promises = names.map((name) => {
    const promise = new Promise((res) => {
      self.getDataSet(name, (result: any) => {
        res(result);
      });
    });
    return promise;
  });

  Promise.all(promises).then(cb);
}

/** 解析变量 */
function getVariable(this: PageSDK, name: string) {
  const { getEbParams, formatVariable } = ebdcoms.get();
  const { appid, pageId, instanceId } = this;

  const isPageVar = name.startsWith('pageVar_');
  const isAppVar = name.startsWith('appVar_');

  if (isPageVar && pageId) {
    const pageVar = getEbParams('pageParams', instanceId || pageId)?.pageVar || [];
    const targetVar = pageVar.find((item: any) => item.varKey === name);

    return formatVariable(targetVar)?.defVal;
  }

  if (isAppVar && appid) {
    const appVar = getEbParams('appParams', appid)?.appVar || [];
    const targetVar = appVar.find((item: any) => item.varKey === name);

    return formatVariable(targetVar)?.defVal;
  }

  return '';
}

/** 变量赋值 */
function setVariable(this: PageSDK, name: string, value: any) {
  const { getEbParams, setEbParams } = ebdcoms.get();
  const { appid, pageId, instanceId } = this;

  const isPageVar = name.startsWith('pageVar_');
  const isAppVar = name.startsWith('appVar_');

  if (isPageVar && pageId) {
    const pageVar = getEbParams('pageParams', instanceId || pageId)?.pageVar || [];
    const targetVarIndex = pageVar.findIndex((item: any) => item.varKey === name);
    const targetVar = pageVar[targetVarIndex] || {};
    const _value = isPlainObject(value) ? value : { defVal: value };

    if (targetVarIndex > -1) {
      pageVar.splice(targetVarIndex, 1, {
        ...targetVar,
        ..._value,
        varKey: targetVar.varKey,
        id: targetVar.id,
      });

      setEbParams('pageParams', instanceId || pageId, { pageVar });
    }
  }

  if (isAppVar && appid) {
    const appVar = getEbParams('appParams', appid)?.appVar || [];
    const targetVarIndex = appVar.findIndex((item: any) => item.varKey === name);
    const targetVar = appVar[targetVarIndex] || {};
    const _value = isPlainObject(value) ? value : { defVal: value };

    if (targetVarIndex > -1) {
      appVar.splice(targetVarIndex, 1, {
        ...targetVar,
        ..._value,
        varKey: targetVar.varKey,
        id: targetVar.id,
      });

      setEbParams('appParams', appid, { appVar });
    }
  }
}

// TODO 临时通过sdk处理数据，后期要调整方案
function formatRefreshData(data: any, dynamicFields: any[]) {
  if (data[0].conditionSet?.datas && dynamicFields?.length) {
    formatValue(data[0].conditionSet?.datas, dynamicFields);
  }
}

export default [
  { name: 'getPageId', fn: getPageId },
  { name: 'getBelongAppId', fn: getBelongAppId },
  { name: 'getParameter', fn: getParameter },
  { name: 'getAllParameter', fn: getAllParameter },
  { name: 'getDataSet', fn: getDataSet },
  { name: 'getDataSets', fn: getDataSets },
  { name: 'getVariable', fn: getVariable },
  { name: 'setVariable', fn: setVariable },
  { name: 'formatRefreshData', fn: formatRefreshData },
];
