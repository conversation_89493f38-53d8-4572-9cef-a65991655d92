import dataFun from './dataFun';
import dom from './dom';
import exportFun from './exportFun';
import form from './form';
import pageEvent from './pageEvent';

const refreshAsync = () => import(
  /* webpackChunkName: "de_sdkfuns_page"  */
  './refresh'
);

const otherAsync = () => import(
  /* webpackChunkName: "de_sdkfuns_page"  */
  './other'
);

const refresh = [
  'refresh',
  'reload',
  'refreshPrevPageComp',
  'refreshDataSet',
  'refreshSysParams',
].map((fnName) => ({
  name: fnName,
  fn(...args: any) {
    return refreshAsync().then((res: any) => res.default[fnName]?.apply(this, args));
  },
}));

const other = ['toggleSidebar', 'changeComLayout', 'downloadPageWithImage'].map((fnName) => ({
  name: fnName,
  fn(...args: any) {
    return otherAsync().then((res: any) => res.default[fnName]?.apply(this, args));
  },
}));

/**
 * 如果之前代码块中可使用const data = fun(xxx)，则不适用以上的异步引入，不要做调整
 * 以上的异步引入仅适用于非同步方式的api调用
 */
export const pageApis = [
  ...refresh,
  ...dataFun,
  ...pageEvent,
  ...dom,
  ...exportFun,
  ...form,
  ...other,
];

export default {};
