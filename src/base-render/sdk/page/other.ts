import { Dialog } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { CompLayout } from '../../../grid-render/types';
import ebdcoms from '../../../utils/ebdcoms';
import { PageSDK } from '../PageSDK';

const { message } = Dialog;

type SidebarType = 'left' | 'right' | 'main';

type SidebarOptions = {
  type: SidebarType;
  // 是否展开
  isExpand?: boolean;
};

function toggleSidebar(this: PageSDK, options: SidebarOptions) {
  const { type, isExpand = true } = options;

  if (type === 'left') {
    this.events.emit('toggleShrink.leftSidebar', isExpand);
  } else if (type === 'right') {
    this.events.emit('toggleShrink.rightSidebar', isExpand);
  } else {
    this.events.emit('toggleShrink.main', isExpand);
  }
}

function changeComLayout(this: <PERSON><PERSON><PERSON>, comId: string, layout: CompLayout) {
  this.events.emit('change.comLayout', comId, layout);
}

function downloadImg(href: string) {
  const eleLink = document.createElement('a');
  eleLink.style.display = 'none';

  eleLink.href = href;
  // 触发点击
  document.body.appendChild(eleLink);
  eleLink.click();
  // 然后移除
  document.body.removeChild(eleLink);
}

function downloadPageWithImage(this: any) {
  const { pageId, layoutType, clientType } = this;

  if (this.isDownloadPageImageing) return;

  this.isDownloadPageImageing = true;

  let destoryMsg: any;

  const info = () => {
    const infoMessage = message({
      type: 'info',
      content: getLabel('317493', '正在生成图片'),
      delay: 0,
    });

    destoryMsg = infoMessage.destroy;
  };

  info();

  return new Promise((resolve, reject) => {
    ebdcoms
      .asyncExcu('page2canvas', { id: pageId, layoutType, client: clientType })
      .then((file: File) => {
        // 调用接口将图片存到文档生成下载地址
        const formData = new FormData();
        const newfile = new File([file], `${this.pageName}.png`, { type: 'image/png' });

        formData.append('file', newfile);

        ebdcoms.excu('ajax', {
          url: '/api/bs/ebuilder/common/image-lib/common/upload',
          method: 'POST',
          data: formData,
          success: (data: any) => {
            // 下载图片
            const staticPublicUrlApiPrefix = window.publicUrlstatic || window.publicUrl || '';

            downloadImg(
              `${window.location.origin}${staticPublicUrlApiPrefix}/api/file/remotedownload/${data.id}/token/image.png`,
            );

            this.isDownloadPageImageing = false;

            destoryMsg();
            resolve(true);
          },
          error: () => {
            this.isDownloadPageImageing = false;
            destoryMsg();
            reject();
          },
        });
      })
      .catch(() => {
        this.isDownloadPageImageing = false;
        destoryMsg();
        reject();
      });
  });
}

export default { toggleSidebar, changeComLayout, downloadPageWithImage };
