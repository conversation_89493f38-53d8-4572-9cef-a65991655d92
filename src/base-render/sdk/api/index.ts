import { AnyObj } from '@weapp/ui';
import { RequestOptionConfig } from '@weapp/utils';
import ebdcoms from '../../../utils/ebdcoms';

export const getOptions = (type: ApiType): RequestOptionConfig => {
  const options = {
    functionCategory: {
      url: '/api/ebuilder/designer/function/listWithCategory',
      method: 'GET',
    },
  };
  return options[type] as RequestOptionConfig;
};

export type ApiType = 'functionCategory';

export const doRequest = (type: ApiType, otherParams?: AnyObj) => {
  const opt = getOptions(type);
  return ebdcoms.asyncExcu('ajax', {
    ...opt,
    ...otherParams,
  });
};
