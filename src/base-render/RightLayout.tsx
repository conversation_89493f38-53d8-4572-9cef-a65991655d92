import { CorsComponent } from '@weapp/ui';
import { eventEmitter } from '@weapp/utils';
import React, {
  useCallback, useEffect, useRef, useState,
} from 'react';
import ReactDOM from 'react-dom';
import { IRightLayoutProps } from './types';

const RightLayout: React.FC<IRightLayoutProps> = (props) => {
  const {
    instanceId: _instanceId = '', layoutType, events, routeWrapDom,
  } = props;
  const [showRightLayout, setShowRightLayout] = useState(false);
  const [instanceId, setInstanceId] = useState('');
  const [contentDom, setContentDom] = useState<Element | null>(null);
  const instanceIdRef = useRef('');
  const renderWrapDom = useRef<Element | null>(null);
  const eventArgs = useRef<any[]>([]);

  const onMount = useCallback(() => {
    const [linkUrl, linkInfo] = eventArgs.current || [];
    if (linkInfo && !linkInfo.instanceId) {
      linkInfo.instanceId = instanceIdRef.current;
    }
    const firstChild = renderWrapDom.current?.firstChild;
    if (!firstChild) {
      return;
    }
    // 触发 RightLayout 组件重新计算宽度
    setContentDom(firstChild as Element);
    // 传递这一次右侧推出事件
    requestAnimationFrame(() => {
      eventEmitter.emit('@weapp/ebdcoms', 'jumplink.rightLayout', linkUrl, linkInfo);
    });
  }, []);

  const handleRightLayout = useCallback((...args: any[]) => {
    setShowRightLayout(true);
    eventArgs.current = args;
    const firstChild = renderWrapDom.current?.firstChild as Element;
    if (firstChild) {
      firstChild?.setAttribute?.('data-store-instance', instanceIdRef.current);
    } else {
      renderWrapDom.current?.setAttribute?.('data-store-instance', instanceIdRef.current);
    }
  }, []);

  useEffect(() => {
    if (!routeWrapDom) {
      return;
    }
    const pageInstanceId = _instanceId || new Date().getTime().toString();
    instanceIdRef.current = pageInstanceId;
    renderWrapDom.current = routeWrapDom;
    setInstanceId(pageInstanceId);
    eventEmitter.once('@weapp/ebdcoms', 'jumplink.rightLayout', handleRightLayout);
  }, []);

  if (!showRightLayout || !renderWrapDom.current) {
    return null;
  }

  return ReactDOM.createPortal((
    <CorsComponent
      weId={`${props.weId || ''}_9na37j`}
      app="@weapp/ebdpage"
      compName="RightLayout"
      layoutType={layoutType}
      contentDom={contentDom}
      onMount={onMount}
      store={{
        events,
        instanceId,
      }}
    />
  ), renderWrapDom.current);
};

export default RightLayout;
