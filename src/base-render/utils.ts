import Events from '../core/utils/event';

export function getCompSDK(comId: string, pageId?: string) {
  const sdk = window.ebuilderSDK;

  if (sdk && pageId) {
    const pageSDK = sdk.getPageSDK(pageId);

    if (pageSDK) {
      return pageSDK.getCompSDK(comId);
    }
  }

  return null;
}

export function initCompLoadEvent(comIds: string[] = [], events?: Events, callback?: Function) {
  if (!comIds.length) {
    callback?.();
    return;
  }
  let childCount = comIds.length;
  events?.on('compLoad', (comId: string) => {
    if (comIds.includes(comId)) {
      --childCount;
      if (childCount === 0) {
        callback?.();
      }
    }
  });
}

export function getComHeight(pageDOM: HTMLElement, comDOM: HTMLElement, correctHeight?: number) {
  if (!pageDOM || !comDOM) return;
  const { top: layoutTop, height: layoutHeight } = pageDOM.getBoundingClientRect();
  const { top: comTop } = comDOM.getBoundingClientRect();
  const height = correctHeight || layoutHeight;

  // 当前组件不在可视区内，不做任何处理
  if (comTop > height + layoutTop) return;

  const { paddingTop } = window.getComputedStyle(pageDOM);
  const newHeight = height - (comTop - layoutTop) - parseFloat(paddingTop);

  return newHeight;
}

export default {};
