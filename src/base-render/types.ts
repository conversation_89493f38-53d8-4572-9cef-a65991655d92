import React, {
  ComponentType, DOMAttributes, ReactElement, ReactNode,
} from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { IComData, ILayoutData } from '../core/types';
import Events from '../core/utils/event';
import { ComViewProps } from './core';
import BaseRenderStore from './store';

export type DroppableViewProps = React.Attributes &
  Pick<ComViewProps, 'renderChild' | 'events'> & {
    /** Droppable的id */
    id: string;
    /** 组件id */
    comId: string;
    /** 是否作为独立的组件，独立的组件可以被选中和有统一配置 */
    standalone?: boolean;
    className?: string;
    onMount?: () => void;
  };

export type Resolver = {
  DroppableView?: ComponentType<DroppableViewProps>;
};

export type IRenderOpts = {
  /** 是否启用页面样式 */
  pageStyle?: boolean;
  /** 是否为自定义页面 */
  customPage?: boolean;
};

export type RenderProps<T = IComData> = React.Attributes & {
  /** 布局数据 */
  data: ILayoutData<T>;
  /** 事件总线对象 */
  events?: Events;
  className?: string;
  /** 页面样式PageStyle上面的className */
  pageStyleClassName?: string;
  /** 后端微服务路径 */
  servicePath?: string;
  /** 渲染RoutePageView组件的父节点 */
  routeWrapDom?: Element;
  /**
   * 渲染不进行长任务拆分，自行处理性能问题
   * - splitLongTask为false时直接加载，默认为true
   */
  splitLongTask?: boolean;
  /** 是否展示源码加载过程中的loading, 默认为true */
  loadingEnabled?: boolean;
  /** pageView传递给page div的属性 */
  pageWrapProps?: DOMAttributes<any>;
  /** 布局渲染配置，按需渲染，优化性能 */
  renderOpts?: IRenderOpts;
  /** 自定义渲染视图 */
  renderView?: (
    view: ReactElement | IComData,
    params?: Record<string, any>,
  ) => ReactElement | ReactNode;
  /** 是否需要根据组件数据自定义渲染视图 */
  shouldRenderWithView?: (data: IComData) => boolean;
  /** 复写content视图 */
  renderContent?: (view: ReactElement) => ReactElement | ReactNode;
  /** 复写comsLayouts视图 */
  renderComsLayout?: (view: ReactElement) => ReactElement | ReactNode;
  /** 视图组件加载完成 */
  onComDidMount?: (comInstance: any, id: string) => void;
  /** ecode加载完成 */
  onEcodeMount?: () => void;
  /** 页面源码加载完成回调 */
  onSourceCodeLoadCallback?: (code?: { load: Function }) => void;
  /** 布局渲染完成 */
  onMount?: (store: any, dom?: HTMLDivElement) => void;
  /** 页面结束loading前 */
  onBeforeLoad?: () => void;
};

export type BaseRenderProps<T = IComData, Store = BaseRenderStore> = React.Attributes & {
  store?: Store;
  resolver: Resolver;
  renderProps: RenderProps<T>;
};

export interface RoutePageViewProps extends React.Attributes, RouteComponentProps {
  pageId?: string;
  routeWrapDom?: Element;
  servicePath?: string;
  terminalType?: string;
  /** 页面实例ID */
  instanceId?: string;
  layoutType?: ILayoutData['layoutType'];
  events?: any;
}

export interface IRightLayoutProps extends React.Attributes {
  /** 渲染RoutePageView组件的父节点 */
  routeWrapDom: Element;
  /** 页面实例ID */
  instanceId?: string;
  /** 布局类型 */
  layoutType?: ILayoutData['layoutType'];
  /** 事件总线对象 */
  events?: Events; 
}
