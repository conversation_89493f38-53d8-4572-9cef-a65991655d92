import EventEmitter from '../../core/utils/event';
import { ExtraSourceCodeData, IComData, LayoutType } from '../../types';
import setTimeoutOnce from '../../utils/setTimeoutOnce';
import EbuilderSDK from '../sdk/EbuilderSDK';
import { LOG } from './log';

type EcodeData = {
  ecodeId: string;
  version?: string;
};

type Params = {
  /** 客户端类型 */
  terminalType?: 'PC' | 'MOBILE';
  /** 页面源码ecodeId */
  ecode?: EcodeData;
  /** 样式缓存ecode */
  cssCacheEcode?: EcodeData;
  /** 页面额外源码内容 */
  extraSourceCodeDatas?: ExtraSourceCodeData[];
  /** eb页面运行唯一id */
  instanceId?: string;
  appid?: string;
  layoutType?: LayoutType;
  pageName?: string;
  // 回调函数中会返回加载ecode是否成功
  cb?: (ecodeSuccess: boolean) => void;
  moduleType?: string;
  events: EventEmitter;
  path: string;
  skipLocalCache: boolean; // 跳过本地缓存以及接口内容判断，直接加载代码文件
  fileTypes?: string[]; // 加载源码文件类型
  sourceCodeLoadCallback?: (code?: { load: Function }) => void;
};

const importEcode = (params: {
  ecodeId?: string;
  version?: string;
  path: string;
  skipLocalCache?: boolean;
  fileTypes?: string[];
  callback?: (code?: { load: Function }) => void;
}) => {
  const _callback = (code?: { load: Function }) => code?.load?.();
  const {
    ecodeId, path, skipLocalCache = true, fileTypes, version, callback = _callback,
  } = params;

  let errorMsg = '';

  if (!ecodeId) {
    errorMsg = 'no ecodeId!';
  }
  if (skipLocalCache && !window.weappEcodesdk?.asyncCustAppImport) {
    errorMsg = 'loader weappEcodesdk.asyncCustAppImport error!';
  }
  if (!skipLocalCache && !window.weappEcodesdk?.asyncImport) {
    errorMsg = 'loader weappEcodesdk.asyncImport error!';
  }
  // 将可能存在的错误信息输出，保证 onEcodeMount 执行，去掉页面loading
  if (errorMsg) {
    return Promise.reject(errorMsg);
  }

  if (skipLocalCache) {
    // version 固定浏览器加载js、css资源版本，浏览器缓存前端资源
    return window.weappEcodesdk
      ?.asyncCustAppImport?.('simple', ecodeId, path, fileTypes, version)
      .then((code?: { load: Function }) => {
        callback(code);

        return code;
      });
  }

  return window.weappEcodesdk
    ?.asyncImport?.(ecodeId, path, skipLocalCache, fileTypes)
    .then((code?: { load: Function }) => {
      callback(code);

      return code;
    });
};

/** 加载页面源码 */
const loadPageSourcecode = (
  path: string,
  params: {
    ecode?: EcodeData;
    cb?: Params['cb'];
    skipLocalCache: boolean;
    fileTypes?: string[];
    pageId: string;
    instanceId?: string;
    callback?: (code?: { load: Function }) => void;
  },
) => {
  const {
    ecode, skipLocalCache, fileTypes, pageId, instanceId, cb, callback,
  } = params;

  const _callback = (code?: { load: Function }) => code?.load?.(instanceId || pageId);

  const { ecodeId, version } = ecode || {};
  // 记录日志便于后期定位，日志含义：源码开始加载
  LOG?.set({ logType: 'load-ecode', data: ecodeId });

  /** 加载js、css, css无需单独引入 */
  const asyncImortEcode = (_skipLocalCache: boolean) => {
    const importParams = {
      ecodeId,
      version,
      path,
      skipLocalCache: _skipLocalCache,
      fileTypes,
      callback: callback || _callback,
    };

    return importEcode(importParams)
      .then((code: { load: Function }) => {
        // 记录日志便于后期定位，日志含义：源码加载成功
        LOG?.set({ logType: 'load-ecode-success', data: code });
        window.ebuilderSDK?.getPageSDK()?.setExports(code);
        cb?.(true);
      })
      .catch((error: any) => {
        /**
         * 前端去兼容兜底，加载源码失败的情况下调用一下另一个模式重新获取一下
         */
        if (_skipLocalCache === skipLocalCache) {
          asyncImortEcode(!_skipLocalCache);
        } else {
          // 记录日志便于后期定位，日志含义：源码加载失败
          LOG?.set({ logType: 'load-ecode-error', data: error });
          cb?.(false);

          // eslint-disable-next-line no-console
          console.error(error);
        }
      });
  };
  /** 存在源码 */
  if (ecodeId && fileTypes?.length) {
    return asyncImortEcode(skipLocalCache);
  }

  cb?.(false);

  return Promise.resolve();
};

/** 加载样式缓存代码 */
const loadStyleCache = (params: Params) => {
  const { cssCacheEcode } = params;
  const styleCacheId = cssCacheEcode?.ecodeId;

  if (!styleCacheId) {
    return;
  }

  // 全局记录link标签
  if (!window.EBCSSCACHE) {
    window.EBCSSCACHE = {};
  }

  // 异步等待loadjs添加标签
  setTimeoutOnce(() => {
    if (window.EBCSSCACHE[cssCacheEcode?.ecodeId]) {
      const cacheCssLink = window.EBCSSCACHE[styleCacheId];
      // 如果已经删除的则加回来
      if (cacheCssLink.classList.contains('hide')) {
        // 添加到最前面，降低优先级
        document.head.insertBefore(cacheCssLink, document.head.firstChild);
        // 取消删除样式
        cacheCssLink.classList.remove('hide');
      }
      return;
    }
    const regex = /\/ecodestatic\/[^/]+\/tenant\/simple/;
    // 排除样式缓存的标签
    const links = document.querySelectorAll('link[rel="stylesheet"]');
    let cacheCssLink: any = null; // 页面源码css
    Array.from(links).find((link) => {
      if (
        link
        && regex.test((link as HTMLLinkElement).href || '')
        && (link as HTMLLinkElement).href.includes(styleCacheId)
      ) {
        cacheCssLink = link;
      }
      return !!cacheCssLink;
    });
    if (cacheCssLink) {
      window.EBCSSCACHE[styleCacheId] = cacheCssLink;
      cacheCssLink.classList.add('css-cache');
      // 添加到最前面，降低优先级
      document.head.insertBefore(cacheCssLink, document.head.firstChild);
    }
  });

  return importEcode({
    path: 'index',
    ecodeId: cssCacheEcode.ecodeId,
    version: cssCacheEcode.version,
    // 直接请求css默认直接请求
    skipLocalCache: true,
    // 样式缓存之请求css，添加此参数不会请求js，减少报错
    fileTypes: ['css'],
  });
};

/** 加载页面额外源码代码 */
// eslint-disable-next-line max-len
const loadPageExtraSourcecode = (extraEcodeDatas: ExtraSourceCodeData[]) => extraEcodeDatas.map((extraEcodeData) => {
  const {
    id, path = 'index', fileTypes, version, callback,
  } = extraEcodeData;

  return importEcode({
    ecodeId: id,
    version,
    path,
    fileTypes,
    callback,
  });
});

// 排除未登录情况
export const isEcodeUsable = () => window.weappEcodesdk
  && (!window.TEAMS?.isPurchasedModule || window.TEAMS?.isPurchasedModule?.('ecode'));

/** 源码加载以及sdk初始化 */
// eslint-disable-next-line max-len
export const initSourceCode = (pageId: string, coms: IComData[], params: Params) => {
  const {
    moduleType,
    ecode,
    cb,
    path = 'index',
    skipLocalCache = false,
    fileTypes,
    extraSourceCodeDatas = [],
    instanceId,
    appid,
    layoutType = 'GRID',
    pageName,
    terminalType,
    sourceCodeLoadCallback,
  } = params || {};
  const events = params?.events!;
  const ebuilderSDK = EbuilderSDK.getInstance(pageId);

  // 注册自定义函数
  ebuilderSDK.registerCustomApis(pageId, moduleType);
  ebuilderSDK.createPageSDK({
    pageId,
    coms,
    events,
    instanceId,
    appid,
    layoutType,
    pageName,
    clientType: terminalType,
  });
  // 记录日志便于后期定位，日志含义：源码初始化并获取是否降级
  LOG?.set({ logType: 'init-source-code', data: { pageId, weappEcodesdk: window.weappEcodesdk } });
  // ecode 源码服务降级，用 window.weappEcodesdk 判断，存在表单外发功能没有登录的情况，但是可以请求到ecode资源
  // window?.TEAMS?.displayModules?.includes?.('ecode')
  // ecode已部署并且已采购才加载
  if (isEcodeUsable()) {
    let ecodeSuccess = false;
    /** 加载缓存，尽量让缓存放最上方，降低优先级 */
    loadStyleCache(params);
    Promise.all([
      /** 加载源码 */
      loadPageSourcecode(path, {
        ecode,
        cb: (_ecodeSuccess: boolean) => {
          ecodeSuccess = _ecodeSuccess;
        },
        skipLocalCache,
        fileTypes,
        pageId,
        instanceId,
        callback: sourceCodeLoadCallback,
      }),

      /** 加载页面额外源码内容，例如逻辑流、表单布局源码等 */
      ...loadPageExtraSourcecode(extraSourceCodeDatas),
    ])
      .then(() => cb?.(ecodeSuccess))
      .catch(() => cb?.(ecodeSuccess));
  } else {
    cb?.(false);
  }
};

export default {};
