export interface IEbdappLogInput {
  // 日志类型
  logType: string;
  // 数据详情
  data?: any;
}
interface IEbdappLogData {
  [key: string]: any;
}
export interface IEbdappLog {
  // 日志类型
  logType: string;
  // 运行时间
  time: number;
  // 数据详情
  data?: IEbdappLogData;
}
interface ICompLogData extends IEbdappLog {}
interface ICompLog {
  [comId: string]: ICompLogData[];
}
interface IWarnLogData {
  // 警告类型
  warnType: string;
  // 运行时间
  warnInfo: any;
}
interface IWarnLog {
  [comId: string]: IWarnLogData[];
}
interface ICompMountedData {
  // 从延迟加载到开始调用组件视图的时间
  lazyTime: number;
  // 从开始调用组件视图到视图加载完成的时间
  viewTime: number;
  // 从允许渲染到视图加载完成的时间
  mountTime: number;
  // 数据详情
  data: any;
}
interface ICompMounted {
  [comId: string]: ICompMountedData;
}
export interface ILogAnalysisResult {
  /** 组件日志 */
  compLog: ICompLog;
  /** 警告日志 */
  warnLog: IWarnLog;
  /** 其他日志列表 */
  otherList: any[];
  /** 相关日志列表 */
  refLogList: any[];
  /** 组件加载日志 */
  compMounted: ICompMounted;
}
interface IAnalyzeWarnOption {
  longTime: boolean;
  mounted: boolean;
}
export interface IAnalyzeOption {
  comId?: string;
  warn?: IAnalyzeWarnOption;
}
export default {};
