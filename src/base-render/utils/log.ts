import { qs } from '@weapp/utils';
import {
  IAnalyzeOption, IEbdappLog, IEbdappLogInput, ILogAnalysisResult,
} from './types';

class Log {
  // 最大长度
  maxLen?: number;

  // 传入最大长度作为初始值
  constructor(maxLen?: number) {
    this.maxLen = maxLen;
  }

  // 日志列表
  list: IEbdappLog[] = [];

  // 允许的logType
  allowedLogTypes: string[] = [
    /** 源码相关 */

    // 源码初始化并获取是否降级
    'init-source-code',
    // 源码开始加载
    'load-ecode',
    // 源码加载成功
    'load-ecode-success',
    // 源码加载失败
    'load-ecode-error',

    /** 组件渲染相关相关 */

    // 组件视图延迟加载
    'view-lazy',
    // 开始调用组件视图
    'view',
    // 组件被允许渲染
    'mounted-defer',
    // 组件视图加载完成
    'view-mounted',

    /** 页面相关 */

    // 首屏渲染完成
    'first-screen-loaded',
  ];

  // 设置一条日志
  set = (log: IEbdappLogInput) => {
    const logType = typeof log === 'object' && log.logType;
    if (logType && this.allowedLogTypes.includes(logType)) {
      // 日志会默认添加时间戳，后续分析使用
      this.list.push({ ...log, time: performance.now() });
      // 日志只保存固定最大长度的日志列表，多余部分会被剔除
      if (this.maxLen) {
        if (this.list.length > this.maxLen) {
          this.list.shift();
        }
      }
    }
  };

  // 分析日志，可传入单独的组件id，针对改组件单独分析
  analyze = (option?: IAnalyzeOption) => {
    const { comId, warn } = option || {};
    // 分析列表
    const analyzeList = this.list.slice();
    // 分析结果
    const analysisResult: ILogAnalysisResult = {
      compLog: {},
      warnLog: {},
      otherList: [],
      refLogList: [],
      compMounted: {},
    };
    const {
      compLog, warnLog, otherList, refLogList, compMounted,
    } = analysisResult;
    analyzeList.forEach((logData) => {
      const { logType, data, time } = logData || {};
      switch (logType) {
        case 'view-lazy':
        case 'view':
        case 'mounted-defer':
        case 'view-mounted': {
          const { id } = data || {};
          if (id) {
            if (comId && comId !== id) {
              break;
            }
            let lastTime = time;
            let beforeLog;
            if (!compLog[id]) {
              compLog[id] = [];
            } else {
              beforeLog = compLog[id][compLog[id].length - 1];
              lastTime = beforeLog.time;
            }
            const currentLog = {
              logType,
              time,
              data,
            };
            compLog[id].push(currentLog);
            // 添加执行间隔较长的警告，现阶段为100ms
            if (warn?.longTime) {
              const gapTime = time - lastTime;
              if (gapTime > 100) {
                if (!warnLog[id]) {
                  warnLog[id] = [];
                }
                warnLog[id].push({
                  warnType: 'longTime',
                  warnInfo: {
                    before: beforeLog,
                    current: currentLog,
                    gapTime,
                  },
                });
              }
            }
            if (!compMounted[id]) {
              compMounted[id] = {
                lazyTime: -1,
                viewTime: -1,
                mountTime: -1,
                data: {},
              };
            }
            if (logType === 'view-lazy') {
              if (!compMounted[id].data.lazy) {
                compMounted[id].data.lazy = logData;
              }
            }
            if (logType === 'view') {
              if (!compMounted[id].data.view) {
                compMounted[id].data.view = logData;
                const lazyData = compMounted[id].data.lazy;
                if (lazyData) {
                  const lazyTime = lazyData?.time || 0;
                  compMounted[id].lazyTime = time - lazyTime;
                }
              }
            }
            if (logType === 'mounted-defer') {
              if (!compMounted[id].data.defer) {
                compMounted[id].data.defer = logData;
              }
            }
            if (logType === 'view-mounted') {
              if (!compMounted[id].data.mounted) {
                compMounted[id].data.mounted = logData;
              }
              const viewData = compMounted[id].data.view;
              if (viewData) {
                const viewTime = viewData?.time || 0;
                compMounted[id].viewTime = time - viewTime;
              }
              const mountedDeferData = compMounted[id].data.defer;
              if (mountedDeferData) {
                const mountedDeferTime = mountedDeferData?.time || 0;
                compMounted[id].mountTime = time - mountedDeferTime;
              }
            }
            refLogList.push(logData);
          }
          break;
        }
        default: {
          otherList.push(logData);
        }
      }
    });
    // 添加没有执行mounted的警告
    if (warn?.mounted) {
      Object.keys(compMounted).forEach((id) => {
        const { data } = compMounted[id];
        if (!data.mounted) {
          if (!warnLog[id]) {
            warnLog[id] = [];
          }
          warnLog[id].push({
            warnType: 'has-no-mounted',
            warnInfo: {
              data,
              compType: data.defer?.data?.type,
            },
          });
        }
      });
    }
    return analysisResult;
  };
}
export type EbdappLogType = Log;
// 页面参数带有logLength才会启动日志，减少内存开销，logLength会作为入参作为日志的最大长度
const { search: locationSearch } = location;
const searchParams = qs.parse(locationSearch?.slice(1));
const { logLength } = searchParams;
if (logLength && !window.EBDAPPLOG) {
  window.EBDAPPLOG = new Log(Number(logLength) || 100);
}
export const LOG = window.EBDAPPLOG as EbdappLogType;
export default {};
