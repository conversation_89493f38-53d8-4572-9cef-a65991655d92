export default class FilterPlugin {
  constructor(opts: any) {
    this.event = opts.event;
  }

  event: any;

  cacheEventEmitFilterComs: Record<string, any> = {};

  cacheEventEmitFilterStatus: any = {};

  beforeEmit(args: any[]) {
    if (!this.cacheEventEmitFilterComs) {
      this.cacheEventEmitFilterComs = {};
    }

    const [comId, ...restArgs] = args;
    const name = `filter_${comId}`;
    const fn = () => this.event.emitWithReturns(name, ...restArgs);
    const emitComId = restArgs[1];
    const canEmit = this.cacheEventEmitFilterStatus[comId!]?.[emitComId] !== 1;
    /**
     * 缓存当前筛选组件的最新值
     * - 在eventOnfilterComs，没有在eventEmitFilterComs注册的情况
     * - 场景：tab 组件下面的组件标签切换重新加载内容
     * _EXCLUDECACHE 标识说明不需要缓存，目前只有熊静那边列表筛选才会需要
     */
    if (emitComId && emitComId.indexOf('_EXCLUDECACHE') === -1) {
      if (this.cacheEventEmitFilterComs[comId!]) {
        this.cacheEventEmitFilterComs[comId!][emitComId] = fn;
      } else {
        this.cacheEventEmitFilterComs[comId!] = { [emitComId]: fn };
      }
      this.cacheEventEmitFilterStatus = {
        ...this.cacheEventEmitFilterStatus,
        [comId]: {
          ...(this.cacheEventEmitFilterStatus[comId] || {}),
          [emitComId]: 0,
        },
      };

      // A组件：筛选组件，B组件：被刷新的组件
      // 1. 如果不存在，代表只是跟筛选组件绑在了一起，还没有渲染
      // 2. 如果缓存监听事件已经存在，说明之前B组件已经监听过了，B组件会通过afterBind触发，A组件无需再触发
      // cacheEventEmitFilterStatus: 0 代表A组件可以继续触发； 1 代表B组件自己触发，后续A组件不需要触发； 2 代表B组件还未加载，A组件可以继续触发；
      if (!this.event?._event?._event?._events?.[name] || !canEmit) {
        if (!this.event?._event?._event?._events?.[name]) {
          this.cacheEventEmitFilterStatus[comId][emitComId] = 2;
        }
        return false;
      }
    }

    return true;
  }

  beforeBind() {
    if (!this.cacheEventEmitFilterComs) {
      this.cacheEventEmitFilterComs = {};
    }

    return false;
  }

  afterBind(args: any[]) {
    const [comId] = args;

    const emitFns = this.cacheEventEmitFilterComs[comId as string];

    if (emitFns) {
      Object.keys(emitFns).forEach((emitComId) => {
        if (this.cacheEventEmitFilterStatus[comId]?.[emitComId] !== 2) {
          this.cacheEventEmitFilterStatus = {
            ...this.cacheEventEmitFilterStatus,
            [comId]: {
              ...(this.cacheEventEmitFilterStatus[comId] || {}),
              [emitComId]: 1,
            },
          };
        }

        emitFns[emitComId]?.();
      });
    }
  }

  destory(comId: string) {
    const emitFns = this.cacheEventEmitFilterComs[comId as string];
    if (emitFns) {
      Object.keys(emitFns).forEach((emitComId) => {
        const emitStatus = this.event?._event?._event?._events?.[`operation.btn_${emitComId}`]
          ? 2
          : 0;
        if (emitStatus === 0) {
          // 删除缓存的监听是为了解决筛选组件一起销毁的时候，下次B组件加载的时候不会拿上次的错误条件过滤
          delete this.cacheEventEmitFilterComs[comId];
        }
        this.cacheEventEmitFilterStatus = {
          ...this.cacheEventEmitFilterStatus,
          [comId]: {
            ...(this.cacheEventEmitFilterStatus[comId] || {}),
            [emitComId]: emitStatus,
          },
        };
      });
    }
  }
}
