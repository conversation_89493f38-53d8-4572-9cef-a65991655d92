export default class LazyRenderPlugin {
  constructor(opts: any) {
    this.event = opts.event;
  }

  event: any;

  cacheEventEmitLazyRenderComs: Record<string, any> = {};

  beforeEmit(args: any[]) {
    if (!this.cacheEventEmitLazyRenderComs) {
      this.cacheEventEmitLazyRenderComs = {};
    }

    const [comId, ...restArgs] = args;
    const name = `lazyrender_${comId}`;
    const fn = () => this.event.emitWithReturns(name, ...restArgs);

    if (!this.cacheEventEmitLazyRenderComs[comId!]) {
      this.cacheEventEmitLazyRenderComs[comId!] = fn;
    }

    // 如果不存在，代表组件还没有渲染
    if (!this.event?._event?._event?._events?.[name]) {
      return false;
    }

    return true;
  }

  beforeBind() {
    if (!this.cacheEventEmitLazyRenderComs) {
      this.cacheEventEmitLazyRenderComs = {};
    }

    return false;
  }

  afterBind(args: any[]) {
    const [comId] = args;

    const emitFns = this.cacheEventEmitLazyRenderComs[comId as string];

    if (emitFns) {
      emitFns();
    }
  }
}
