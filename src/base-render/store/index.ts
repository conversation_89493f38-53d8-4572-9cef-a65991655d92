import EventEmitter from '../../core/utils/event';
import { UUID } from '../../utils';
import FirstScreenStore from './firstScreenStore';

export default class BaseRenderStore {
  /** 事件总线，用于组件渲染视图组件间联动交互 */
  events = new EventEmitter();

  firstScreenStore: FirstScreenStore | null = null;

  instanceId: string = UUID();

  initFirstScreenStore = () => {
    this.firstScreenStore = new FirstScreenStore(this);
  };

  distoryFirstScreenStore = () => {
    this.firstScreenStore?.distory();
  };

  emitFirstScreenLoaded = () => {
    this.firstScreenStore?.emitFirstScreenLoaded();
  };

  setEvents(events: EventEmitter) {
    this.events = events;
  }

  registerApis = (apis: { name: string; fn: Function }[]) => {
    const pageSDK = window.ebuilderSDK?.getPageSDK?.();

    if (pageSDK) {
      pageSDK.registerApis(apis);
    }
  };
}
