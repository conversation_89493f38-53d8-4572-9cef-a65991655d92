import { debounce, ResizeObserver } from '@weapp/utils';
import { LOG } from '../utils/log';
import BaseRenderStore from './index';

type RenderStore = BaseRenderStore & {
  page?: any;
  coms?: any;
  getFirstScreenComs?: () => any;
  firstScreenEnabled?: boolean;
};

export default class FirstScreenStore {
  // 调用的store主体
  renderStore: RenderStore | null = null;

  // 是否已经触发首屏渲染完成事件
  firstScreenLoaded = false;

  // 首屏渲染的组件
  firstScreenComs: string[] | null = null;

  // 已经加载的组件
  loadedComs: Record<string, boolean> = {};

  // 标注是否绑定首屏加载完成事件触发兜底策略
  boundLoadTimer: boolean = false;

  // 记录滚动高度，避免后面加载的滚动高度拿不到
  scrollTop: number = 0;

  resizeObserver: any = null;

  constructor(store: BaseRenderStore) {
    this.renderStore = store;
    // 设置true和不设置都是默认开启，除非手动关闭
    if (this.renderStore.firstScreenEnabled === false) {
      this.firstScreenLoaded = true;
    } else {
      this.init();
    }
  }

  setFirstScreenLoaded = (loaded: boolean) => {
    this.firstScreenLoaded = loaded;
  };

  init = () => {
    const { events } = this.renderStore || {};
    events?.on('compLoad', this.onCompLoad);
    // 避免页面组件不触发compLoad，手动触发第一次
    this.onCompLoad();
    window.addEventListener('scroll', this.onScroll, true);
  };

  distory = () => {
    const { events } = this.renderStore || {};

    events?.off('compLoad', this.onCompLoad);
    window.removeEventListener('scroll', this.onScroll, true);
    this.resizeObserver?.disconnect?.();
  };

  delayEmitLoaded = debounce(() => {
    // 组件加载完成停止1000ms会触发首屏渲染完成
    this.emitFirstScreenLoaded();
  }, 1000);

  onCompLoad = (comId?: string) => {
    if (this.firstScreenLoaded) return;

    if (comId) {
      this.loadedComs[comId] = true;
    }

    const firstScreenComs = this.getFirstScreenComs();
    const firstScreenLength = firstScreenComs?.length || 0;
    // eslint-disable-next-line max-len
    const firstScreenLoadedLength = firstScreenComs?.filter((id) => this.loadedComs[id])?.length || 0;

    if (firstScreenLength && firstScreenLoadedLength === firstScreenLength) {
      // 首屏组件全部加载完成触发事件
      this.emitFirstScreenLoaded();
    } else {
      this.delayEmitLoaded();
    }
  };

  registerRootResize = () => {
    const { events, page } = this.renderStore || {};

    const pageParent = page?.id && document.getElementById(`ebpage_${page.id}`)?.parentElement;
    const pageRoot = pageParent || document.getElementById('root');

    const pageRootClientHeight = pageRoot.clientHeight;

    if (pageRoot) {
      const resizeObserver = new ResizeObserver(
        debounce(() => {
          events?.emit('com.rootResize', pageRootClientHeight);
        }, 200),
      );
      resizeObserver.observe(pageRoot);
      this.resizeObserver = resizeObserver;
    }
  };

  getFirstScreenComs = () => {
    if (!this.firstScreenComs?.length) {
      this.firstScreenComs = this.renderStore?.getFirstScreenComs?.();
    }
    return this.firstScreenComs;
  };

  emitFirstScreenLoaded = () => {
    const { events } = this.renderStore || {};

    if (this.firstScreenLoaded) return;

    this.setFirstScreenLoaded(true);
    // 记录日志便于后期定位，日志含义：首屏加载完成
    LOG?.set({
      data: { firstScreenComs: this.firstScreenComs },
      logType: 'first-screen-loaded',
    });
    events?.emit('com.firstScreenLoaded');
    this.registerRootResize();
  };

  onScroll = debounce((e: any) => {
    const { events } = this.renderStore || {};

    // 分栏布局的非主边栏不触发滚动加载组件
    const isSiderLayout = e.srcElement?.className?.indexOf?.('Sider') > -1;
    if (!isSiderLayout) {
      this.scrollTop = e.srcElement.scrollTop;
      events?.emit('com.lazyLoad', this.scrollTop);
    }
  }, 20);
}
