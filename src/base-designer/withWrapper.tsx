import { Empty } from '@weapp/ui';
import { ua } from '@weapp/utils';
import { useEffect, useRef, useState } from 'react';
import { prefixCls } from '../constants';
import PluginCenter from './plugin-center';
import { PluginCenterType } from './plugin-center/types';
import { BaseDesignerProps } from './types';

const defaultRenderDesigner = (content: any) => content;

const withWrapper = (BaseDesigner: any) => (props: BaseDesignerProps) => {
  const { renderDesigner = defaultRenderDesigner, store, layoutDatas } = props;
  const [load, setLoad] = useState<any>(false);
  const pluginRef = useRef<PluginCenterType | null>(null);
  // 检查是否为 IE 浏览器
  const isIEBrowser = ua.browser === 'IE';

  useEffect(() => {
    if (!layoutDatas || layoutDatas?.length === 0) return;

    if (!pluginRef.current) {
      const pluginOpts = {
        designerProps: props,
      };
      pluginRef.current = new PluginCenter(props.plugins || [], pluginOpts);
      store.pluginCenter = pluginRef.current;
    }

    if (!load) {
      pluginRef.current
        .invoke('onSyncBeforeMount', { args: [Promise.resolve(true)] })
        .then((loaded: boolean) => {
          if (loaded) {
            setLoad(true);
          }
        })
        .catch(() => {
          setLoad(true);
        });
    }
  }, [load, layoutDatas?.length]);

  if (!load) return null;

  if (isIEBrowser) {
    return renderDesigner(
      <Empty weId="7ufe53" displayType={'ie' as any} className={`${prefixCls}-empty`} />,
    );
  }

  return <BaseDesigner weId="o3vyff" {...props} />;
};

export default withWrapper;
