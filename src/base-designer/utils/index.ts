import { getLang } from '@weapp/utils';
import { anonymousLoginModule, cNLang } from '../../constants';
import { CompDesignStores } from '../../core/types';
import { ClientType, IComData, ILayoutData } from '../../types';
import ebdcoms from '../../utils/ebdcoms';
import CompDesignStore from '../core/CompDesignStore';
import BaseDesignerStore from '../store';

type InitData = {
  store: BaseDesignerStore;
  layoutDatas: ILayoutData[];
  internal: any;
};

export function initCompDesignStores(data: InitData) {
  const { store, layoutDatas = [], internal } = data;
  const { setCompDesignStores, clientType } = store;

  if (!layoutDatas) return;

  const compDesignStore = new CompDesignStore({
    designer: store,
    internal,
    clientType,
  });

  let refCompDesignStore = null;
  const refClientType = clientType === 'PC' ? 'MOBILE' : 'PC';

  if (layoutDatas.length > 1) {
    refCompDesignStore = new CompDesignStore({
      designer: store,
      internal,
      clientType: refClientType,
    });
  }

  const compDesignStores = {
    [clientType]: compDesignStore,
    [refClientType]: refCompDesignStore,
  };

  setCompDesignStores(compDesignStores as CompDesignStores);
}

export const isJsonStr = (str: string) => {
  try {
    const obj = JSON.parse(str);
    if (typeof obj === 'object' && obj) {
      return true;
    }
    return false;
  } catch (e) {
    return false;
  }
};

export const getComposeCom = (sourceCom: IComData, clientType: ClientType) => {
  const { configPC, configMobile } = sourceCom;
  const config = clientType === 'MOBILE' ? configMobile : configPC;
  const data = isJsonStr(config) ? JSON.parse(config) : config;
  if (data && data.length) {
    return data[0];
  }
  return sourceCom;
};

/**
 * 根据当前语言环境调整ui布局
 * - 新增的自定义变量要维护到var.css文件中
 * - 需要兼容 IE11 css变量问题，ie11浏览器中 css变量不生效
 */
export const initStylePropertysByLang = () => {
  const lang = getLang();

  const stylePropertys: Record<string, string> = {
    '--eb-left-content-w': lang === cNLang ? '200px' : '270px',
    '--eb-comp-width': lang === cNLang ? '32%' : '46%',
    '--eb-comp-margin': lang === cNLang ? '0' : '0 2%',
    '--eb-pagelayout-type-width': lang === cNLang ? '23%' : '48%',
  };

  Object.keys(stylePropertys).forEach((property) => {
    document.documentElement.style.setProperty(property, stylePropertys[property]);
  });
};

// 判断是否是登陆前页面 true 为登陆前页面
export function isPassportEbpage(pageId?: string) {
  const { getUrlParams } = ebdcoms.get() || {};
  const urlParams = getUrlParams?.(pageId);
  const isAnonymous = anonymousLoginModule.includes(window?.EBUILDER?.pageInfo?.module || '');

  return (
    isAnonymous
    || window?.inPassportEbpage
    || urlParams?.from === 'loginSetting'
  );
}

// 记录设计页面关联主题
export const designThemeInfo = {
  id: '',
  version: '',
};

export default {};
