import {
  computed, IEnhancer, IObservableFactories, IObservableFactory, observable,
} from 'mobx';

function createShallowObservable(v: any, name: string, desc: PropertyDescriptor) {
  // 创建shallow属性
  observable(v, `${name}_shallow`, desc);

  const initalVal = (desc as any).initializer();

  // 创建shallow value属性，用于存放[name]属性的值
  Object.defineProperty(v, `${name}_value`, { ...desc, value: initalVal });

  return computed(v, name, {
    ...desc,
    get(this: any) {
      // 此判断逻辑上是多余的，目的是为了触发computed的监听机制
      // 借助computed的特性监听shallow属性的变更，以达到set [name]触发视图渲染的效果
      if (this[`${name}_shallow`]) {
        if (!this.deleteRelatedComIds.length) return this[`${name}_value`];

        return this[`${name}_value`].filter(
          (com: any) => !this.deleteRelatedComIds.includes(com.id),
        );
      }
      return this[`${name}_value`];
    },
    set(this: any, value: any) {
      this[`${name}_value`] = value;
      this[`${name}_shallow`] = new Date().getTime();
    },
  });
}

const shallowComsObservable: IObservableFactory &
  IObservableFactories & {
    enhancer: IEnhancer<any>;
  } = createShallowObservable as any;

export default shallowComsObservable;
