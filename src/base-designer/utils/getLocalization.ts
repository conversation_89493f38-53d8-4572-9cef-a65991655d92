import { defaultsDeep, getLabel } from '@weapp/utils';
import { Localization } from '../types';

/** 默认的本地化配置 */
export const defaultLolalization = () => ({
  toolbar: {
    template: {
      title: getLabel('99291', '存为模板'),
      dialog: {
        title: get<PERSON>abel('99291', '存为模板'),
        placeholder: get<PERSON>abel('99296', '请输入模板名称'),
        generate: getLabel('99293', '生成新模板'),
        cover: getLabel('99294', '覆盖已有模板'),
        categoryPlaceholder: getLabel('101594', '请输入模板分类名称'),
      },
    },
  },
});

/**
 * 获取本地化配置
 * @param localization
 * @returns
 */
export default function getLocalization(localization?: Localization) {
  if (!localization) return defaultLolalization();

  return defaultsDeep(localization, defaultLolalization());
}
