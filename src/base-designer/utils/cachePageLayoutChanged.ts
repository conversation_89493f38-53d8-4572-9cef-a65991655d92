/**
 * 点击预览不再进行拦截
 * 记录页面是否被改动，在预览时给出提示
 */
export const cachePageLayoutChanged = {
  isChanged: (pageId: string) => {
    const layoutChangedPageIds = JSON.parse(
      localStorage.getItem('eb_layoutChanged_pageIds') || '[]',
    );

    return layoutChangedPageIds.includes(pageId);
  },

  delete: (pageId: string) => {
    let layoutChangedPageIds = JSON.parse(localStorage.getItem('eb_layoutChanged_pageIds') || '[]');

    layoutChangedPageIds = layoutChangedPageIds.filter((id: string) => id !== pageId);

    if (!layoutChangedPageIds.length) {
      localStorage.removeItem('eb_layoutChanged_pageIds');
    } else {
      localStorage.setItem('eb_layoutChanged_pageIds', JSON.stringify(layoutChangedPageIds));
    }
  },

  add: (pageId: string) => {
    const layoutChangedPageIds = JSON.parse(
      localStorage.getItem('eb_layoutChanged_pageIds') || '[]',
    );

    if (!layoutChangedPageIds.includes(pageId)) {
      layoutChangedPageIds.push(pageId);
    }

    localStorage.setItem('eb_layoutChanged_pageIds', JSON.stringify(layoutChangedPageIds));
  },
};

export default {};
