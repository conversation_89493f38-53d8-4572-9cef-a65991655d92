/**
 * Waterfall: 将函数执行结果作为参数传入到事件回调中
 * Normal: 将函数的调用参数传入到事件回调中
 */
type HookType = 'Waterfall' | 'Normal';
type EmitType = 'Normal' | 'Series';

type Options = {
  hookType?: HookType;
  emitType?: EmitType;
  name?: string;
};

function bindEvent(opts?: Options) {
  const { hookType, emitType, name } = {
    hookType: 'Normal',
    emitType: 'Normal',
    ...opts,
  };

  return function bindEventFn(this: any, target: Object, fnName: string, descriptor: any) {
    const { value, initializer } = descriptor!;

    return {
      configurable: true,
      enumerable: false,
      get(this: any) {
        const oldvalue = value || initializer?.call(this);
        const boundValue = oldvalue.bind(this);

        Object.defineProperty(this, fnName, {
          enumerable: false,
          writable: true,
          configurable: true,
          value: async (...args: any[]) => {
            const result = await boundValue(...args);
            const _evtName = name || fnName;
            const emitMethod = emitType === 'Normal' ? 'emit' : 'emitSeries';

            if (hookType === 'Waterfall') {
              return this.events[emitMethod](_evtName, result);
            }

            return this.events.emit[emitMethod](_evtName, ...args);
          },
        });

        return this[fnName];
      },
      set() {},
    };
  };
}

export default bindEvent as (
  opts?: Options
) => (target: Object, propertyKey: string | symbol, descriptor?: PropertyDescriptor) => void;
