// 原始的方法存放在__${actName}__
const getTargetName = (actName: string) => `__${actName}__`;

/**
 * 执行同步操作
 * @param actName 操作的方法名称，即注入的方法名
 * @param self store，如GridLayoutStore，MGridLayoutStore
 * @param args 操作方法的参数
 * @returns 操作方法的
 */
const executeAction = async (actName: string, self: any, args: any[]) => {
  const targetName = getTargetName(actName);

  // 触发get，初始化__${actName}__的值
  if (self.refStore?.[actName]) {
    const { refStore, syncManager } = self;
    const { argsAdapter, afterReturns } = syncManager;
    const argsHandler = argsAdapter?.[actName];
    let refArgs = args;

    // 这里需要先转换参数，防止执行操作后产生变化，导致同步方法调用时出现问题
    // 如删除操作，先执行删除，会导致获取不到com等
    // 获取转换后的参数，不存在handler则说明参数相同不需要转换
    if (argsHandler) {
      refArgs = await argsHandler.apply(self, args);
    }

    // 可能存在转换失败的情况，如不存在关联插件等情况
    if (refArgs) {
      const res = self[targetName](...args);
      const refRes = refStore[targetName](...refArgs);

      return Promise.all([res, refRes]).then(async (result: any[]) => {
        // 同步操作结束后执行的回调，如同步refCompId等
        await afterReturns[actName]?.apply(self, result);

        return result[0];
      });
    }

    return self[targetName](...args).then(async (result: any) => {
      // 未发生同步也执行下return，防止有特殊情况需要处理(不一定是只在同步的前提下)
      // 开发afterReturns时需要处理结果为一个的情况，避免出现异常情况
      await afterReturns[actName]?.apply(self, [result]);

      return result;
    });
  }
};

/**
 * 创建同步的action
 * @param actName action名称
 * @param ctx 上下文对象，指向this
 * @returns 重写后的同步action
 */
const createAyncAction = (actName: string, ctx: any) => function action(this: any, ...args: any[]) {
  return executeAction(actName, ctx, args);
};

/**
 * 同步注解，用于pc移动同步操作
 * @param target 实例，如GridLayoutStore，MGridLayoutStore
 * @param actName 方法名，加了注解的方法的名称
 * @param descriptor 属性描述
 * @returns
 */
export default function sync(this: any, target: any, actName: string, descriptor?: any): any {
  const { value, initializer } = descriptor!;

  return {
    configurable: true,
    enumerable: false,
    get(this: any) {
      const targetName = getTargetName(actName);
      const oldvalue = value || initializer.call(this);
      // 为原方法绑定this，避免调用时获取不到store
      const boundValue = oldvalue.bind(this);

      // 将原方法存放到__${actName}__中，当同步操作执行后
      // 需要调用原方法完成实际的操作
      Object.defineProperty(this, targetName, {
        enumerable: false,
        writable: true,
        configurable: true,
        value: boundValue,
      });

      // 重写actName方法，使其能够达到同步操作的目的
      Object.defineProperty(this, actName, {
        enumerable: false,
        writable: true,
        configurable: true,
        value: createAyncAction(actName, this),
      });

      return this[actName];
    },
    set() {},
  };
}
