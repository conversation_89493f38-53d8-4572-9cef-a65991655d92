type Scope = 'ls' | 'rs' | 'main' | 'actions' | 'component' | 'page' | 'compList';

export const prefix = '__action__';

export function isActionMethod(methodName: string) {
  return methodName.indexOf(prefix) === 0;
}

export function getMethodName(actName: string) {
  return actName.replace(prefix, '');
}

export default function method(scope?: Scope): any {
  return function actionMethod(this: any, target: any, actName: string, descriptor: any) {
    const actMethodName = `${prefix}${actName}`;

    // 在当前上下文上添加__action__xxx方法，作为标记，便于在autobind中能够识别出需要注册的action方法
    target[actMethodName] = (store: any, _value: any) => {
      if (scope) {
        store[scope][actName] = _value;
      } else {
        store[actName] = _value;
      }
    };

    return descriptor;
  };
}
