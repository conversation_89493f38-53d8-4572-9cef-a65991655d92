import React, { useEffect, useRef } from 'react';
import { useDesigner } from '../../hooks';
import { getMethodName, isActionMethod } from './method';

export default function autobind(Comp: any): any {
  return function ActionComponent(props: any) {
    const ref = useRef();
    const store = useDesigner();

    useEffect(() => {
      const instance: any = ref.current;

      if (instance && store) {
        // 找出所有'__action__'开头的方法，进行action注册
        // eslint-disable-next-line no-restricted-syntax,guard-for-in
        for (const prop in instance) {
          if (isActionMethod(prop)) {
            const actName = getMethodName(prop);
            const actMethod = instance[actName].bind(instance);

            // 将action方法注册到store中
            instance[prop](store, actMethod);
          }
        }
      }
    }, []);

    return <Comp weId={`${props.weId || ''}_jr5qgn`} ref={ref} {...props} />;
  };
}
