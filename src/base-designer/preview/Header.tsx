import { documentTitle as documentTitleUtils } from '@weapp/utils';
import React, { PureComponent, ReactNode, useEffect } from 'react';
import { When } from 'react-if';
import { ClientType } from '../../core/types';
import Header from '../header';
import ClientTypes from '../header/ClientTypes';

interface PreviewHeaderProps {
  title?: ReactNode;
  documentTitle?: string;
  isSingleClient: boolean;
  clientType: ClientType;
  onClientTypeChange: (clientType: ClientType) => void;
}
type LeftSideHeaderProps = React.Attributes & {
  title?: ReactNode;
  documentTitle?: string;
};

const LeftSideHeader: React.FC<LeftSideHeaderProps> = (props) => {
  const { title, documentTitle } = props;

  useEffect(() => {
    if (documentTitle) {
      documentTitleUtils.setTitle({ title: `${documentTitle}` });
    }
  }, [documentTitle]);

  if (!title) {
    return null;
  }

  return <>{title}</>;
};

export default class PreviewHeader extends PureComponent<PreviewHeaderProps> {
  render() {
    const {
      title, documentTitle, onClientTypeChange, isSingleClient, clientType,
    } = this.props;

    return (
      <Header
        weId={`${this.props.weId || ''}_iyom60`}
        showClient={false}
        showPreview={false}
        showSave={false}
        showTips={false}
        clientType={clientType}
        leftSide={
          <LeftSideHeader
            weId={`${this.props.weId || ''}_kohch5`}
            title={title}
            documentTitle={documentTitle}
          />
        }
      >
        <When weId={`${this.props.weId || ''}_cp8xp6`} condition={!isSingleClient}>
          <ClientTypes
            weId={`${this.props.weId || ''}_0ff181`}
            value={clientType}
            onChange={onClientTypeChange}
          />
        </When>
      </Header>
    );
  }
}
