import React, { PureComponent, ReactNode } from 'react';
import { prefixCls } from '../../constants';
import { MPreviewSize } from './constant';

const mobileDeviceSize = {
  iphone678: {
    width: '375px',
    height: '667px',
  },
  iphone678Plus: {
    width: '414px',
    height: '736px',
  },
  iphoneX: {
    width: '375px',
    height: '812px',
  },
};

interface MContentProps {
  content: ReactNode;
  device: MPreviewSize;
}

export default class MContent extends PureComponent<MContentProps> {
  render() {
    const { device, content } = this.props;
    const mobileStyle = {
      ...mobileDeviceSize[device],
      boxShadow: '0 0 10px 0 rgba(0, 0, 0, 0.1)',
      overflow: 'auto',
    };

    return (
      <div className={`${prefixCls}-mobile`}>
        <div className={`${prefixCls}-mobile-content`} style={mobileStyle}>
          <div>{content}</div>
        </div>
      </div>
    );
  }
}
