@import (reference) '../../style/prefix.less';
@import (reference) '../../style/var.less';

@buttonH: 32px;

.@{prefix} {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  overflow-y: hidden;

  &-hidden-header {
    height: 100%;
    .@{prefix}-header {
      transform: translateY(-@header-h);
      height: 0;
      transition: all 0.3s linear;
    }
  }

  &-show-header {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;

    .@{prefix}-header {
      height: @header-h;
      transition: all 0.3s linear;

      strong {
        color: var(--de-icon-color);
        padding-top: 1px;
        margin-left: 15px;

        & ~ span {
          padding-top: 2px;
        }

        & + span {
          margin: 0 6px;
          padding-top: 0;
        }
      }
    }
  }

  &-header-leftside {
    font-size: 13px;
  }

  &-hidden-content {
    flex: 1 1 auto;
    flex-basis: 0;
    height: 100%;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    position: relative;

    .@{prefix}-ls-content {
      width: 0;
      transition: all 0.3s linear;
    }
  }

  .@{prefix}-preview-main {
    flex: 1 1 auto;
    overflow: auto;
    background-color: var(--de-body-bgColor);
    box-shadow: 0px 0px 6px 4px var(--de-main-box-shandow-color) inset;
  }

  &-show-content {
    flex: 1 1 auto;
    flex-basis: 0;
    height: 100%;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    position: relative;

    .@{prefix}-ls-content {
      border-right: 1px solid var(--de-box-lineColor);
      transition: all 0.3s linear;
      background-color: var(--de-box-bgColor);
    }
  }

  & &-preview {
    left: 0;
    position: static;
  }

  &-button-group-wrap {
    margin-top: 10px;
    display: flex;
    color: var(--de-icon-color);
    cursor: pointer;
    box-shadow: 0 0 10px 0 rgba(24, 16, 16, 0.1);

    &:hover {
      color: var(--main-fc);
    }

    .@{prefix}-button-group-item {
      display: flex;
      border: 1px solid var(--border-color);
      flex-direction: column;
      overflow: hidden;

      .ui-btn-default:hover {
        color: var(--de-icon-color);
      }

      .ui-btn-default:before {
        height: @buttonH;
      }

      button {
        display: flex;
        align-items: center;
        height: @buttonH;
        width: 34px;
        color: var(--de-icon-color);
        border: 0;
        border-radius: 0;
        justify-content: center;
        background-color: var(--base-white);

        svg {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  &-dialog-wrap {
    .ui-dialog-content {
      width: 230px !important;
    }

    .ui-dialog-header-draggable {
      display: none;
    }
  }

  &-preview-toolbar {
    position: fixed;
    right: 40px;
    top: 100px;
    z-index: 200;
  }

  &-trigger-content-wrap {
    width: @ls-panel-w;
    max-width: @ls-panel-w;
    background-color: var(--base-white);
    transform: translateX(-5px);

    .ui-menu-menu {
      width: 140px;
    }

    .ui-menu-select-trigger .ui-menu-list {
      max-width: @ls-panel-w;
    }

    .ui-menu-select-trigger.ui-menu-select-trigger-normal {
      margin-right: 0px;
    }
  }

  .@{prefix}-preview {
    .@{prefix}-thumb-nail {
      right: auto !important;
      left: 200px !important;
    }
  }

  .@{prefix}-mobile {
    padding-top: 30px;
    padding-bottom: 30px;

    & > div {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin: 0 auto;
    }

    &-content {
      padding: 0;
      background-repeat: no-repeat;

      & > div > .weapp-ebpv {
        height: 100%;
      }
    }

    &-content > div {
      height: 100%;
      width: 100%;
      min-height: 0;
    }

    &-content &-content-border {
      width: 330px;
      height: 590px !important; // 兼容不显示边框时height为100%
      margin: 84px 0px 0 24px;
    }

    .ui-spin-nested-loading {
      height: 100%;

      & > div {
        height: 100%;
      }
    }
  }
}
