import { classnames } from '@weapp/utils';
import React, { PureComponent } from 'react';
import { Else, If, Then } from 'react-if';
import { prefixCls } from '../../constants';
import { ClientType } from '../../core/types';
import setTimeoutOnce from '../../utils/setTimeoutOnce';
import { MPreviewSize } from './constant';
import Header from './Header';
import './index.less';
import LeftSide from './LeftSide';
import MContent from './MContent';
import ToolBar from './toolbar';
import { PreviewProps } from './types';

interface PreviewStates {
  clientType: ClientType;
  isFullScreen: boolean;
  device: MPreviewSize;
}

export default class Preview extends PureComponent<PreviewProps, PreviewStates> {
  constructor(props: PreviewProps) {
    super(props);

    this.state = {
      clientType: this.props.defaultClientType || 'PC',
      isFullScreen: false,
      device: MPreviewSize.iphone678,
    };
  }

  onDeviceChange = (device: MPreviewSize) => {
    this.setState({ device });
  };

  onClientTypeChange = (clientType: ClientType) => {
    this.setState({ clientType });
  };

  onFullScreenChange = (isFullScreen: boolean) => {
    const { onScreenChange } = this.props;

    this.setState(
      {
        isFullScreen,
      },
      () => {
        setTimeoutOnce(() => {
          onScreenChange?.();
        }, 300);
      },
    );
  };

  render() {
    const { isFullScreen, device, clientType } = this.state;
    const {
      title,
      ls,
      renderContent,
      renderToolbarIcons,
      mQRCodeUrl,
      isSingleClient = false,
      documentTitle,
      allowFullScreen = true,
    } = this.props;
    const isMobile = clientType === 'MOBILE';

    return (
      <div
        className={classnames(prefixCls, {
          [`${prefixCls}-hidden-header`]: isFullScreen,
          [`${prefixCls}-show-header`]: !isFullScreen,
        })}
      >
        <Header
          weId={`${this.props.weId || ''}_h53yfu`}
          title={title}
          documentTitle={documentTitle}
          isSingleClient={isSingleClient}
          clientType={clientType}
          onClientTypeChange={this.onClientTypeChange}
        />
        <main
          className={classnames({
            [`${prefixCls}-hidden-content`]: isFullScreen,
            [`${prefixCls}-show-content`]: !isFullScreen,
          })}
        >
          <LeftSide weId={`${this.props.weId || ''}_r0gs95`} isFullScreen={isFullScreen} {...ls} />
          <main className={`${prefixCls}-preview-main`}>
            <If weId={`${this.props.weId || ''}_we86oz`} condition={!isMobile}>
              <Then weId={`${this.props.weId || ''}_razkan`}>{renderContent(clientType)}</Then>
              <Else weId={`${this.props.weId || ''}_95ak7s`}>
                <MContent
                  weId={`${this.props.weId || ''}_9xutb2`}
                  device={device}
                  content={renderContent(clientType)}
                />
              </Else>
            </If>
          </main>
          <ToolBar
            weId={`${this.props.weId || ''}_69za3f`}
            isMobile={isMobile}
            isFullScreen={isFullScreen}
            allowFullScreen={allowFullScreen}
            onFullScreenChange={this.onFullScreenChange}
            device={device}
            onDeviceChange={this.onDeviceChange}
            mQRCodeUrl={mQRCodeUrl}
            renderToolbarIcons={renderToolbarIcons}
          />
        </main>
      </div>
    );
  }
}
