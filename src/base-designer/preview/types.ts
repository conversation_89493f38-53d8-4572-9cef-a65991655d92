import { ReactNode } from 'react';
import { ClientType } from '../../core/types';

export interface LeftSideProps {
  /** 左侧栏宽度 */
  width?: number | string;
  /** 自定义左侧栏样式 */
  containerClassNames?: string;
  /** 渲染左侧栏 */
  renderPanelContent?: () => ReactNode;
}

export interface PreviewProps {
  /** 标题 */
  title?: ReactNode;
  /** 网页标题 */
  documentTitle?: string;
  /** 默认客户端类型 */
  defaultClientType: ClientType;
  /** 是否为单客户端 */
  isSingleClient?: boolean;
  /** 左侧栏属性 */
  ls?: LeftSideProps;
  /** 移动端二维码url */
  mQRCodeUrl?: string;
  /** 是否支持全屏展示 */
  allowFullScreen?: boolean;
  /** 预览主体内容 */
  renderContent: (clientType: ClientType) => ReactNode;
  /** 预览页面重新渲染的回调 */
  onScreenChange?: () => void;
  renderToolbarIcons?: (content: ReactNode) => ReactNode;
}
