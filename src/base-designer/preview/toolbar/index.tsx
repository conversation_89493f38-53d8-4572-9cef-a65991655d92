import React, { ComponentType, PureComponent, ReactNode } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import { MPreviewSize } from '../constant';
import Device from './item/Device';
import FullScreen from './item/FullScreen';
import QrCode from './item/QRCode';
import ToolbarGroup from './ToolbarGroup';

interface ToolBarProps {
  isFullScreen: boolean;
  allowFullScreen: boolean;
  device?: MPreviewSize;
  isMobile: boolean;
  mQRCodeUrl?: string;
  onFullScreenChange: (isFullScreen: boolean) => void;
  onDeviceChange: (device: MPreviewSize) => void;
  renderToolbarIcons?: (content: ReactNode) => ReactNode;
}

class PanelBoard extends PureComponent<ToolBarProps> {
  render() {
    const {
      isFullScreen,
      onFullScreenChange,
      isMobile,
      device,
      onDeviceChange,
      renderToolbarIcons = (v) => v,
      mQRCodeUrl,
      allowFullScreen,
    } = this.props;

    return (
      <div className={`${prefixCls}-preview-toolbar`}>
        <ToolbarGroup weId={`${this.props.weId || ''}_3kh41i`}>
          {renderToolbarIcons(
            <>
              <When weId={`${this.props.weId || ''}_9yg6e1`} condition={allowFullScreen}>
                <FullScreen
                  weId={`${this.props.weId || ''}_plhdma`}
                  value={isFullScreen}
                  onChange={onFullScreenChange}
                />
              </When>
              <When weId={`${this.props.weId || ''}_9yg6e1`} condition={isMobile}>
                <Device
                  weId={`${this.props.weId || ''}_plhdma`}
                  value={device}
                  onChange={onDeviceChange}
                />
              </When>
            </>,
          )}
        </ToolbarGroup>
        <When weId={`${this.props.weId || ''}_0x18gl`} condition={isMobile && mQRCodeUrl}>
          <ToolbarGroup weId={`${this.props.weId || ''}_3kh41i`}>
            <QrCode weId={`${this.props.weId || ''}_plhdma`} url={mQRCodeUrl!} />
          </ToolbarGroup>
        </When>
      </div>
    );
  }
}

export default PanelBoard as unknown as ComponentType<ToolBarProps>;
