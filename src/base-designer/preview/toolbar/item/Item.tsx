import { Popover } from '@weapp/ui';
import React, { PureComponent } from 'react';

interface ItemProps extends React.Attributes {
  title: string;
}

export default class Item extends PureComponent<ItemProps> {
  render() {
    const { children, title } = this.props;

    return (
      <Popover
        weId={`${this.props.weId || ''}_4zu5fr`}
        popup={title}
        placement="left"
        popoverType="tooltip"
      >
        {children}
      </Popover>
    );
  }
}
