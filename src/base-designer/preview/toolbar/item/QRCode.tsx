import {
  Button, Dialog, Icon, Qrcode,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { PureComponent } from 'react';
import { prefixCls } from '../../../../constants';
import Item from './Item';

interface DialogDemoProps {
  url: string;
}

interface DialogDemoStates {
  visible?: boolean;
}

export default class QrCode extends PureComponent<DialogDemoProps, DialogDemoStates> {
  state = {
    visible: false,
  };

  onClose = () => {
    this.setState({ visible: false });
  };

  showDialog = () => {
    this.setState({ visible: true });
  };

  render() {
    const { visible } = this.state;
    const { url } = this.props;

    return (
      <Item weId={`${this.props.weId || ''}_61abp4`} title={getLabel('54531', '二维码')}>
        <Button
          weId={`${this.props.weId || ''}_8nwg55`}
          size="small"
          type="default"
          onClick={this.showDialog}
        >
          <Icon weId={`${this.props.weId || ''}_qzeyv4`} name="Icon-QR-code-o" size="md" />
        </Button>
        <Dialog
          weId={`${this.props.weId || ''}_d6obqh`}
          visible={visible}
          onClose={this.onClose}
          draggable
          mask
          maskClosable
          className={`${prefixCls}-dialog-wrap`}
          icon="Icon-e-builder-o"
        >
          <Qrcode weId={`${this.props.weId || ''}_d8wyy4`} url={url} size={200} level="H" />
        </Dialog>
      </Item>
    );
  }
}
