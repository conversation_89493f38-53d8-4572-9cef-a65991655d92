import {
  <PERSON><PERSON>, Cors<PERSON>omponent, Icon, Menu, MenuItemData,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { PureComponent } from 'react';
import { prefixCls } from '../../../../constants';
import { MPreviewSize } from '../../constant';
import Item from './Item';

const menuData = [
  {
    id: MPreviewSize.iphone678,
    content: 'iPhone 6/7/8',
    groupId: 'Device',
  },
  {
    id: MPreviewSize.iphone678Plus,
    content: 'iPhone 6/7/8 Plus',
    groupId: 'Device',
  },
  {
    id: MPreviewSize.iphoneX,
    content: 'iPhone X',
    groupId: 'Device',
  },
];

interface DeviceProps {
  value?: MPreviewSize;
  onChange?: (value: MPreviewSize) => void;
}

export default class Device extends PureComponent<DeviceProps> {
  onChange = (value: string) => {
    const { onChange } = this.props;

    if (onChange) {
      onChange(value as MPreviewSize);
    }
  };

  customRenderItem = (_: any, ele: React.ReactNode, itemData: MenuItemData) => {
    const { value } = this.props;

    if (value === itemData.id) {
      return (
        <>
          <div
            className="ui-menu-list-item-active ui-menu-list-item-select-active ui-menu-list-item ui-menu-list-item-select"
            id={itemData.id}
          >
            <span className="ui-menu-list-item-content">{itemData.content}</span>
            <span className="ui-menu-list-item-lefticon">
              <CorsComponent
                weId={`${this.props.weId || ''}_1v6skd@${itemData.id}`}
                app="@weapp/ebdcoms"
                compName="IconFont"
                type="lujing"
                size="s"
              />
            </span>
          </div>
        </>
      );
    }

    return ele;
  };

  render() {
    const { value = MPreviewSize.iphone678 } = this.props;

    return (
      <Item weId={`${this.props.weId || ''}_61abp4`} title={getLabel('54528', '机型')}>
        <div>
          <Menu
            weId={`${this.props.weId || ''}_zdwldg`}
            data={menuData}
            action={['click']}
            type="select"
            overlayClassName={`${prefixCls}-trigger-content-wrap`}
            popupPlacement="left"
            value={value}
            onChange={this.onChange}
            customRenderItem={this.customRenderItem}
            customSelectContent={
              <Button weId={`${this.props.weId || ''}_8nwg55`} size="small" type="default">
                <Icon
                  weId={`${this.props.weId || ''}_qzeyv4`}
                  name="Icon-mobile-phone-o"
                  size="md"
                />
              </Button>
            }
          />
        </div>
      </Item>
    );
  }
}
