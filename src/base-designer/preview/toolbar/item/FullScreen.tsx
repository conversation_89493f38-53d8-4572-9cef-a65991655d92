import { Button, Icon } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { ComponentType, PureComponent } from 'react';
import { MPreviewSize } from '../../constant';
import Item from './Item';

export interface FullScreenProps {
  value: boolean;
  device?: MPreviewSize;
  onChange: (isFullScreen: boolean) => void;
}

class FullScreen extends PureComponent<FullScreenProps> {
  onClick = () => {
    const { onChange, value } = this.props;
    onChange(!value);
  };

  render() {
    const { value } = this.props;

    return (
      <Item
        weId={`${this.props.weId || ''}_61abp4`}
        title={value ? getLabel('54529', '退出全屏') : getLabel('54530', '全屏')}
      >
        <Button
          weId={`${this.props.weId || ''}_so9hwx`}
          size="small"
          type="default"
          onClick={this.onClick}
        >
          <Icon
            weId={`${this.props.weId || ''}_qzeyv4`}
            name={value ? 'Icon-Global-reduction' : 'Icon-Global-zoom-in'}
            size="md"
          />
        </Button>
      </Item>
    );
  }
}

export default FullScreen as unknown as ComponentType<FullScreenProps>;
