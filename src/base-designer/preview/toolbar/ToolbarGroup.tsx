import React, { ComponentType, PureComponent } from 'react';
import { prefixCls } from '../../../constants';

class ToolbarGroup extends PureComponent<React.Attributes> {
  render() {
    const { children } = this.props;

    return (
      <div className={`${prefixCls}-button-group-wrap`}>
        <div className={`${prefixCls}-button-group-item`}>{children}</div>
      </div>
    );
  }
}

export default ToolbarGroup as unknown as ComponentType<{}>;
