import React, { PureComponent } from 'react';
import { prefixCls } from '../../constants';
import { LeftSideProps } from './types';

interface PreviewLSProps extends LeftSideProps {
  isFullScreen: boolean;
}

export default class LeftSide extends PureComponent<PreviewLSProps> {
  render() {
    const { isFullScreen, renderPanelContent, containerClassNames } = this.props;
    let { width = 200 } = this.props;

    if (typeof width === 'number') {
      width = `${width}px`;
    }

    if (!renderPanelContent) return null;

    return (
      <div
        className={`${prefixCls}-ls-content ${prefixCls}-lsc-float ${prefixCls}-preview ${containerClassNames}`}
        style={isFullScreen ? { transform: `translateX(-${width})` } : { width }}
      >
        {renderPanelContent!()}
      </div>
    );
  }
}
