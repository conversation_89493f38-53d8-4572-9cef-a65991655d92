import React, { ComponentType } from 'react';
import useInternal from '../../hooks/useInternal';
import {
  AttributePanel,
  ComponentPanel,
  EditorPanel,
  PageConfigPanel,
  PagePanel,
  PageTempPanel,
  RightsPanel,
  StylePanel,
  TemplatePanel,
} from '../../panels';
import ComEventsPanel from '../../panels/com-events';
import DatasetPanel from '../../resolver/DatasetPanel';
import ElementPanel from '../../resolver/ElementPanel';

type PanelsType = Record<string, ComponentType<any>>;

interface PanelProps extends React.Attributes {
  /** 面板唯一标识 */
  id?: string;
  data: { type: string; props?: any };
}

const internalCommonPanels = {
  attr: AttributePanel,
  containerStyle: StylePanel,
  comStyle: StylePanel,
  pageStyle: StylePanel,
  element: ElementPanel,
  dataset: DatasetPanel,
  page: PagePanel,
  component: ComponentPanel,
  template: Template<PERSON>ane<PERSON>,
  editor: EditorPanel,
  pageTemplate: PageTempPanel,
  comEvents: ComEventsPanel,
  pageConfig: PageConfigPanel,
  rights: RightsPanel,
};

export default function Panel(props: PanelProps) {
  const { data, ...restProps } = props;
  const { type, props: panelProps } = data;
  // 设计器内部自定义的panels，可以重写公共的内置panels
  const { panels: customPanels } = useInternal()!;
  const allPanels: PanelsType = { ...internalCommonPanels, ...customPanels };
  const PanelComponent = allPanels[type];

  if (PanelComponent) {
    return <PanelComponent weId={`${props.weId || ''}_8o2wct`} {...panelProps} {...restProps} />;
  }

  return null;
}
