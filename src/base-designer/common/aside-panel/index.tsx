import { utils } from '@weapp/ui';
import React, { ReactElement } from 'react';
import { FormInitAllDatasEx } from '../../../common/form/types';
import reactTypeof from '../../../core/utils/reactTypeof';
import Panel from './Panel';
import { AsideMenuData } from './types';

interface AsidePanelProps extends React.Attributes {
  menu?: AsideMenuData | null;
  onBeforeInitConfig?: (config: FormInitAllDatasEx, com: any) => FormInitAllDatasEx;
}

export default function AsidePanel(props: AsidePanelProps) {
  // 加上restProps, 是为了通过 getPanelContentByType 方法获取到面板组件，额外加props可以传入到组件中去
  const { menu, ..._restProps } = props;
  const panel = props.menu?.panel;
  const restProps = { ..._restProps, id: menu?.id };

  if (!panel) return null;

  if (typeof panel === 'string') {
    return <Panel weId={`${props.weId || ''}_t4fs1y`} data={{ type: panel }} {...restProps} />;
  }

  // panel为React.ComponentType
  if (reactTypeof.isReactComponent(panel)) {
    const PanelComponent = panel as React.ComponentType;

    return <PanelComponent weId="4kze6e" {...restProps} />;
  }

  // panel为ReactNode
  if (React.isValidElement(panel)) {
    return React.cloneElement(panel as ReactElement, { ...panel.props, ...restProps });
  }

  // panel为 { type: '', props: '' }
  if (utils.isObject(panel)) {
    return <Panel weId={`${props.weId || ''}_3bxnfi`} data={panel as any} {...restProps} />;
  }

  return null;
}
