import { eventEmitter } from '@weapp/utils';
import { UUID } from '../../utils';
import { commonPlugins, corePlugins } from './plugins/mixins';
import {
  DesignerPlugin,
  DesignerPluginClass,
  InvokeOpts,
  PluginCenterOptsType,
  PluginHookType,
  PluginNormalHookType,
} from './types';

/** 同步get */
const isReduceHook = (hookName: string) => hookName.indexOf('get') === 0;
/** 异步get */
const isSyncReduceHook = (hookName: string) => hookName.indexOf('onSync') === 0;

class PluginCenter {
  /** 设计器唯一标识 */
  designerId = UUID(6);

  private opts: Omit<PluginCenterOptsType, 'designerProps'>;

  private design: PluginCenterOptsType['designerProps'];

  /** 所有启用的插件 */
  private enabledPlugins: DesignerPlugin[] = [];

  /** 所有启用插件的map记录 */
  private enabledPluginRecord: Record<string, DesignerPlugin> = {};

  constructor(plugins: DesignerPluginClass[], opts: PluginCenterOptsType) {
    const corePluginClasses = corePlugins.map((name) => commonPlugins[name]);
    const { designerProps, ...restOpts } = opts;

    this.opts = restOpts;
    this.design = designerProps;

    this.initPlugins([...corePluginClasses, ...plugins]);
  }

  private initPlugins(plugins: DesignerPluginClass[]) {
    const pluginNames: string[] = [];

    plugins.forEach((plugin) => {
      const instance = this.createPlugin(plugin);

      // 避免出现外部的插件与内部插件重名，导致功能异常的情况
      if (!pluginNames.includes(instance.name)) {
        pluginNames.push(instance.name);
        this.initEnabledPlugin(instance);
        return;
      }
      console.error(`${instance.name} plugin already exists！`);
    });
  }

  private createPlugin(PluginClass: DesignerPluginClass, plugOpts?: Record<string, any>) {
    const instance = new PluginClass({
      pluginCenter: this,
      design: this.design,
      ...plugOpts,
    });

    instance.pluginCenter = this;
    instance.design = this.design;

    // 插件缺少name属性，不符合规范，不启用该插件
    if (!instance.name) {
      console.error('Missing "name" property, plugin will be disabled.');

      Object.defineProperty(instance, 'isEnabled', {
        get() {
          return false;
        },
      });
    }
    return instance;
  }

  private initEnabledPlugin(plug: DesignerPlugin) {
    const isEnabled = plug.isEnabled !== false;

    if (isEnabled) {
      this.enabledPlugins.push(plug);
      this.enabledPluginRecord[plug.name] = plug;
    }
  }

  public invoke(hookName: PluginHookType, opts?: InvokeOpts) {
    const { args = [] } = opts || {};

    // 执行getXXX的hook
    if (isReduceHook(hookName)) {
      return this.enabledPlugins.reduce((prevArgs, plugin) => {
        const currHook = plugin[hookName] as Function;

        if (currHook) {
          return currHook(...prevArgs);
        }
        return prevArgs;
      }, args);
    }

    // 执行SyncGetXXX的hook
    if (isSyncReduceHook(hookName)) {
      return this.enabledPlugins.reduce(async (_prevArgs, plugin) => {
        const currHook = plugin[hookName] as Function;
        const prevArgs = await _prevArgs;

        if (currHook) {
          return currHook(prevArgs);
        }
        return prevArgs;
      }, args[0]);
    }

    this.enabledPlugins.forEach((plugin) => {
      // @ts-ignore
      plugin[hookName as PluginNormalHookType]?.(...args);
    });

    return null;
  }

  /**
   * 使用插件实例
   * @param plugName 插件名称
   * @returns 插件实例
   */
  public use(plugName: string) {
    return this.enabledPluginRecord[plugName];
  }

  /**
   * 用于插件间事件监听交互
   * @param evtName 事件名称
   * @param callback 处理事件的回调
   */
  public on(evtName: string, callback: (...args: any[]) => void) {
    const name = this.getEvtName(evtName);

    eventEmitter.on('@weapp/designer', name, callback);
  }

  /**
   * 用于插件间事件监听交互
   * @param evtName 事件名称
   * @param callback 处理事件的回调
   */
  public emit(evtName: string, ...args: any[]) {
    const name = this.getEvtName(evtName);

    eventEmitter.emit('@weapp/designer', name, ...args);
  }

  /**
   * 用于解绑插件事件，注意：如果使用了on，则必须在插件销毁destory时调用off方法，避免内存泄露
   * @param evtName 事件名称
   * @param callback 处理事件的回调
   */
  public off(evtName: string, callback: (...args: any[]) => void) {
    const name = this.getEvtName(evtName);

    eventEmitter.off('@weapp/designer', name, callback);
  }

  private getEvtName(evtName: string) {
    return `${evtName}_${this.designerId}`;
  }
}

export default PluginCenter;
