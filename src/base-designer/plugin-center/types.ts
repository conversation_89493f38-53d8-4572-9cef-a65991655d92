import { ClientType } from '../../types';
import { BaseDesignerProps } from '../types';
import PluginCenter from './index';

export type PluginCenterType = PluginCenter;

export interface DesignerPlugin {
  pluginCenter?: PluginCenterType;

  design?: BaseDesignerProps;

  /** 插件名称，唯一标志 */
  name: string;

  /** 插件是否启用 */
  readonly isEnabled?: boolean;

  /** --------------------------------------- 设计器插件钩子 --------------------------------------- */

  /** didmount回调 */
  onDidMount?(): void;

  /** 插件销毁 */
  destory?: () => void;

  /** 设计器初始化前 */
  onBeforeMount?(): void;

  /** 设计器异步初始化前 */
  onSyncBeforeMount?(load: Promise<boolean>): Promise<boolean>;
  /** 获取布局生成前的布局数据 */
  getLayoutDatas?(
    layoutDatas: BaseDesignerProps['layoutDatas'],
  ): BaseDesignerProps['layoutDatas'][];

  /** 切换终端 */
  onClientChange?(client: ClientType): void;
}

export interface DesignerPluginClass {
  new (opts?: any): DesignerPlugin;
}

export type PluginHookType = Exclude<
  keyof DesignerPlugin,
  'pluginCenter' | 'isEnabled' | 'name' | 'design'
>;

export type InvokeOpts = {
  args?: any[];
};

export type PluginReduceHookType = keyof Pick<
  DesignerPlugin,
  keyof DesignerPlugin & `get${string}`
>;

export type PluginSyncReduceHookType = keyof Pick<
  DesignerPlugin,
  keyof DesignerPlugin & `onSync${string}`
>;

export type PluginNormalHookType = Exclude<
  PluginHookType,
  PluginReduceHookType | PluginSyncReduceHookType
>;

/**
 * 插件中心选项配置（支持外部传入的属性）
 */
export type PluginCenterOptsType = {
  designerProps: BaseDesignerProps;
};

export default {};
