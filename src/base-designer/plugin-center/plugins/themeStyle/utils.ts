import { BaseDesignerProps } from '../../../types';

export const getPageLayouts = (data: BaseDesignerProps['layoutDatas'], opts: any) => {
  const {
    themeStyle = {},
    isSetTheme = false,
    isSpecialTheme = false,
    isSwitchMobile = false,
    setSwitchMobile,
    formatComsLibStyle,
  } = opts || {};
  if (formatComsLibStyle) {
    data = (data || []).map((item) => {
      let { comps } = item;
      if (
        item.comps
        && item.comps.length > 0
        && (!isSpecialTheme || (isSpecialTheme && (item.terminalType === 'PC' || isSwitchMobile)))
      ) {
        if (isSwitchMobile) {
          setSwitchMobile(false);
        }
        // 移动端时，不需要追加特色主题样式
        comps = formatComsLibStyle(item.comps, item.compStyles || [], themeStyle, {
          isSetTheme,
        });
      }
      return {
        ...item,
        comps,
      };
    });
  }

  return data;
};

export default {};
