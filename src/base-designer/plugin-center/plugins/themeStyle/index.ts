import { cloneDeep, corsImport, getLabel } from '@weapp/utils';
import { EVENT_BEFORE_CREATE_COM } from '../../../../constants';
import { ClientType, IComData } from '../../../../types';
import ebdcoms from '../../../../utils/ebdcoms';
import { getStorage, setStorage } from '../../../../utils/ls';
import setTimeoutOnce from '../../../../utils/setTimeoutOnce';
import { BaseDesignerProps } from '../../../types';
import { designThemeInfo, isPassportEbpage } from '../../../utils';
import { DesignerPlugin, PluginCenterType } from '../../types';
import { ThemeStyleProps } from './types';
import { getPageLayouts } from './utils';

const pluginName = 'PageThemeStyle';
// 类型保护函数,返回类型为function
const tsGurdFunction = (obj: any): obj is Function => typeof obj === 'function';

class ThemeStylePlugin implements DesignerPlugin {
  name = pluginName;

  pluginCenter!: PluginCenterType;

  design!: BaseDesignerProps;

  pageId: string = '';

  urlPrefix = '';

  /** 系统特色主题关联的页面主题id */
  sysSpecialThemeId: string = '';

  themeLoaded: boolean = false;

  themeStyle: ThemeStyleProps = {
    themeCss: '',
    refStyles: [], // 主题关联的组件样式
    styleConfig: null, // 主题自定义样式
    themeId: '', // 主题id
  };

  /** 系统特色主题样式配置 */
  specialThemeStyle: ThemeStyleProps = {
    themeCss: '',
    refStyles: [], // 主题关联的组件样式
    styleConfig: null, // 主题自定义样式
    themeId: '',
  };

  /** 是否是设置页面主题 */
  setTheme = false;

  /** 是否切换移动端 */
  isSwitchMobile = false;

  isEnabled: boolean = false;

  layoutDatas: BaseDesignerProps['layoutDatas'] = [];

  formatComsLibStyle: any = null;

  get isSpecialTheme() {
    /** 当前系统主题是否为特色主题 */
    const isSysSpecialTheme = window.TEAMS?.theme?.themeType === 'feature';

    return (
      isSysSpecialTheme
      && this.sysSpecialThemeId
      && this.themeStyle?.themeId === this.sysSpecialThemeId
    );
  }

  constructor(opts: any) {
    const { design } = opts || {};
    const { pluginOptions, layoutDatas } = design || {};
    const config = pluginOptions?.[pluginName] || {};

    if (config?.urlPrefix) {
      Object.keys(config).forEach((key) => {
        this[key as keyof this] = config[key];
      });
      this.layoutDatas = layoutDatas;
      this.isEnabled = true;
    }
  }

  onDidMount = () => {
    this.design.store.on(EVENT_BEFORE_CREATE_COM, this.onBeforeCreate);
    this.design.store.events?.on('ctrl+c', this.onCopy);
    this.design.store.events?.on('ctrl+v', this.onPaste);
  };

  destory = () => {
    this.design.store.off(EVENT_BEFORE_CREATE_COM, this.onBeforeCreate);
    this.design.store.events?.off('ctrl+c', this.onCopy);
    this.design.store.events?.off('ctrl+v', this.onPaste);
  };

  updatedesignThemeInfo = (themeId: string, themeVersion: string) => {
    // 更新关联主题的id
    designThemeInfo.id = themeId;
    // 更新关联主题的版本
    designThemeInfo.version = themeVersion;
  };

  updateCssCacheInfo = () => {
    // 更新当前终端的主题信息
    this.design.store.layoutStore?.updateCssCacheInfo?.(
      designThemeInfo.id,
      designThemeInfo.version,
    );
  };

  onSyncBeforeMount = () => {
    this.pageId = this.design.layoutDatas?.[0]?.pageId || '';

    if (!this.pageId) {
      return Promise.resolve(false);
    }

    const { layoutDatas } = this.design;
    const client = layoutDatas![0].terminalType;

    return new Promise((resolve) => {
      this.initTheme(this.pageId, client)
        .then(() => {
          corsImport('@weapp/ebdpage')
            .then((mod) => {
              const { viewUtils } = mod;
              const { formatComsLibStyle: _formatComsLibStyle } = viewUtils || {};
              this.formatComsLibStyle = _formatComsLibStyle;
              this.formartLayoutDatas(layoutDatas);
              resolve(true);
            })
            .catch(() => {
              resolve(true);
            });
        })
        .catch(() => {
          resolve(true);
        });
    }) as Promise<boolean>;
  };

  formartLayoutDatas = (layoutDatas: BaseDesignerProps['layoutDatas'], opts?: any) => {
    this.layoutDatas = getPageLayouts(cloneDeep(layoutDatas), {
      themeStyle: this.themeStyle,
      isSpecialTheme: this.isSpecialTheme,
      isSwitchMobile: this.isSwitchMobile,
      setSwitchMobile: this.setSwitchMobile,
      formatComsLibStyle: this.formatComsLibStyle,
      ...opts,
    });
  };

  onBeforeCreate = (com: IComData<any>, refcom: IComData<any>) => new Promise((resolve) => {
    corsImport('@weapp/ebdpage')
      .then((mod: any) => {
        const { viewUtils } = mod;
        const { formatComsLibStyle } = viewUtils || {};
        if (refcom) {
          formatComsLibStyle([refcom], [], this.themeStyle);
        }
        [com] = formatComsLibStyle([com], [], this.themeStyle);
        resolve(com);
      })
      .catch(() => {
        resolve(com);
      });
  });

  onCopy = () => {
    if (this.design.store.layoutType === 'GRID') {
      return this.onCopyGrid();
    }

    return this.onCopyFlow();
  };

  onPaste = () => {
    if (this.design.store.layoutType === 'GRID') {
      return this.onPasteGrid();
    }

    return this.onPasteFlow();
  };

  onCopyFlow = () => {
    const storageKeys = ['configPC', 'configMobile'];
    // 定时解决复制组件可能异步的情况
    setTimeoutOnce(() => {
      const copyComFlow = getStorage('copyComFlow' as any);
      if (!copyComFlow) return;
      storageKeys.forEach((key: any) => {
        const lsCopyComs = copyComFlow[key] ? JSON.parse(copyComFlow[key]) : undefined;
        if (lsCopyComs) {
          lsCopyComs.forEach((com: any) => {
            if (com.config && com.config.styles) {
              com.config.styles.forEach((item: any) => {
                if (item.themeId && !item.useComStyle) {
                  item.libStyle = undefined;
                  item.id = '';
                }
                delete item.themeId;
                delete item.themeLibStyle;
                delete item.themeLibStyleId;
              });
            }
          });
          copyComFlow[key] = JSON.stringify(lsCopyComs);
        }
      });
      setStorage('copyComFlow' as any, copyComFlow);
    }, 1500);
  };

  onPasteFlow = () => {
    const storageKeys = ['configPC', 'configMobile'];
    if (this.formatComsLibStyle) {
      const copyComFlow = getStorage('copyComFlow' as any);
      if (!copyComFlow) return;
      storageKeys.forEach((key: any) => {
        const lsCopyComs = copyComFlow[key] ? JSON.parse(copyComFlow[key]) : undefined;
        if (lsCopyComs) {
          const newComs = this.formatComsLibStyle(lsCopyComs, [], this.themeStyle, {
            isPaste: true,
          });
          copyComFlow[key] = JSON.stringify(newComs);
        }
      });
      setStorage('copyComFlow' as any, copyComFlow);
    }
  };

  onCopyGrid = () => {
    const storageKeys = ['copyCom', 'copyMCom'];
    // 定时解决复制组件可能异步的情况
    setTimeoutOnce(() => {
      storageKeys.forEach((key: any) => {
        const lsCopyCom = getStorage(key);
        if (lsCopyCom && lsCopyCom.config && lsCopyCom.config.styles) {
          lsCopyCom.config.styles.forEach((item: any) => {
            if (item.themeId && !item.useComStyle) {
              item.libStyle = undefined;
              item.id = '';
            }
            delete item.themeId;
            delete item.themeLibStyle;
            delete item.themeLibStyleId;
          });
          setStorage(key, lsCopyCom);
        }
      });
    }, 1500);
  };

  onPasteGrid = () => {
    const storageKeys = ['copyCom', 'copyMCom'];
    if (this.formatComsLibStyle) {
      storageKeys.forEach((key: any) => {
        const lsCopyCom = getStorage(key);
        if (lsCopyCom) {
          const [newCom] = this.formatComsLibStyle([lsCopyCom], [], this.themeStyle, {
            isPaste: true,
          });
          setStorage(key, newCom);
        }
      });
    }
  };

  onClientChange = (client: ClientType) => {
    this.loadSpecialThemeStyle(client);
    // 切换终端时，更新当前终端的主题信息
    this.updateCssCacheInfo();
  };

  getLayoutDatas = (layoutDatas: BaseDesignerProps['layoutDatas']) => {
    this.formartLayoutDatas(layoutDatas);
    return [this.layoutDatas];
  };

  // 主题设计器内，以当前特色主题为最高优先级 - (此处没有判断isThemePage，是因为走到这里时isThemePage还没有开始取值)
  // 页面主题样式大于系统主题样式，不共存关系，存在页面主题时，忽略系统页面主题样式
  initTheme = async (pageId: string, client: ClientType, callBack?: Function) => {
    await this.setSysSpecialThemeId(pageId);

    return ebdcoms
      .asyncExcu('ajax', {
        url: `${this.urlPrefix}/theme/info`,
        params: {
          pageId,
          // 设计器内切换至移动端时，不需要追加特色主题id
          // 登录前页面不需要加载特色主题
          refSysTheme:
            client === 'MOBILE' || isPassportEbpage() ? '' : this.sysSpecialThemeId || '',
        },
        ebBusinessId: pageId,
        success: (data: ThemeStyleProps) => {
          this.themeLoaded = true;
          const dataThemeId = data.themeId;
          const changes = data;
          this.updatedesignThemeInfo(dataThemeId || '', data.themeVersion || '');
          // 设计器内不再返回特色主题样式，门户移动端/eb移动端（单端）会被修改themeId导致影响isSpecialTheme的判断
          // if (client === 'MOBILE' && this.sysSpecialThemeId) {
          //   // 该页面没有绑定主题且存在系统特色主题时，切换至移动端themeId不变（避免影响isSpecialTheme的判断）
          //   changes.themeId = this.sysSpecialThemeId;
          // }

          this.setThemeStyle(changes);

          // 取消当前页面主题后，如果返回了特色主题样式，需要缓存下
          if (dataThemeId === this.sysSpecialThemeId && this.isSpecialTheme) {
            this.setSpecialThemeStyle(data);
          }

          this.loadThemeStyle();
        },
      })
      .finally(() => {
        callBack?.();
      });
  };

  setSysSpecialThemeId = (pageId?: string) => ebdcoms.load().then(({ getUrlParams }) => {
    const urlParams = getUrlParams(pageId || this.pageId || '');

    /** 当前系统主题是否为特色主题 */
    const isSysSpecialTheme = window.TEAMS?.theme?.themeType === 'feature';
    /** 特色主题关联的页面主题id */
    const refSysTheme = window.TEAMS?.theme?.moduleStyle;

    this.sysSpecialThemeId = urlParams?.themeId || (isSysSpecialTheme && refSysTheme);
  });

  // 处理设计器页面内 页面主题的css样式（预览、运行页在ebdpage内实现）
  loadThemeStyle = (doc?: Document) => {
    const { appThemeCss = '', themeCss = '' } = this.themeStyle;

    ebdcoms.asyncExcu(
      'importStyles',
      {
        styleId: 'ebd_appthemecode_style',
        styleKey: `app_${this.design.main?.id || 'appid'}`,
      },
      appThemeCss,
      doc,
    );
    ebdcoms.asyncExcu(
      'importStyles',
      {
        styleId: 'ebd_pagethemecode_style',
        styleKey: `page_${this.pageId || 'pageId'}`,
      },
      themeCss,
      doc,
    );
  };

  loadSpecialThemeStyle = (client: ClientType) => {
    // 当前页面主题是系统特色主题
    if (this.isSpecialTheme) {
      this.setTheme = true;
      if (client === 'PC' && this.specialThemeStyle?.themeId) {
        this.setThemeStyle(this.specialThemeStyle);

        this.loadThemeStyle();
      } else {
        this.setThemeStyle({
          themeId: '',
          themeCss: '',
          refStyles: [],
          styleConfig: null, // 主题自定义样式
        });
        this.isSwitchMobile = true;
        this.initTheme(this.pageId || '', client).catch(() => {
          this.loadThemeStyle();
        });
      }
    } else {
      this.loadThemeStyle();
    }
  };

  setThemeStyle(themeStyle: ThemeStyleProps, callBack?: Function) {
    this.themeStyle = { ...themeStyle };
    if (this.setTheme || this.isSwitchMobile) {
      this.setTheme = false;
      this.onUpdatePageLayouts();
    }
    callBack?.();
  }

  onUpdatePageLayouts = async () => {
    const baseDesignStore = this.design.store;
    const { getLayoutDatas } = baseDesignStore;
    const layoutDatas = getLayoutDatas();
    await this.formartLayoutDatas(layoutDatas, { isSetTheme: true });
    (this.layoutDatas || []).forEach((layout) => {
      if (layout.terminalType) {
        const layoutStore = (baseDesignStore as any).layoutStores[layout.terminalType];
        if (layoutStore) {
          layoutStore.updatePageConfig({ style: layout.config.style });
          layoutStore.updateComs(layout.comps || []);
        }
      }
    });
  };

  setPageTheme = (themeId: string, themeVersion?: string | Function, callBack?: Function) => {
    const reqParams = {
      pageId: this.pageId || '',
      themeId,
      terminalType: 'PC',
    };
    const client = this.design.store.clientType;

    ebdcoms.asyncExcu('ajax', {
      url: `${this.urlPrefix}/layout/refTheme`,
      method: 'POST',
      params: reqParams,
      ebBusinessId: reqParams?.pageId,
      success: (res: any) => {
        this.setTheme = true;
        this.themeStyle.themeId = themeId;
        // 兼容其他调用数据，待都排查完毕后可删除
        if (tsGurdFunction(themeVersion)) {
          callBack = themeVersion;
          themeVersion = '';
        }
        this.updatedesignThemeInfo(res.themeId || '', res.themeVersion || '');
        this.updateCssCacheInfo();
        if (themeId) {
          this.setThemeStyle(res, callBack);
          this.loadThemeStyle();

          return getLabel('106997', '主题设置成功');
        }

        if (!this.sysSpecialThemeId) {
          this.setThemeStyle(res, callBack);
          this.loadThemeStyle();

          return getLabel('109833', '已取消主题设置');
        }

        // 去掉页面主题时，如果系统租户有配置特色主题，则使用特色主题内关联的页面主题样式
        if (this.specialThemeStyle?.themeId && client === 'PC') {
          this.setThemeStyle(this.specialThemeStyle, callBack);
        } else {
          this.isSwitchMobile = true;
          this.initTheme(this.pageId || '', client, callBack);
        }

        this.loadThemeStyle();
        return getLabel('109833', '已取消主题设置');
      },
      error: () => {
        callBack?.();
      },
    });
  };

  setSpecialThemeStyle = (specialThemeStyle: ThemeStyleProps) => {
    this.specialThemeStyle = specialThemeStyle;
  };

  setSwitchMobile = (val: boolean) => {
    this.isSwitchMobile = val;
  };
}

export default ThemeStylePlugin;
