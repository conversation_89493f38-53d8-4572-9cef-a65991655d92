type PageStyleAttrs = {
  [key: string]: string;
};

type PageStyle = {
  background: PageStyleAttrs;
  width?: number;
  height?: number;
  padding: PageStyleAttrs;
  margin: PageStyleAttrs;
};

export interface ThemeStyleProps {
  themeCss: string;
  appThemeCss?: string;
  refStyles: any[];
  styleConfig: PageStyle | null;
  // 主题id
  themeId?: string;
  // 主题版本
  themeVersion?: string;
}

export default {};
