import { Cors<PERSON>omponent, Menu, MenuItemData } from '@weapp/ui';
import {
  classnames, getLabel, isEmpty, isEqual, omit,
} from '@weapp/utils';
import { inject, observer } from 'mobx-react';
import React from 'react';
import { DesignerContext } from '..';
import { ComMasterTip } from '../../common';
import {
  EVENT_DELETE_COM,
  EVENT_HIGHLIGHT_UPDATE,
  EVENT_RIGHTSIDE_VISIBLE,
  EVENT_SELECT_COM,
  EVENT_SELECT_PAGE,
  prefixCls,
} from '../../constants';
import getDesignInfo from '../../core/utils/getDesignInfo';
import ebdcoms from '../../utils/ebdcoms';
import setTimeoutOnce from '../../utils/setTimeoutOnce';
import AsidePanel from '../common/aside-panel';
import { actions } from '../decorator';
import { ingoreProps } from '../left-side/Content';
import CompBreadCrumb from './CompBreadCrumb';
import Content from './Content';
import './index.less';
import Layout from './Layout';
import { RightMenuData, RightSideProps } from './types';
import { getMenu } from './utils';

const { MenuContent } = Menu;

interface RightSideStates {
  selectedMenu?: RightMenuData;
  selectedSubMenu?: MenuItemData;
  menus: RightMenuData[];
  invisible: boolean;
}

@actions.autobind
@inject('designStore')
@observer
export default class RightSide extends React.PureComponent<RightSideProps, RightSideStates> {
  static contextType = DesignerContext;

  constructor(props: RightSideProps) {
    super(props);

    const { menus = [], visible = true } = props;

    this.state = {
      selectedMenu: menus[0],
      selectedSubMenu: menus[0]?.subMenus?.[0],
      menus,
      invisible: !visible,
    };
  }

  componentDidMount() {
    const store = this.props.designStore;
    // layoutStore实例化为异步，无法确定是在组件渲染前还是渲染后，这里判断layout对象是否已经示例化完成，如果完成则直接获取右侧配置数据
    if (store?.layoutStore?.selectedCom) {
      this.getComMenu(store?.layoutStore?.selectedCom);
    }
    store?.on(EVENT_SELECT_PAGE, () => {
      this.getPageMenu();
    });

    store?.on(EVENT_SELECT_COM, this.getComMenu);

    store?.on(EVENT_DELETE_COM, (comId: string) => {
      const selectedCom = store?.layoutStore?.selectedCom;
      if (!selectedCom || selectedCom.id === comId) {
        this.setState({
          menus: [],
          selectedMenu: undefined,
          selectedSubMenu: undefined,
        });
      }
    });
  }

  getPageMenu = () => {
    const { getMenuDatas, designStore } = this.props;
    if (getMenuDatas) {
      const menus = getMenuDatas('PAGE', designStore!);
      this.setState({
        menus,
        selectedMenu: menus[0],
        selectedSubMenu: menus[0]?.subMenus?.[0],
      });
    }
  };

  getComMenu = async (selectedCom: any) => {
    const { getMenuDatas, designStore, reSelectDefaultMenu } = this.props;
    if (getMenuDatas && designStore) {
      let menus = getMenuDatas('COMPONENT', designStore, selectedCom);
      const comDesign = await getDesignInfo(selectedCom, designStore.clientType);

      // menuConfig支持组件自定义屏蔽右侧菜单等配置（属性、样式、事件....）
      const { menuConfig = {} } = comDesign?.defaultOpts || {};
      if (!isEmpty(menuConfig)) {
        menus = menus.filter((menu) => menuConfig[menu.id || ''] !== false);
      }

      const { selectedMenu } = this.state;
      const hasSelectedMenu = menus?.find((menu) => menu?.id === selectedMenu?.id);
      this.setState({
        menus,
        ...(hasSelectedMenu && !reSelectDefaultMenu
          ? {}
          : {
            selectedMenu: menus[0],
            selectedSubMenu: menus[0]?.subMenus?.[0],
          }),
      });
    }
  };

  static getDerivedStateFromProps(nextProps: RightSideProps, prevStates: RightSideStates) {
    const { menus = [], selectedMenu = [] } = nextProps;

    // 去除 panel、subMenu 进行equal比较，panel为ReactNode时，会报错
    // 优化减小数组遍历的次数
    const preMenus = prevStates.menus;
    let changed = menus.length !== preMenus?.length;
    const _menuData = menus?.map((data, index) => {
      const _data = omit(data, ingoreProps);
      const _preData = omit(preMenus?.[index], ingoreProps);
      if (!isEqual(_data, _preData)) {
        changed = true;
      }
      return _data;
    });

    if (_menuData?.length && changed) {
      const hasSelectedMenu = _menuData?.find((menu) => menu?.id === prevStates?.selectedMenu?.id);
      return {
        menus,
        ...(hasSelectedMenu
          ? {}
          : {
            selectedMenu: selectedMenu[0] || menus[0],
          }),
      };
    }

    return null;
  }

  onMenuChange = (key: string, item: object) => {
    const { onMenuChange } = this.props;
    this.setState({
      selectedMenu: item as MenuItemData,
      selectedSubMenu: (item as RightMenuData).subMenus?.[0],
    });
    if (onMenuChange) {
      onMenuChange(item);
    }
  };

  onSubMenuChange = (key: string, item: object) => {
    this.setState({ selectedSubMenu: item as MenuItemData });
  };

  onVisibleChange = (val: boolean) => {
    this.setState({ invisible: !val }, () => {
      const { events, layoutStore } = this.context;
      const { designStore } = this.props;

      if (layoutStore?.selectedCom) {
        this.getComMenu(layoutStore?.selectedCom);
      }
      // 等待折叠动画执行完成后，更新选中 dom 的位置
      setTimeoutOnce(() => {
        // 侧边栏显示和隐藏事件
        events?.emit(EVENT_RIGHTSIDE_VISIBLE, val);

        // 右侧边栏折叠后，需要触发背景图的resize事件，重新计算背景视频的宽高及定位
        designStore?.pageEvent?.emit?.('background.resize');

        events?.emit(EVENT_HIGHLIGHT_UPDATE, layoutStore?.selectedComDom, 'selected');
        // 点击左右两边的收缩框 防止空白
        window.workbookInstance?.refresh?.();
      }, 300);
    });
  };

  @actions.method('rs')
  selectMenu(id: string) {
    const { menus = [] } = this.props;
    const menu = menus.find((m) => m.id === id);

    this.setState({
      selectedMenu: menu,
      selectedSubMenu: menu?.subMenus?.[0],
    });
  }

  @actions.method('rs')
  selectMenuByPanelType(type: string) {
    const { menus = [] } = this.props;
    const menu = getMenu(type, menus);

    if (menu?.id) {
      this.selectMenu(menu.id);
    }
  }

  // 通过 panel 类型获取面板内容
  @actions.method('rs')
  getPanelContentByType(type: string) {
    const { menus = [] } = this.props;
    const menu = getMenu(type, menus);

    return <AsidePanel weId={`${this.props.weId || ''}_90wy3g`} menu={menu} />;
  }

  renderContent = () => {
    const { selectedMenu, selectedSubMenu, menus: stateMenus } = this.state;
    const { menus: propMenus, designStore, onBeforeInitConfig } = this.props;
    const { showMasterMode, layoutStore } = designStore || {};
    const menus = propMenus || stateMenus || [];
    const isTabId = selectedMenu?.subMenus && selectedSubMenu?.id;
    const menuId = isTabId ? selectedSubMenu?.id : selectedMenu?.id;
    const _menus = isTabId ? selectedMenu?.subMenus : menus;
    const menu = _menus?.find((_menu) => _menu.id === menuId);

    const bindKey = `${prefixCls}_bind_key_${selectedMenu?.id}`;

    // 母版组件在不编辑状态下不显示属性、样式
    if (layoutStore?.selectedCom?.master && showMasterMode) {
      return <ComMasterTip weId={`${this.props.weId || ''}_mmtxst`} />;
    }

    if (!menus?.length) {
      return (
        <CorsComponent
          weId={`${this.props.weId || ''}_sd9heg`}
          app="@weapp/ebdcoms"
          compName="Empty"
          description={getLabel('116397', '请在左侧区域选择组件')}
          type="noData"
        />
      );
    }

    if (layoutStore?.selectedCom) {
      const { verified, errorMsgContent } = ebdcoms.excu(
        'verifyCompModule',
        layoutStore?.selectedCom as any,
      );

      if (!verified) {
        return (
          <>
            <CompBreadCrumb weId={`${this.props.weId || ''}_ev5ovr`} {...this.props} />
            {errorMsgContent}
          </>
        );
      }
    }

    return (
      <>
        <CompBreadCrumb weId={`${this.props.weId || ''}_ev5ovr`} {...this.props} />
        <Menu
          weId={`${this.props.weId || ''}_yy3mtf`}
          value={selectedMenu?.id}
          data={menus}
          className={`${prefixCls}-rs-menu`}
          onChange={this.onMenuChange}
          bindKey={bindKey}
          overflowType="scroll"
        />
        <MenuContent
          weId={`${this.props.weId || ''}_rllnlc`}
          bindKey={bindKey}
          dataId={selectedMenu?.id}
          value={selectedMenu?.id}
        >
          <Content
            weId={`${this.props.weId || ''}_mma5jv`}
            menus={selectedMenu?.subMenus}
            type={selectedMenu?.menuType}
            menu={menu}
            onMenuChange={this.onSubMenuChange}
          >
            <AsidePanel
              weId={`${this.props.weId || ''}_2rx10t`}
              onBeforeInitConfig={onBeforeInitConfig}
              menu={menu}
            />
          </Content>
        </MenuContent>
      </>
    );
  };

  render() {
    const { invisible } = this.state;
    const { style, className = '', customRenderContent } = this.props;
    const classStr = classnames(`${prefixCls}-rs ${className}`.trim(), { hidden: invisible });
    const renderContent = customRenderContent || this.renderContent;

    return (
      <Layout
        weId={`${this.props.weId || ''}_awv2tj`}
        classStr={classStr}
        style={style}
        invisible={invisible}
        onVisibleChange={this.onVisibleChange}
      >
        {renderContent()}
      </Layout>
    );
  }
}
