import { Layout as UILayout } from '@weapp/ui';
import React from 'react';
import { LayoutProps } from './types';

export default class Layout extends React.PureComponent<LayoutProps> {
  render() {
    const {
      classStr, style, invisible, onVisibleChange, children,
    } = this.props;
    return (
      <aside className={classStr} style={style}>
        <UILayout.Box
          weId={`${this.props.weId || ''}_pueww7`}
          type="side"
          right
          allowHidden
          hidden={invisible}
          onVisibleChange={onVisibleChange}
        >
          {children}
        </UILayout.Box>
      </aside>
    );
  }
}
