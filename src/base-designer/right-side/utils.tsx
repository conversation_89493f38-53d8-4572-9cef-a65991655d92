import { utils } from '@weapp/ui';
import React from 'react';
import reactTypeof from '../../core/utils/reactTypeof';
import { RightMenuData } from './types';

/**
 *
 * @param panelType
 * @param menus
 * @returns 根据panel的type 获取用户传入的menus 的id （只能判断panel 为string｜{ type: '', props: '' } 的情况）
 */
export const getMenu = (panelType: string, menus: RightMenuData[]) => {
  const is = (menu: RightMenuData) => {
    const { panel: _panel } = menu;

    if (typeof _panel === 'string') {
      return _panel === panelType;
    }

    // 传值为 ReactNode 不能判断，根据传入的id进行判断
    if (reactTypeof.isReactComponent(_panel) || React.isValidElement(_panel)) {
      return menu.id === panelType;
    }

    if (utils.isObject(_panel)) {
      return (_panel as any).type === panelType;
    }

    return false;
  };

  return menus.find((_menu) => {
    const { subMenus } = _menu;

    if (!is(_menu)) {
      return subMenus?.find(is);
    }

    return true;
  });
};

export default {};
