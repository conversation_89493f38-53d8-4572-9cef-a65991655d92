import { AnyObj, MenuItemData } from '@weapp/ui';
import { ShowType } from '@weapp/ui/lib/components/menu/types';
import React, { CSSProperties, ReactNode } from 'react';
import { FormInitAllDatasEx } from '../../common/form/types';
import { AttributePanelProps } from '../panels/attribute/Panel';
import { EditorProps, EditorType } from '../panels/editor/types';
import { StylePanelProps } from '../panels/style/types';
import BaseDesignerStore from '../store';

export type RightPanelType = 'attr' | 'containerStyle' | 'comStyle' | 'editor';

type PropsMap = {
  attr: AttributePanelProps;
  containerStyle: StylePanelProps<'container'>;
  comStyle: StylePanelProps<'component'>;
  editor: EditorProps<EditorType>;
};

export type RightPanel<T extends RightPanelType> = { type: T; props?: PropsMap[T] };

export type PanelData =
  | RightPanel<RightPanelType>
  | React.ComponentType
  | RightPanelType
  | ReactNode;

export type MenuData = MenuItemData & {
  panel?: PanelData;
  menuType?: ShowType;
};

export type RightMenuData = MenuData & {
  subMenus?: MenuData[];
};

export interface RightSideProps {
  style?: CSSProperties;
  /** 显示隐藏 */
  visible?: boolean;
  className?: string;
  /** 顶部菜单(支持子菜单) */
  menus?: RightMenuData[];
  designStore?: BaseDesignerStore;
  getMenuDatas?: (type: string, store: BaseDesignerStore, selectedCom?: any) => RightMenuData[];
  /** 组件配置初始化前的回调 */
  onBeforeInitConfig?: (config: FormInitAllDatasEx, com: any) => FormInitAllDatasEx;
  /** 组件切换时 是否重新选中第一个菜单 */
  reSelectDefaultMenu?: boolean;
  /** 自定义渲染右侧面板内容 */
  customRenderContent?: () => ReactNode;
  /** 自定义切换菜单动作 */
  onMenuChange?: (item: object) => void;
  /** 顶部选中菜单 */
  selectedMenu?: RightMenuData[];
}

export interface CompBreadCrumbProps {
  designStore?: BaseDesignerStore;
}

export interface CrumbData {
  id: string;
  name: string;
  com: AnyObj;
}

export interface CrumbItemProps {
  data: CrumbData;
  selectCom?: Function;
}

export interface LayoutProps extends React.Attributes {
  classStr: string;
  style?: React.CSSProperties;
  invisible: boolean;
  onVisibleChange: (val: boolean) => void;
}

export interface ComInfo {
  name: string;
  helpInfo?: AnyObj;
}
export interface ComInfos {
  [props: string]: ComInfo;
}
export interface CompBreadCrumbState {
  comInfos: ComInfos;
}
