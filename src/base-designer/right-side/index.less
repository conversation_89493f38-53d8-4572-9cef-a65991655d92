@import (reference) '../../style/prefix.less';
@import (reference) '../../style/var.less';

@topmenuHeight: 36px;

.@{prefix}-rs {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: @rightside-w;
  min-width: @rightside-w;
  border-left: 1px solid var(--de-box-lineColor);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

  & .ui-layout-box-wrap .ui-layout-box {
    transform: translate(0, 0);
  }

  &.hidden {
    width: 0px;
    min-width: 0px;
  }

  .ui-layout-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    min-width: calc(@rightside-w - 1px);
  }

  &-content {
    flex: 1 1 auto;
    padding: 5px 15px 0 15px;
    overflow-y: auto;

    &.noPadding {
      padding-left: 0;
      padding-right: 0;
      padding-top: 0;
    }

    & > .ui-spin-nested-loading,
    .ui-spin-container {
      height: 100%;
    }

    .ui-spin-spinning {
      display: flex;
      //为了和左边对齐
      margin-top: 20px;
      height: 100%;
      align-items: center;
      justify-content: center;
    }

    > .@{prefix}-com-config {
      height: 100%;
    }

    .ui-empty {
      transform: translateY(-10%);
    }

    &-menu {
      z-index: @rs-menu-zIndex;
    }

    &-menu.ui-menu-tab {
      border-bottom: none;
      margin: 8px 0;

      .ui-menu-nav-scroll {
        width: 100%;

        & > .ui-menu-nav {
          width: 100%;
        }
      }

      .ui-menu-list {
        width: 100%;
        display: flex;
        border-radius: 2px !important;
        border-left: 1px solid #e5e5e5;
        border-right: 1px solid #e5e5e5;

        &-item:first-child {
          margin: 0;
          border-top-left-radius: 2px;
          border-bottom-left-radius: 2px;
        }

        &-item:last-child {
          border-right: none;
          border-top-right-radius: 2px;
          border-bottom-right-radius: 2px;
        }
      }

      .ui-menu-list-item {
        width: 68px;
        height: 30px;
        line-height: 30px;
        padding: 0 7px;
        background: #fff;
        color: #666;
        border-bottom: 1px solid #e5e5e5;
        border-top: 1px solid #e5e5e5;
        border-right: 1px solid #e5e5e5;
        display: flex;
        justify-content: center;
        flex-grow: 1;
      }

      .ui-menu-list-item:hover {
        background-color: #5d9cecd1;
        color: #fff;
      }

      .ui-menu-list-item-active {
        background-color: var(--border-color-active);
        color: var(--base-white) !important;

        .ui-icon {
          color: var(--base-white) !important;
        }
      }
    }
  }

  &-menu {
    padding: 0 15px;
    height: @topmenuHeight;
    max-height: @topmenuHeight;
    min-height: @topmenuHeight;
    text-transform: capitalize;
  }

  &-top {
    display: flex;
    border-bottom: var(--border-width) solid var(--border-color);

    &-breadcrumb {
      flex: 1;
      padding: 10px;
      min-height: @topmenuHeight;

      .ui-bread-crumb-item {
        user-select: none;
        color: var(--regular-fc);
        font-size: var(--font-size-12);

        &:last-child {
          font-weight: bold;
          line-height: 16px;
        }

        &:hover {
          text-decoration: none;
          color: var(--regular-fc);
          font-weight: bold;
        }
      }
    }

    &-help {
      margin: 0 8px;
      display: flex;
      align-items: center;
    }
  }

  .@{prefix}-rs-content {
    .ui-collapse-panel--inactive {
      border-bottom: none;
    }
  }
}

.@{prefix}-attr-panel {
  margin-top: 5px;
}
