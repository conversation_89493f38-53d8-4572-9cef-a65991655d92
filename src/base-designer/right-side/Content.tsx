import { Menu, MenuItemData } from '@weapp/ui';
import { ShowType } from '@weapp/ui/lib/components/menu/types';
import { classnames } from '@weapp/utils';
import { PureComponent } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../constants';
import { AsideMenuData } from '../common/aside-panel/types';

interface ContentProps {
  menus?: MenuItemData[];
  type?: ShowType;
  menu?: AsideMenuData | null;
  onMenuChange?: (key: string, item: object) => void;
}

export default class Content extends PureComponent<ContentProps> {
  render() {
    const {
      menus = [], menu, children, type, onMenuChange,
    } = this.props;
    const isMatrixRight = (menu as any)?.panel?.type === 'rights' && (menu as any)?.panel?.props?.enableMulti;

    return (
      <div
        className={classnames([`${prefixCls}-rs-content`], {
          noPadding:
            menu?.id === 'comStyle' || (menu as any)?.panel?.type === 'comStyle' || isMatrixRight,
        })}
      >
        <When weId={`${this.props.weId || ''}_7jn5rn`} condition={menus?.length > 0}>
          <Menu
            weId={`${this.props.weId || ''}_p2gw35`}
            data={menus}
            type={type}
            onChange={onMenuChange}
            className={`${prefixCls}-rs-content-menu`}
            defaultValue={menus[0]?.id}
          />
        </When>
        {children}
      </div>
    );
  }
}
