import { AnyObj, BreadCrumb, Help } from '@weapp/ui';
import { handleSiteDomain, isEmpty, isObject } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { useCallback } from 'react';
import { prefixCls } from '../../constants';
import getDefaultOpts from '../../core/loader/com/getDefaultOpts';
import getEbPlugin from '../../core/loader/com/getEbPlugin';
import { ComLoaderInfo } from '../../core/loader/com/types';
import { ClientType } from '../../types';
import ebdcoms from '../../utils/ebdcoms';
import {
  ComInfos,
  CompBreadCrumbProps,
  CompBreadCrumbState,
  CrumbData,
  CrumbItemProps,
} from './types';

const CrumbItem = observer((props: CrumbItemProps) => {
  const { data, selectCom } = props;

  const onCrumbClick = useCallback(() => {
    selectCom?.(data?.id);
  }, [data, selectCom]);

  return (
    <span onClick={onCrumbClick} key={data?.id}>
      {data?.name}
    </span>
  );
});

const formatCrumbData = (data: CrumbData[], clientType: ClientType) => {
  data.forEach((da, index: number) => {
    const curCom = da?.com;
    if (curCom && clientType) {
      const defaultOpts = getDefaultOpts(curCom as ComLoaderInfo, clientType);
      const isCustom = !!defaultOpts?.customBreadCrumbData;

      // 组件可通过defaultOpts自定义层级数据
      if (isCustom) {
        data = defaultOpts?.customBreadCrumbData?.(data, index);
      }
    }
  });

  return data.slice(-3);
};

@observer
export default class CompBreadCrumb extends React.PureComponent<
  CompBreadCrumbProps,
  CompBreadCrumbState
> {
  state = {
    comInfos: {} as ComInfos,
  };

  componentDidMount() {
    const { designStore } = this.props;
    const { moduleScope = 'EB_PAGE' } = designStore || {};
    // 目前仅网格布局存在独立部署场景
    const { isFrontDeskEntry = false } = (designStore || {}) as any;
    // 独立部署相关场景中 module 要处理下
    const pageModule = isFrontDeskEntry && moduleScope ? moduleScope : 'EB_PAGE';

    ebdcoms
      .asyncExcu('ajax', {
        url: '/api/ebuilder/coms/component/helper/list',
        method: 'get',
        params: { module: pageModule },
      })
      .then((res) => {
        const { comInfos } = this.state;
        this.setState({ comInfos: { ...comInfos, ...res } });
      });
  }

  componentDidUpdate() {
    // 如果有插件，需要获取插件中的helpInfo
    this.getEbPlugin();
  }

  getSimpleCom = (selectedCom: any) => {
    const {
      config: { _plugins = [] },
      type,
      refPluginPackageId,
    } = selectedCom;
    const plugin = _plugins?.[_plugins?.length - 1];

    if (!plugin) {
      return {
        comType: type,
      };
    }

    const comType = `${plugin.resourceType.toUpperCase()}_${type}`;
    return {
      plugin,
      comType,
      refPluginPackageId,
      type,
    };
  };

  getEbPlugin = async () => {
    const { selectedCom } = this.props.designStore?.layoutStore as AnyObj;
    if (!selectedCom) return;

    const { package: packageName } = selectedCom;
    const { plugin, comType } = this.getSimpleCom(selectedCom);
    const { comInfos } = this.state;
    if (!plugin || !packageName || comInfos[comType]) return;

    const ComPlugin = await getEbPlugin(packageName, plugin.resourceType, 'Design');
    if (!ComPlugin) return;

    const { defaultOpts = {} } = ComPlugin;
    const { helpInfo } = defaultOpts;
    if (!helpInfo) return;

    this.setState({
      comInfos: {
        ...comInfos,
        [comType]: { helpInfo },
      },
    });
  };

  getComInfo = () => {
    const { selectedCom } = this.props.designStore?.layoutStore as AnyObj;
    if (selectedCom) {
      const { comType, refPluginPackageId, type } = this.getSimpleCom(selectedCom);
      if (type && refPluginPackageId) {
        if (this.state.comInfos && this.state.comInfos?.[type]) {
          const { pluginPackage }: any = this.state.comInfos?.[type];
          return pluginPackage && pluginPackage[refPluginPackageId];
        }
      }
      return this.state.comInfos?.[comType];
    }
  };

  getBreadCrumbData = () => {
    const { selectComOrPage, getParents, clientType } = this.props.designStore
      ?.layoutStore as AnyObj;
    const data = getParents?.() || [];
    const sourceData = formatCrumbData(data?.reverse(), clientType);

    return sourceData?.map((da: CrumbData, index: number) => (
      <CrumbItem
        weId={`${this.props.weId || ''}_8wfzt9@${index}`}
        key={da?.id}
        data={da}
        selectCom={selectComOrPage}
      />
    ));
  };

  getComRest = () => {
    const comInfo = this.getComInfo();
    if (!comInfo) return [];
    const { name, helpinfo = {} } = comInfo;
    const { refLink = '', content = '' } = helpinfo;
    if (isObject(content)) {
      return [name, refLink, this.renderDesc(content)];
    }
    return [name, refLink, content];
  };

  renderDesc = (desc: AnyObj) => (
    <div>
      {Object.keys(desc)?.map((k: string) => (
        <div key={k}>{`${k}. ${desc?.[k]}`}</div>
      ))}
    </div>
  );

  render() {
    const { designStore } = this.props;
    const breadCrumbData = this.getBreadCrumbData();
    const [, comHelpLink, comDesc] = this.getComRest();

    const isUserCustom = (designStore as any)?.userCustom;

    if (!breadCrumbData || isEmpty(breadCrumbData) || isUserCustom) return null;

    return (
      <div className={`${prefixCls}-rs-top`}>
        <BreadCrumb
          className={`${prefixCls}-rs-top-breadcrumb`}
          weId={`${this.props.weId || ''}_xz3ksk`}
          separator="/"
          data={breadCrumbData}
        />
        <Help
          weId={`${this.props.weId || ''}_oey0w1`}
          title={comDesc}
          className={`${prefixCls}-rs-top-help`}
          helpUrl={
            comHelpLink || `${handleSiteDomain('https://eteams.cn')}/help/2510887996489221110`
          }
          placement="bottomRight"
          width={150}
        />
      </div>
    );
  }
}
