@import (reference) '../style/prefix.less';
@import (reference) './left-side/index.less';

.@{prefix} {
  display: flex;
  height: 100%;
  flex-direction: column;
  background: #f7f7f7;
  background: var(--de-body-bgColor);
}

.@{prefix}-empty {
  display: flex;
  height: 100%;
  flex-wrap: wrap;
  align-content: center;
}

.@{prefix}-main {
  position: relative;
  display: flex;
  height: 100%;
  flex: 1 1 0;
  flex-direction: row;
  overflow: hidden;

  &>main,
  &>aside {
    min-height: 100%;
    background-color: var(--de-box-bgColor);
  }

  &>main {
    flex: 1 1 auto;
    flex-basis: 0;
    overflow: auto;
    margin-left: @content-w;
    background-color: var(--bg-base);
  }

  &-loading {
    flex: 1 1 auto;
    background-color: var(--de-body-bgColor);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &.@{prefix}-main-narrower {
    &>main {
      margin-left: @content-wider;
    }
  }
}

.@{prefix}-workspace.m {
  &>div:last-child {
    width: 375px;
    position: relative;
    height: 677px;
    margin: 30px auto 10px;
    box-shadow: 0 0 10px 0 rgb(0 0 0 / 10%);
    background: var(--bg-base);
    overflow-y: auto;
    min-height: auto !important;
  }
}

body[page-rtl='true'] {

  // 设计器区域不镜像处理，组件内容做镜像处理
  .ebpage {
    transform: scaleX(-1) !important;

    &>.ebcoms-empty-noData {
      .ui-empty-title {
        transform: none;
      }
    }
  }

  .ebcom-design:not(.demo-com) {
    transform: scaleX(-1) !important;
  }

  .@{ebPrefix} {
    overflow-x: hidden;
  }

  // 拖拽按钮不需要镜像
  .@{ebPrefix}-gl-pc-card-zoom {
    transform: none;
  }

  .@{ebPrefix}-gl-pc-tab-card-zoom {
    transform: none;
  }

  .@{ebPrefix}-toolbar {
    transform: scaleX(-1);
  }

  .@{ebPrefix}-gl-flow-tools {
    transform: scaleX(-1);
  }

  .@{comPrefix}.rgl-dnd-flow-layout {
    transform: scaleX(-1);
  }

  .@{ebPrefix}-droppable-empty.ui-rtl {
    transform: none;
  }

  .@{prefix}-flow-tools {
    transform: scaleX(-1);

    .@{prefix}-toolbar-layout {
      transform: scaleX(-1);
    }
  }
}