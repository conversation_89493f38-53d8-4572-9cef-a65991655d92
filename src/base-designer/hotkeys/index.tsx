import hotkeys from 'hotkeys-js';
import { useEffect } from 'react';
import { INTERNAL_CONTAINER } from '../../constants';
import setTimeoutOnce from '../../utils/setTimeoutOnce';
import { useDesigner } from '../hooks';

const clipboardKeys = ['ctrl+c', 'ctrl+v'];

export default function Hotkeys() {
  const designStore = useDesigner();

  useEffect(() => {
    const {
      events, redo, undo, actions,
    } = designStore;

    const listeners = {
      /** 删除 */
      delete: () => {
        const { selectedCom } = designStore.layoutStore;
        const { canDelete } = selectedCom?.config || {};
        const { canDel } = selectedCom?.config.layout || {};

        // eslint-disable-next-line max-len
        if (selectedCom && !(canDel === false) && !(canDelete === false) && selectedCom.type !== INTERNAL_CONTAINER) {
          designStore.layoutStore.deleteCom(selectedCom.id);
        }
      },
      /** 撤销 */
      'ctrl+z': () => {
        // TODO: 后期需要优化，与RedoUndo用同一个
        undo();
        // 异步触发unredo事件，方便获取到最新数据
        // setNodes 有debounce 20ms 延迟
        setTimeoutOnce(() => {
          events.emit('unredo', 'undo');
        }, 25);
      },
      /** 重做 */
      'ctrl+y': () => {
        // TODO: 后期需要优化，与RedoUndo用同一个
        redo();
        // 异步触发unredo事件，方便获取到最新数据
        // setNodes 有debounce 20ms 延迟
        setTimeoutOnce(() => {
          events.emit('unredo', 'redo');
        }, 25);
      },
      /** 保存 */
      'ctrl+s': async () => {
        try {
          await actions.save();
        } catch (e) {
          console.error(e);
        }
      },
      /** 复制 */
      'ctrl+c': () => {
        designStore.layoutStore.copyByHotKey?.();
      },
      // /** 粘贴 */
      'ctrl+v': () => {
        designStore.layoutStore.pastByHotKey?.();
      },
    };
    const eventKeys = Object.keys(listeners);

    hotkeys(eventKeys.join(','), (e, handler) => {
      const hotkey = handler.key as keyof typeof listeners;
      let isDefaultAction = false;
      if (clipboardKeys.includes(hotkey) && window?.getSelection()?.type === 'Range') {
        isDefaultAction = true;
      }
      if (isDefaultAction) {
        return;
      }
      const method = listeners[hotkey];

      if (!clipboardKeys.includes(hotkey)) {
        e.preventDefault();
        e.stopPropagation();
      }

      events.emit(hotkey);

      if (method) {
        method();
      }
    });
  }, []);

  return null;
}
