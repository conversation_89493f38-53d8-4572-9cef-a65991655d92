import React, { ComponentType } from 'react';
import Loadable from '../common/loadable';
import { DesignerProvider } from './Context';
import { BaseDesignerProps } from './types';

export const BaseDesigner = Loadable({
  name: 'BaseDesigner',
  loader: () => import(
    /* webpackChunkName: "de_base_designer" */
    './Designer'
  ),
}) as ComponentType<BaseDesignerProps<any>>;

BaseDesigner.displayName = 'BaseDesigner';

export { DesignerContext } from './Context';
export * from './core';
export * from './decorator';
export * from './dialogs';
export * from './header/action-button';
export * from './hooks';
export * from './panels';
export { default as Preview } from './preview';
export * from './resolver';
export * from './types';
export { DesignerProvider };

export const MiniConfig = React.lazy(
  () => import(
    /* webpackChunkName: "de_base_designer_miniconfig" */
    './toolbar/MiniConfig'
  ),
) as ComponentType<any>;

export default BaseDesigner;
