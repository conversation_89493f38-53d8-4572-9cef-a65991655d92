import React, { createContext, ReactNode } from 'react';
import { Page } from '../core';
import { ILayoutData } from '../core/types';
import { MainProps } from './main/types';
import {
  Localization,
  RenderConfig,
  RenderDesign,
  RenderDragComponent,
  ResolverType,
} from './types';

export type InternalDesignerContextType = {
  resolver?: ResolverType;
  panels?: Record<string, React.ComponentType<any>>;
  page?: Page;
  isLayoutChanged?: boolean;
  localization?: Localization;
  splitLongTask?: boolean;
  pageStyleClassName?: string;
  channel?: BroadcastChannel;
  renderDragComponent?: RenderDragComponent;
  renderDesign?: RenderDesign;
  renderContent?: (content: ReactNode) => ReactNode;
  renderConfig?: RenderConfig;
  onLayoutChange?: (layoutDatas: ILayoutData[]) => void;
};

export const DesignerContext = createContext<any>(null);

export const DesignerProvider = DesignerContext.Provider;

export const InternalDesignerContext = createContext<InternalDesignerContextType | null>(null);

export const InternalDesignerProvider = InternalDesignerContext.Provider;

export const DesignerMainContext = createContext<MainProps | undefined>(undefined);

export const DesignerMainProvider = DesignerMainContext.Provider;

export default {};
