@import (reference) '../../style/prefix.less';
@import (reference) '../../style/var.less';

@menu-w: 40px;
@content-w: 200px;
@header-h: 35px;
@content-wider: 270px;

.@{prefix}-ls {
  min-width: @menu-w;
  display: flex;
  flex-direction: row;
}

.@{prefix}-ls-menu {
  position: relative;
  width: @menu-w;
  z-index: @ls-menu-zIndex;
  background-color: var(--de-box-bgColor);

  .Icon-custom10-o {
    transform: rotate(332deg);
  }

  .ui-menu-bar {
    right: 0;
  }

  .ui-menu-nav-scroll .ui-menu-list {
    width: 100%;
    border-right: var(--border-solid);
  }

  .ui-menu-list-item {
    border: none;
    height: @menu-w;
  }

  .ui-menu-list-item-active {
    background-color: var(--de-active-color);
  }

  .ui-menu-list-item-lefticon {
    margin-right: 0;
    width: @menu-w - 1;
    height: @menu-w;
    justify-content: center;
  }

  .ui-menu-nav {
    width: 100%;
  }

  &-single {
    .ui-menu-list-item-active .ui-menu-list-item-content-wrap {
      border-color: transparent;

      .ui-menu-list-item-content {
        color: var(--main-fc);
      }
    }
  }
}

.@{prefix}-ls-content {
  position: absolute;
  display: flex;
  flex-direction: column;
  top: 0;
  bottom: 0;
  left: @menu-w;
  width: @content-w;
  z-index: @ls-content-zIndex;
  border-right: 1px solid var(--de-box-lineColor);
  background-color: var(--de-box-bgColor);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

  .ui-menu {
    padding: 0 10px;
  }

  .ui-menu-extra {
    margin-right: 0;
    margin-left: 0;
    display: flex;
    height: 100%;

    & > span {
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      width: 20px;
    }

    .ding {
      color: var(--primary);
      opacity: 0.75;

      &:hover {
        opacity: 1;
      }
    }

    .ding > svg {
      transform: rotate(-42deg);
    }

    .float {
      color: var(--secondary-fc);
    }
  }

  .ui-menu-extra > span:not(:last-child) {
    margin-right: 7px;
  }

  .ui-menu-bar {
    width: 100% !important;
  }

  .ui-empty {
    z-index: @empty-zIndex;
  }

  &-loading {
    height: 100%;

    & > div > .ui-spin-spinning {
      display: none;
    }

    & > .ui-spin-blur {
      opacity: 1;
    }

    header {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 101;
      background-color: var(--de-box-bgColor);
    }

    main {
      flex: 1;
      height: 100%;
      overflow: hidden;

      > .panel-hide {
        display: none;
      }

      & > .panel-show > div:first-child {
        position: absolute;
        top: @header-h;
        left: 0;
        width: 100%;
        z-index: 101;
        background-color: var(--de-box-bgColor);
        height: calc(100% - @header-h);

        .@{prefix}-tree-content-wrapper {
          flex: 1;

          .ui-empty {
            transform: translateY(0);
          }
        }
      }
    }
  }

  &.@{prefix}-ls-content-wider {
    width: @content-wider;
  }
}

.@{prefix}-ls-content.@{prefix}-lsc-hidden {
  &.@{prefix}-ls-content {
    left: -@ls-panel-w;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
}

.@{prefix}-ls.@{prefix}-ls-closed {
  & + main {
    margin-left: 0;
  }
}

.@{prefix}-ls.@{prefix}-ls-floated {
  & + main {
    margin-left: 0;
  }
}

.@{prefix}-main {
  & > main {
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
}
