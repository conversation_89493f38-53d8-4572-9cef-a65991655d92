import { IconNames } from '@weapp/ui';
import { ReactNode } from 'react';
import { AsideMenuData } from '../common/aside-panel/types';
import { ExtraAction } from '../main/types';

export type LeftMenuData = {
  id: string;
  /** 图标popover的提示标题，当subMenus没有值时，默认为右侧tab的标题 */
  title: string;
  /** 竖向菜单图标 */
  icon: IconNames | ReactNode;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否隐藏 */
  hide?: boolean;
  /** 渲染面板 */
  panel?: AsideMenuData['panel'];
  /** 右侧子tab菜单, 当subMenus不传时，默认生成同父级菜单id一致的subMenus */
  subMenus?: AsideMenuData[];
  /** Menu右侧 自定义按钮 */
  extraActions?: ExtraAction[];
  /** 角标显示个数 （数据集） */
  contentNumber?: number;
  renderQuickMenu?: (v: ReactNode) => ReactNode;
};

export interface LeftSideProps {
  /** 默认选中的菜单项 */
  defaultMenuId?: string;
  /** 左侧竖向菜单(含右侧子tab菜单) */
  menus?: LeftMenuData[];
  /** 浮动改变后的回调 */
  onFloatedChange?: (floated: boolean) => void;
  /** 左侧菜单或子菜单选中改变后的回调 */
  onMenuChange?: (menu: LeftMenuData | AsideMenuData | null) => void;
  /** 手动修改key值 */
  key?: string;
}
