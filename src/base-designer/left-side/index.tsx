import {
  Icon, IconNames, Menu, Popover,
} from '@weapp/ui';
import { classnames, isEqual, middleware } from '@weapp/utils';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, ReactNode } from 'react';
import { When } from 'react-if';
import {
  EVENT_HIGHLIGHT_UPDATE,
  EVENT_LEFTSIDE_VISIBLE,
  libName,
  prefixCls,
} from '../../constants';
import setTimeoutOnce from '../../utils/setTimeoutOnce';
import { actions } from '../decorator';
import { BaseDesignerStore } from '../export';
import Content from './Content';
import './index.less';
import { LeftMenuData, LeftSideProps } from './types';

const { MenuContent } = Menu;

interface InjectLeftSideProps extends LeftSideProps {
  designStore: BaseDesignerStore;
}

interface MenuIconProps {
  title: string;
  name: IconNames | ReactNode;
}

const MenuIcon: React.FC<MenuIconProps> = ({ title, name }) => (
  <Popover weId="x3dvnl" popoverType="tooltip" placement="right" popup={title}>
    <span className="ui-menu-list-item-lefticon">
      {typeof name === 'string' ? <Icon weId="3mqlx9" name={name as IconNames} /> : name}
    </span>
  </Popover>
);

const getMenuData = (menus = [] as LeftMenuData[]) => menus
  .filter((menu) => !menu.hide)
  .map((menu, index) => ({
    ...menu,
    icon: <MenuIcon weId={`m55jaf@${index}`} title={menu.title} name={menu.icon as IconNames} />,
  }));

interface LeftSideStates {
  menuData: LeftMenuData[];
  floated: boolean;
  selectedMenuId: string;
  loading: boolean;
}

const duration = 300;

@actions.autobind
@middleware(libName, 'BaseLeftSide')
@inject('designStore')
@observer
class LeftSide extends React.PureComponent<InjectLeftSideProps, LeftSideStates> {
  shouldClosed: boolean = false;

  constructor(props: InjectLeftSideProps) {
    super(props);

    const menuData = getMenuData(props.menus);

    this.state = {
      menuData,
      floated: false,
      loading: false,
      selectedMenuId: props.defaultMenuId || menuData[0]?.id,
    };
  }

  componentDidMount = () => {
    document.body.addEventListener('mousedown', this.closeFloatPanel);
    document.body.addEventListener('dragstart', this.closeFloatPanel);
  };

  componentWillUnmount = () => {
    document.body.removeEventListener('mousedown', this.closeFloatPanel);
    document.body.removeEventListener('dragstart', this.closeFloatPanel);
  };

  onMouseDown = () => {
    this.shouldClosed = false;
  };

  /** 关闭单元格编辑面板 */
  @actions.method('ls')
  closeFloatPanel = () => {
    if (!this.state.floated || !this.state.menuData || !this.shouldClosed) {
      this.shouldClosed = true;
      return;
    }
    // 加延时，防止刚拖动面板图标的时候  就触发关闭，导致图标消失
    setTimeoutOnce(() => this.setState({ selectedMenuId: '' }), 0);
  };

  static getDerivedStateFromProps(nextProps: LeftSideProps, prevStates: LeftSideStates) {
    const menuData = getMenuData(nextProps.menus);

    /**
     * 去除 panel、subMenu 进行equal比较，panel为ReactNode时，会报错
     * - 参考组件库内部的使用, 主要比较了 id 和 content
     * - contentNumber: 数据集数量变化后，角标数量需及时刷新
     */

    // 减小数组的遍历的次数
    const preMenuData = prevStates.menuData;
    const changed = menuData?.length !== preMenuData?.length
      || !!menuData.find((data, index) => {
        const preData = preMenuData?.[index];
        return (
          data?.id !== preData?.id
          || data?.title !== preData?.title
          || !isEqual(data?.extraActions, preData?.extraActions)
          || data?.contentNumber !== preData?.contentNumber
          || data?.disabled !== preData?.disabled
          // eslint-disable-next-line max-len
          || !isEqual(
            data?.subMenus?.map((_sub) => _sub.disabled),
            preData?.subMenus?.map((_sub) => _sub.disabled),
          )
        );
      });

    if (changed) {
      // 查找新的菜单数据中，是否有当前已经激活的菜单，如果没有，重置selectedMenuId
      const hasActiveMenu = menuData.some((menu) => menu.id === prevStates.selectedMenuId);
      if (hasActiveMenu) {
        return {
          menuData,
        };
      }
      return {
        menuData,
        selectedMenuId: nextProps.defaultMenuId || menuData[0]?.id,
      };
    }

    return null;
  }

  getMenuDatas() {
    const { menus = [] } = this.props;

    return menus.map((menu, index) => ({
      ...menu,
      icon: <MenuIcon weId={`m55jaf@${index}`} title={menu.title} name={menu.icon as IconNames} />,
    }));
  }

  selectMenu = (value: string, item: object) => {
    const { designStore } = this.props;
    const { selectedMenuId } = this.state;
    let newSelectedMenu = null;
    let newSelectedMenuId = '';

    if (!selectedMenuId || selectedMenuId !== value) {
      newSelectedMenu = item as LeftMenuData;
      newSelectedMenuId = value;
    }

    const loading = !selectedMenuId;

    this.setState({ selectedMenuId: newSelectedMenuId, loading }, () => {
      // 侧边动画结束后
      setTimeoutOnce(() => {
        designStore?.events?.emit(
          EVENT_HIGHLIGHT_UPDATE,
          designStore?.layoutStore?.selectedComDom,
          'selected',
        );
        // 点击左右两边的收缩框 防止空白
        window.workbookInstance?.refresh?.();

        designStore?.events?.emit(EVENT_LEFTSIDE_VISIBLE, !!newSelectedMenuId);

        this.setState({ loading: false });
      }, duration);
    });

    this.props.onMenuChange?.(newSelectedMenu);
  };

  onFloatedChange = (floated: boolean) => {
    this.setState({ floated });
    this.props.onFloatedChange?.(floated);
  };

  getSelectedMenuData = () => {
    const { menuData, selectedMenuId } = this.state;

    return menuData.find((data) => data.id === selectedMenuId);
  };

  render() {
    const { onMenuChange } = this.props;
    const {
      menuData, selectedMenuId, floated, loading,
    } = this.state;
    const selectedMenuData = this.getSelectedMenuData();
    const bindKey = `${prefixCls}_bind_key_${selectedMenuId}`;

    return (
      <aside
        onMouseDown={this.onMouseDown}
        className={classnames(
          `${prefixCls}-ls`,
          { [`${prefixCls}-ls-closed`]: !selectedMenuId },
          { [`${prefixCls}-ls-floated`]: floated },
        )}
      >
        <Menu
          weId={`${this.props.weId || ''}_w9eabd`}
          className={`${prefixCls}-ls-menu`}
          position="left"
          value={selectedMenuId}
          data={menuData}
          bindKey={bindKey}
          onChange={this.selectMenu}
        />
        <div>
          <MenuContent
            weId={`${this.props.weId || ''}_wd3s69`}
            bindKey={bindKey}
            dataId={selectedMenuId}
            value={selectedMenuId}
          >
            <When weId={`${this.props.weId || ''}_j3xib7`} condition={!!selectedMenuId}>
              <Content
                weId={`${this.props.weId || ''}_bqinv4`}
                floated={floated}
                data={selectedMenuData!}
                loading={loading}
                onMenuChange={onMenuChange}
                onFloatedChange={this.onFloatedChange}
                selectedMenuId={selectedMenuId}
              />
            </When>
          </MenuContent>
        </div>
      </aside>
    );
  }
}

export default LeftSide as unknown as ComponentType<LeftSideProps>;
