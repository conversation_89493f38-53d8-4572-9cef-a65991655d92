import {
  AnyObj, CorsComponent, Menu, MenuItemData, Spin, utils,
} from '@weapp/ui';
import {
  classnames, getLabel, getLang, omit,
} from '@weapp/utils';
import React, { SyntheticEvent } from 'react';
import { When } from 'react-if';
import { cNLang, prefixCls } from '../../constants';
import AsidePanel from '../common/aside-panel';
import { AsideMenuData } from '../common/aside-panel/types';
import { actions } from '../decorator';
import { ExtraAction } from '../main/types';
import Actions from '../toolbar/Actions';
import { LeftMenuData } from './types';

const { MenuContent } = Menu;

// menuData equal 对比去除的属性
export const ingoreProps = ['panel', 'subMenus', 'icon'];

const getMenuData = (data: LeftMenuData | null) => {
  if (!data) {
    return [];
  }

  const {
    id, title, subMenus, panel,
  } = data;

  return (
    subMenus || [
      {
        id,
        content: title,
        panel,
      },
    ]
  );
};

interface ContentProps {
  data: LeftMenuData | null;
  floated: boolean;
  loading: boolean;
  onMenuChange?: (menuData: AsideMenuData) => void;
  onFloatedChange: (floated: boolean) => void;
  selectedMenuId?: string;
}

interface ContentStates {
  menuData: AsideMenuData[];
  selectedMenu: AsideMenuData | null;
}
@actions.autobind
export default class Content extends React.PureComponent<ContentProps, ContentStates> {
  constructor(props: ContentProps) {
    super(props);

    const menuData = getMenuData(props.data);

    this.state = {
      menuData,
      selectedMenu: menuData[0],
    };
  }

  panelCacheMap = [] as AnyObj[];

  componentDidUpdate(preProps: ContentProps) {
    const menuData = getMenuData(this.props.data);
    const preMenuData = getMenuData(preProps.data);

    // 去除 panel、subMenu 进行equal比较，panel为ReactNode时，会报错
    const _menuData = menuData.map((data) => omit(data, ingoreProps));
    const _preMenuData = preMenuData.map((data) => omit(data, ingoreProps));

    if (!utils.isEqual(_menuData, _preMenuData)) {
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState({
        menuData,
        selectedMenu: menuData[0],
      });
    }
  }

  onMenuChange = (id: string, item: object) => {
    this.setState({ selectedMenu: item as MenuItemData });
    this.props.onMenuChange?.(item as MenuItemData);
  };

  toggleFloated = () => {
    const { floated, onFloatedChange } = this.props;

    onFloatedChange(!floated);
  };

  handleAction = (e: SyntheticEvent, action: ExtraAction) => {
    if (action.onAction) {
      action.onAction(action.id);
    }
  };

  @actions.method('ls')
  toggleMenuDisabled = (id: string, disabled: boolean) => {
    const { menuData, selectedMenu } = this.state;
    const ind = menuData.findIndex((data) => data.id === id);

    if (ind >= 0) {
      const menu = menuData[ind];

      menu.disabled = disabled;

      const _menuData = menuData.splice(0, menuData.length);
      let _selectedMenu = selectedMenu;

      _menuData.splice(ind, 1, menu);

      if (_selectedMenu?.id === id) {
        _selectedMenu = _menuData.find((data) => data.id !== id) as AsideMenuData;
      }

      this.setState({ menuData: _menuData, selectedMenu: _selectedMenu });
    }
  };

  renderExtraContent = () => {
    const { data, floated } = this.props;

    return (
      <>
        <When weId={`${this.props.weId || ''}_4rofl0`} condition={!!data?.extraActions}>
          <Actions
            weId={`${this.props.weId || ''}_9y3l5l`}
            onAction={this.handleAction}
            actions={data?.extraActions!}
          />
        </When>
        <CorsComponent
          weId={`${this.props.weId || ''}_v1eqhx`}
          app="@weapp/ebdcoms"
          compName="IconFont"
          className={floated ? 'float' : 'ding'}
          name="Icon-Bida-o"
          title={
            utils.needRTL() ? getLabel('301837', '固定在右侧') : getLabel('57073', '固定在左侧')
          }
          onClick={this.toggleFloated}
        />
      </>
    );
  };

  renderPanel = () => {
    const { selectedMenuId } = this.props;
    const { menuData, selectedMenu } = this.state;
    if (!this.panelCacheMap.find((p) => p.id === `${selectedMenuId}_${selectedMenu?.id}`)) {
      const bindKey = `${prefixCls}_bind_key_${selectedMenu?.id}`;
      const node = (
        <div>
          <MenuContent
            weId={`${this.props.weId || ''}_dbfcha`}
            bindKey={bindKey}
            dataId={selectedMenu?.id}
            value={selectedMenu?.id}
          >
            <AsidePanel
              weId={`${this.props.weId || ''}_89j8m9_${selectedMenu?.id}`}
              key={selectedMenu?.id}
              menu={selectedMenu}
            />
          </MenuContent>
        </div>
      );
      this.panelCacheMap.push({ id: `${selectedMenuId}_${selectedMenu?.id}`, node });
    }
    return menuData
      .map((data) => {
        const show = data?.id === selectedMenu?.id;
        const curPanel = this.panelCacheMap.find(
          (panel: any) => panel.id === `${selectedMenuId}_${data.id}`,
        );

        if (!curPanel) return null;

        const { node } = curPanel;

        return React.cloneElement(node, {
          ...node.props,
          className: show ? 'panel-show' : 'panel-hide',
        });
      })
      .filter(Boolean);
  };

  render() {
    const { menuData, selectedMenu } = this.state;
    const { data, loading } = this.props;
    const userLanguage = getLang();
    const clsNameStr = classnames({
      [`${prefixCls}-ls-content`]: true,
      [`${prefixCls}-lsc-hidden`]: !data,
      [`${prefixCls}-ls-content-wider`]: userLanguage !== cNLang,
    });

    const bindKey = `${prefixCls}_bind_key_${selectedMenu?.id}`;
    const isSingleMenu = menuData.length === 1;

    return (
      <div className={clsNameStr}>
        <Spin
          weId={`${this.props.weId || ''}_uk86wz`}
          wrapperClassName={`${prefixCls}-ls-content-loading`}
          spinning={loading}
        >
          <header>
            <Menu
              weId={`${this.props.weId || ''}_hnwaso`}
              defaultValue={menuData[0]?.id}
              data={menuData}
              value={selectedMenu?.id}
              bindKey={bindKey}
              overflowType="scroll"
              extraContent={this.renderExtraContent()}
              onChange={this.onMenuChange}
              className={classnames({ [`${prefixCls}-ls-menu-single`]: isSingleMenu })}
            />
          </header>
          <main>{this.renderPanel()}</main>
        </Spin>
      </div>
    );
  }
}
