import { useEffect, useRef, useState } from 'react';
import useMemoProps from './useMemoProps';

export default function useLazySelectedCom() {
  const memoProps = useMemoProps();
  const [loading, setLoading] = useState(true);
  const _selectedCom = memoProps.store.selectedCom;
  const _clientType = memoProps.client;
  const ref = useRef({ selectedId: _selectedCom?.id, clientType: _clientType });
  const [selectedCom, setSelectedCom] = useState(() => _selectedCom);

  useEffect(() => {
    if (ref.current.selectedId === _selectedCom?.id) {
      setSelectedCom(_selectedCom);
    }
    ref.current.selectedId = _selectedCom?.id;
  }, [_selectedCom]);

  useEffect(() => {
    setLoading(true);

    if (ref.current.clientType !== _clientType) {
      setLoading(false);
      setSelectedCom(_selectedCom);
      ref.current.clientType = _clientType;
    } else {
      setLoading(false);
      setSelectedCom(_selectedCom);
    }
  }, [_selectedCom?.id]);

  return {
    loading,
    selectedCom,
  };
}
