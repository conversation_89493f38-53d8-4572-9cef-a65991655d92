import { FormDatas } from '@weapp/ui';
import { useCallback } from 'react';
import { useDesigner } from '.';
import { DesignProps, IComData } from '../../core';
import { useCompDesignStore } from '../core';
import useMemoProps from './useMemoProps';

/**
 * 获取传到设计视图的props
 */
export default function useDesignProps(com?: IComData): DesignProps {
  const designer = useDesigner();
  const { selectedCom } = useCompDesignStore();
  const memoProps = useMemoProps();
  const data = com || selectedCom!;
  const onConfigChange = useCallback(
    (changes: FormDatas, otherParams?: any) => {
      const { layoutStore } = designer;
      const comId = data?.id || '';

      layoutStore.updateComConfig(changes, comId, otherParams);
    },
    [data?.id],
  );

  return {
    data,
    onConfigChange,
    ...data,
    ...memoProps,
  };
}
