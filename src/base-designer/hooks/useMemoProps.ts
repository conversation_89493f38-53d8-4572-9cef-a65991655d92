import { useMemo } from 'react';
import { useDesigner } from '.';
import { useCompDesignStore } from '../core';

/**
 * 将使用范围在get级别或组件不需要实时获取的属性进行memo处理，只在用时去取
 * 避免造成组件重复渲染
 */
export default function useMemoProps() {
  const designer = useDesigner();
  const store = useCompDesignStore();
  const { clientType, page } = designer;

  return useMemo(() => {
    const { events, comServicePath } = designer;

    return {
      page,
      store,
      events,
      comServicePath,
      client: clientType,
    };
  }, [designer, clientType, page.id]);
}
