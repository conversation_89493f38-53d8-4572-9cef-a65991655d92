import { ReactElement } from 'react';
import { ICategoryViewData } from '../panels/component/types';

export type DesignerActions = {
  save: () => Promise<void>;
};

export type RightSideActions = {
  /** 选中右侧菜单，包含子菜单 */
  selectMenu: (id: string) => void;
  /** 通过面板类型来选中右侧菜单，包含子菜单 */
  selectMenuByPanelType: (type: string) => void;
  /** 通过面板类型来获取面板的内容 */
  getPanelContentByType: (type: string) => ReactElement;
};

export type LeftSideActions = {
  /** 切换子菜单disabled */
  toggleMenuDisabled?: (id: string, disabled: boolean) => void;
};

export type componentActions = {
  /** init 更新组件面板数据 */
  init: () => void;
  /** getDatas 获取组件面板数据 */
  getDatas: () => ICategoryViewData[] | undefined;
};

export type compListAction = {
  /** 选中menu */
  selectMenu: (value: string) => void;
};

/** 设计器全局设置 */
export type GlobalSetting = {
  /** 是否允许选中组件 */
  selectable: boolean;
};
