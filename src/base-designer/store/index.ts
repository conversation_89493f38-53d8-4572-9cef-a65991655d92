import { pick } from '@weapp/utils';
import {
  action, computed, observable, toJS,
} from 'mobx';
import { getCodeData, importCssText } from '../../common/sourcecode/utils';
import EventEmitter from '../../core/utils/event';
import getServicePath from '../../core/utils/getServicePath';
import {
  ClientType,
  CompDesignStores,
  IComCategory,
  IComData,
  IComDescription,
  ILayoutData,
  LayoutType,
  Page,
  PageModuleType,
} from '../../types';
import shallowObservable from '../../utils/shallowObservable';
import { ComTempType } from '../dialogs/com-temp/constants';
import { DatasetValue } from '../panels/dataset/types';
import { PluginCenterType } from '../plugin-center/types';
import BaseLayoutStore from './LayoutStore';
import MainStore from './MainStore';
import {
  compListAction,
  componentActions,
  DesignerActions,
  GlobalSetting,
  LeftSideActions,
  RightSideActions,
} from './types';
import {
  compareLayoutDatas,
  getComDescriptions,
  getInitTerminalType,
  pickedPropNames,
} from './utils';

export default abstract class BaseDesignerStore<T = IComData> {
  /**
   * 设计器保存
   */
  abstract save: Function;

  /**
   * 设计器初始化方法
   */
  abstract init: Function;

  abstract get layoutStore(): BaseLayoutStore<T>;

  abstract getLayoutDatas: () => ILayoutData[];

  /** 撤销操作 */
  abstract undo: () => void;

  /** 重做操作 */
  abstract redo: () => void;

  /** 设置组件是否为容器，并且可设置组件拖入规则 */
  abstract setComDroppable?: (
    comId: string,
    canDrop: boolean | ((target: any, source: any) => boolean),
  ) => void;

  /**
   * 设计器事件总线，用于设计器事件的绑定和触发
   */
  events = new EventEmitter();

  /**
   * 设计器动作，通过@actions.method('actions')的方式挂载在actions上
   */
  actions = {} as DesignerActions;

  /**
   * 右侧边栏提供的一些方法，通过@actions.method('rs')的方式挂载在rs上
   */
  rs = {} as RightSideActions;

  /**
   * 左侧边栏提供的一些方法，通过@actions.method('ls')的方式挂载在ls上
   */
  ls = {} as LeftSideActions;

  /**
   * component 组件面板提供的一些方法，通过@action.method('component')的方式挂载在component上
   */
  component = {} as componentActions;

  /**
   * compList 组件内容面板提供的一些方法，通过@action.method('compList')的方式挂载在compList上
   */
  compList = {} as compListAction;

  /**
   * 设计区域提供的store
   */
  main = new MainStore();

  /**
   * 专用频道，用于浏览器夸标签页通信
   */
  channel?: BroadcastChannel = undefined;

  /**
   * 基础设计器提供的组件数据、组件方法的store
   */
  get compDesignStore() {
    return this.compDesignStores[this.clientType]!;
  }

  compDesignStores: CompDesignStores = {
    PC: null,
    MOBILE: null,
  };

  /** 事件绑定 */
  on = this.events.on.bind(this.events);

  /** 事件解绑 */
  off = this.events.off.bind(this.events);

  /**
   * 所有组件的描述信息
   */
  comDescriptions: Record<string, IComDescription> = {};

  /** 保存的所有组件信息 */
  @shallowObservable prevLayoutDatas: ILayoutData[] = [];

  /**
   * 页面所属的模块类型
   */
  moduleScope: PageModuleType = 'EB_PAGE';

  /**
   * 设计器布局类型
   */
  layoutType: LayoutType = 'GRID';

  /**
   * weapp/ebdpage内的event事件
   */
  pageEvent?: any = undefined;

  /**
   * 设计器页面的布局信息
   */
  @shallowObservable layoutDatas: ILayoutData[] = [];

  /**
   * 组件服务地址，不同模块的路径不同（如果表单和ebuilder）
   * 组件内部可能存在向后端组件服务请求数据的情况
   */
  @observable comServicePath: string | undefined = '';

  /**
   * 是否允许撤销
   */
  @observable canUndo = false;

  /**
   * 是否允许重做
   */
  @observable canRedo = false;

  /** 设计器客户端类型 */
  @observable clientType: ClientType = getInitTerminalType();

  /**
   * 记录需要打开mini弹窗的组件id
   * 由于组件是异步导入，event触发、绑定时机不好确认，通过store记录
   */
  @observable miniConfigComId: string = '';

  /**
   * 母版编辑模式
   */
  @observable isMasterEditMode: boolean = false;

  /**
   * 母版编辑记录母版组件id
   */
  @observable editerMasterComId: string = '';

  /**
   * 当前页面的数据集
   */
  @shallowObservable datasetVals: DatasetValue[] = [];

  /**
   * 是否已经实现自动更新layoutDatas功能
   */
  autoUpdateLayoutDatas = false;

  // 是否设计器开启组件母版相关内容
  @observable enabledComMaster: boolean = true;

  /**
   * 设计器自定义的数据，用于通过compDesignStore获取
   */
  customData: any = null;

  @computed get showMasterMode() {
    return !this.isMasterEditMode && this.enabledComMaster;
  }

  /**
   * 设计器全局设置
   */
  globalSetting = {
    /** 是否允许选中组件 */
    selectable: true,
  };

  /** 基础设计器插件中心 */
  pluginCenter?: PluginCenterType;

  @action.bound
  resetLayoutDatas(layoutDatas?: ILayoutData[] | null) {
    this.autoUpdateLayoutDatas = true;
    this.initLayoutDatas(layoutDatas);
  }

  initSourceCode() {
    const { sourceCode } = this.layoutStore || {};
    importCssText(getCodeData(sourceCode?.ecode).compiledCodeData.css);
  }

  @action.bound
  initLayoutDatas(_layoutDatas?: ILayoutData[] | null) {
    const hasLayoutDatas = !!_layoutDatas && _layoutDatas.length > 0;

    if (hasLayoutDatas) {
      const [layoutDatas] = this.pluginCenter?.invoke('getLayoutDatas', {
        args: [_layoutDatas],
      }) || [_layoutDatas];
      const [layoutData] = layoutDatas;

      this.main.toggleLoading(true);

      // 初始化页面基本信息
      this.moduleScope = layoutData.module;
      this.layoutType = layoutData.layoutType;

      this.setLayoutDatas(layoutDatas);
      this.init(layoutDatas);
      this.setPrevLayoutDatas(layoutDatas);

      this.initSourceCode();
      this.main.toggleLoading(false);
    }
  }

  /**
   * 初始化组件描述信息
   * @param categories 组件的分类信息
   */
  initComDescriptions(categories: IComCategory[]) {
    // 由于可能存在多个组件面板(表单设计器)，故此处需要进行对象合并
    // 取多个面板组件信息的合集
    this.comDescriptions = {
      ...this.comDescriptions,
      ...getComDescriptions(categories),
    };
  }

  isUnqiue = (comType: string) => {
    const desc = this.comDescriptions[comType];

    return !!desc?.unique;
  };

  /** 设计器支持的客户端类型 */
  get terminalScopes() {
    return this.layoutDatas.map((data) => data.terminalType);
  }

  /** 页面信息 */
  get page() {
    const layoutData = this.layoutDatas.find(
      (data: ILayoutData) => this.clientType === data.terminalType,
    );

    const {
      pageId,
      layoutType = 'FLOW',
      module,
      thumbnail,
      refTheme,
      ebViewModule,
      appid,
    } = layoutData! || {};

    return {
      id: pageId,
      client: this.clientType,
      layoutType,
      module,
      thumbnail,
      refTheme,
      ebViewModule,
      appId: appid,
    } as Page;
  }

  /** 页面只支持单个客户端的设计，则不需要显示PC,MOBILE的切换 */
  @computed get singleClient(): boolean {
    return this.layoutDatas.length <= 1;
  }

  /** 设计器内部组件布局是否发生改变 */
  get isLayoutChanged(): boolean {
    const currDatas = this.filterLayoutDatas(
      this.getLayoutDatas().map((layoutData) => {
        const pickData = pick(layoutData, pickedPropNames);
        const { codeData } = getCodeData(
          layoutData.sourceCode?.ebcode || layoutData.sourceCode?.ecode,
        );

        pickData.sourceCode = codeData;
        return pickData;
      }) as ILayoutData[],
    );

    const prevDatas = this.filterLayoutDatas(
      toJS(this.prevLayoutDatas).map((layoutData) => {
        const pickData = pick(layoutData, pickedPropNames);
        const { codeData } = getCodeData(
          layoutData.sourceCode?.ebcode || layoutData.sourceCode?.ecode,
        );

        pickData.sourceCode = codeData;
        return pickData;
      }) as ILayoutData[],
    );

    return !compareLayoutDatas(currDatas, prevDatas);
  }

  /** 去除LayoutData的属性 */
  // eslint-disable-next-line max-len
  filterLayoutDatas = (layoutDatas: ILayoutData[]) => layoutDatas.map((layout: ILayoutData) => {
    const comps = layout.comps.map((comp: IComData) => {
      if (comp.config.layout) {
        // 组件选中会改变selected属性，影响判断布局变化
        const {
          selected, isEmpty, delayRenderTime, ebCodeFileTypes, ...restLayout
        } = comp.config.layout;

        return { ...comp, config: { ...comp.config, layout: { ...restLayout } } };
      }

      return comp;
    });

    // 源码加载文件ebCodeFileTypes属性，影响判断布局变化
    const { ebCodeFileTypes, ...restConfig } = layout.config;

    return { ...layout, config: { ...restConfig }, comps };
  });

  @action
  setMiniConfigComId = (id: string) => {
    this.miniConfigComId = id;
  };

  @action
  setLayoutDatas = (_layoutDatas: ILayoutData[]) => {
    const [layoutDatas] = this.pluginCenter?.invoke('getLayoutDatas', { args: [_layoutDatas] }) || [
      _layoutDatas,
    ];
    const { terminalType } = layoutDatas[0];
    this.layoutDatas = layoutDatas;

    if (this.singleClient && terminalType !== this.clientType) {
      this.clientType = terminalType;
      // 提供全局设计器终端
      window.EBDesignClient = terminalType;
    }
  };

  @action
  changeClient = (clientType: ClientType) => {
    this.clientType = clientType;
    // 提供全局设计器终端
    window.EBDesignClient = clientType;
    this.setIsMasterEditMode(false);
  };

  @action
  setCanUndo = (canUndo: boolean) => {
    this.canUndo = canUndo;
  };

  @action
  setCanRedo = (canRedo: boolean) => {
    this.canRedo = canRedo;
  };

  @action
  setServicePath = (comServicePath?: string) => {
    this.comServicePath = getServicePath(comServicePath);
  };

  @action
  setPrevLayoutDatas = (layoutDatas: ILayoutData[]) => {
    this.prevLayoutDatas = layoutDatas;
  };

  @action
  selectPage = () => {};

  @action
  setIsMasterEditMode = (isMasterEditMode: boolean) => {
    this.isMasterEditMode = isMasterEditMode;

    this.ls.toggleMenuDisabled?.(ComTempType.MASTER, isMasterEditMode);
  };

  @action
  setEditerMasterComId = (comId: string) => {
    this.editerMasterComId = comId;
  };

  /**
   * 为 母版 绑定双击事件，双击母版进行编辑
   * @param dom 组件的 DOM
   * @param comId 组件的ID
   * - 组件ID不传入的话，会尝试取 layoutStore.selectedCom.id
   */
  @action
  addMasterEditEvent: (dom: HTMLElement, comId?: string) => void = () => {};

  /** 存为母版 */
  @action
  updateComToMaster: (masterId: string) => void = () => {};

  @action
  setPageEvent = (pageEvent: any) => {
    this.pageEvent = pageEvent;
  };

  @action
  setCompDesignStores = (compDesignStores: CompDesignStores) => {
    this.compDesignStores = compDesignStores;
  };

  @action
  setDatasetVals = (datasetVals: DatasetValue[]) => {
    this.datasetVals = datasetVals;
  };

  @action
  setGlobalSetting = (globalSetting: GlobalSetting) => {
    this.globalSetting = { ...this.globalSetting, ...globalSetting };
  };

  getCompDesignStore = (clientType = this.clientType) => this.compDesignStores[clientType];

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getDragCls = (id: string) => '';

  setCustomData = (_customData: any) => {
    this.customData = _customData;
  };

  getCustomData = () => this.customData;
}
