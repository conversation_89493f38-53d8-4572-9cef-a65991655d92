import { FormDatas } from '@weapp/ui';
import { uniq } from '@weapp/utils';
import { action, observable, toJS } from 'mobx';
import { SourceCodeType } from '../../common/sourcecode/types';
import { verifyCode } from '../../common/sourcecode/utils';
import { ClientType, IComData, SortType } from '../../types';
import shallowObservable from '../../utils/shallowObservable';
import { ComTempConfig } from '../dialogs/com-temp/types';
import shallowComsObservable from '../utils/shallowComsObservable';
import BaseDesignerStore from './index';

export default abstract class BaseLayoutStore<T = IComData> {
  clientType: ClientType = 'PC';

  /** 当前选中的组件 */
  @observable selectedCom: T | null = null;

  /** 当前选中的组件的DOM */
  @observable selectedComDom: HTMLElement | null = null;

  /** 设计区域的所有组件 */
  @shallowComsObservable coms: T[] = [];

  /** 设计区域的所有已保存的组件 */
  @shallowObservable savedComs: T[] = [];

  /** 页面配置信息 */
  @observable pageConfig: Record<string, any> = {};

  /** 页面源码信息 */
  @observable sourceCode?: SourceCodeType;

  /** 页面上被删除关联关系的组件集合，存在coms中，但是没有被页面渲染 */
  @observable deleteRelatedComIds: string[] = [];

  /** 样式缓存绑定的主题id */
  protected cssCacheThemeId?: string;

  /** 样式缓存绑定的主题版本 */
  protected cssCacheThemeVersion?: string;

  /** 新增组件 */
  abstract addNewCom: Function;

  /** 选中组件 */
  abstract selectCom: Function;

  /** 更新组件配置 */
  abstract updateComConfig: (changes: FormDatas, comId: string, otherParams?: any) => void;

  abstract updateCom: (changes: Partial<any>, comId?: string) => void;

  /** 获取组件的子组件对象 */
  abstract getChildComs: (comId?: string, filter?: (childCom: T) => boolean) => T[];

  /** 获取组件的父组件对象 */
  abstract getParentCom: (comId: string, ignoreInternalContainer?: boolean) => T | null;

  /** 设置选中组件的dom，用来获取组件容器样式的初始化样式 */
  abstract setSelectedComDom: (dom: any) => void;

  /** 获取当前选中组件的组件模板数据 */
  abstract getComTempConfig: (comId?: string) => ComTempConfig;

  /** 复制组件 */
  abstract copyCom: Function;

  /** 快捷键复制统一调用方法 */
  copyByHotKey?: () => Promise<void> | void;

  /** 快捷键粘贴统一调用方法 */
  pastByHotKey?: () => Promise<void> | void;

  /** 删除组件 */
  abstract deleteCom: (comId: string) => void;

  /** 删除Droppable组件 */
  abstract deleteDroppableCom: (droppableId: string, comId: string) => void;

  /** 设置运行隐藏组件 */
  abstract changeComVisible: (visible: boolean, comId?: string) => void;

  /**
   * 交换组件顺序
   * 当sortType类型为child时，id为是子组件id
   * 当sortType类型为droppable时，id为droppable id
   */
  abstract sortComs: (id: string, parentComId?: string, sortType?: SortType) => void;

  /** droppableId获取组件对象 */
  abstract getDroppableCom: (comId: string, droppableId: string) => T | null;

  /** 判断是否为根节点下的组件 */
  abstract isRoot: (id: string) => boolean;

  /** 设置容器子组件 */
  abstract setDroppableChilds: (comId: string, childComIds: string[]) => void;

  getLayoutData = () => {
    const comps = this.coms;

    const layoutData: Record<string, any> = {
      comps,
      config: toJS(this.pageConfig),
      // 绑定的主题id
      cssCacheThemeId: this.cssCacheThemeId,
      // 绑定的主题版本
      cssCacheThemeVersion: this.cssCacheThemeVersion,
    };

    const ebCodeFileTypes: string[] = [];

    if (this.sourceCode) {
      layoutData.sourceCode = toJS(this.sourceCode);

      // 判断当前页面源码里面内容信息，添加源码运行加载标识
      if (this.sourceCode.ecode) {
        const ebcode = JSON.parse(decodeURI(this.sourceCode.ecode));

        Object.keys(ebcode).forEach((fileType) => {
          if (ebcode[fileType].content) {
            ebCodeFileTypes.push(fileType);
          }
        });
      }
    }

    layoutData.config = { ...layoutData.config, ebCodeFileTypes };

    return layoutData;
  };

  /** 设置页面配置信息 */
  @action
  updatePageConfig = (changes: FormDatas) => {
    this.pageConfig = {
      ...this.pageConfig,
      ...changes,
    };
  };

  /** 更新样式缓存主题信息 */
  updateCssCacheInfo = (themeId: string, themeVersion: string) => {
    this.cssCacheThemeId = themeId;
    this.cssCacheThemeVersion = themeVersion;
  };

  /** 设置页面源码信息 */
  @action.bound
  updateSourceCode = (code: SourceCodeType, isFromCssCache?: boolean) => {
    // 需要继承的缓存
    const cssCachePre = !isFromCssCache && this.sourceCode?.cssCache
      ? {
        cssCache: {
          ...this.sourceCode?.cssCache,
        },
      }
      : undefined;
    this.sourceCode = isFromCssCache
      ? code
      : {
        ...cssCachePre,
        ...code,
      };
  };

  /** 更新样式缓存 */
  @action
  updateStyleCacheCode = (styleId: string, cssText: string) => {
    const sourceCode = {
      ...this.sourceCode,
      // 更新样式缓存
      cssCache: {
        ...this.sourceCode?.cssCache,
        [styleId]: cssText,
      },
    };
    this.updateSourceCode(sourceCode, true);
  };

  /**
   * 保存之前数据校验
   */
  @action
  verify = () => {
    if (this.sourceCode) {
      return verifyCode(this.clientType, toJS(this.sourceCode));
    }
  };

  getParentComs = (comId: string, ignoreInternalContainer = true) => {
    const parentComs: T[] = [];
    let parentCom: any = this.getParentCom(comId, ignoreInternalContainer);

    while (parentCom) {
      parentComs.push(parentCom);
      parentCom = this.getParentCom(parentCom.id, ignoreInternalContainer);
    }

    return parentComs;
  };

  // 设置组件加载顺序关系
  lazyRender = (com: IComData, preconditionComIds: string[]) => {
    if (com && preconditionComIds && preconditionComIds.length) {
      com.config.__lazy = true;
      com.config.__preRenderComs = preconditionComIds;
    }
  };

  /**
   * 更改preLayoutDatas里面coms config，用于跳过预览layoutDatas 数据判断校验
   * @param comId
   * @param changes
   */
  changePreComConfig = (parent: BaseDesignerStore, comId: string, changes: FormDatas) => {
    const layoutData = parent.prevLayoutDatas.find(
      (data) => data.terminalType === this.clientType,
    )!;

    const { comps = [] } = layoutData;
    const comIndex = comps.findIndex((_com) => _com.id === comId);

    if (comIndex > -1) {
      const com = JSON.parse(JSON.stringify(comps[comIndex]));

      com.config = { ...com.config, ...changes };
      comps.splice(comIndex, 1, com);
    }
  };

  // 逻辑删除组件
  @action
  softDelete = (comIds: string[]) => {
    this.deleteRelatedComIds = uniq([...this.deleteRelatedComIds, ...comIds]);
  };

  // 撤销逻辑删除的组件
  @action
  restoreSoftDelete = (comIds: string[]) => {
    this.deleteRelatedComIds = this.deleteRelatedComIds.filter((id) => !comIds.includes(id));
  };
}
