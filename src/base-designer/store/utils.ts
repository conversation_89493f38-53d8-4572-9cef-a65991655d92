import { isEqual, pick, qs } from '@weapp/utils';
import { ClientType, IComDescription, ILayoutData } from '../../core/types';
import ebdcoms from '../../utils/ebdcoms';
import { ICategory } from '../panels/component/types';

/** 布局比较时只比较以下属性的值 */
export const pickedPropNames = [
  'pageId',
  'comps',
  'terminalType',
  'layoutType',
  'config',
  'sourceCode',
];

const cloneDeep = (o: object) => JSON.parse(JSON.stringify(o));

export function compareLayoutDatas(datas1: ILayoutData[], datas2: ILayoutData[]) {
  // 优化后减小数组的遍历次数
  return (
    datas1?.length === datas2?.length
    && !datas1.find(
      (data, index) => !isEqual(
        cloneDeep(pick(data, pickedPropNames)),
        cloneDeep(pick(datas2[index], pickedPropNames)),
      ),
    )
  );
}

/**
 * 通过组件分类信息，获取该分类下所有的组件的描述信息
 * @param categories 组件分类信息
 * @returns Record<string, IComDescription>
 */
export function getComDescriptions(categories: ICategory[]) {
  const comDescriptions: Record<string, IComDescription> = {};
  const setDescriptions = (category: ICategory) => {
    const { childs = [], comps = [] } = category;

    comps.forEach((com: IComDescription) => {
      comDescriptions[com.type] = com;
    });

    childs.forEach((child) => {
      setDescriptions(child);
    });
  };

  categories.forEach(setDescriptions);

  return comDescriptions;
}

export const getInitTerminalType = () => {
  const clientList = ['PC', 'MOBILE'];
  const { search } = window.location;
  const [, searchParams] = search?.split('?') || [];
  const { defaultTerminalType = 'PC' } = qs.parse(searchParams);

  const terminalType = (defaultTerminalType as string)?.toUpperCase() as ClientType;

  if (terminalType && clientList.includes(terminalType)) {
    return terminalType;
  }

  return 'PC';
};

export const getConditionSqlIdByComId = (
  comServicePath: string,
  sourceCompId: string,
  targetCompId: string,
) => {
  if (sourceCompId && targetCompId) {
    comServicePath = comServicePath || 'ebuilder/coms';

    return ebdcoms.asyncExcu('ajax', {
      url: `/api/${comServicePath}/comssdk/condition/copyByCompId`,
      params: { sourceCompId, targetCompId },
    });
  }

  return Promise.resolve({});
};

export default {};
