import { action, observable } from 'mobx';
import { MainProps } from '../main/types';

export default class MainStore {
  @observable loading: boolean = true;

  mainProps: MainProps | undefined = undefined;

  @action
  toggleLoading(loading?: boolean) {
    if (typeof loading === 'undefined') {
      this.loading = !this.loading;
    } else {
      this.loading = !!loading;
    }
  }

  @action
  setMainProps(mainProps: MainProps | undefined) {
    if (mainProps) {
      this.mainProps = mainProps;
    }
  }
}
