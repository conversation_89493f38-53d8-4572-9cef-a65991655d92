import { ResizeObserver, debounce } from '@weapp/utils';
import React, {
  ReactNode, useCallback, useEffect, useState,
} from 'react';
import { prefixCls } from '../../constants';

interface PageTitleProps extends React.Attributes {
  title?: ReactNode;
}

const PageTitle: React.FC<PageTitleProps> = (props) => {
  const [maxWidth, setMaxWidth] = useState(150);

  const calcMaxWidth = useCallback(
    debounce(() => {
      const centerWidth = document.getElementsByClassName(`${prefixCls}-header-center`)?.[0]
        ?.clientWidth;
      const operationWidth = document.getElementsByClassName(`${prefixCls}-header-operation`)?.[0]
        ?.clientWidth;
      const rightside = document.getElementsByClassName(`${prefixCls}-header-rightside`)?.[0]
        ?.clientWidth;
      const sideWidth = Math.max(operationWidth, rightside - 250);
      setMaxWidth(centerWidth - sideWidth * 2);
    }, 200),
    [],
  );

  useEffect(() => {
    calcMaxWidth();
    let resizeObserver: any = null;
    // 监听尺寸变化
    const operationNode = document.getElementsByClassName(`${prefixCls}-header-operation`)?.[0];
    if (operationNode) {
      resizeObserver = new ResizeObserver(
        debounce(() => {
          calcMaxWidth();
        }, 200),
      );
      resizeObserver.observe(operationNode as any);
    }
    window.addEventListener('resize', calcMaxWidth);
    return () => {
      window.removeEventListener('resize', calcMaxWidth);
      resizeObserver?.disconnect();
    };
  }, []);

  return (
    <div
      className={`${prefixCls}-header-title`}
      title={props.title as string}
      style={{ maxWidth: `${maxWidth}px` }}
    >
      {props.title}
    </div>
  );
};

export default PageTitle;
