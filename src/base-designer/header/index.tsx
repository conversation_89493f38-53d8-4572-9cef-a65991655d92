import { classnames, middleware } from '@weapp/utils';
import { observer } from 'mobx-react';
import React from 'react';
import { When } from 'react-if';
import { libName, prefixCls } from '../../constants';
import './index.less';
import LeftSide from './LeftSide';
import OperationGroup from './operation-group';
import PageTitle from './PageTitle';
import RightSide from './RightSide';
import { InternalHeaderProps } from './types';

@middleware(libName, 'BaseHeader')
@observer
export default class Header extends React.PureComponent<InternalHeaderProps> {
  render() {
    const {
      children,
      title,
      leftSide,
      rightSide,
      showOperations = true,
      changeDisabled = false,
    } = this.props;
    const langId = window.TEAMS?.locale?.lang || '';
    const chineseList = ['zh_CN', 'zh_TW'];
    const isNormal = chineseList.includes(langId);

    return (
      <header
        className={classnames(`${prefixCls}-header`, {
          [`${prefixCls}-header-wider`]: !isNormal,
        })}
      >
        <LeftSide
          weId={`${this.props.weId || ''}_jlwtxt`}
          changeDisabled={changeDisabled}
          {...this.props}
        >
          {leftSide}
        </LeftSide>
        <div className={`${prefixCls}-header-center`}>
          <When weId={`${this.props.weId || ''}_q7b8n1`} condition={!children}>
            {/* 操作按钮需要等到layoutDatas有数据后再展示，否则容易造成问题 */}
            <When weId={`${this.props.weId || ''}_bpprde`} condition={showOperations !== false}>
              <OperationGroup
                weId={`${this.props.weId || ''}_y21n8h`}
                changeDisabled={changeDisabled}
                {...this.props}
              />
            </When>
            <PageTitle weId={`${this.props.weId || ''}_tf6mbo`} title={title} />
          </When>
          <When weId={`${this.props.weId || ''}_owyihs`} condition={!!children}>
            {children}
          </When>
        </div>
        <RightSide
          weId={`${this.props.weId || ''}_3jscab`}
          showSave={showOperations !== false}
          saveDisabled={changeDisabled}
          {...this.props}
        >
          {rightSide}
        </RightSide>
      </header>
    );
  }
}
