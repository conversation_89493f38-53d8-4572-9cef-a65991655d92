import { LibType } from '@weapp/ebdcoms/lib/common/assets-lib/types';
import { CorsComponent, Trigger } from '@weapp/ui';
import { classnames } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { PureComponent, ReactNode } from 'react';
import { noop, prefixCls } from '../../../constants';
import { DesignerContext } from '../../Context';
import HotKeysDialog from '../dialog/hotkeys';
import './index.less';
import MenuList from './List';
import QucikMenuStore from './store';
import { MenuActionType } from './types';

interface LeftHeaderStates {
  assetsType: LibType;
}

interface LeftHeaderProps {
  /** 无权保存、修改配置 */
  changeDisabled?: boolean;
  renderQuickMenu?: (v: ReactNode) => ReactNode;
}

@observer
export default class QuickMenu extends PureComponent<LeftHeaderProps, LeftHeaderStates> {
  store = new QucikMenuStore();

  static contextType = DesignerContext;

  state = {
    assetsType: 'ICON' as LibType,
  };

  save = async () => {
    const { actions } = this.context;
    try {
      await actions.save();
    } catch (e) {
      console.error(e);
    }
  };

  onMenuAction = (type: MenuActionType) => {
    const { showDialog, onVisibleChange } = this.store;
    switch (type) {
      case 'setting':
      case 'hotkeys':
        showDialog(type);
        break;
      case 'save':
        this.save();
        break;
      case 'assets-icon':
        this.setState({ assetsType: 'ICON' });
        showDialog('assets');
        break;
      case 'assets-picture':
        this.setState({ assetsType: 'PIC' });
        showDialog('assets');
        break;
      case 'assets-video':
        this.setState({ assetsType: 'VIDEO' });
        showDialog('assets');
        break;
      default:
        break;
    }
    // 点击菜单之后，关闭菜单
    onVisibleChange(false);
  };

  render() {
    const { renderQuickMenu, changeDisabled = false } = this.props;
    const {
      visible, visibles, hideDialog, onVisibleChange,
    } = this.store;
    const { assetsType } = this.state;
    const clsNameStr = classnames(`${prefixCls}-quick-menu`, {
      open: visible,
    });

    return (
      <>
        <Trigger
          weId="lhyfgo"
          popupVisible={visible}
          popup={
            <MenuList
              weId="cfkuyd"
              onAction={this.onMenuAction}
              saveDisabled={changeDisabled}
              renderQuickMenu={renderQuickMenu}
            />
          }
          popupPlacement="bottomLeft"
          className={clsNameStr}
          popupClassName={`${prefixCls}-quick-menu-popup`}
          onPopupVisibleChange={onVisibleChange}
        >
          <span>
            <CorsComponent
              weId={`${this.props.weId || ''}_v1eqhx`}
              app="@weapp/ebdcoms"
              compName="IconFont"
              type="menu"
              size="s"
            />
          </span>
        </Trigger>
        <HotKeysDialog
          weId={`${this.props.weId || ''}_0p5u1g`}
          visible={visibles.hotkeys}
          onClose={hideDialog('hotkeys')}
        />
        <CorsComponent
          weId={`${this.props.weId || ''}_o7bwnd`}
          app="@weapp/ebdcoms"
          compName="AssetsModal"
          type={assetsType}
          visible={visibles.assets}
          changeDisabled={changeDisabled}
          onOk={noop}
          onCancel={hideDialog('assets')}
          showFooter={false}
        />
      </>
    );
  }
}
