import { memoize } from '@weapp/utils';
import { action, observable } from 'mobx';
import { Visibles } from './types';

export default class QucikMenuStore {
  /**
   * 控制菜单弹出的显隐
   */
  @observable visible = false;

  /**
   * 弹窗显隐聚合对象
   */
  @observable visibles: Visibles = {
    hotkeys: false,
    setting: false,
    assets: false,
  };

  @action
  onVisibleChange = (visible: boolean) => {
    this.visible = visible;
  };

  @action
  showDialog = (type: keyof Visibles) => {
    this.visibles[type] = true;
  };

  hideDialog = memoize((type: keyof Visibles) => action(() => {
    this.visibles[type] = false;
  }));
}
