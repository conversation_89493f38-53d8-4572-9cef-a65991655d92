@import (reference) '../../../style/prefix.less';
@import (reference) '../../../style/var.less';

@icon-size: 36px;

.@{prefix}-quick-menu {
  position: relative;
  display: flex;
  padding: 1px 12px 0;
  align-items: center;
  border-right: 1px solid transparent;
  border-bottom: 1px solid transparent;
  height: 100%;
  cursor: pointer;

  &.open {
    background-color: var(--de-popover-bgColor);
    border-right: var(--border-solid);
    border-bottom: 1px solid var(--de-box-bgColor);
    z-index: @quickMenu-zIndex;
  }

  &-popup {
    overflow: inherit !important;
  }

  &-list {
    padding: 10px 0;
    min-width: 180px;
    background-color: var(--de-popover-bgColor);
    border: var(--border-solid);
    border-left: none;
    margin-top: -1px;
    font-size: var(--font-size-sm);
    text-transform: capitalize;

    .ui-icon {
      position: absolute;
      left: 0;
      width: @icon-size;
      top: 0;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    li {
      position: relative;
      cursor: pointer;
      padding: 8px 15px 8px @icon-size;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: var(--main-fc);
      height: 30px;

      &:hover {
        color: var(--primary);
      }
      & > .keys {
        color: #7f7f7f;
      }
      &:hover {
        background-color: var(--de-header-leftSide-menu-hover-bgColor);
      }
      & > span:nth-child(2) {
        &.ui-icon {
          left: auto;
          right: 0;
        }
      }
    }

    .divider {
      margin: 5px 0;
      padding: 0;
      height: 1px;
      border-bottom: 1px solid var(--de-divider-border-color);

      &:hover {
        background-color: inherit;
      }
    }

    &-assets {
      display: flex;
      position: absolute;
      top: 0;
      left: 179px;
      white-space: nowrap;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      padding: 4px 0;
      background-color: var(--de-popover-bgColor);
      border: var(--border-solid);

      > span {
        display: flex;
        width: 100%;
        align-items: center;
        height: 30px;
        padding: 0 8px;
        color: var(--main-fc);

        &:hover {
          background-color: var(--de-header-leftSide-menu-hover-bgColor);
          color: var(--primary);
        }
      }

      .ui-icon {
        position: static;
        height: auto;
        width: 24px;
      }
    }

    &-disabled {
      .@{componentsPrefix}-disabled > li {
        color: var(--btn-primary-disable-bg);

        & > span {
          color: var(--btn-primary-disable-bg);
        }
      }
    }
  }
}
