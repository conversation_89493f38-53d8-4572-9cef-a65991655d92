import { CorsComponent, Icon } from '@weapp/ui';
import { getLabel, memoize } from '@weapp/utils';
import React, {
  ReactNode, useCallback, useMemo, useState,
} from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import { MenuActionType } from './types';

interface MenuListProps extends React.Attributes {
  /** 无权保存、修改配置 */
  saveDisabled?: boolean;
  onAction: (type: MenuActionType) => void;
  renderQuickMenu?: (v: ReactNode) => ReactNode;
}

const MenuList: React.FC<MenuListProps> = (props) => {
  const { renderQuickMenu = (v: ReactNode) => v, saveDisabled = false } = props;
  const [visible, setVisible] = useState(false);
  // TODO 检测变化
  const exit = useCallback(() => window.close(), []);
  const onAction = useMemo(
    () => memoize((type: MenuActionType) => () => {
      if (saveDisabled && type === 'save') return;

      props.onAction(type);
    }),
    [],
  );

  const onMouseEnter = useCallback(() => {
    setVisible(true);
  }, []);

  const onMouseLeave = useCallback(() => {
    setVisible(false);
  }, []);

  return (
    <ul className={`${prefixCls}-quick-menu-list`}>
      {renderQuickMenu(
        <>
          <CorsComponent
            weId={`${props.weId || ''}_ltip1d`}
            app="@weapp/components"
            compName="Disabled"
            className={`${prefixCls}-quick-menu-list-disabled`}
            condition={saveDisabled}
          >
            <li onClick={onAction('save')}>
              <Icon weId="0j89qq" name="Icon-Save-as-picture" />
              <span>{getLabel('97216', '保存')}</span>
              <span className="keys">Ctrl+S</span>
            </li>
          </CorsComponent>
          <li onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave}>
            <Icon weId="jmmdvr" name="Icon-picture-o" />
            <span>{getLabel('54337', '素材库')}</span>
            <Icon weId="qrvckj" name="Icon-Right-arrow01" />
            <When weId={`${props.weId || ''}_teea8j`} condition={visible}>
              <div className={`${prefixCls}-quick-menu-list-assets`}>
                <span onClick={onAction('assets-icon')}>
                  <Icon weId="gzof4b" name="Icon-Upload-pictures" />
                  <span>{getLabel('54338', '图标管理')}</span>
                </span>
                <span onClick={onAction('assets-picture')}>
                  <Icon weId="l1y2pu" name="Icon-Message-picture" />
                  <span>{getLabel('54339', '图片管理')}</span>
                </span>
                <span onClick={onAction('assets-video')}>
                  <Icon weId="lh8wyx" name="Icon-News-video-o" />
                  <span>{getLabel('85180', '视频管理')}</span>
                </span>
              </div>
            </When>
          </li>
          <li onClick={onAction('hotkeys')}>
            <Icon weId="ojdesu" name="Icon-custom38-o" />
            <span>{getLabel('54331', '快捷键')}</span>
          </li>
          <li onClick={exit}>
            <Icon weId="56zvqi" name="Icon-Right-exit" />
            <span>{getLabel('54340', '退出')}</span>
          </li>
        </>,
      )}
    </ul>
  );
};

export default MenuList;
