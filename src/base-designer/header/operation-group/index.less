@import (reference) '../../../style/prefix.less';
@import (reference) '../../../style/var.less';

.@{prefix}-header-operation-more {
  .ui-icon-wrapper.ui-icon-wrapper {
    padding: 13px 8px 14px 8px;
  }
}

.@{prefix}-operation-more-trigger {
  .ui-menu-select-cover-children {
    border-radius: unset !important;
  }

  .ui-menu-select-iconOverlay .ui-icon {
    padding: 13px 8px 14px 8px;
    color: var(--de-icon-color);
    cursor: pointer;

    &:hover {
      background-color: var(--base-white);
    }
  }

  .ui-menu-trigger {
    max-width: 150px;
  }
}
