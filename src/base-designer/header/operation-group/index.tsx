import { corsImport, isString } from '@weapp/utils';
import React from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import splicingPublicUrl from '../../../utils/splicingPublicUrl';
import { InternalDesignerContext } from '../../Context';
import ClientTypes from '../ClientTypes';
import RedoUndoIcons from '../icons/RedoUndo';
import RefreshIcon from '../icons/Refresh';
import SourceCode from '../SourceCode';
import { ActionBtn, HeaderProps } from '../types';
import CustomAction from './CustomAction';
import './index.less';
import MoreActions from './MoreActions';
import SystemAction from './SystemAction';

interface OperationGroupProps extends HeaderProps {
  /** 无权保存、修改配置 */
  changeDisabled: boolean;
}

class OperationGroup extends React.PureComponent<OperationGroupProps> {
  static contextType = InternalDesignerContext;

  state = {
    redirectUrl: '',
  };

  componentDidMount() {
    const { page } = this.context || {};

    corsImport('@weapp/ebdcoms').then(({ getDomainName }) => {
      const domainName = getDomainName();

      const pageUrl = splicingPublicUrl(
        `/mobile/ebdpage/view/${page?.id}?pageScope=${page?.module}`,
      );

      this.setState({
        redirectUrl: `${domainName}${pageUrl}`,
      });
    });
  }

  render() {
    const {
      actionBtns = [],
      showClient = true,
      showUndoRedo = true,
      changeDisabled = false,
      clientType,
      actions = [],
      codeShowTypes,
      showHiddenEcode = true,
      onRefresh,
      onClientChange,
    } = this.props;
    const { redirectUrl } = this.state;

    const actionIds = actions?.filter((action) => {
      const id = isString(action) ? action : (action as ActionBtn).id;

      return id;
    });

    return (
      <div className={`${prefixCls}-header-operation`}>
        {/* 系统按钮 */}
        <When weId={`${this.props.weId || ''}_89kx5o`} condition={showClient !== false}>
          <ClientTypes
            weId={`${this.props.weId || ''}_kj2k3p`}
            value={clientType}
            onChange={onClientChange}
          />
          <span className="divider" />
        </When>

        <When weId={`${this.props.weId || ''}_1szgkt`} condition={showUndoRedo !== false}>
          <RedoUndoIcons weId={`${this.props.weId || ''}_ttztaq`} />
        </When>

        <When weId={`${this.props.weId || ''}_hbfcvq`} condition={onRefresh !== undefined}>
          <RefreshIcon weId={`${this.props.weId || ''}_spxvav`} onRefresh={onRefresh} />
        </When>

        {/* 不支持的团队暂时先按照样式隐藏（方便手动F12放开试用） */}
        <When
          weId={`${this.props.weId || ''}_0vhh03`}
          condition={!actionIds?.includes('ecode') && showHiddenEcode}
        >
          <span className={`${prefixCls}-header-operation-hidden-ecode`}>
            <SourceCode weId={`${this.props.weId || ''}_nzkr43`} codeShowTypes={codeShowTypes} />
          </span>
        </When>

        {actions
          .filter((action) => isString(action) || !action.isMore)
          .map((action) => {
            if (isString(action)) {
              return (
                <SystemAction
                  weId={`${this.props.weId || ''}_fchrsf@${action}`}
                  key={action}
                  id={action}
                  redirectUrl={redirectUrl}
                  templateDisabled={changeDisabled}
                  {...this.props}
                />
              );
            }

            // 自定义按钮
            return (
              <CustomAction
                weId={`${this.props.weId || ''}_1skhjp@${action.id}`}
                key={action.id}
                action={action}
              />
            );
          })}

        {/* 用户自定义按钮 */}
        {actionBtns}
        {/* 更多按钮 */}
        <MoreActions weId={`${this.props.weId || ''}_d875v3`} actions={actions} />
      </div>
    );
  }
}

export default OperationGroup;
