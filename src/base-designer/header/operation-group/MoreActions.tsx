import { CorsComponent, Menu } from '@weapp/ui';
import { isString } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { useCallback, useMemo } from 'react';
import { prefixCls } from '../../../constants';
import { useDesigner } from '../../hooks';
import { ActionBtn, ActionType, systemActions } from '../types';
import SystemAction from './SystemAction';

interface MoreActionsProps extends React.Attributes {
  actions: (ActionType | ActionBtn)[];
}

const MoreActions: React.FC<MoreActionsProps> = (props) => {
  const { events } = useDesigner() || {};

  const moreActions = useMemo(() => {
    const _moreActions = props.actions.filter(
      (action) => !isString(action) && action.isMore,
    ) as ActionBtn[];

    return _moreActions.map((action) => {
      if (systemActions[action.id as ActionType]) {
        return {
          ...action,
          icon: <SystemAction weId={`${props.weId || ''}_wuqsc6@${action.id}`} id={action.id} />,
        };
      }

      return action;
    });
  }, [props.actions]);

  const onMoreClick = useCallback((id: string) => {
    const action = moreActions.find((_action) => _action.id === id)!;
    const { onClick } = action;

    if (!onClick) {
      events?.emit(`${action.id}.action`);
    }

    onClick?.();
  }, []);

  if (!moreActions.length) return null;

  return (
    <Menu
      weId={`${props.weId || ''}_5qlifd`}
      data={moreActions}
      type="select"
      className={`${prefixCls}-header-operation-more`}
      selectType="iconOverlay"
      selectIcon={
        <CorsComponent
          weId={`${props.weId || ''}_r3v4zo`}
          app="@weapp/ebdcoms"
          compName="IconFont"
          name="Icon-more02"
          size="s"
        />
      }
      triggerProps={{
        action: 'click',
        popupPlacement: 'bottomLeft',
        popupClassName: `${prefixCls}-operation-more-trigger`,
      }}
      onItemClick={onMoreClick}
    />
  );
};

export default observer(MoreActions);
