import { CorsComponent } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React from 'react';
import { Case, Switch } from 'react-if';
import SetTheme from '../../../common/theme';
import { InternalDesignerContext } from '../../Context';
import { DataModel } from '../action-button/data-model';
import { Variable } from '../action-button/variable';
import SaveAsTemplate from '../SaveAsTemplate';
import SourceCode from '../SourceCode';
import { HeaderProps } from '../types';

interface SystemActionProps extends HeaderProps {
  id: string;
  /** 禁用存为模板 */
  templateDisabled?: boolean;
  redirectUrl?: string;
}

export default class SystemAction extends React.PureComponent<SystemActionProps> {
  static contextType = InternalDesignerContext;

  render() {
    const { isLayoutChanged = false, page } = this.context || {};
    const {
      id, codeShowTypes, redirectUrl, templateDisabled = false, onThemeChange,
    } = this.props;

    return (
      <Switch weId={`${this.props.weId || ''}_17cqmm`}>
        <Case weId={`${this.props.weId || ''}_9cb38d`} condition={id === 'ecode'}>
          <SourceCode weId={`${this.props.weId || ''}_nzkr43`} codeShowTypes={codeShowTypes} />
        </Case>
        <Case weId={`${this.props.weId || ''}_8ckvgd`} condition={id === 'theme'}>
          <SetTheme
            weId={`${this.props.weId || ''}_jolohn`}
            refTheme={page?.refTheme}
            onThemeChange={onThemeChange}
            module={page?.module}
          />
        </Case>
        <Case weId={`${this.props.weId || ''}_nezm9u`} condition={id === 'saveTemplate'}>
          <SaveAsTemplate
            weId={`${this.props.weId || ''}_nc0kdi`}
            isLayoutChanged={isLayoutChanged}
            disabled={templateDisabled}
          />
        </Case>
        <Case weId={`${this.props.weId || ''}_t73pdi`} condition={id === 'QRCode'}>
          <CorsComponent
            weId={`${this.props.weId || ''}_dwy4yp`}
            app="@weapp/ebdcoms"
            compName="QRCode"
            redirectUrl={redirectUrl}
            pageId={page?.id}
          >
            <CorsComponent
              app="@weapp/ebdcoms"
              compName="IconFont"
              weId={`${this.props.weId || ''}_c05bjz`}
              name="Icon-QR-code-o"
              size="s"
              title={getLabel('54531', '二维码')}
            />
          </CorsComponent>
        </Case>
        <Case weId={`${this.props.weId || ''}_aw9mck`} condition={id === 'variable'}>
          <Variable weId={`${this.props.weId || ''}_ps2cbe`} />
        </Case>
        <Case weId={`${this.props.weId || ''}_q2ehyr`} condition={id === 'dataModel'}>
          <DataModel weId={`${this.props.weId || ''}_al5l1e`} />
        </Case>
      </Switch>
    );
  }
}
