import { CorsComponent } from '@weapp/ui';
import { isString } from '@weapp/utils';
import React from 'react';
import { ActionBtn } from '../types';

interface CustomActionsProps extends React.Attributes {
  action: ActionBtn;
}

const CustomAction: React.FC<CustomActionsProps> = (props) => {
  const {
    id, icon, content, onClick,
  } = props.action;

  if (isString(icon)) {
    return (
      <CorsComponent
        weId={`${props.weId || ''}_2k5yjb@${id}`}
        key={id}
        app="@weapp/ebdcoms"
        compName="IconFont"
        name={icon}
        size="xs"
        title={content}
        onClick={onClick}
      />
    );
  }

  return React.cloneElement(icon as any, {
    key: id,
    onClick,
  });
};

export default CustomAction;
