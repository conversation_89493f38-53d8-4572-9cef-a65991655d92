@import (reference) '../../style/prefix.less';
@import (reference) '../../style/var.less';

@icon-padding: 14px 9px;
@icon-size: 16px;
@left-side-width: 240px;
@left-side-width-wider: 310px;
@menu-width: 40px;
@menu-height: 40px;
@menu-icon-size: 14px;
@tab-height: 36px;

.@{prefix}-header {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  // 隐藏右滑页面头部的阴影效果
  z-index: calc(var(--dialog-draw-zindex) + 1);
  height: @header-h;
  background-color: var(--de-box-bgColor);
  border-bottom: 1px solid var(--de-box-lineColor);

  svg {
    color: var(--de-icon-color);
  }

  .icon-menu {
    height: 13px;
  }

  .deicon:not(.@{prefix}-logo):hover svg {
    color: var(--main-fc);
  }

  & > div {
    top: 0px;
    display: flex;
    align-items: center;
    height: 100%;
  }

  &-leftside {
    .ui-menu-select-cover-children,
    .ui-menu-select-cover-outer {
      width: @menu-width;
      text-align: center;
      line-height: @header-h;
    }

    .@{prefix}-quick-menu {
      height: 100%;
    }

    &-app-name {
      color: var(--de-icon-color);
    }

    &-ebuilder-icon {
      height: 18px;
      color: var(--de-icon-color);
      font-weight: bolder;
      font-size: 16px;
      cursor: default;
      padding-left: 5px;

      &:hover {
        color: var(--main-fc);
      }
    }
  }

  &-center {
    position: absolute;
    left: @left-side-width;
    right: @left-side-width;
  }

  &-operation {
    display: inline-flex;
    align-items: center;
    height: 100%;

    &-hidden-ecode {
      display: none !important;

      .ui-icon {
        height: 100%;
      }
    }

    // 保存icon 单独调整为18px
    > span:first-child {
      > span {
        padding: 14px 9px;
      }
    }

    span {
      display: inline-block;
      height: 100%;
    }

    .deicon,
    .ui-icon-wrapper {
      padding: @icon-padding;
      color: var(--de-icon-color);
      cursor: pointer;

      &:hover {
        background-color: var(--de-active-color);
      }

      &:hover svg {
        color: var(--main-fc);
      }
    }

    .ui-icon.disabled {
      opacity: 0.7;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
      }

      &:hover svg {
        color: var(--de-icon-color);
      }
    }

    .@{prefix}-save-loading {
      display: flex;
      padding: 0 9px;
      width: 34px;
      height: 40px;
      align-items: center;
      justify-content: center;
    }

    .divider {
      height: 20px;
      width: 1px;
      background: #eee;
      margin: 0 8px;
    }
  }

  &-client-type {
    display: inline-flex;
    margin: 0 auto;
    height: 100%;

    & > div {
      display: inline-flex;
      cursor: pointer;
      text-align: center;
      height: 100%;

      > span {
        height: 100%;
        > span {
          height: 100%;
        }
      }

      .ui-icon.deicon:hover {
        svg {
          color: var(--primary);
        }

        background-color: var(--de-active-color);
      }

      &.active {
        svg {
          color: var(--primary);
        }
      }

      .deicon {
        padding: 14px 9px;
      }
    }
  }

  &-title {
    top: 0px;
    line-height: @header-h;
    display: inline-block;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: var(--font-size-12);
    user-select: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-word;
    padding: 0 10px;
  }

  &-rightside {
    display: inline-flex;
    position: absolute;
    right: 5px;
    z-index: @rs-zIndex;

    & > div {
      display: flex;
    }

    .deicon {
      padding: @icon-padding;
      cursor: pointer;

      &:hover {
        background-color: var(--de-active-color);
      }
    }

    .divider {
      height: 20px;
      width: 1px;
      background: #eee;
      margin: 0 8px;
    }

    > span {
      height: 100%;
      > span {
        height: 100%;
      }
    }
  }

  &-leftside-menu {
    &-extra {
      width: 36px;
    }

    .ui-trigger-popupInner &-extra {
      background: var(--input-bg);
    }

    .ui-trigger-popupInner {
      .ui-menu-trigger {
        background: var(--input-bg);
      }

      .ui-menu-list-subitem-title {
        border-bottom: none;
      }

      .ui-menu-list-subitem-title:hover {
        background: var(--de-header-leftSide-menu-hover-bgColor);

        .ui-icon {
          svg {
            color: var(--primary);
          }
        }
      }
    }
  }

  &-save {
    min-width: 64px;
    display: flex;
    align-items: center;
    margin: 0 6px 0 6px;

    svg {
      color: var(--btn-font-color);
    }

    .ui-spin {
      display: flex;
    }

    & > span:nth-child(2) {
      margin-left: 2px;
    }
  }

  &-preview {
    min-width: 54px;
    margin: 0 6px 0 2px;
  }

  &.@{prefix}-header-wider {
    .@{prefix}-header-center {
      left: @left-side-width-wider;
    }
  }
}

.@{prefix}-save-loading {
  .ui-spin-dot {
    width: 16px;
    height: 16px;
  }

  .loading-bottom-layer.loading-bottom-layer {
    width: 13px;
    height: 13px;
    margin: 1px;
  }

  .loading-left.loading-left,
  .loading-right.loading-right {
    width: calc(50% + 4px);
    height: calc(100% + 6px);
  }

  .loading-left-progress.loading-left-progress,
  .loading-right-progress.loading-right-progress {
    width: 10px;
    height: 10px;
    top: 3px;
  }

  .loading-left-progress.loading-left-progress {
    left: 2px;
  }
}

.@{prefix}-header-save-preview {
  .ui-btn {
    padding: 6px 10px;
  }
}

/** 页面镜像反转之后，特殊样式处理 */
body[page-rtl='true'] {
  .@{prefix}-header-title.ui-rtl {
    transform: translateX(-50%) scaleX(-1);
  }

  .@{prefix}-quick-menu-list > li {
    transform: none;
    direction: ltr;

    &:hover {
      & > span.ui-rtl {
        color: var(--primary);
      }

      & > span:nth-of-type(3n).ui-rtl {
        color: #7f7f7f;
      }
    }

    & > span.ui-rtl {
      color: var(--main-fc);
    }

    & > span:nth-of-type(3n).ui-rtl {
      color: #7f7f7f;
    }

    & > span:nth-of-type(3n) {
      &.ui-icon {
        left: auto;
        right: 0;
      }
    }
  }
}
