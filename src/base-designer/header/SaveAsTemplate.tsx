import { observer } from 'mobx-react';
import React, { useCallback } from 'react';
import { SaveTemplate } from '../../common';
import { EVENT_PAGE_TEMP_RELOAD, prefixCls } from '../../constants';
import { useDesigner } from '../hooks';

interface SaveAsTemplateProps extends React.Attributes {
  isLayoutChanged: boolean;
  /** 无权保存、修改配置 */
  disabled?: boolean;
}

const SaveAsTemplate: React.FC<SaveAsTemplateProps> = (props) => {
  const { isLayoutChanged, disabled = false } = props;
  const {
    page, comServicePath, singleClient, events,
  } = useDesigner();

  const onOk = useCallback(() => {
    events.emit(EVENT_PAGE_TEMP_RELOAD);
  }, []);

  return (
    <SaveTemplate
      weId={`${props.weId || ''}_p013jz`}
      isLayoutChanged={isLayoutChanged}
      disabled={disabled}
      pageClassName={`${prefixCls}-paper`}
      page={page}
      comServicePath={comServicePath}
      singleClient={singleClient}
      onOk={onOk}
    />
  );
};

export default observer(SaveAsTemplate);
