import {
  But<PERSON>, Cors<PERSON>omponent, Dialog, Spin,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { PureComponent } from 'react';
import { prefixCls } from '../../../constants';
import { DesignerContext } from '../../Context';
import { actions } from '../../decorator';

interface RefreshIconProps {
  onRefresh?: () => Promise<any> | void;
  isLayoutChanged?: boolean;
}
interface RefreshIconStates {
  loading: boolean;
}

@actions.autobind
class RefreshIcon extends PureComponent<RefreshIconProps, RefreshIconStates> {
  static contextType = DesignerContext;

  state = {
    loading: false,
  };

  instance: any;

  closeRefresh = () => {
    this.instance?.onClose?.();
  };

  okRefresh = () => {
    this.instance?.onOk?.();
  };

  @actions.method('actions')
  refresh = () => {
    const { isLayoutChanged: _isLayoutChanged } = this.props;
    const { isLayoutChanged } = this.context;
    if (!isLayoutChanged && !_isLayoutChanged) {
      this.doRefresh();
    } else {
      Dialog.confirm({
        content: getLabel('105434', '刷新设计器区域会丢失未保存的改动，确定刷新吗？'),
        mask: true,
        onOk: () => {
          this.doRefresh();
        },
        getInstance: (el: any) => {
          this.instance = el;
        },
        footer: (
          <>
            <Button
              weId={`${this.props.weId || ''}_jekodp`}
              onClick={this.okRefresh}
              type="primary"
            >
              {getLabel('91791', '刷新')}
            </Button>
            <Button weId={`${this.props.weId || ''}_hczv54`} onClick={this.closeRefresh}>
              {getLabel('97217', '取消')}
            </Button>
          </>
        ),
      });
    }
  };

  doRefresh = async () => {
    const { onRefresh } = this.props;

    this.setState({ loading: true });

    await onRefresh?.();

    this.setState({ loading: false });
  };

  render() {
    if (this.state.loading) {
      return (
        <Spin
          weId={`${this.props.weId || ''}_cwk640`}
          size="small"
          className={`${prefixCls}-save-loading`}
        />
      );
    }

    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_1x8voo`}
        app="@weapp/ebdcoms"
        compName="IconFont"
        type="refresh"
        size="s"
        title={getLabel('91791', '刷新')}
        onClick={this.refresh}
      />
    );
  }
}

export default RefreshIcon;
