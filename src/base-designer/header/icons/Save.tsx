import {
  <PERSON><PERSON>, Cors<PERSON><PERSON>ponent, Icon, Spin,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React from 'react';
import { Else, If, Then } from 'react-if';
import { prefixCls } from '../../../constants';
import { DesignerContext } from '../../Context';
import { actions } from '../../decorator';
import { cachePageLayoutChanged } from '../../utils/cachePageLayoutChanged';

interface SaveIconProps {
  visible: boolean;
  /** 无权保存、修改配置 */
  disabled?: boolean;
}

interface SaveIconStates {
  loading: boolean;
}

@actions.autobind
class SaveIcon extends React.PureComponent<SaveIconProps, SaveIconStates> {
  static contextType = DesignerContext;

  state = {
    loading: false,
  };

  @actions.method('actions')
  save = async () => {
    const { disabled = false } = this.props;
    const {
      save, getLayoutDatas, page, setPrevLayoutDatas, onSave, channel,
    } = this.context;

    if (this.state.loading || disabled) return;

    this.setState({ loading: true });

    return new Promise((resolve, reject) => {
      save()
        .then(async (res: any) => {
          try {
            await onSave?.(res);

            const newLayoutDatas = getLayoutDatas();

            setPrevLayoutDatas(newLayoutDatas);

            // 保存后，发送最新的广播数据
            channel?.postMessage({
              layoutDatas: JSON.stringify(newLayoutDatas),
              isSaved: true,
            });

            cachePageLayoutChanged.delete(page.id);

            /** 外部手动掉 store.actions.save()，需要返回结果 */
            this.setState({ loading: false });
            resolve(newLayoutDatas);
          } catch (error) {
            this.setState({ loading: false });
          }
        })
        .catch((error: any) => {
          this.setState({ loading: false });
          reject(error);
        });
    });
  };

  render() {
    const { visible = true, disabled = false } = this.props;

    if (!visible) {
      return null;
    }

    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_i5zrxb`}
        app="@weapp/components"
        compName="Disabled"
        hideMask
        condition={disabled}
      >
        <Button
          weId={`${this.props.weId || ''}_y1mp64`}
          type="primary"
          disabled={this.state.loading}
          className={`${prefixCls}-header-save`}
          onClick={this.save}
        >
          <If weId={`${this.props.weId || ''}_edbm1n`} condition={this.state.loading}>
            <Then weId={`${this.props.weId || ''}_wrx2h0`}>
              <Spin
                weId={`${this.props.weId || ''}_cwk640`}
                size="small"
                className={`${prefixCls}-save-loading`}
              />
            </Then>
            <Else weId={`${this.props.weId || ''}_s0vwrv`}>
              <Icon weId={`${this.props.weId || ''}_98hisn`} size="s" name="Icon-Save-as-picture" />
            </Else>
          </If>
          <span>{getLabel('97216', '保存')}</span>
        </Button>
      </CorsComponent>
    );
  }
}

export default SaveIcon;
