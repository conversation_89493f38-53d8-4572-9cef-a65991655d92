import { CorsComponent } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import setTimeoutOnce from '../../../utils/setTimeoutOnce';
import { DesignerContext } from '../../Context';

@observer
export default class RedoUndo extends PureComponent<{}> {
  static contextType = DesignerContext;

  unredo = (type: string) => () => {
    const store = this.context;

    if ((type === 'undo' && !store?.canUndo) || (type === 'redo' && !store?.canRedo)) return;

    store?.[type]();
    // 异步触发unredo事件，方便获取到最新数据
    // setNodes 有debounce 20ms 延迟
    setTimeoutOnce(() => {
      store?.events.emit('unredo', type);
    }, 25);
  };

  render() {
    const store = this.context;
    const icons = [
      {
        name: 'Icon-Top-left',
        title: getLabel('53837', '撤销'),
        onClick: this.unredo('undo'),
        className: classnames({ disabled: !store?.canUndo }),
      },
      {
        name: 'Icon-Top-right',
        title: getLabel('53838', '重做'),
        onClick: this.unredo('redo'),
        className: classnames({ disabled: !store?.canRedo }),
      },
    ];

    return icons.map((icon) => (
      <CorsComponent
        weId={`${this.props.weId || ''}_1x8voo`}
        key={icon.name}
        app="@weapp/ebdcoms"
        compName="IconFont"
        size="s"
        {...icon}
      />
    ));
  }
}
