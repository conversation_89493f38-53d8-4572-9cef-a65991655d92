import { Button, Dialog } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { PureComponent } from 'react';
import { prefixCls } from '../../../constants';
import { DesignerContext } from '../../Context';
import { cachePageLayoutChanged } from '../../utils/cachePageLayoutChanged';

interface PreviewIconProps extends React.Attributes {
  onPreview?: () => void;
  isLayoutChanged: boolean;
  ignoreLayoutChanged?: boolean;
}
export default class PreviewIcon extends PureComponent<PreviewIconProps> {
  static contextType = DesignerContext;

  onClick = () => {
    const { onPreview, isLayoutChanged: _isLayoutChanged, ignoreLayoutChanged } = this.props;
    const { isLayoutChanged, page } = this.context;

    if (onPreview) {
      if (isLayoutChanged || _isLayoutChanged) {
        if (ignoreLayoutChanged) {
          Dialog.message({
            type: 'info',
            content: getLabel('294346', '布局有改动，请及时保存'),
          });

          cachePageLayoutChanged.add(page.id);

          onPreview();
        } else {
          Dialog.message({
            type: 'info',
            content: getLabel('80835', '布局有改动，请先保存后再进行预览'),
          });
        }
      } else {
        onPreview();
      }
    }
  };

  render() {
    return (
      <Button
        weId={`${this.props.weId || ''}_y1mp64`}
        className={`${prefixCls}-header-preview`}
        onClick={this.onClick}
      >
        <span>{getLabel('53842', '预览')}</span>
      </Button>
    );
  }
}
