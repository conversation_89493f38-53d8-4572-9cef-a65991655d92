import { observer } from 'mobx-react';
import React, { ComponentType } from 'react';
import { SourceCode } from '../../common/sourcecode';
import { CodeType, SourceCodeProps } from '../../common/sourcecode/types';
import { useDesigner } from '../hooks';
import useInternal from '../hooks/useInternal';
import { ActionButton } from './action-button';

interface CodeProps extends React.Attributes {
  codeShowTypes?: CodeType[];
}

const Code = (props: CodeProps) => {
  const {
    page, clientType, layoutStore, actions,
  } = useDesigner();
  const { resolver } = useInternal()!;
  const { sourceCode, updateSourceCode } = layoutStore || {};

  let Panel = SourceCode;

  if (resolver?.SourceCode) {
    Panel = resolver.SourceCode as unknown as ComponentType<SourceCodeProps>;
  }

  // 源码功能服务降级
  const isDisplay = window.TEAMS?.isPurchasedModule?.('ecode');

  return (
    <ActionButton
      weId={`${props.weId || ''}_p013jz`}
      type="sourcecode"
      code={sourceCode}
      clientType={clientType}
      disabled={!isDisplay}
      panel={
        <Panel
          weId={`${props.weId || ''}_eyuanl`}
          codeShowTypes={props.codeShowTypes}
          code={sourceCode}
          pageId={page.id}
          clientType={clientType}
          onChange={updateSourceCode}
          onSave={actions.save}
          moduleType={page.module}
        />
      }
    />
  );
};

export default observer(Code);
