import React, { ReactNode } from 'react';
import { CodeType } from '../../common/sourcecode/types';
import { ClientType } from '../../types';

interface ClientProps {
  clientType?: ClientType;
  onClientChange?: (client: ClientType) => void;
}

export interface HeaderProps extends React.Attributes, ClientProps {
  title?: ReactNode;
  /** 自定义扩展按钮 */
  actionBtns?: ReactNode[];
  showClient?: boolean;
  showPreview?: boolean;
  showSave?: boolean;
  showAvatar?: boolean;
  showTips?: boolean;
  showUndoRedo?: boolean;
  helpLink?: string;
  rightSide?: ReactNode;
  showHiddenEcode?: boolean;
  /** 系统按钮配置显示 */
  actions?: (ActionType | ActionBtn)[];
  renderQuickMenu?: (v: ReactNode) => ReactNode;
  /** 源码类型显示 CSS JS */
  codeShowTypes?: CodeType[];
  /** 忽略预览前的校验 */
  ignoreLayoutChanged?: boolean;
  /** 无权保存、修改配置 */
  saveDisabled?: boolean;
  onPreview?: () => void;
  renderLeft?: () => ReactNode;
  renderLeftIconName?: () => ReactNode;
  onRefresh?: () => Promise<any> | void;
  onThemeChange?: (themeid: string) => void;
  renderButtonGroup?: (buttonGroup: React.ReactElement) => ReactNode;
}

export interface LeftSideProps extends HeaderProps {
  /** 无权保存、修改配置 */
  changeDisabled: boolean;
}

export interface InternalHeaderProps extends HeaderProps, ClientProps {
  title?: ReactNode;
  leftSide?: ReactNode;
  actionBtns?: ReactNode[];
  renderQuickMenu?: (v: ReactNode) => ReactNode;
  showClient?: boolean;
  /** 无权保存、修改配置 */
  changeDisabled?: boolean;
  /** 设计器数据还未加载时，隐藏所有操作按钮 */
  showOperations?: boolean;
  renderLeft?: () => ReactNode;
}

export const systemActions = {
  ecode: 'ecode',
  saveTemplate: 'saveTemplate',
  QRCode: 'QRCode',
  theme: 'theme',
  /** 页面变量 */
  variable: 'variable',
  /** 模型 */
  dataModel: 'dataModel',
};

export type ActionType = keyof typeof systemActions;

export type ActionBtn = {
  id: string;
  icon?: string | ReactNode;
  content?: string;
  isMore?: boolean;
  onClick?: () => void;
};
