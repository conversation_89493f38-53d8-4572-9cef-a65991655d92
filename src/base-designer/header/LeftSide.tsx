import React from 'react';
import { Else, If, Then } from 'react-if';
import { prefixCls } from '../../constants';
import QuickMenu from './quick-menu';
import { LeftSideProps } from './types';

// 左侧
const LeftSide: React.FC<LeftSideProps> = (props) => {
  const {
    renderLeft,
    renderQuickMenu,
    renderLeftIconName,
    children,
    changeDisabled = false,
  } = props;

  if (children) {
    return <div className={`${prefixCls}-header-leftside`}>{children}</div>;
  }

  return (
    <div className={`${prefixCls}-header-leftside`}>
      <If weId={`${props.weId || ''}_x8xzny`} condition={!!renderLeft}>
        <Then weId={`${props.weId || ''}_cinohn`}>{renderLeft?.()}</Then>
        <Else weId={`${props.weId || ''}_wlwesd`}>
          <>
            <QuickMenu
              weId="brh038"
              changeDisabled={changeDisabled}
              renderQuickMenu={renderQuickMenu}
            />
            <span className={`${prefixCls}-header-leftside-ebuilder-icon`}>
              {(renderLeftIconName && renderLeftIconName()) || 'e-builder'}
            </span>
          </>
        </Else>
      </If>
    </div>
  );
};

export default LeftSide;
