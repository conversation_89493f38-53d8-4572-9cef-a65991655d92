@import (reference) '../../../../style/prefix.less';

.@{prefix}-hotkeysul {
  padding: 10px 0;
  min-width: 180px;
  border-left: none;
  margin-top: -1px;

  li {
    height: 40px;
    width: 200px;
    position: relative;
    margin-left: 20px;
    border-bottom: 1px solid #ddd;
    padding-top: 5px;
    display: inline-block;
    align-items: center;
    justify-content: space-between;
    color: var(--main-fc);

    div {
      display: flex;
      align-items: center;
      justify-content: space-between;

      :nth-child(2) {
        font-size: 14px;
        line-height: 36px;

        span {
          margin: 0 0.25em;
          padding: 0 0.25em;
          min-width: 1.5em;
          font-family: inherit;
          line-height: 1.6;
          text-align: center;
          border: 1px solid #dddddd;
          border-bottom-width: 2px;
          border-radius: 2px;
          background-color: #fff;
        }

        :nth-child(1) {
          margin-right: 5px;
        }

        :nth-child(2) {
          margin-left: 5px;
        }
      }
    }
    span {
      color: #7f7f7f;
    }
  }
}
