import { Dialog } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { PureComponent } from 'react';
import { prefixCls } from '../../../../constants';
import { DesignerContext } from '../../../Context';
import { DialogProps } from '../../quick-menu/types';
import './index.less';

export default class HotKeysDialog extends PureComponent<DialogProps> {
  static contextType = DesignerContext;

  render() {
    const { visible, onClose } = this.props;

    return (
      <Dialog
        weId={`${this.props.weId || ''}_lay63r`}
        visible={visible}
        onClose={onClose}
        title={getLabel('54331', '快捷键')}
        width={500}
        closable
        destroyOnClose
        draggable
        mask
        maskClosable
        resize
        icon="Icon-e-builder-o"
      >
        <ul className={`${prefixCls}-hotkeysul`}>
          <li>
            <div>
              <span>{getLabel('97216', '保存')}</span>
              <span>
                <span>Ctrl</span>+<span>S</span>
              </span>
            </div>
          </li>
          <li>
            <div>
              <span>{getLabel('53837', '撤销')}</span>
              <span>
                <span>Ctrl</span>+<span>Z</span>
              </span>
            </div>
          </li>
          <li>
            <div>
              <span>{getLabel('54332', '还原')}</span>
              <span>
                <span>Ctrl</span>+<span>Y</span>
              </span>
            </div>
          </li>
          <li>
            <div>
              <span>{getLabel('116424', '删除选中组件')}</span>
              <span>
                <span>Delete</span>
              </span>
            </div>
          </li>
          <li>
            <div>
              <span>{getLabel('54010', '复制')}</span>
              <span>
                <span>Ctrl</span>+<span>C</span>
              </span>
            </div>
          </li>
          <li>
            <div>
              <span>{getLabel('118005', '粘贴')}</span>
              <span>
                <span>Ctrl</span>+<span>V</span>
              </span>
            </div>
          </li>
        </ul>
      </Dialog>
    );
  }
}
