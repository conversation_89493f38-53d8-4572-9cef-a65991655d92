import { CorsComponent, Help } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { ReactNode } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../constants';
import { InternalDesignerContext } from '../Context';
import PreviewIcon from './icons/Preview';
import SaveIcon from './icons/Save';
import { HeaderProps } from './types';

const defaultRenderButtonGroup = (v: ReactNode) => v;

export default class RightSide extends React.PureComponent<HeaderProps> {
  static contextType = InternalDesignerContext;

  render() {
    const {
      showPreview = true,
      showSave = true,
      showAvatar = true,
      showTips = true,
      ignoreLayoutChanged,
      children,
      helpLink = '',
      saveDisabled = false,
      onPreview,
    } = this.props;
    // 兼容错误命名renderButtonGrouup,等调用方调整完成后取消兼容
    const renderButtonGroup = this.props?.renderButtonGroup
      || (this.props as any)?.renderButtonGrouup
      || defaultRenderButtonGroup;

    if (children !== undefined) {
      return <div className={`${prefixCls}-header-rightside`}>{children}</div>;
    }

    const { isLayoutChanged = false } = this.context || {};

    return (
      <div className={`${prefixCls}-header-rightside`}>
        {renderButtonGroup(
          <>
            <div className={`${prefixCls}-header-save-preview`}>
              <SaveIcon
                weId={`${this.props.weId || ''}_330l5p`}
                visible={showSave}
                disabled={saveDisabled}
              />
              <When weId={`${this.props.weId || ''}_kxzxz7`} condition={showPreview}>
                {/* 预览, 编辑，显示，新建布局没有预览 */}
                <PreviewIcon
                  weId={`${this.props.weId || ''}_xlp3zo`}
                  onPreview={onPreview}
                  isLayoutChanged={isLayoutChanged}
                  ignoreLayoutChanged={ignoreLayoutChanged}
                />
              </When>
            </div>
            <When weId={`${this.props.weId || ''}_2ddanj`} condition={showTips}>
              <Help weId={`${this.props.weId || ''}_840m1h`} helpUrl={helpLink}>
                <CorsComponent
                  app="@weapp/ebdcoms"
                  compName="IconFont"
                  weId={`${this.props.weId || ''}_d7iwvg`}
                  title={getLabel('153710', '点击查看帮助')}
                  type="help"
                  size="s"
                />
              </Help>
            </When>
            {/* avatar */}
            <When weId={`${this.props.weId || ''}_2ddanj`} condition={showAvatar}>
              <CorsComponent
                weId={`${this.props.weId || ''}_v1eqhx`}
                app="@weapp/ebdcoms"
                compName="UserAvatar"
              />
            </When>
          </>,
        )}
      </div>
    );
  }
}
