import React, { ComponentType } from 'react';
import Loadable from '../../../../common/loadable';

export { Variable, VariableDialog };

const Variable = Loadable({
  name: 'Variable',
  loader: () => import(
    /* webpackChunkName: "de_base_designer" */
    './Variable'
  ),
}) as ComponentType<React.Attributes>;

const VariableDialog = Loadable({
  name: 'VariableDialog',
  loader: () => import(
    /* webpackChunkName: "de_variabel_dialog" */
    './VariableDialog'
  ),
}) as ComponentType<any>;

Variable.displayName = 'Variable';

VariableDialog.displayName = 'VariableDialog';

export default {};
