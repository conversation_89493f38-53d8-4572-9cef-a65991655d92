@import (reference) '../../../../style/prefix.less';

.@{prefix}-variable-dialog {
  &-top {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;

    & > span {
      font-size: 12px;
      font-weight: 400;
    }

    &-icon {
      color: var(--primary);
      margin-left: 5px;
      cursor: pointer;
    }

    &-varKey {
      display: flex;
      align-items: center;
      justify-content: start;

      & > span:first-child {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      & > span:last-child {
        margin-left: 4px;
        cursor: pointer;
        color: var(--btn-link-fc);
      }
    }

    &-actions {
      display: flex;
      align-items: center;

      & > a {
        margin-right: 6px;
      }
    }
  }

  &-confirm-batch-delete {
    display: flex;
    flex-direction: column;

    & > span:first-child {
      color: red;
      margin-bottom: 6px;
      width: 250px;
      word-wrap: break-word;
    }
  }
}

.@{prefix}-variable-form {
  background: var(--base-white);
  border: 1px solid var(--border-color);

  .@{prefix}-form {
    .ui-form-row-first {
      padding-top: 0px;
    }

    .ui-locale {
      width: 100%;
    }

    .ui-input-wrap {
      max-width: unset;
    }

    .ui-formItem {
      padding: var(--form-item-padding-module);
    }

    .ui-form-row {
      .ui-form-col {
        border-bottom: var(--form-item-border-module);
      }
    }

    .ui-form-row:last-of-type .ui-form-col {
      border-bottom: 0px;
    }
  }
}
