import {
  <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ponent, Dialog, Table,
} from '@weapp/ui';
import {
  copy, getLabel, isEmpty, isUndefined,
} from '@weapp/utils';
import { observer } from 'mobx-react';
import React, {
  useCallback, useMemo, useRef, useState,
} from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../../constants';
import {
  getLocaleInputData,
  returnTypeEnum,
  setLocaleInputData,
} from '../../../../logic-composer-designer/components/common/locale-input/utils';
import { LogicVariableForm } from '../../../../logic-composer-designer/components/panels/variable';
import { LogicVariable } from '../../../../logic-composer-designer/types';
import ebdcoms from '../../../../utils/ebdcoms';
import { useDesigner } from '../../../hooks';
import { compatibleModelDatas } from '../data-model/utils';
import uuid from '../data-model/uuid';
import {
  Base, DataSet, dialogCls, VariableScope,
} from './constants';
import './index.less';
import { LogicVariableInfo } from './types';
import { getVariableTypeText } from './utils';

const { message } = Dialog;

interface VariableDialogContentProps extends React.Attributes {
  data?: LogicVariableInfo[];
  scope?: VariableScope;
  onChange: (data?: LogicVariableInfo[]) => void;
}

// 将后端的数据转换成前端组件所需数据
const formatVarDataForBackend = (data?: LogicVariableInfo) => {
  const {
    desc, descLabelId, name, ...restData
  } = data || {};
  return {
    ...restData,
    name: name || '',
    desc: setLocaleInputData(desc || '', descLabelId || ''),
  };
};
// 将前端的数据转换成后端组件所需数据
const formatVarDataForFrontend = (data: LogicVariable.DataType): LogicVariableInfo => {
  const { desc: _desc, name, ...restData } = data || {};
  const { nameAlias: desc, nameAliasLabelId } = getLocaleInputData(
    _desc,
    returnTypeEnum.ALL,
  ) as any;
  return {
    ...restData,
    name: name || '',
    desc,
    descLabelId: nameAliasLabelId,
  };
};
const VariableDialogContent: React.FC<VariableDialogContentProps> = (props) => {
  const { data = [], scope, onChange } = props;
  const [selectedData, setSelectData] = useState<LogicVariableInfo | undefined>();
  const [detailDlgType, setDetailDlgType] = useState<string>('add');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [pageNum, setPageNum] = useState<number>(1);
  const [pageSize, setPageize] = useState<number>(5);
  const [detailVisible, setDetailVisible] = useState(false);
  const logicVariableFormRef = useRef<any>();
  const { page } = useDesigner();
  const modelData = useMemo(
    () => compatibleModelDatas(ebdcoms.excu('getEbParams', 'pageParams', page.id)?.pageModel || []),
    [page.id],
  );

  const customVariableType = useCallback(
    (variableType: any) => variableType.filter((variableTypeData: any) => {
      const { id, children } = variableTypeData;

      if (id === Base) {
        variableTypeData.children = children.filter(({ id: _id }: any) => !['DOM'].includes(_id));
      }

      return id !== DataSet;
    }),
    [],
  );

  const renderListButton = useCallback(() => null, []);

  const updatePageNum = useCallback(
    (_data: LogicVariableInfo[]) => {
      const total = _data.length;
      const needUpdate = total % pageSize === 0 && pageNum > Math.floor(total / pageSize);

      if (needUpdate) {
        setPageNum(Math.floor(total / pageSize));
      }
    },
    [pageNum, pageSize],
  );

  /** 编辑 */
  const editVariableInfo = (_data: LogicVariableInfo) => () => {
    setDetailDlgType('edit');
    setSelectData(_data);
    setDetailVisible(true);
  };

  /** 删除 */
  const deleteVariableInfo = useCallback(
    (_data: LogicVariableInfo) => {
      const _tableData = data.filter((item: LogicVariableInfo) => item.id !== _data.id);

      onChange(_tableData);
      updatePageNum(_tableData);
    },
    [data],
  );

  /** 删除确认框 */
  const confirmDelete = (_data: LogicVariableInfo) => () => {
    Dialog.confirm({
      content: getLabel('283913', '请确认是否要删除该条变量？'),
      onOk: () => deleteVariableInfo(_data),
    });
  };

  /** 新增 */
  const onAddVeriable = useCallback(() => {
    setDetailDlgType('add');

    setSelectData({
      id: uuid.id,
      name: '',
      type: { type: 'STRING', refId: 'STRING' },
      desc: '',
      defVal: '',
    });

    setDetailVisible(true);
  }, []);

  /** 复制标识 */
  const onCopyVarKey = (content: string) => () => {
    const res = copy(content);
    if (res) {
      message({
        type: 'success',
        content: getLabel('57856', '复制成功'),
      });
    }
  };

  const variableColumns = () => [
    {
      title: getLabel('260116', '变量标识'),
      dataIndex: 'name',
      isPrimaryKey: true,
      bodyRender: (_data: LogicVariableInfo) => {
        const content = _data.name;

        return (
          <div className={`${dialogCls}-top-varKey`}>
            <span>{content}</span>
            <span title="">
              <CorsComponent
                weId={`${props.weId || ''}_iqvukn`}
                app="@weapp/ebdcoms"
                compName="IconFont"
                name="Icon-copy"
                title={getLabel('287891', '复制标识')}
                onClick={onCopyVarKey(content)}
              />
            </span>
          </div>
        );
      },
    },
    {
      title: getLabel('260119', '变量类型'),
      dataIndex: 'type',
      bodyRender: (_data: LogicVariableInfo) => {
        const variableTypeText = getVariableTypeText(_data.type.type);

        return <span>{variableTypeText}</span>;
      },
    },
    {
      title: getLabel('318488', '变量名称'),
      dataIndex: 'desc',
    },
    {
      title: getLabel('85220', '默认值'),
      dataIndex: 'defVal',
      bodyRender: (_data: LogicVariableInfo) => (isUndefined(_data?.defVal) ? '' : String(_data?.defVal)),
    },
    {
      title: getLabel('54342', '操作'),
      dataIndex: 'action',
      bodyRender: (_data: LogicVariableInfo) => (
        <div className={`${dialogCls}-top-actions`}>
          <Button
            weId={`${props.weId || ''}_dmo54e`}
            key="edit"
            type="link"
            onClick={editVariableInfo(_data)}
          >
            {getLabel('54009', '编辑')}
          </Button>
          <Button
            weId={`${props.weId || ''}_5bp06u`}
            key="delete"
            type="link"
            onClick={confirmDelete(_data)}
          >
            {getLabel('53951', '删除')}
          </Button>
        </div>
      ),
    },
  ];

  const onTableSelect = (selectedkeys: string[]) => {
    setSelectedRowKeys(selectedkeys);
  };

  const onPageSizeChange = (_pageNum: number, _pageSize: number) => {
    setPageNum(_pageNum);
    setPageize(_pageSize);
  };

  /** 更新变量信息 */
  const onSaveDetailDlg = useCallback(async () => {
    logicVariableFormRef.current.validate().then((res: any) => {
      if (!isEmpty(res.errors)) return;

      const value = {
        ...selectedData,
        ...formatVarDataForFrontend(logicVariableFormRef.current.store.getFormDatas()),
      };

      const _tableData = JSON.parse(JSON.stringify(data));

      if (detailDlgType === 'add') {
        _tableData.push(value);
      } else {
        const dataIndex = _tableData.findIndex((item: LogicVariableInfo) => item.id === value.id);

        if (dataIndex > -1) {
          _tableData.splice(dataIndex, 1, value);
        }
      }

      onChange(_tableData);
      setDetailVisible(false);
    });
  }, [data, detailDlgType, selectedData]);

  const onCloseDetailDlg = useCallback(() => {
    setDetailVisible(false);
  }, []);

  /** 批量删除 */
  const onBatchDelete = useCallback(() => {
    const _tableData = data.filter(
      (item: LogicVariableInfo) => !selectedRowKeys.includes(item.id || ''),
    );

    onChange(_tableData);
    updatePageNum(_tableData);
    setSelectedRowKeys([]);
  }, [data, selectedRowKeys]);

  /** 批量删除确认框 */
  const confirmBatchDelete = useCallback(() => {
    const _tableData = JSON.parse(JSON.stringify(data));
    const selectDataNames = _tableData
      .filter((item: LogicVariableInfo) => selectedRowKeys.includes(item.id || ''))
      .map((item: LogicVariableInfo) => item.name)
      .join('、');

    Dialog.confirm({
      content: (
        <div className={`${dialogCls}-confirm-batch-delete`}>
          <span title={selectDataNames}>{selectDataNames}</span>
          <span>{getLabel('284036', '请确认是否要删除这些变量？')}</span>
        </div>
      ),
      onOk: () => {
        onBatchDelete();
      },
    });
  }, [data, selectedRowKeys]);

  return (
    <div>
      <div className={`${dialogCls}-top`}>
        <span>
          {scope === VariableScope.AppVar
            ? getLabel('211250', '全局变量')
            : getLabel('283670', '当前页面变量')}
        </span>
        <div>
          <When weId={`${props.weId || ''}_4ad6k6`} condition={selectedRowKeys.length}>
            <CorsComponent
              app="@weapp/ebdcoms"
              compName="IconFont"
              weId={`${props.weId || ''}_bp658c`}
              name="Icon-Batch-delete"
              className={`${dialogCls}-top-icon`}
              size="sm"
              onClick={confirmBatchDelete}
            />
          </When>
          <CorsComponent
            app="@weapp/ebdcoms"
            compName="IconFont"
            weId={`${props.weId || ''}_xw9hqw`}
            name="Icon-add-to"
            className={`${dialogCls}-top-icon`}
            size="sm"
            onClick={onAddVeriable}
          />
        </div>
      </div>
      <Table
        weId={`${props.weId || ''}_svd0qi`}
        columns={variableColumns()}
        data={data}
        rowKey="id"
        pageInfo={{
          value: pageNum,
          pageSize,
          total: data.length,
          showSizeChanger: true,
          showTotal: true,
          pageSizeOptions: [5, 10, 20, 50, 100],
          onChange: onPageSizeChange,
        }}
        scroll={{ y: 280 }}
        isShowIndex
        selection={{
          selectedRowKeys,
          onSelect: onTableSelect,
        }}
      />
      {detailVisible ? (
        <Dialog
          weId={`${props.weId || ''}_7mqajc`}
          visible={detailVisible}
          onClose={onCloseDetailDlg}
          title={
            detailDlgType === 'add'
              ? getLabel('256847', '新建变量')
              : getLabel('316807', '编辑变量')
          }
          width={440}
          icon={ebdcoms.get().dlgIconName}
          closable
          footer={[
            <Button
              weId={`${props.weId || ''}_o48w7x`}
              onClick={onSaveDetailDlg}
              key="sure"
              type="primary"
            >
              {getLabel('97216', '保存')}
            </Button>,
            <Button weId={`${props.weId || ''}_zdo9oi`} onClick={onCloseDetailDlg} key="cancel">
              {getLabel('53937', '取消')}
            </Button>,
          ]}
        >
          <div className={`${prefixCls}-variable-form`}>
            <LogicVariableForm
              weId={`${props.weId || ''}_7ef6me`}
              ref={logicVariableFormRef}
              data={formatVarDataForBackend({ ...selectedData! }) as any}
              variableData={data as any}
              modelData={(modelData || []) as any}
              server="FRONTEND"
              customVariableType={customVariableType}
              renderListButton={renderListButton}
              localeConfig={{
                tablefield: 'ebdd_page_var.var_des',
                configServicePath: 'ebuilder/designer',
                module: 'ebuilder_designer',
              }}
            />
          </div>
        </Dialog>
      ) : null}
    </div>
  );
};

export default observer(VariableDialogContent);
