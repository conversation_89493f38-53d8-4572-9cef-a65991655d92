import { eventEmitter, getLabel } from '@weapp/utils';
import ebdcoms from '../../../../utils/ebdcoms';

/** 更新变量配置 */
export const updateVariable = (options: any) => {
  const {
    pageVar, appVar, pageId, appId, isInit = false,
  } = options || {};

  if (pageId) {
    ebdcoms.excu('setEbParams', 'pageParams', pageId, {
      // 当前页面变量
      pageVar: ebdcoms.excu('formatVariable', pageVar || []),
    });
  }

  if (appId) {
    ebdcoms.excu('setEbParams', 'appParams', appId, {
      // 全局变量
      appVar: ebdcoms.excu('formatVariable', appVar || []),
    });
  }

  eventEmitter.emit('@weapp/components', 'update.variable');

  if (!isInit) {
    eventEmitter.emit('@weapp/ebddesigner', 'update.variable');
  }
};

export const getVariableTypeTextMaps: () => Record<string, string> = () => ({
  STRING: getLabel('55788', '文本'),
  NUMBER: getLabel('189937', '数字'),
  BOOLEAN: getLabel('158875', '布尔值'),
  DOM: getLabel('274352', '文档对象'),
  ARRAY: getLabel('158876', '数组'),
  CUSTOM: getLabel('262397', '模型'),
});

// eslint-disable-next-line max-len
export const getVariableTypeText = (_type: string) => getVariableTypeTextMaps()[_type.toLocaleUpperCase()];

export default {};
