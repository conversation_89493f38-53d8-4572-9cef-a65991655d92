import { getLabel } from '@weapp/utils';
import { prefixCls } from '../../../../constants';

export const dialogCls = `${prefixCls}-variable-dialog`;

export const DataSet = 'group-dataset';

export const Base = 'group-basic';

/** 变量范围 */
export enum VariableScope {
  PageVar = 'pageVar',
  AppVar = 'appVar',
}

export const getVariableScopeOptions = () => [
  { id: VariableScope.PageVar, content: getLabel('256337', '页面变量') },
  { id: VariableScope.AppVar, content: getLabel('211250', '全局变量') },
];

export default {};
