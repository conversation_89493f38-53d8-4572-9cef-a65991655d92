import { Button, Dialog } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, {
  useCallback, useEffect, useMemo, useState,
} from 'react';
import { Page } from '../../../../types';
import ebdcoms from '../../../../utils/ebdcoms';
import { getVariableScopeOptions, VariableScope } from './constants';
import Content from './Content';
import { LogicVariableInfo } from './types';
import { updateVariable } from './utils';

interface VariableDlgProps extends React.Attributes {
  visible: boolean;
  page?: Page;
  hideDialogMenu?: boolean;
  scope?: VariableScope;
  onClose: () => void;
}

const VariableDlg: React.FC<VariableDlgProps> = (props) => {
  const {
    visible, hideDialogMenu = true, scope = VariableScope.PageVar, page, onClose,
  } = props;
  let pageId = '';
  let appId = '';
  if (page) {
    pageId = page.id;
    appId = page.appId!;
  } else {
    const pageInfo = ebdcoms.excu('getEbParams', 'pageInfo');
    pageId = pageInfo.pageId;
    appId = pageInfo.appid;
  }

  const [pageVariableData, setPageVariableData] = useState<LogicVariableInfo[]>([]);
  const [appVariableData, setAppVariableData] = useState<LogicVariableInfo[]>([]);
  const [variableScope, setVariableScope] = useState<VariableScope>(scope);

  const variableScopeOptions = useMemo(() => getVariableScopeOptions(), []);

  useEffect(() => {
    const pageVar = ebdcoms.excu('getEbParams', 'pageParams', pageId)?.pageVar;
    const appVar = ebdcoms.excu('getEbParams', 'appParams', appId)?.appVar;

    setPageVariableData(ebdcoms.excu('formatVariablePageToLogic', pageVar || []));
    setAppVariableData(ebdcoms.excu('formatVariablePageToLogic', appVar || []));
  }, [pageId, appId, visible]);

  /** 变量来源变更回调（pageVar / appVar） */
  const onMenuChange = useCallback((value: string) => {
    setVariableScope(value as VariableScope);
  }, []);

  /** 变量的保存回调 */
  const handleSave = useCallback(() => {
    // 更新变量
    updateVariable({
      pageId,
      pageVar: pageVariableData,
      appId,
      appVar: appVariableData,
    });

    onClose();
  }, [pageVariableData, appVariableData, appId, pageId]);

  /** 变量的更新回调 */
  const onVariableChange = useCallback(
    (data?: LogicVariableInfo[]) => {
      // eslint-disable-next-line max-len
      const setVariableData = variableScope === VariableScope.AppVar ? setAppVariableData : setPageVariableData;

      setVariableData(data || []);
    },
    [variableScope],
  );

  const variableData = variableScope === VariableScope.AppVar ? appVariableData : pageVariableData;

  return (
    <Dialog
      weId={`${props.weId || ''}_hcuvqj`}
      visible={visible}
      title={variableScope ? getLabel('256337', '页面变量') : getLabel('211250', '全局变量')}
      onClose={onClose}
      closable
      width={800}
      height={528}
      destroyOnClose
      menus={hideDialogMenu ? undefined : variableScopeOptions}
      menuValue={variableScope}
      onMenuChange={onMenuChange}
      icon={ebdcoms.get().dlgIconName}
      footer={[
        <Button weId={`${props.weId || ''}_o48w7x`} onClick={handleSave} key="sure" type="primary">
          {getLabel('97216', '保存')}
        </Button>,
        <Button weId={`${props.weId || ''}_zdo9oi`} onClick={onClose} key="cancel">
          {getLabel('53937', '取消')}
        </Button>,
      ]}
    >
      <Content
        weId={`${props.weId || ''}_obkb8p`}
        data={variableData}
        scope={variableScope}
        onChange={onVariableChange}
      />
    </Dialog>
  );
};

export default VariableDlg;
