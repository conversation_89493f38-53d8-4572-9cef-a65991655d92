import { CorsComponent } from '@weapp/ui';
import { observer } from 'mobx-react';
import React, { useCallback, useEffect, useState } from 'react';
import { useDesigner } from '../../../hooks';
import './index.less';
import VariableDlg from './VariableDialog';

const Variable: React.FC<React.Attributes> = (props) => {
  const { events, page } = useDesigner();
  const [visible, setVisible] = useState<boolean>(false);

  const onClose = useCallback(() => {
    setVisible(false);
  }, []);

  const onShow = useCallback(() => {
    setVisible(true);
  }, []);

  useEffect(() => {
    events.on('variable.action', onShow);

    return () => {
      events.off('variable.action', onShow);
    };
  }, []);

  return (
    <>
      <CorsComponent
        weId={`${props.weId || ''}_iycab9`}
        app="@weapp/ebdcoms"
        compName="IconFont"
        name="Icon-variable03"
        size="s"
      />
      {visible ? (
        <VariableDlg
          weId={`${props.weId || ''}_ld80cu`}
          visible={visible}
          page={page}
          onClose={onClose}
        />
      ) : null}
    </>
  );
};

export default observer(Variable);
