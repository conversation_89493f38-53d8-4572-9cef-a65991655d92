import { LocaleInputValueType } from '../../../../logic-composer-designer/components/common/locale-input/type';

export type Structure = {
  id: string;
  name: string;
  showName?: string;
  type: string;
  pid?: string;
};

export type ModelDataInfo = {
  // 后端唯一id标识，保存过的变量才会返回，新建的变量先用uuid作为唯一标识
  id: string;
  desc?: LocaleInputValueType;
  name: string;
  structure: Structure[];
};

export default {};
