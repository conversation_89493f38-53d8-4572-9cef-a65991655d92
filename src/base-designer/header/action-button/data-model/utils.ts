import {
  getLocaleInputData,
  returnTypeEnum,
  setLocaleInputData,
} from '../../../../logic-composer-designer/components/common/locale-input/utils';
import ebdcoms from '../../../../utils/ebdcoms';
import { ModelDataInfo } from './types';
import uuid from './uuid';

// 解密转义字符串并解析
export const parseValue = (str: string) => {
  let decodeStr = str;
  try {
    decodeStr = decodeURIComponent(str);
  } catch (error) {
    console.warn(error);
  }
  // 兼容非json数据
  try {
    const json = JSON.parse(decodeStr);
    return json;
  } catch (error) {
    return decodeStr;
  }
};

export const compatibleModelDatas = (datas: any[]) => datas.map((data: any) => {
  const {
    properties: _structure, desc, descLabelId, ...restData
  } = data;
    // 转换下type数值
  const structure = _structure?.map((_: any) => {
    const { showName, showNameLabelId, ...restValue } = _;
    const _showName = setLocaleInputData(showName || '', showNameLabelId);
    return {
      ...restValue,
      showName: _showName,
      type: parseValue(_.type),
    };
  });
  const _desc = setLocaleInputData(desc || '', descLabelId);
  return {
    ...restData,
    desc: _desc,
    structure,
  };
});

/** 判断是否是本地id */
export const isMockId = (id: string) => id.length < 7;

export const transformPageVarDataModel = (data: ModelDataInfo) => {
  // 记录属性对应的id
  const propertiesMap: { [key: string]: string } = {};
  // 修改内部id
  const properties = data.structure?.map((_value) => {
    // 获取父级id和当前id
    const {
      pid, id: _id, type, showName, ...restValue
    } = _value;
      // 真实id长度会大于6
    const uid = isMockId(_id) ? uuid.id : _value.id;
    const property = {
      showName: getLocaleInputData(showName),
      showNameLabelId: getLocaleInputData(showName, returnTypeEnum.ID),
      ...restValue,
      type: encodeURIComponent(JSON.stringify(type)),
      // 使用修改后的id值
      id: uid,
      // 使用修改后的父级id值
      ...(pid ? { pid: propertiesMap[pid] } : {}),
    };
      // 记录当前id
    propertiesMap[_id] = uid;
    return property;
  }) || [];

  return {
    id: data.id,
    name: data.name,
    desc: getLocaleInputData(data.desc) || '',
    properties,
    descLabelId: getLocaleInputData(data.desc, returnTypeEnum.ID),
  };
};

/** 更新变量模型配置 */
export const updatePageVarDataModel = (options: any) => {
  const { pageModel = [], pageId } = options || {};

  ebdcoms.excu('setEbParams', 'pageParams', pageId, {
    pageModel: pageModel.map(transformPageVarDataModel),
  });
};

export default {};
