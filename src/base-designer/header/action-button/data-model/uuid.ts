import ebdcoms from '../../../../utils/ebdcoms';

const getGenerateId = (count: number) => ebdcoms.asyncExcu('ajax', {
  url: `/api/ebuilder/common/distribution/generateId?count=${count}`,
  method: 'GET',
});

class UUID {
  _ids: string[] = [];

  _index: number = -1;

  hasInit: boolean = false;

  constructor() {
    this.getIds();
  }

  get id() {
    ++this._index;

    // 不够50条，重新获取id
    if (this._ids.length - this._index < 50) {
      this.getIds();
    }

    return this._ids[this._index];
  }

  getIds = async () => {
    const ids = await getGenerateId(100);
    this._ids = [...this._ids, ...ids];
  };
}

export default new UUID();
