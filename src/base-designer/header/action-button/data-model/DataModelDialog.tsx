import {
  <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ponent, Dialog, Table,
} from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, {
  useCallback, useEffect, useMemo, useRef, useState,
} from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../../constants';
import { getLocaleInputData } from '../../../../logic-composer-designer/components/common/locale-input/utils';
import { LogicVariableModelForm } from '../../../../logic-composer-designer/components/panels/data-model';
import ebdcoms from '../../../../utils/ebdcoms';
import { useDesigner } from '../../../hooks';
import { Base, DataSet } from '../variable/constants';
import { ModelDataInfo } from './types';
import { compatibleModelDatas, updatePageVarDataModel } from './utils';
import uuid from './uuid';

interface DataModelDlgProps extends React.Attributes {
  visible: boolean;
  onClose: () => void;
}

const DataModelDlg: React.FC<DataModelDlgProps> = (props) => {
  const { visible, onClose } = props;
  const { page } = useDesigner();
  const pageId = page?.id;
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [pageModelData, setPageModelData] = useState<ModelDataInfo[]>([]);
  const logicVariableModelRef = useRef<any>();
  const [detailVisible, setDetailVisible] = useState(false);
  const [selectedData, setSelectData] = useState<ModelDataInfo | undefined>();
  const [detailDlgType, setDetailDlgType] = useState<string>('add');
  const [pageNum, setPageNum] = useState<number>(1);
  const [pageSize, setPageize] = useState<number>(5);

  const onPageSizeChange = (_pageNum: number, _pageSize: number) => {
    setPageNum(_pageNum);
    setPageize(_pageSize);
  };

  const updatePageNum = useCallback(
    (_data: ModelDataInfo[]) => {
      const total = _data.length;
      const needUpdate = total % pageSize === 0 && pageNum > Math.floor(total / pageSize);

      if (needUpdate) {
        setPageNum(Math.floor(total / pageSize));
      }
    },
    [pageNum, pageSize],
  );

  useEffect(() => {
    const pageModel = ebdcoms.excu('getEbParams', 'pageParams', page.id)?.pageModel;

    setPageModelData(compatibleModelDatas(pageModel || []));
    updatePageNum(pageModel || []);
  }, []);

  const handleSave = useCallback(() => {
    // 更新变量
    if (pageId) {
      updatePageVarDataModel({ pageId, pageModel: pageModelData });
    }

    onClose();
  }, [pageModelData, pageId]);

  const onDetailDlgClose = useCallback(() => {
    setDetailVisible(false);
  }, []);

  const onSaveDetailDlg = useCallback(() => {
    // 校验数据
    logicVariableModelRef.current.validate().then((res: any) => {
      if (!isEmpty(res.errors)) return;

      const value = { ...selectedData, ...logicVariableModelRef.current.store.getFormDatas() };

      const _tableData = JSON.parse(JSON.stringify(pageModelData));

      if (detailDlgType === 'add') {
        _tableData.push(value);
      } else {
        const dataIndex = _tableData.findIndex((item: ModelDataInfo) => {
          if (item.id) {
            return item.id === value.id;
          }

          return false;
        });

        if (dataIndex > -1) {
          _tableData.splice(dataIndex, 1, value);
        }
      }

      setPageModelData(_tableData);
      setDetailVisible(false);
    });
  }, [detailDlgType, selectedData]);

  /** 编辑 */
  const editModelDataInfo = useCallback(
    (_data: ModelDataInfo) => () => {
      setDetailDlgType('edit');
      setSelectData(_data);
      setDetailVisible(true);
    },
    [],
  );

  /** 删除 */
  const deleteModelDataInfo = (_data: ModelDataInfo) => {
    const _tableData = pageModelData.filter((item: ModelDataInfo) => item.id !== _data.id);

    setPageModelData(_tableData);
    updatePageNum(_tableData);
  };

  /** 删除确认框 */
  const confirmDelete = (_data: ModelDataInfo) => () => {
    Dialog.confirm({
      content: getLabel('316777', '请确认是否要删除该条模型？'),
      onOk: () => {
        deleteModelDataInfo(_data);
      },
    });
  };

  /** 批量删除 */
  const onBatchDelete = useCallback(() => {
    const _tableData = pageModelData.filter(
      (item: ModelDataInfo) => !selectedRowKeys.includes(item.id || ''),
    );

    setPageModelData(_tableData);
    updatePageNum(_tableData);
    setSelectedRowKeys([]);
  }, [pageModelData, selectedRowKeys]);

  /** 批量删除确认框 */
  const confirmBatchDelete = useCallback(() => {
    const _tableData = JSON.parse(JSON.stringify(pageModelData));
    const selectDataNames = _tableData
      .filter((item: ModelDataInfo) => selectedRowKeys.includes(item.id || ''))
      .map((item: ModelDataInfo) => item.name)
      .join('、');

    Dialog.confirm({
      content: (
        <div className={`${prefixCls}-datamodel-dialog-confirm-batch-delete`}>
          <span title={selectDataNames}>{selectDataNames}</span>
          <span>{getLabel('317722', '请确认是否要删除这些模型？')}</span>
        </div>
      ),
      onOk: () => {
        onBatchDelete();
      },
    });
  }, [pageModelData, selectedRowKeys]);

  /** 新增 */
  const onAddDataModel = useCallback(() => {
    setDetailDlgType('add');
    setSelectData({
      desc: '',
      id: uuid.id,
      name: '',
      structure: [],
    });

    setDetailVisible(true);
  }, []);

  const onTableSelect = (selectedkeys: string[]) => {
    setSelectedRowKeys(selectedkeys);
  };

  const customVariableType = useCallback(
    (variableType: any) => variableType.filter((variableTypeData: any) => {
      const { id, children } = variableTypeData;

      if (id === Base) {
        variableTypeData.children = children.filter(({ id: _id }: any) => !['DOM'].includes(_id));
      }

      return id !== DataSet;
    }),
    [],
  );

  const renderListButton = useCallback(() => null, []);

  const dataModelColumns = useMemo(
    () => [
      {
        title: getLabel('316778', '模型名称'),
        dataIndex: 'name',
        isPrimaryKey: true,
      },
      {
        title: getLabel('316779', '模型描述'),
        dataIndex: 'desc',
        bodyRender: (_data: ModelDataInfo) => {
          const { desc } = _data || {};
          return getLocaleInputData(desc);
        },
      },
      {
        title: getLabel('54342', '操作'),
        dataIndex: 'action',
        bodyRender: (_data: ModelDataInfo) => (
          <div className={`${prefixCls}-datamodel-dialog-top-actions`}>
            <Button
              weId={`${props.weId || ''}_dmo54e`}
              key="edit"
              type="link"
              onClick={editModelDataInfo(_data)}
            >
              {getLabel('54009', '编辑')}
            </Button>
            <Button
              weId={`${props.weId || ''}_5bp06u`}
              key="delete"
              type="link"
              onClick={confirmDelete(_data)}
            >
              {getLabel('53951', '删除')}
            </Button>
          </div>
        ),
      },
    ],
    [],
  );

  return (
    <>
      <Dialog
        weId={`${props.weId || ''}_q14q4z`}
        visible={visible}
        title={getLabel('262397', '模型')}
        icon={ebdcoms.get().dlgIconName}
        onClose={onClose}
        closable
        width={800}
        height={528}
        destroyOnClose
        footer={[
          <Button
            weId={`${props.weId || ''}_o48w7x`}
            onClick={handleSave}
            key="sure"
            type="primary"
          >
            {getLabel('97216', '保存')}
          </Button>,
          <Button weId={`${props.weId || ''}_zdo9oi`} onClick={onClose} key="cancel">
            {getLabel('53937', '取消')}
          </Button>,
        ]}
      >
        <>
          <div className={`${prefixCls}-datamodel-dialog-top`}>
            <span>{getLabel('317748', '当前页面变量模型')}</span>
            <div>
              <When weId={`${props.weId || ''}_4ad6k6`} condition={selectedRowKeys.length}>
                <CorsComponent
                  app="@weapp/ebdcoms"
                  compName="IconFont"
                  weId={`${props.weId || ''}_bp658c`}
                  name="Icon-Batch-delete"
                  className={`${prefixCls}-datamodel-dialog-top-icon`}
                  size="sm"
                  onClick={confirmBatchDelete}
                />
              </When>
              <CorsComponent
                app="@weapp/ebdcoms"
                compName="IconFont"
                weId={`${props.weId || ''}_xw9hqw`}
                name="Icon-add-to"
                className={`${prefixCls}-datamodel-dialog-top-icon`}
                size="sm"
                onClick={onAddDataModel}
              />
            </div>
          </div>

          <Table
            weId={`${props.weId || ''}_svd0qi`}
            columns={dataModelColumns}
            data={pageModelData}
            rowKey="id"
            pageInfo={{
              value: pageNum,
              pageSize,
              total: pageModelData.length,
              showSizeChanger: true,
              showTotal: true,
              pageSizeOptions: [5, 10, 20, 50, 100],
              onChange: onPageSizeChange,
            }}
            scroll={{ y: 280 }}
            isShowIndex
            selection={{
              selectedRowKeys,
              onSelect: onTableSelect,
            }}
          />
        </>
      </Dialog>

      {detailVisible ? (
        <Dialog
          weId={`${props.weId || ''}_bpbg6k`}
          width={340}
          title={
            detailDlgType === 'add'
              ? getLabel('262412', '新建数据模型')
              : getLabel('262413', '修改数据模型')
          }
          onClose={onDetailDlgClose}
          icon={ebdcoms.get().dlgIconName}
          visible={detailVisible}
          closable
          footer={[
            <Button
              weId={`${props.weId || ''}_o48w7x`}
              onClick={onSaveDetailDlg}
              key="sure"
              type="primary"
            >
              {getLabel('97216', '保存')}
            </Button>,
            <Button weId={`${props.weId || ''}_zdo9oi`} onClick={onDetailDlgClose} key="cancel">
              {getLabel('53937', '取消')}
            </Button>,
          ]}
        >
          <div className={`${prefixCls}-datamodel-form`}>
            <LogicVariableModelForm
              ref={logicVariableModelRef}
              weId={`${props.weId || ''}_ly127n`}
              data={selectedData as any}
              server="FRONTEND"
              modelData={pageModelData as any[]}
              renderListButton={renderListButton}
              customVariableType={customVariableType}
              modellocaleConfig={{
                desc: {
                  tablefield: 'ebdd_logic_model.model_desc',
                  configServicePath: 'ebuilder/designer',
                  module: 'ebuilder_designer',
                },
                structure: {
                  tablefield: 'ebdd_logic_model_property.show_name',
                  configServicePath: 'ebuilder/designer',
                  module: 'ebuilder_designer',
                },
              }}
            />
          </div>
        </Dialog>
      ) : null}
    </>
  );
};

export default observer(DataModelDlg);
