import { CorsComponent } from '@weapp/ui';
import { observer } from 'mobx-react-lite';
import React, { useCallback, useEffect, useState } from 'react';
import { useDesigner } from '../../../hooks';
import DataModelDlg from './DataModelDialog';
import './index.less';

const DataModel: React.FC<React.Attributes> = (props) => {
  const { events } = useDesigner();
  const [visible, setVisible] = useState<boolean>(false);

  const onClose = useCallback(() => {
    setVisible(false);
  }, []);

  const onShow = useCallback(() => {
    setVisible(true);
  }, []);

  useEffect(() => {
    events.on('dataModel.action', onShow);

    return () => {
      events.off('dataModel.action', onShow);
    };
  }, []);

  return (
    <>
      <CorsComponent
        weId={`${props.weId || ''}_iycab9`}
        app="@weapp/ebdcoms"
        compName="IconFont"
        name="Icon-Model"
        size="s"
        onClick={onShow}
      />
      {visible ? (
        <DataModelDlg weId={`${props.weId || ''}_ld80cu`} visible={visible} onClose={onClose} />
      ) : null}
    </>
  );
};

export default observer(DataModel);
