import { Cors<PERSON>omponent, Trigger } from '@weapp/ui';
import {
  classnames, debounce, get<PERSON><PERSON><PERSON>, ResizeObserver,
} from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { createRef } from 'react';
import { SourceCodeProps } from '../../../common/sourcecode';
import { SourceCodeType } from '../../../common/sourcecode/types';
import { getCodeData, importCssText } from '../../../common/sourcecode/utils';
import domUtils from '../../../common/utils/dom';
import { prefixCls } from '../../../constants';
import { ClientType } from '../../../types';
import './index.less';

export interface SourceCodeBtnProps extends React.Attributes {
  clientType: ClientType;
  code?: SourceCodeType;
  disabled?: boolean;
  panel: React.ReactElement<SourceCodeProps>;
}

interface SourceCodeBtnStates {
  visible: boolean;
  popupStyle: any;
}

@observer
export default class SourceCode extends React.PureComponent<
  SourceCodeBtnProps,
  SourceCodeBtnStates
> {
  state = {
    visible: false,
    popupStyle: {},
  };

  ref = createRef<any>();

  container: any = null;

  componentDidMount() {
    const { code, disabled } = this.props;

    if (code && !disabled) {
      importCssText(getCodeData(code.ecode).compiledCodeData.css);
    }

    if (this.ref) {
      const main = domUtils.getParent(this.ref.current, '.weapp-de')?.childNodes?.[1];
      const rect = (main as HTMLElement)?.getClientRects()?.[0];
      if (rect?.width && rect?.height) {
        this.container = main;
        this.setState({
          popupStyle: {
            width: `${rect.width}px`,
            height: `${rect.height}px`,
          },
        });
      }

      if (main) {
        // 创建监听器
        const resizeObserver = new ResizeObserver(
          debounce((entries) => {
            const { width, height } = entries[0]?.contentRect;

            if (width && height) {
              this.setState({
                popupStyle: {
                  width: `${width}px`,
                  height: `${height}px`,
                },
              });
            }
          }, 200),
        );

        (main as any).resizeObserver = resizeObserver;
        // 监听尺寸变化
        resizeObserver.observe(main as Element);
      }
    }
  }

  componentDidUpdate(preProps: SourceCodeBtnProps) {
    const { code, disabled } = this.props;

    if (preProps.clientType !== this.props.clientType && code && !disabled) {
      importCssText(getCodeData(code.ecode).compiledCodeData.css);
    }
  }

  componentWillUnmount() {
    const main = domUtils.getParent(this.ref.current, '.weapp-de')?.childNodes?.[1];

    (main as any)?.resizeObserver?.disconnect?.();
  }

  onVisibleChange = (visible: boolean) => {
    if (this.props.disabled) return;

    this.setState({ visible });
  };

  onClick = () => {
    if (this.props.disabled) return;

    this.setState({ visible: true });
  };

  getTitle = () => (this.props.disabled ? getLabel('151156', 'ecode源码服务不可用') : getLabel('97957', '源码'));

  getPopupContainer = () => this.container;

  render() {
    const { visible } = this.state;
    const { disabled } = this.props;

    return (
      <div ref={this.ref}>
        <Trigger
          weId={`${this.props.weId || ''}_so15ep`}
          action={['click']}
          // 禁止关闭弹层的触发器区域容器标记
          blacklist={[
            `.${prefixCls}-header-operation-icon`,
            `.${prefixCls}-ho-disabled`,
            '.ui-trigger-popupInner',
          ]}
          mask
          maskClosable={false}
          maskStyle={{
            backgroundColor: 'transparent',
            height: 0,
          }}
          popup={this.props.panel}
          // 要低于按钮popver的层级
          zIndex={998}
          popupPlacement="bottom"
          popupClassName={`${prefixCls}-ecode-trigger`}
          popupVisible={visible}
          onPopupVisibleChange={this.onVisibleChange}
          getPopupContainer={this.getPopupContainer}
          popupStyle={this.state.popupStyle}
        >
          <CorsComponent
            weId={`${this.props.weId || ''}_b4l5bx`}
            app="@weapp/ebdcoms"
            compName="IconFont"
            className={classnames(
              `${prefixCls}-ecode-icon`,
              { [`${prefixCls}-ho-disabled`]: disabled },
              { hover: visible },
            )}
            type="html"
            size="s"
            title={this.getTitle()}
            onClick={this.onClick}
          />
        </Trigger>
      </div>
    );
  }
}
