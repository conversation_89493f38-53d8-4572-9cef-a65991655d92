import { ComponentType } from 'react';
import Loadable from '../../../common/loadable';
import { SourceCodeBtnProps } from './SourceCode';

export { SourceCode };

const SourceCode = Loadable({
  name: 'CodeAction',
  loader: () => import(
    /* webpackChunkName: "de_base_designer" */
    './SourceCode'
  ),
}) as ComponentType<SourceCodeBtnProps>;

SourceCode.displayName = 'SourceCodeBtn';

export default {};
