@import (reference) '../../../style/var.less';
@import (reference) '../../../style/prefix.less';

.@{prefix}-ecode-icon {
  &.hover > svg {
    color: var(--primary) !important;
  }

  &.@{prefix}-ho-disabled {
    opacity: 0.6;
    cursor: not-allowed !important;
    &:hover > svg {
      color: var(--de-icon-color) !important;
    }
  }
}

.@{prefix}-ecode-trigger {
  position: absolute;
  top: 0 !important;
  left: 0 !important;
  background-color: var(--bg-base);
}

.@{prefix}-ecode-trigger > div {
  height: 100%;
}
