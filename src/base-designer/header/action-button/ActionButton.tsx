import React from 'react';
import { SourceCode } from './lib';
import { ActionButtonProps, ActionButtonType } from './types';

const sourceMaps = {
  sourcecode: SourceCode,
};

const ActionButton: React.FC<ActionButtonProps<ActionButtonType>> = (props) => {
  const { type, ...restProps } = props;
  const ActionBtn = sourceMaps[type];

  return <ActionBtn weId={`${props.weId || ''}_fud0wj`} {...restProps} />;
};

export default ActionButton;
