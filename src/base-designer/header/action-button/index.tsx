import { ComponentType } from 'react';
import Loadable from '../../../common/loadable';
import { ActionButtonProps, ActionButtonType } from './types';

export { ActionButton };

const ActionButton = Loadable({
  name: 'ActionButton',
  loader: () => import(
    /* webpackChunkName: "de_base_designer" */
    './ActionButton'
  ),
}) as ComponentType<ActionButtonProps<ActionButtonType>>;

ActionButton.displayName = 'ActionButton';

export default {};
