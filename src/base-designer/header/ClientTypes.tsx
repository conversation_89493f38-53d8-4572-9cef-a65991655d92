import { CorsComponent } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import React, { useCallback, useState } from 'react';
import { prefixCls } from '../../constants';
import { ClientType } from '../../core';

interface ClientIconProps extends React.Attributes {
  active: boolean;
  title: string;
  type: ClientType;
  onClick: (type: ClientType) => void;
}

const ClientIcon: React.FC<ClientIconProps> = (props) => {
  const onClick = useCallback(() => {
    props.onClick?.(props.type);
  }, []);

  return (
    <div className={classnames({ active: props.active })} onClick={onClick}>
      <CorsComponent
        weId={`${props.weId || ''}_v1eqhx`}
        app="@weapp/ebdcoms"
        compName="IconFont"
        title={props.title}
        type={props.type.toLocaleLowerCase()}
        size="s"
      />
    </div>
  );
};

interface ClientTypesProps extends React.Attributes {
  value?: ClientType;
  onChange?: (clientType: ClientType) => void;
}

const ClientTypes: React.FC<ClientTypesProps> = (props) => {
  const { value = 'PC' } = props;
  const [clientType, setClientType] = useState(value);
  const onChange = useCallback((type: ClientType) => {
    setClientType(type);
    props.onChange?.(type);
  }, []);

  return (
    <div className={`${prefixCls}-header-client-type`}>
      <ClientIcon
        weId={`${props.weId || ''}_ki3rdj`}
        type="PC"
        title={getLabel('53840', 'PC端')}
        active={clientType === 'PC'}
        onClick={onChange}
      />
      <ClientIcon
        weId={`${props.weId || ''}_al2ilp`}
        type="MOBILE"
        title={getLabel('53841', '移动端')}
        active={clientType === 'MOBILE'}
        onClick={onChange}
      />
    </div>
  );
};

export default ClientTypes;
