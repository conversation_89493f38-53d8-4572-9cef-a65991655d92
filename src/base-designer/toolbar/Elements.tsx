import { CorsComponent, IconNames } from '@weapp/ui';
import { observer } from 'mobx-react';
import React, {
  ReactNode, SyntheticEvent, useCallback, useState,
} from 'react';
import { When } from 'react-if';
import { ExtraActionOpts } from '../main/types';

interface ElementsProps extends React.Attributes {
  icon: IconNames | ReactNode;
  title: string;
  className?: string;
  /** 无权保存、修改配置 */
  disabled?: boolean;
  options?: ExtraActionOpts;
  onClick?: (e: SyntheticEvent) => void;
}

const Elements: React.FC<ElementsProps> = (props) => {
  const {
    icon, title, className, options, disabled = false,
  } = props;
  const {
    pageScope, pageInfo, onBeforeInitForm, onBeforeSave,
  } = options || {};
  const [visible, setVisible] = useState<boolean>(false);

  const onClick = useCallback((e: SyntheticEvent) => {
    setVisible(true);
    props?.onClick?.(e);
  }, []);

  const onClose = useCallback(() => {
    setVisible(false);
  }, []);

  return (
    <>
      <CorsComponent
        weId={`${props.weId || ''}_8yc5uw`}
        app="@weapp/ebdcoms"
        compName="IconFont"
        className={className}
        name={icon}
        size="xs"
        title={title}
        onClick={onClick}
      />
      {/* 组件管理弹窗 */}
      <When weId={`${props.weId || ''}_mr8gk7`} condition={pageInfo && visible}>
        <CorsComponent
          weId={`${props.weId || ''}_6vzhui`}
          app="@weapp/ebddesigner"
          compName="EleDialogView"
          visible={visible}
          pageScope={pageScope}
          pageInfo={pageInfo}
          changeDisabled={disabled}
          onClose={onClose}
          onBeforeInitForm={onBeforeInitForm}
          onBeforeSave={onBeforeSave}
        />
      </When>
    </>
  );
};

export default observer(Elements);
