import { CorsComponent } from '@weapp/ui';
import React, { SyntheticEvent, useCallback } from 'react';
import useInternal from '../hooks/useInternal';
import { ExtraAction } from '../main/types';
import DraggableIcon from '../resolver/DraggableIcon';
import Copy from './Copy';
import Elements from './Elements';
import SaveAsTemp from './SaveAsTemp';

export interface ActionIconProps extends React.Attributes {
  action: ExtraAction;
  onAction?: (e: SyntheticEvent, action: ExtraAction) => void;
}

const ActionIcon: React.FC<ActionIconProps> = (props) => {
  const { action, onAction } = props;
  const {
    id, icon, title = '', className, hidden, style, disabled,
  } = action;
  const { localization } = useInternal()!;
  const actionTitle = localization?.toolbar?.[id]?.title || title;

  const handleClick = useCallback(
    (e) => {
      onAction?.(e, action);
      e.stopPropagation();
    },
    [onAction],
  );

  if (hidden || id === 'compose-pretemp') {
    return null;
  }

  if (id === 'drag') {
    return React.cloneElement(
      <DraggableIcon weId={`${props.weId || ''}_zcwqba`}>{icon}</DraggableIcon>,
      {
        key: id,
        onClick: handleClick,
      },
    );
  }

  if (id === 'template' || id === 'master') {
    return (
      <SaveAsTemp
        weId={`${props.weId || ''}_ed7lm7`}
        type={id}
        title={actionTitle}
        disabled={disabled}
        onClick={handleClick}
      />
    );
  }

  if (id === 'copy') {
    return (
      <Copy
        weId={`${props.weId || ''}_3qtp5c`}
        type={id}
        icon={icon}
        title={actionTitle}
        onClick={handleClick}
      />
    );
  }

  if (id === 'elements') {
    return (
      <Elements
        weId={`${props.weId || ''}_3qtp5c`}
        icon={icon}
        title={actionTitle}
        options={action?.options}
        disabled={disabled}
        onClick={handleClick}
      />
    );
  }

  if (typeof icon === 'string') {
    if (icon.startsWith('Icon-')) {
      return (
        <CorsComponent
          key={id}
          weId={`${props.weId || ''}_dhxc9l`}
          app="@weapp/ebdcoms"
          compName="IconFont"
          className={className}
          name={icon}
          size="xs"
          title={actionTitle}
          onClick={handleClick}
          style={style}
        />
      );
    }

    return (
      <CorsComponent
        weId={`${props.weId || ''}_zst6qi`}
        key={id}
        app="@weapp/ebdcoms"
        compName="IconFont"
        className={className}
        type={icon}
        size="xs"
        title={actionTitle}
        onClick={handleClick}
        style={style}
      />
    );
  }

  return React.cloneElement(icon as any, {
    key: id,
    onClick: handleClick,
  });
};

export default React.memo(ActionIcon);
