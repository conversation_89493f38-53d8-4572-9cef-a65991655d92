import { CorsComponent, Dialog, Icon } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, {
  CSSProperties, useCallback, useEffect, useState
} from 'react';
import { When } from 'react-if';
import { useDesigner } from '..';
import { prefixCls } from '../../constants';
import compUtils from '../../core/utils/comp';
import { IComData, IComDescription } from '../../types';
import setTimeoutOnce from '../../utils/setTimeoutOnce';
import { getPosition } from './utils';

interface MiniConfigPropsType<T = IComData> extends React.Attributes {
  com: T;
}

interface ConfigDlgProps<T = IComData> extends React.Attributes {
  com: T;
  visible: boolean;
  onClose: () => void;
}

// 简易配置modal
const ConfigDlg: React.FC<ConfigDlgProps> = (props) => {
  // 设置两个隐藏是为了拿到Config的高度做定位
  const { visible, onClose } = props;
  const [position, setPosition] = useState<CSSProperties | null>(null);
  const store = useDesigner();
  const Config = store.rs.getPanelContentByType('attr');

  useEffect(() => {
    if (visible) {
      // 延迟获取定位时间，获取到弹窗高度
      setTimeoutOnce(() => {
        setPosition(getPosition());
      }, 300);
    }
  }, [visible]);

  const onDlgClose = useCallback(() => {
    onClose();
  }, []);

  const onShowAll = useCallback(() => {
    onDlgClose();

    // 右侧栏切换至属性
    store.rs.selectMenuByPanelType('attr');
  }, []);

  return (
    <Dialog
      weId={`${props.weId || ''}_cwfldw`}
      visible={visible}
      draggable
      closable
      title={`${props.com?.config?.name}${getLabel('54205', '设置')}`}
      headerStyle={{ height: 40, paddingBottom: 0, paddingTop: 0 }}
      icon="Icon-set-up-o"
      width={285}
      wrapClassName={`${prefixCls}-config-panel`}
      onClose={onDlgClose}
      mask={false}
      maskClosable={false}
      noMaskClose={false}
      /** 避免弹窗移动 */
      wrapStyle={position ? {} : { visibility: 'hidden' }}
      /** 公共组件Dialog调整，新增ignoreCalculationContainer属性忽略限制容器最大最小高度 */
      ignoreCalculationContainer
      {...((position as any) || {})}
    >
      <When weId={`${props.weId || ''}_rdwwvq`} condition={visible}>
        {React.cloneElement(Config, {
          ...Config.props,
          mini: true,
        })}
        <div className={`${prefixCls}-config-panel-all`}>
          <span onClick={onShowAll}>
            {getLabel('148337', '显示所有配置')}{' '}
            <Icon weId={`${props.weId || ''}_60soav`} name="Icon-forward-o" />
          </span>
        </div>
      </When>
    </Dialog>
  );
};

const MiniConfig: React.FC<MiniConfigPropsType> = (props) => {
  const { com } = props;
  const [enabled, setEnabled] = useState(false);
  const [visible, setVisible] = useState(false);
  const store = useDesigner();

  useEffect(() => {
    const desc = com as unknown as IComDescription;

    // setVisible(store.miniConfigComId === com.id);

    /**
     * 只有当组件第一次被拖拽到设计器，才默认展示简易配置，切换组件后，简易配置不会默认展示
     * 所以在第一次默认打开后，此处清空miniConfigComId
     */
    store.setMiniConfigComId('');

    compUtils.hasMiniConfig(desc, store.clientType).then((_enabled) => {
      setEnabled(_enabled);
    });
  }, [com]);

  const showConfigPanel = useCallback(() => {
    setVisible(true);
  }, []);

  const hideConfigPanel = useCallback(() => {
    setVisible(false);
  }, []);

  if (!enabled) {
    return null;
  }

  return (
    <div className={classnames(`${prefixCls}-toolbar-config-setting`)}>
      <CorsComponent
        weId={`${props.weId || ''}_jbap94`}
        app="@weapp/ebdcoms"
        compName="IconFont"
        name="Icon-set-up-o"
        title={getLabel('54205', '设置')}
        onClick={showConfigPanel}
      />
      <ConfigDlg
        weId={`${props.weId || ''}_n1o0d1`}
        com={com}
        visible={visible}
        onClose={hideConfigPanel}
      />
    </div>
  );
};

export default observer(MiniConfig);
