import { CorsComponent } from '@weapp/ui';
import React, { SyntheticEvent, useCallback, useState } from 'react';
import { When } from 'react-if';
import { EVENT_MASTER_RELOAD, EVENT_TEMP_RELOAD, prefixCls } from '../../constants';
import { CompTempDlg } from '../dialogs/com-temp';
import { ComTempType } from '../dialogs/com-temp/constants';
import { useDesigner } from '../hooks';

interface SaveAsTempProps extends React.Attributes {
  type: string;
  title: string;
  /** 无权保存、修改配置 */
  disabled?: boolean;
  onClick?: (e: SyntheticEvent) => void;
}

export default function SaveAsTemp(props: SaveAsTempProps) {
  const { title, disabled = false, onClick } = props;
  const [visible, setVisible] = useState(false);
  const {
    layoutStore, events, updateComToMaster, addMasterEditEvent,
  } = useDesigner();
  const { selectedComDom, getComTempConfig } = layoutStore;
  const tempConfig = visible ? getComTempConfig() : undefined;

  const handleClick = useCallback((e: SyntheticEvent) => {
    if (disabled) return;
    setVisible(true);
    onClick?.(e);
  }, []);

  const onOk = useCallback((type: ComTempType, compTempId: string, comDom?: HTMLElement | null) => {
    setVisible(false);

    if (type === ComTempType.TEMP) {
      events.emit(EVENT_TEMP_RELOAD);
    } else {
      events.emit(EVENT_MASTER_RELOAD);
      updateComToMaster(compTempId);

      // 如果是新建母版
      if (type === ComTempType.MASTER && comDom) {
        // 给当前选中的 DOM 绑定双击事件，支持双击编辑操作
        addMasterEditEvent(comDom);
      }
    }
  }, []);

  return (
    <>
      <CorsComponent
        weId={`${props.weId || ''}_ltip1d`}
        app="@weapp/components"
        compName="Disabled"
        className={`${prefixCls}-toolbar-disabled`}
        style={{ display: 'flex' }}
        condition={disabled}
      >
        <CorsComponent
          weId={`${props.weId || ''}_330iy1`}
          app="@weapp/ebdcoms"
          compName="IconFont"
          name="Icon-add-to03"
          size="xs"
          title={title}
          onClick={handleClick}
        />
      </CorsComponent>
      <When weId={`${props.weId || ''}_tile1l`} condition={visible}>
        <CompTempDlg
          weId={`${props.weId || ''}_szagr7`}
          comDom={selectedComDom}
          tempConfig={tempConfig}
          visible={visible}
          showMaster={props.type === 'master'}
          onCancel={setVisible}
          onOk={onOk}
        />
      </When>
    </>
  );
}
