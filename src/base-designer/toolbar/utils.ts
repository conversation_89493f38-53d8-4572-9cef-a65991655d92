import { CSSProperties } from 'react';
import { prefixCls } from '../../constants';

export const getPosition = () => {
  const style: CSSProperties = {};
  const toolbar = document.querySelector(`.${prefixCls}-toolbar`) as HTMLDivElement;
  const { clientHeight, clientWidth } = toolbar;

  if (toolbar) {
    const { top, left } = toolbar.getBoundingClientRect();

    const bottom = document.body.clientHeight - top - clientHeight;
    const right = document.body.clientWidth - left - clientWidth;

    if (top > bottom) {
      const configPanel = document.querySelector(
        `.${prefixCls}-config-panel .ui-dialog-layout`,
      ) as HTMLDivElement;

      style.top = top - configPanel?.clientHeight - 5;
    } else {
      style.top = top + clientHeight + 5;
    }

    if (left > right) {
      style.right = right;
    } else {
      style.left = left;
    }
  }

  return style;
};

export default {};
