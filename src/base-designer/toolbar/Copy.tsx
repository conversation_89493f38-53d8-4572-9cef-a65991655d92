import { CorsComponent, IconNames } from '@weapp/ui';
import { observer } from 'mobx-react';
import React, { ReactNode, SyntheticEvent, useEffect } from 'react';
import { EVENT_BEFORE_COPY_COM } from '../../constants';
import getDesignInfo from '../../core/utils/getDesignInfo';
import { ComTempConfig } from '../dialogs/com-temp/types';
import { useDesigner, useLayout } from '../hooks';

interface CopyProps extends React.Attributes {
  icon: IconNames | ReactNode;
  type: string;
  title: string;
  className?: string;
  onClick?: (e: SyntheticEvent) => void;
}

const Copy: React.FC<CopyProps> = (props) => {
  const {
    type, icon, title, className, onClick,
  } = props;
  const { events } = useDesigner();
  const { selectedCom, clientType } = useLayout();

  useEffect(() => {
    if (selectedCom) {
      getDesignInfo(selectedCom!, clientType).then((ComDesign) => {
        events.off(EVENT_BEFORE_COPY_COM).on(EVENT_BEFORE_COPY_COM, (tempConfig: ComTempConfig) => {
          if (ComDesign?.defaultOpts?.onBeforeCopy) {
            return ComDesign?.defaultOpts?.onBeforeCopy(tempConfig);
          }

          return tempConfig;
        });
      });
    }
  }, [selectedCom?.id]);

  if (typeof icon === 'string') {
    return (
      <CorsComponent
        weId={`${props.weId || ''}_8yc5uw`}
        app="@weapp/ebdcoms"
        compName="IconFont"
        className={className}
        name={icon}
        size="xs"
        title={title}
        onClick={onClick}
      />
    );
  }

  return React.cloneElement(icon as any, {
    key: type,
    onClick,
  });
};

export default observer(Copy);
