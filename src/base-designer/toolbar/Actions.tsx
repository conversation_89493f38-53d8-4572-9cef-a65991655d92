import React, { SyntheticEvent } from 'react';
import { ExtraAction } from '../main/types';
import ActionIcon from './ActionIcon';

interface ActionsProps extends React.Attributes {
  actions: ExtraAction[];
  onAction?: (e: SyntheticEvent, action: ExtraAction) => void;
}

const Actions: React.FC<ActionsProps> = (props) => {
  const { actions, onAction } = props;

  return (
    <>
      {actions.map((act) => {
        if (act?.customAction) {
          return <span key={act.id}>{act.customAction(act)}</span>;
        }
        return (
          <ActionIcon
            key={act.id}
            weId={`${props.weId || ''}_n5ji1g@${act.id}`}
            action={act}
            onAction={onAction}
          />
        );
      })}
    </>
  );
};

export default Actions;
