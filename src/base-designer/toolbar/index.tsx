import { CorsComponent } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { CSSProperties, ReactNode, SyntheticEvent } from 'react';
import { prefixCls } from '../../constants';
import { useLayout } from '../hooks';
import { ExtraAction } from '../main/types';
import Actions from './Actions';
import './index.less';

/**
 * 基础设计器toobar 只做渲染以及点击事件
 */

interface ToolBarProps extends React.Attributes {
  actions?: ExtraAction[];
  addonBefore?: ReactNode;
  addonAfter?: ReactNode;
  className?: string;
  style?: CSSProperties;
  filter?: (action: ExtraAction) => boolean;
  onAction: (e: SyntheticEvent, act: ExtraAction) => void;
}

export interface ToolBarLocalization {
  template?: Record<string, any>;
  [key: string]: any;
}

// 内部支持actions
export const getSupportActions: (disabled?: boolean) => ExtraAction[] = (
  disabled: boolean = false,
) => [
  {
    id: 'drag',
    title: getLabel('142334', '拖拽'),
    icon: (
      <CorsComponent
        weId="zfx5qf@"
        app="@weapp/ebdcoms"
        compName="IconFont"
        className={`${prefixCls}-toolbar-move`}
        type="cursor-move"
        size="xs"
        title={getLabel('142334', '拖拽')}
      />
    ),
    hidden: true,
  },
  {
    id: 'template',
    icon: 'Icon-add-to03',
    title: getLabel('99291', '存为模板'),
    disabled,
  },
  {
    id: 'master',
    icon: 'Icon-add-to03',
    title: getLabel('118361', '另存为'),
    disabled,
    hidden: true,
  },
  {
    id: 'separate-master',
    icon: 'Icon-Right-exit',
    title: getLabel('118358', '脱离母板'),
  },
  {
    id: 'copy',
    icon: 'Icon-copy',
    title: getLabel('124867', '复制'),
  },
  {
    id: 'separate',
    title: getLabel('66699', '分离'),
    icon: 'Icon-Right-exit',
    hidden: true,
  },
  {
    id: 'hidden',
    title: getLabel('54034', '隐藏'),
    icon: 'eye',
    hidden: true,
  },
  {
    id: 'delete',
    icon: 'Icon-delete02',
    title: getLabel('53951', '删除'),
  },
];

const Toolbar = React.forwardRef((props: ToolBarProps, ref: React.ForwardedRef<HTMLDivElement>) => {
  const {
    addonBefore, addonAfter, onAction, filter, className = '', style,
  } = props;
  const { selectedCom } = useLayout();
  const supportActions = getSupportActions();

  let { actions = supportActions } = props;

  if (selectedCom?.config.layout?.canDel === false || selectedCom?.config?.canDelete === false) {
    actions = actions.filter((action) => action.id !== 'delete');
  }

  if (selectedCom?.master) {
    actions = actions.filter((action) => action.id !== 'master');
  }

  if (!selectedCom?.master?.root) {
    actions = actions.filter((action) => action.id !== 'separate-master');
  }

  if (selectedCom?.master?.root || selectedCom?.config?.layout?.canCopy === false) {
    actions = actions.filter((action) => action.id !== 'copy');
  }

  if (filter) {
    /** 布局隐藏系统按钮 */
    actions = actions.filter(filter);
  }

  return (
    <div ref={ref} style={style} className={`${prefixCls}-toolbar ${className}`.trim()}>
      {addonBefore}
      <Actions weId={`${props.weId || ''}_5occpx`} actions={actions} onAction={onAction} />
      {addonAfter}
    </div>
  );
});

export default observer(Toolbar);
