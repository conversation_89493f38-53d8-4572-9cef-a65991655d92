@import (reference) '../../style/prefix.less';
@import (reference) '../../style/var.less';

@iconSize: 22px;

.@{prefix}-toolbar {
  display: none;
  position: absolute;
  height: @iconSize;
  z-index: @toolbar-zIndex;

  & > span {
    height: @iconSize;
    background: var(--primary);
    color: var(--base-white);

    & > span {
      height: 100%;
      width: @iconSize;
      justify-content: center;
      align-items: center;
      display: flex;
      cursor: pointer;
    }
  }

  &-config-setting {
    margin-right: 1px;
    width: 22px;
    height: 22px;
    background: var(--primary);
    color: var(--base-white);
    cursor: pointer;

    &.hide {
      display: none;
    }

    span {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  &-move {
    cursor: move;
  }

  &-disabled {
    .@{componentsPrefix}-disabled {
      color: var(--btn-primary-disable-bg);

      .ui-icon {
        height: 100%;
        width: 22px;
        justify-content: center;
        align-items: center;
        display: flex;
        cursor: pointer;
      }
    }
  }
}

.@{prefix}-config-panel {
  opacity: 1;

  & > div {
    position: relative;

    & > div {
      position: fixed;
    }
  }

  // 移除弹窗动画，避免同时打开弹窗和容器样式后造成卡顿
  .ui-dialog-content {
    animation: none;
    transition: none;
  }

  .ui-dialog-body-container {
    background-color: var(--base-white);
    max-height: 400px;
    border-bottom-left-radius: var(--dialog-border-radius);
    border-bottom-right-radius: var(--dialog-border-radius);

    .ui-dialog-body {
      padding-bottom: 4px;
    }
  }

  .ui-dialog-content {
    transition: 0s;
  }

  .ui-title-icon {
    width: 25px;
    height: 25px;
    border: 0;
    margin-bottom: 2px;

    .ui-icon-svg {
      width: 14px;
      height: 14px;
    }
  }

  .ui-title-title > div {
    font-size: 12px;
    font-weight: normal;
    margin-left: -8px;
  }

  .ui-title-inDialog {
    .ui-title-left {
      padding-left: 10px;

      .ui-title-icon .ui-icon {
        color: #333;
      }
    }

    & > div:first-of-type {
      flex: 1 1 auto;
      max-width: 100%;
    }

    & > div:last-of-type {
      flex: 0;

      svg {
        width: 14px;
        height: 14px;
      }
    }
  }

  &-all {
    display: flex;
    margin-top: 4px;
    flex-direction: row-reverse;

    & > span {
      color: #aaa;
      font-size: var(--btn-sm-font-size);
      cursor: pointer;
      outline: none;
      position: relative;
      text-align: center;
      padding: 4px 0px;
    }

    svg {
      width: 12px;
      height: 12px;
    }
  }
}
