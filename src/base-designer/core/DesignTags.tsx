import { CorsComponent } from '@weapp/ui';
import { getLabel, isEqual } from '@weapp/utils';
import React, {
  memo, useCallback, useEffect, useState,
} from 'react';
import ReactDOM from 'react-dom';
import { InternalDesignProps } from '../../core';
import { clsPrefix, ebcomPrefix } from '../../grid-designer/constants';
import setTimeoutOnce from '../../utils/setTimeoutOnce';

type DesignProps = Omit<
  InternalDesignProps,
  'client' | 'selectedCom' | 'events' | 'coms' | 'comServicePath'
> & {
  [key: string]: any;
};

const LockDesign: React.FC<DesignProps> = (props) => {
  const {
    data, showLock, showCustom, children,
  } = props;

  const [designDom, setDesignDom] = useState<Element | null>(null);

  const withTags = showLock || showCustom;

  const initDesignCom = useCallback(() => {
    let _designDom = document.querySelector(`[id=${data.id}].ebcom-design`);
    if (!_designDom) {
      const wrapper = document.querySelector(`[id=${data.id}].ebcom`);
      if (wrapper?.childNodes?.length) {
        wrapper?.childNodes.forEach((_dom: any) => {
          if (_dom?.classList?.contains('ebcom-design')) {
            _designDom = wrapper;
          }
        });
      }
    }
    if (_designDom) {
      const wrapDom = _designDom.parentElement || _designDom;
      wrapDom.classList.add(`${ebcomPrefix}-with-tags`);
      setDesignDom(wrapDom);
    } else {
      setTimeoutOnce(() => {
        // 确保子组件节点渲染出来后设置锁定图标
        initDesignCom();
      }, 200);
    }
  }, []);

  useEffect(
    () => () => {
      if (designDom) {
        designDom.classList.remove(`${ebcomPrefix}-with-tags`);
      }
    },
    [],
  );

  useEffect(() => {
    if (!designDom && withTags) {
      initDesignCom();
    }
    if (designDom && withTags) {
      designDom.classList.add(`${ebcomPrefix}-with-tags`);
    }
    if (designDom && !withTags) {
      designDom.classList.remove(`${ebcomPrefix}-with-tags`);
    }
  }, [withTags, designDom]);

  return (
    <>
      {React.cloneElement(
        children as React.ReactElement,
        {
          ...((children as React.ReactElement)?.props || {}),
          ...(props || {}),
        },
        (children as React.ReactElement)?.props?.children,
      )}
      {showLock
        && designDom
        && ReactDOM.createPortal(
          <div className={`${clsPrefix}-gl-lock`}>
            <CorsComponent
              app="@weapp/ebdcoms"
              compName="IconFont"
              weId={`${props.weId || ''}_h4ftp7`}
              type="suo"
              title={getLabel('131143', '组件已锁定')}
              size="xs"
            />
          </div>,
          designDom,
        )}
      {showCustom
        && designDom
        && ReactDOM.createPortal(
          <div className={`${clsPrefix}-gl-custom-label`}>
            <span>{getLabel('54002', '自定义')}</span>
          </div>,
          designDom,
        )}
    </>
  );
};

export default memo(LockDesign, isEqual);
