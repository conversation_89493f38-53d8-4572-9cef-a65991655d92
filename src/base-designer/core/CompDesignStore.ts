import { computed, toJS } from 'mobx';
import { ClientType } from '../../core/types';
import { InternalDesignerContextType } from '../Context';
import BaseDesignerStore from '../store';

type CompDesignStoreOptions = {
  designer: BaseDesignerStore;
  internal: InternalDesignerContextType;
  clientType?: ClientType;
};

/**
 * 组件公共的视图（设计，配置，运行）Store
 */
export default class CompDesignStore {
  designer: BaseDesignerStore;

  internal: InternalDesignerContextType;

  _clientType: ClientType | undefined;

  constructor(options: CompDesignStoreOptions) {
    const { designer, internal, clientType } = options;
    this.designer = designer;
    this.internal = internal;
    this._clientType = clientType;
  }

  @computed get layoutStore() {
    return (this.designer as any).layoutStores[this.clientType];
  }

  @computed get clientType() {
    return this._clientType || this.designer.clientType;
  }

  /** 当前设计区域包含的所有组件 */
  @computed get coms() {
    return this.layoutStore.coms;
  }

  /** 当前设计区域选中的组件 */
  @computed get selectedCom() {
    const { selectedCom } = this.layoutStore;

    if (!selectedCom) {
      return null;
    }

    return toJS(selectedCom);
  }

  /** 获取组件的所有子组件 */
  get getChildComs() {
    return this.layoutStore.getChildComs;
  }

  /** 获取组件的所有父组件 */
  get getParentComs() {
    return this.layoutStore.getParentComs;
  }

  /** 获取组件的父组件 */
  get getParentCom() {
    return this.layoutStore.getParentCom;
  }

  get copyCom() {
    return this.layoutStore.copyCom;
  }

  get selectCom() {
    return this.layoutStore.selectComByComId;
  }

  get getDroppableCom() {
    return this.layoutStore.getDroppableCom;
  }

  get deleteDroppableCom() {
    return this.layoutStore.deleteDroppableCom;
  }

  get sortComs() {
    return this.layoutStore.sortComs;
  }

  get isRoot() {
    return this.layoutStore.isRoot;
  }

  get lazyRender() {
    return this.layoutStore.lazyRender;
  }

  get changeComVisible() {
    return this.layoutStore.changeComVisible;
  }

  get setComDraggable() {
    return this.layoutStore?.setComDraggable;
  }

  get updateComsWithoutSync() {
    return this.layoutStore?.updateComsWithoutSync;
  }

  get deleteCom() {
    return this.layoutStore?.deleteCom;
  }

  get setComDroppable() {
    return this.designer?.setComDroppable;
  }

  get setDroppableChilds() {
    return this.layoutStore?.setDroppableChilds;
  }

  get softDelete() {
    return this.layoutStore?.softDelete;
  }

  get restoreSoftDelete() {
    return this.layoutStore?.restoreSoftDelete;
  }

  /** 获取所有保存过的组件 */
  @computed get savedComs() {
    return this.layoutStore.savedComs;
  }

  /** 获取当前布局是否改动的判断 */
  getLayoutChanged = () => !!(this.designer.isLayoutChanged || this.internal.isLayoutChanged);

  /** 查询组件拖拽的classname */
  get getDragCls() {
    return this.designer?.getDragCls;
  }

  /** 查询设计器设置的自定义数据 */
  get getCustomData() {
    return this.designer?.getCustomData;
  }
}
