import { isEqual } from '@weapp/utils';
import React, { memo } from 'react';
import { InternalDesignProps } from '../../core';
import BaseDesign from '../../core/design/Design';
import useInternal from '../hooks/useInternal';
import useMemoProps from '../hooks/useMemoProps';

const DesignTags = React.lazy(
  () => import(
    /* webpackChunkName: "de_base_designtags" */
    './DesignTags'
  ),
);

type DesignProps = Omit<
  InternalDesignProps,
  'client' | 'selectedCom' | 'events' | 'coms' | 'comServicePath'
> & {
  [key: string]: any;
};

const Design: React.FC<DesignProps> = (props) => {
  const { renderDesign } = useInternal()!;
  const memoProps = useMemoProps();

  const design = (
    <DesignTags weId={`${props.weId || ''}_u81ynl`} {...props}>
      <BaseDesign weId={`${props.weId || ''}_6msiwq`} {...props} {...memoProps} />
    </DesignTags>
  );

  if (renderDesign) {
    return renderDesign(design);
  }

  return design;
};

export default memo(Design, isEqual);
