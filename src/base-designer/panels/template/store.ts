import { OptionData } from '@weapp/ui/lib/components/select/types';
import { getLabel } from '@weapp/utils';
import { action, observable, runInAction } from 'mobx';
import { ReactText } from 'react';
import { IComCategory, IComCategoryChild } from '../../../core/types';
import ebdcoms from '../../../utils/ebdcoms';
import shallowObservable from '../../../utils/shallowObservable';
import { ComTempCategory, ComTempData } from '../../dialogs/com-temp/types';
import BaseDesignerStore from '../../store';
import { CompTempDataType, FooterData, Params } from './types';
import { TemplateToComp } from './utils';

const getDftCategories = (): IComCategory[] => [
  {
    id: '1',
    name: get<PERSON>abel('55626', '系统'),
    showOrder: 1,
    childs: [],
  },
  {
    id: '100',
    name: get<PERSON>abel('54002', '自定义'),
    showOrder: 100,
    childs: [],
  },
];

export class TemplatePanelStore {
  designStore: BaseDesignerStore | null = null;

  @observable loading: boolean = false;

  @observable panelLoading: boolean = false;

  @observable hasMore: boolean = true;

  /** 页数 */
  @observable pageNo: number = 1;

  /** 获取模板列表参数 */
  @observable params: Params = { module: 'EB_PAGE', layoutType: 'FLOW', pageSize: 50 };

  /**
   *  模板分类列表
   */
  @shallowObservable tempCategories: ComTempCategory[] = [];

  /**
   *  模板列表
   */
  @shallowObservable templates: CompTempDataType[] = [];

  @shallowObservable categories: IComCategory[] | null = null;

  @observable searchVal: ReactText = '';

  @observable selectedCategoryType: string = 'ALL';

  @observable footerData: FooterData | null = null;

  @action
  init(params: Params, designStore: BaseDesignerStore, reload?: boolean) {
    this.designStore = designStore;

    if (this.categories && !reload) {
      return;
    }

    this.categories = null;
    this.params = params;
    this.pageNo = 1;
    this.selectedCategoryType = 'ALL';
    this.hasMore = true;
    this.getTempCategories(params).then(() => {
      runInAction(() => {
        this.getTemplates(params);
      });
    });
  }

  @action
  getTempCategories = (params: Params) => ebdcoms.asyncExcu('ajax', {
    url: '/api/ebuilder/coms/tmpl/category/list',
    params,
    success: (data: ComTempCategory[]) => {
      runInAction(() => {
        this.tempCategories = data;
        this.templates = data.map((item: any) => {
          item.templates = [];

          return item;
        });
      });
    },
  });

  @action.bound
  getTemplates = (params: Params) => {
    const { clientType, singleClient } = this.designStore!;
    let { pageNo } = this;

    if (singleClient) {
      params.clientType = clientType;
    }

    if (params.pageNo) {
      pageNo = params.pageNo;
    }

    this.loading = true;
    ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/component/tmpl/list',
      params: {
        pageNo,
        name: this.searchVal,
        isAll: false,
        ...params,
      },
      success: (res: ComTempData) => {
        runInAction(() => {
          // 组件查询时先清空模板列表数据
          if (pageNo === 1) {
            this.templates = this.templates.map((item: any) => {
              item.templates = [];

              return item;
            });
          }

          res.templates.forEach((data) => {
            this.templates.find((template) => {
              if (template.id === data.category) {
                template.templates.push(data);
              }

              return template.id === data.category;
            });
          });

          const templates = this.templates.filter((tpl) => !!tpl.templates.length);
          const category = res.templates[res.templates.length - 1]?.category;

          this.getCategories(templates);
          this.pageNo = pageNo + 1;

          if (res.templates.length < params.pageSize) {
            this.hasMore = false;
          }

          // 点击加载更多时，记录下最后模板的分类和是否加载更多状态
          if (!params.pageNo) {
            this.categories?.find((_category) => {
              const categroyType = _category.childs.find((child) => child.id === category)?.pid;

              if (categroyType) {
                this.footerData = {
                  categroyType,
                  hasMore: this.hasMore,
                };
              }
              return categroyType;
            });
          }
        });
      },
      effect: () => {
        runInAction(() => {
          this.loading = false;
          this.panelLoading = false;
        });
      },
    });
  };

  @action
  getCategories = (tplDatas: CompTempDataType[]) => {
    const { clientType } = this.designStore!;
    const categories = getDftCategories();
    const [sysCategory, customCategory] = categories;

    tplDatas.forEach((tplData) => {
      const {
        id, name, isSys, templates,
      } = tplData;
      const comps = templates.map((com) => TemplateToComp(com, clientType));
      const { id: pid, childs, showOrder } = isSys ? sysCategory : customCategory;
      const category: IComCategoryChild = {
        id,
        pid,
        name,
        showOrder,
        comps,
      };

      childs!.push(category);
    });

    this.categories = categories;
  };

  @action
  onSearch = (searchVal: ReactText) => {
    this.searchVal = searchVal;
    this.pageNo = 1;
    this.hasMore = true;
    this.panelLoading = true;

    if (searchVal) {
      this.getTemplates({ ...this.params, pageSize: 100000, pageNo: 1 });
    } else {
      this.getTemplates({ ...this.params, pageNo: 1 });
    }

    // 筛选值为空时，hasMore取决于最近一次点击加载更多时记录的hasMore
    if (!searchVal) {
      if (
        this.selectedCategoryType === this.footerData?.categroyType
        || this.selectedCategoryType === 'ALL'
      ) {
        this.hasMore = this.footerData!.hasMore;
      } else {
        this.hasMore = false;
      }
    }
  };

  @action
  onSelect = (selectedCategoryType: string | OptionData) => {
    // 当选择的分类和记录的分类一致或为'ALL',筛选值为空时，hasMore的状态为最近一次点击加载时记录的hasMore
    if (
      (selectedCategoryType === this.footerData?.categroyType || selectedCategoryType === 'ALL')
      && !this.searchVal
    ) {
      this.hasMore = this.footerData!.hasMore;
    } else {
      this.hasMore = false;
    }

    this.selectedCategoryType = selectedCategoryType as any;
  };
}

export default new TemplatePanelStore();
