import { ClientType, IComDescription, IComTemplateData } from '../../../core/types';
import ebdcoms from '../../../utils/ebdcoms';
import json from '../../../utils/json';

export const isValidTempConfig = (configStr: string) => {
  const config = json.parse(configStr);

  if (config && Array.isArray(config)) {
    return config.filter(Boolean).length > 0;
  }
  return false;
};

export function TemplateToComp(
  tplData: IComTemplateData,
  clientType: ClientType,
  type?: string,
): IComDescription {
  // 终端作用域
  const terminalScope: ClientType[] = [];
  const { configPC: configPCStr, configMobile: configMobileStr, icon } = tplData;
  const iconPath = icon || '';
  // 模板图标设置了样式后，icon为json string，用于在组件AssetItem上显示
  const tplIcon = json.parse(iconPath) || ebdcoms.excu('getImgUrl', iconPath);

  let comsData: any = null;

  if (isValidTempConfig(configPCStr)) {
    terminalScope.push('PC');
    if (clientType === 'PC') {
      comsData = JSON.parse(configPCStr);
    }
  } else {
    tplData.configPC = '';
  }

  if (isValidTempConfig(configMobileStr)) {
    terminalScope.push('MOBILE');
    if (clientType === 'MOBILE') {
      comsData = JSON.parse(configMobileStr);
    }
  } else {
    tplData.configMobile = '';
  }

  return {
    ...tplData,
    terminalScope,
    package: '',
    icon: tplIcon,
    type: type || 'Compose',
    applyStyle: '',
    moduleScope: [],
    data: comsData,
  };
}

export default {};
