import {
  ClientType,
  IComTemplateData,
  LayoutType,
  Page,
  PageModuleType,
} from '../../../core/types';
import { ComponentPanelProps } from '../component/types';

type TemplatePanelStore = {
  reload: () => void;
};

export type Params = {
  module: PageModuleType;
  layoutType?: LayoutType;
  pageSize: number;
  category?: string;
  clientType?: ClientType;
  pageNo?: number;
  name?: string;
  isSys?: number;
  isAll?: boolean;
};

export interface TemplatePanelProps extends ComponentPanelProps {
  /** 独立使用时，需要以下属性 */
  /** 所属模块 */
  moduleScope?: PageModuleType;
  /** 设计器布局类型 */
  layoutType?: LayoutType;
  /** 页面信息 */
  pageInfo?: Page;
  clientType?: ClientType;
  /** 模板管理是否显示 */
  visible: boolean;
  /** 组件didmount的回调 */
  onDidMount?: (store: TemplatePanelStore) => void;
  /** 模板管理关闭的回调 */
  closeTempDia: () => void;
  /** 初始化时请求参数 */
  fetchParams?: Partial<Params>;
  /** 无权保存、修改配置 */
  changeDisabled?: boolean;
}

export interface CompTempDataType {
  /** 创建者 */
  creator: string;
  /** 是否删除 */
  deleteType: number;
  id: string;
  isSys: number;
  name: string;
  showOrder: number;
  templates: IComTemplateData[];
  nameLabelId?: string;
  defaultName?: string;
}

export interface FooterData {
  categroyType: string;
  hasMore: boolean;
}
