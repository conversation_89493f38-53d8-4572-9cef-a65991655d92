import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { useCallback, useEffect, useMemo } from 'react';
import { EVENT_TEMP_RELOAD, prefixCls } from '../../../constants';
import json from '../../../utils/json';
import { TempDialogView } from '../../dialogs';
import { useDesigner } from '../../hooks';
import getPageModule from '../../utils/getPageModule';
import { ComponentPanel } from '../component';
import { ICategoryViewData } from '../component/types';
import Footer from './Footer';
import './index.less';
import tplStore from './store';
import { TemplatePanelProps } from './types';

/** 每页模板组件数量 */
const pageSize = 50;

const footer = <Footer weId="60tvo0" />;

const TemplatePanel: React.FC<TemplatePanelProps> = (props) => {
  const {
    visible,
    moduleScope = 'EB_PAGE',
    layoutType,
    pageInfo,
    clientType = 'PC',
    closeTempDia,
    fetchParams,
    changeDisabled = false,
  } = props;
  const {
    categories, panelLoading, searchVal, selectedCategoryType, onSearch, onSelect,
  } = tplStore;
  // 提供给外部单独使用，不在设计器内
  const designer = useDesigner();

  const { layoutStore, comDescriptions, isUnqiue } = designer || {};

  const onDidMount = useCallback((store: any) => {
    designer?.events.on('update.comptemp', () => {
      store?.reload();
    });
  }, []);

  useEffect(() => {
    const init = (reload = true) => {
      const params = {
        pageSize,
        module: getPageModule(moduleScope || designer?.moduleScope),
        layoutType: layoutType || designer?.layoutType,
        clientType,
        ...(fetchParams || {}),
      };

      tplStore.init(params, designer, reload);
    };

    designer?.events.off(EVENT_TEMP_RELOAD);
    designer?.events.on(EVENT_TEMP_RELOAD, init);

    init(false);

    onDidMount?.({
      reload: init,
    });

    return () => {
      designer?.events.off('update.comptemp');
      designer?.events.off(EVENT_TEMP_RELOAD);
    };
  }, []);

  useEffect(() => {
    const params = {
      pageSize,
      module: getPageModule(moduleScope || designer?.moduleScope),
      layoutType: layoutType || designer?.layoutType,
      clientType,
      ...(fetchParams || {}),
    };

    tplStore.init(params, designer, true);
  }, [clientType]);

  const filter = useCallback(
    (datas: ICategoryViewData[]) => datas.filter(
      (data) => data.category === selectedCategoryType || selectedCategoryType === 'ALL',
    ),
    [selectedCategoryType],
  );

  const formatCardData = useCallback(
    (data: any) => {
      const { configPC, configMobile } = data;

      const coms = clientType === 'PC' ? json.parse(configPC) : json.parse(configMobile);
      const unqiueCom = (coms || []).find((com: any) => isUnqiue?.(com.type));

      const disabled = unqiueCom
        ? layoutStore.coms?.find((com) => unqiueCom.type === com.type)
        : false;

      return { ...data, disabled: Boolean(disabled) };
    },
    [clientType],
  );

  const length = designer?.layoutStore.coms?.length || 0;

  const getCategories = useMemo(() => {
    if (!categories) return categories;

    const _categories = toJS(categories);

    _categories.forEach((category) => {
      const { childs } = category;

      childs.forEach((child: any) => {
        child.comps = child.comps?.map((comp: any) => formatCardData(comp));
      });
    });

    return _categories;
  }, [clientType, comDescriptions, length, categories]);

  return (
    <>
      <ComponentPanel
        weId={`${props.weId || ''}_5gixmu`}
        className={`${prefixCls}-temp`}
        placeholder={getLabel('99296', '请输入模板名称')}
        {...props}
        datas={getCategories}
        footer={footer}
        loading={panelLoading}
        searchVal={searchVal}
        selectedCategoryType={selectedCategoryType}
        onSearch={onSearch}
        onCategoryTypeSelect={onSelect}
        filter={filter}
        menuType="template"
      />
      <TempDialogView
        weId="h2muv1"
        visible={visible}
        pageScope={moduleScope}
        pageInfo={pageInfo}
        clientType={clientType}
        changeDisabled={changeDisabled}
        events={designer?.events}
        onClose={closeTempDia}
      />
    </>
  );
};

export default observer(TemplatePanel);
