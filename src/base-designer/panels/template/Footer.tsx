import { Icon, Spin } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { prefixCls } from '../../../constants';
import tplStore from './store';

class Footer extends PureComponent {
  onClickMore = () => {
    const { params, getTemplates } = tplStore;

    getTemplates(params);
  };

  renderFooter = () => {
    const { loading, hasMore } = tplStore;

    if (loading) {
      return (
        <div className={`${prefixCls}-temp-footer-loading`}>
          <Spin
            weId={`${this.props.weId || ''}_xpeqvg`}
            text={`${getLabel('115120', '正在加载模板')},${getLabel('21380', '请稍候')}...`}
            size="small"
          />
        </div>
      );
    }

    if (!hasMore) {
      return (
        <div className={`${prefixCls}-temp-footer-nomore`}>
          {getLabel('115119', '已加载所有模板')}
        </div>
      );
    }

    return (
      <div className={`${prefixCls}-temp-footer-more`} onClick={this.onClickMore}>
        {getLabel('60173', '加载更多')}
        <Icon weId={`${this.props.weId || ''}_k5r6i1`} name="Icon-Down-arrow01" />
      </div>
    );
  };

  render() {
    return <div className={`${prefixCls}-temp-footer`}>{this.renderFooter()}</div>;
  }
}

export default observer(Footer);
