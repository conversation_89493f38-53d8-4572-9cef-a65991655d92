import { CorsComponent } from '@weapp/ui';
import { cloneDeep, getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import ebdcoms from '../../../utils/ebdcoms';
import { InternalDesignerContext } from '../../Context';
import CompDesignStore from '../../core/CompDesignStore';
import { onDatasetChange } from '../attribute/utils';
import { DatasetValue } from '../dataset/types';
import { ComsEventsPanelProps } from './types';
import { comLink, compatibleComConfig, getCustomActions } from './utils';

interface ComEventsStates extends React.Attributes {
  customEventsAction: any;
}

const eventKeys = [
  { clientType: 'PC', key: 'gl-pageconfig.update', refKey: 'mgl-pageconfig.update' },
  { clientType: 'MOBILE', key: 'mgl-pageconfig.update', refKey: 'gl-pageconfig.update' },
];

@inject('designStore')
@observer
class ComEvents extends PureComponent<ComsEventsPanelProps, ComEventsStates> {
  static contextType = InternalDesignerContext;

  compStore: CompDesignStore;

  selectedComId = '';

  constructor(props: ComsEventsPanelProps) {
    super(props);
    this.compStore = new CompDesignStore({
      designer: props.designStore,
      internal: this.context,
    });

    this.state = {
      customEventsAction: undefined,
    };
  }

  componentDidMount() {
    this.init();
  }

  getSnapshotBeforeUpdate() {
    const { designStore } = this.props;
    const { layoutStore } = designStore;
    if (layoutStore?.selectedCom) {
      if (this.selectedComId !== layoutStore.selectedCom.id) {
        this.init();
        this.selectedComId = layoutStore.selectedCom.id;
      }
    }
  }

  init = async () => {
    const { designStore } = this.props;
    const { layoutStore } = designStore;
    if (layoutStore?.selectedCom) {
      const eventAction = await getCustomActions(layoutStore);

      this.setState({ customEventsAction: eventAction });
    }
  };

  onChange = (changes: any) => {
    const { designStore } = this.props;
    const { layoutStore, selectedNodeId } = designStore as any;
    const isSelectPage = layoutStore?.selected || selectedNodeId === 'ROOT';

    if (isSelectPage) {
      this.onPageChange(changes);
    } else {
      this.onComChange(changes);
    }
  };

  onComChange = (changes: any) => {
    const store = this.props.designStore.layoutStore;
    const layoutType = this.props?.designStore?.layoutType;
    const { selectedCom } = store!;
    const { type } = selectedCom!;
    const { customEventsAction } = this.state;
    const customAction: any = {};
    if (type && comLink.type === type && comLink.key) {
      changes[comLink.key] = null;
    }
    // 获取自定义动作更新对应终端数据方法
    customEventsAction?.actions?.forEach((el: any) => {
      customAction[el.id] = {
        updateRefData: el.updateRefData,
      };
      if (el.config?.items) {
        Object.keys(el.config?.items).forEach((key) => {
          if (el.config.items[key].transformKeys) {
            customAction[el.id].keys = {
              ...customAction[el.id].keys,
              [key]: el.config.items[key].transformKeys,
            };
          }
        });
      }
    });
    const refChanges = cloneDeep(changes);

    const refCom = (store as any)?.refStore?.selectedCom;

    ebdcoms.excu('updateRefEventGroup', refChanges?.eventGroup || [], {
      customAction,
      store,
      prevEventGroup: refCom?.config?.eventGroup || [],
      client: store.clientType === 'PC' ? 'MOBILE' : 'PC',
    });

    if (layoutType === 'FLOW' || layoutType === 'EXCEL') {
      store.updateComConfig(changes, store.selectedCom?.id || '', {
        action: 'common.update.actions',
        args: [refChanges],
      });
    } else {
      (store as any).emit('com.update', { config: { ...changes } }, store.selectedCom?.id);
      (store as any).refStore?.emit?.(
        'com.update',
        { config: { ...refChanges } },
        store.selectedCom?.refCompId,
      );
    }
  };

  /** 页面config改变 */
  onPageChange = (val: any) => {
    const store = this.props.designStore.layoutStore;
    const layoutType = this.props?.designStore?.layoutType;
    const { designStore } = this.props;
    const { clientType } = designStore;
    const changes: any = val?.eventGroup || [];
    const refChanges = cloneDeep(changes);

    const refPageConfig = (store as any)?.refStore?.pageConfig;

    ebdcoms.excu('updateRefEventGroup', refChanges || [], {
      store,
      prevEventGroup: refPageConfig?.eventGroup || [],
      client: store.clientType === 'PC' ? 'MOBILE' : 'PC',
    });

    if (layoutType === 'FLOW') {
      store.updatePageConfig({ eventGroup: changes });
      (store as any).refStore?.updatePageConfig?.({ eventGroup: refChanges });
    } else {
      const eventKey = eventKeys.find((item) => item.clientType === clientType);
      if (!eventKey) return;

      (store as any).emit(eventKey.key, 'eventGroup', changes);
      (store as any).refStore?.emit?.(eventKey.refKey, 'eventGroup', refChanges);
    }
  };

  /** 数据集配置改变 */
  onDatasetChange = (datasetVal?: any, type?: string) => {
    const {
      layoutDatas, page, datasetVals, setDatasetVals,
    } = this.props.designStore;
    if (!layoutDatas?.length) return;

    const cb = (v: DatasetValue[]) => {
      setDatasetVals(v);
      ebdcoms.excu('setEbParams', 'pageParams', page?.id, {
        datasetVals: v || [],
      });
    };

    onDatasetChange({
      datasetVal,
      type,
      datasetVals,
      setDatasetVals: cb,
      userCustom: (this.props.designStore as any)?.userCustom,
    });
  };

  render() {
    const {
      designStore, page: propsPage, hiddenActions, appId,
    } = this.props;
    const {
      clientType, pageId, datasetVals, page: _page,
    } = designStore as any;
    const { layoutStore, layoutType, selectedNodeId } = designStore as any;
    const { customEventsAction } = this.state;
    const isSelectPage = layoutStore?.selected || selectedNodeId === 'ROOT';

    if (!layoutStore || (!layoutStore?.selectedCom && !isSelectPage)) {
      return (
        <CorsComponent
          app="@weapp/ebdcoms"
          compName="Empty"
          weId={`${this.props.weId || ''}_u163ku`}
          type="noData"
          description={getLabel('116397', '请在左侧区域选择组件')}
        />
      );
    }

    const { selectedCom } = layoutStore;
    const { id, type, config } = selectedCom || {};
    const data = isSelectPage
      ? {
        eventGroup: toJS(layoutStore?.config?.eventGroup || layoutStore?.pageConfig?.eventGroup),
      }
      : compatibleComConfig(toJS(config), clientType);
    const page = {
      ...propsPage,
      id: _page.id || pageId,
      client: clientType,
      module: _page.module,
      appid: propsPage?.appid || appId,
      layoutType,
      datasetVals: toJS(datasetVals),
    };

    return (
      <CorsComponent
        app="@weapp/ebdcoms"
        compName="EventsAction"
        weId={`${this.props.weId || ''}_ji3kt6`}
        source="basic_designer" /** 特殊处理历史部分耦合事件动作 */
        key={`${id || pageId}_${clientType}`}
        hiddenActions={hiddenActions}
        page={page}
        data={data}
        comId={id}
        type={type}
        store={this.compStore}
        formBuilderStore={(this.props as any).formBuilderStore} // TODO 表单按钮事件，将来按钮组件逻辑挪至按钮内部，这里删除
        eventAction={customEventsAction}
        onChange={this.onChange}
        onDatasetChange={this.onDatasetChange}
      />
    );
  }
}

export default ComEvents as unknown as ComponentType<ComsEventsPanelProps>;
