// import { RootStoreProps } from '@weapp/ebddesigner/lib/components/design/types';
// import { CommonStoreProps } from '@weapp/ebddesigner/lib/stores/types';

import { BaseDesignerStore } from '../../export';

export interface ComsEventsPanelProps {
  designStore: BaseDesignerStore;
  page?: any;
  appId?: string;
  hiddenActions?: string[];
}
// RootStoreProps & CommonStoreProps &
export default {};
