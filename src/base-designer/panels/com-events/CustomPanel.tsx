import React from 'react';
import { InternalDesignerContext } from '../../Context';
import ComEvents from './Panel';
import { ComsEventsPanelProps } from './types';

class CustomPanel extends React.PureComponent<ComsEventsPanelProps> {
  static contextType = InternalDesignerContext;

  render() {
    const { renderConfig } = this.context;
    const data = <ComEvents weId={`${this.props.weId || ''}_h9xu9d`} {...this.props} />;
    const content = renderConfig ? renderConfig(data) : data;

    return content;
  }
}

export default CustomPanel;
