import { isString } from '@weapp/utils';
import { ClientType, getComponent } from '../../../core';
import { EventHandler } from '../../../core/event-action';
import {
  getEventAction,
  configPluginCenter as pluginCenter,
} from '../../../core/plugins/core/ConfigPluginCenter';
import ebdcoms from '../../../utils/ebdcoms';
import { BaseLayoutStore } from '../../export';

// 组件需要删除的链接地址key
export const comLink = {
  key: '',
  type: '',
};

/**
 * 链接地址转事件动作
 * @param config 组件配置
 * @returns
 */
export const compatibleComConfig = (config: any, clientType: ClientType) => {
  const newConfig = {
    ...config,
  };
  const { compatible } = ebdcoms.get();
  if (!compatible) return newConfig;

  if (ebdcoms.excu('isChart', newConfig?.type)) {
    if (!newConfig.eventGroup && !!newConfig.url) {
      // 兼容eb数据
      if (isString(newConfig.url)) {
        newConfig.url = {
          page: {
            pageName: newConfig.url,
            pageId: newConfig.url,
            pageType: 'LINK',
          },
          openMode: clientType === 'MOBILE' ? '4' : '5',
          winSize: '60%',
        };
      }
      newConfig.eventGroup = compatible.linkConvertEventAction(newConfig.url, 'CLICKDATA');
      comLink.key = 'url';
      comLink.type = newConfig?.type;
      delete newConfig.url;
    }
    return newConfig;
  }

  switch (newConfig?.type) {
    case 'Picture':
      if (!!newConfig.link && !newConfig.eventGroup) {
        newConfig.eventGroup = compatible.linkConvertEventAction(newConfig.link);
        comLink.key = 'link';
        comLink.type = newConfig?.type;
        delete newConfig.link;
      }
      return newConfig;
    case 'List':
    case 'Map':
      if (!!newConfig.url && !newConfig.eventGroup) {
        newConfig.eventGroup = compatible.linkConvertEventAction(newConfig.url, 'CLICKDATA');
        comLink.key = 'url';
        comLink.type = newConfig?.type;
        delete newConfig.url;
      }
      return newConfig;
    default:
      return newConfig;
  }
};

/**
 * 获取组件自定义事件动作描述
 * @param com 组件元信息
 * @param clientType 终端类型
 * @returns
 */
export const getCustomActions = async (store: BaseLayoutStore) => {
  const viewType = store.clientType === 'MOBILE' ? 'MConfig' : 'Config';
  let Config: any = await getComponent(store.selectedCom!, viewType, {
    wrapped: false,
    default: false,
  });
  let plugins = [];
  Config = Config.default ? Config.default : Config;
  const config = typeof Config === 'function' ? Config(store.selectedCom, store, pluginCenter) : Config;
  try {
    const result = await config;
    plugins = result.plugins;
  } catch (e) {
    console.error(e);
  }
  const newEventAction = getEventAction({ eventAction: Config.eventAction, plugins });

  return newEventAction;
};

/**
 * 实例化事件动作方法
 * @param com 组件元信息
 * @param params 事件动作额外参数
 * @returns
 */
export const getEventHandlerInstance = (com: any, params: any) => {
  const {
    page, events, renderLayout, dom,
  } = params;
  const eventParams = {
    page,
    comId: com.id,
    events,
    renderLayout,
    dom,
  };
  if (page?.config?.pageLayout) {
    eventParams.page.layout = page?.config?.pageLayout;
  }
  const eventHandler = new EventHandler(com, eventParams);
  // eslint-disable-next-line max-len
  const create = (eventGroup: any[][]) => EventHandler.createHandler(eventGroup, { com, params: eventParams, eventHandler });

  return {
    eventHandler,
    trigger: eventHandler.trigger.bind(eventHandler),
    create,
  };
};

export default {};
