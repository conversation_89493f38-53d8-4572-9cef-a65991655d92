import { observer } from 'mobx-react';
import React from 'react';
import { SourceCodeType } from '../../../common/sourcecode/types';
import { useDesigner } from '../../hooks';
import CssEditor from './css';
import { EditorProps, EditorType } from './types';

const Editor = <T extends EditorType>(props: EditorProps<T>) => {
  const { type } = props;

  const { clientType, layoutStore, page } = useDesigner();
  const { sourceCode, updateSourceCode } = layoutStore;

  const onChange = React.useCallback(
    (code: SourceCodeType) => {
      updateSourceCode(code);
    },
    [clientType],
  );

  if (type === 'css') {
    return (
      <CssEditor
        weId={`${props.weId || ''}_gn1c2l`}
        clientType={clientType}
        sourceCode={sourceCode}
        pageId={page.id}
        onChange={onChange}
      />
    );
  }

  return null;
};

export default observer(Editor);
