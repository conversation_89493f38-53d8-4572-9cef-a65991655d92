import { Cors<PERSON>omponent, Spin } from '@weapp/ui';
import React from 'react';
import { When } from 'react-if';
import { SourceCodeType } from '../../../../common/sourcecode/types';
import { getCodeData, getECodeJson, importCssText } from '../../../../common/sourcecode/utils';
import { prefixCls } from '../../../../constants';
import { ClientType } from '../../../../core';
import './index.less';
import QuickSetting from './quick-setting';
import Tools from './tools';
import { getHoveredDom } from './utils';
import setTimeoutOnce from '../../../../utils/setTimeoutOnce';

export interface CssEditorProps extends React.Attributes {
  clientType: ClientType;
  sourceCode?: SourceCodeType;
  pageId: string;
  onChange: (code: SourceCodeType) => void;
}

interface CssEditorStates {
  loading: boolean;
  hoveredDom: HTMLElement | null;
  selectedDoms: HTMLElement[];
}
export default class CssEditor extends React.PureComponent<CssEditorProps, CssEditorStates> {
  editor: any = null;

  constructor(props: CssEditorProps) {
    super(props);

    this.state = {
      loading: true,
      hoveredDom: null,
      selectedDoms: [],
    };
  }

  componentDidMount() {
    // 隐藏tools
    document.querySelector(`.${prefixCls}-flow-tools`)!.classList.add('hide');

    const workSpace = document.querySelector(`.${prefixCls}-workspace`)! as HTMLElement;

    workSpace.addEventListener('mouseover', this.setHoverDom);

    workSpace.addEventListener('click', this.cancelSelected);
  }

  componentWillUnmount() {
    document.querySelector(`.${prefixCls}-flow-tools`)!.classList.remove('hide');

    const workSpace = document.querySelector(`.${prefixCls}-workspace`)! as HTMLElement;

    workSpace.removeEventListener('mouseover', this.setHoverDom);

    workSpace.removeEventListener('click', this.cancelSelected);
  }

  // 设置鼠标hover的Dom
  setHoverDom = (e: MouseEvent) => {
    const hoveredDom = getHoveredDom(e.target as HTMLElement);

    this.setState({ hoveredDom });

    hoveredDom?.addEventListener('click', this.onClick);
  };

  onMount = (editor: any) => {
    this.editor = editor;

    // editor model 会延迟生成
    setTimeoutOnce(() => {
      this.setState({ loading: false });
    });
  };

  onClick = (e: MouseEvent) => {
    e.stopPropagation();

    const { hoveredDom } = this.state;
    const styleName = hoveredDom!.dataset.style;

    if (hoveredDom!.classList.contains(`${prefixCls}-editor-css-select`)) {
      this.setState({ selectedDoms: [] }, () => {
        this.setState({ selectedDoms: [hoveredDom as HTMLElement] });
      });

      if (hoveredDom!.id) {
        this.insertClsCode(`#${hoveredDom!.id}`);
      } else {
        this.insertClsCode(`.${styleName}`);
      }
    } else {
      const doms = document.querySelectorAll(`[data-style = ${styleName}]`);
      const selectedDoms: HTMLElement[] = [];

      doms.forEach((_dom) => {
        selectedDoms.push(_dom as HTMLElement);
      });

      this.setState({ selectedDoms: [] }, () => {
        this.setState({ selectedDoms });
      });

      this.insertClsCode(`.${styleName}`);
    }
  };

  cancelSelected = () => {
    this.setState({ selectedDoms: [] });
  };

  // 插入类名
  insertClsCode = (clsName: string) => {
    const reg = new RegExp(`${clsName}(\\s)*\\{`);
    const range = this.editor.getModel().findMatches(reg, null, true);

    if (!range.length) {
      const css = this.editor.getValue() ? `${this.editor.getValue()}\n\n` : '';

      this.editor.setValue(`${css}${clsName} {\n\n}`);
    }

    const { range: _range } = this.editor.getModel().findMatches(reg, null, true)[0];

    this.editor.revealRangeAtTop(_range);
    this.editor.setSelection(_range);
  };

  // 快捷插入css样式
  insertCode = (code: string) => {
    const { selectedDoms } = this.state;
    const [selectedDom] = selectedDoms;

    const clsName = selectedDom.dataset.style;
    const { id } = selectedDom;

    // 记录当前要插入的类名是否在code中
    const regCls = new RegExp(`(.${clsName}|#${id})(\\s)*\\{[^\\}]*\\}`);
    const hasClsNameInCode = regCls.exec(this.editor.getModel().getValue());

    if (!hasClsNameInCode) {
      const css = this.editor.getValue() ? `${this.editor.getValue()}\n\n` : '';

      this.editor.setValue(`${css}.${clsName} {\n  ${code}\n}`);

      const { range } = this.editor.getModel().findMatches(`.${clsName} {`)[0];

      this.editor.setSelection(range);
    } else {
      const selection = this.editor.getModel().getValueInRange(this.editor.getSelection());
      const reg = new RegExp(`(.${clsName}|#${id})(\\s){`);
      const hasClsNameInSelection = reg.exec(selection);

      let clsCode = regCls.exec(this.editor.getModel().getValue())![0];

      if (hasClsNameInSelection) {
        const _regCls = new RegExp(`${selection}(\\s)*[^\\}]*\\}`);

        clsCode = _regCls.exec(this.editor.getModel().getValue())![0] as string;
      }

      const { range } = this.editor.getModel().findMatches(clsCode)[0];
      const { startLineNumber, endLineNumber } = range;
      const cssCode = this.editor.getModel().getValueInRange(range);
      const cssAtrributes: Record<string, string> = {};

      cssCode
        .replace(/(\n|\s)/g, '')
        .split('{')[1]
        .split('}')[0]
        .split(';')
        .filter((_item: string) => !!_item)
        .forEach((_item: string) => {
          const [name, value] = _item.split(':');

          cssAtrributes[name] = value;
        });

      const [_name, _value] = code.split(';')[0].split(':');

      cssAtrributes[_name] = _value;

      const _code = Object.keys(cssAtrributes).reduce(
        (css, attr) => `${css}  ${attr}: ${cssAtrributes[attr]};\n`,
        '',
      );

      // 插入代码的位置和内容
      this.editor.executeEdits('insert-code', [
        {
          range: {
            startLineNumber: startLineNumber + 1,
            endLineNumber,
          },
          text: _code,
        },
      ]);
    }
  };

  onCssChange = (cssCode: string) => {
    const {
      sourceCode, pageId, clientType, onChange,
    } = this.props;
    const { codeData, compiledCodeData } = getCodeData(sourceCode?.ebcode);
    const data = {
      pageId,
      clientType,
      codeData: { ...codeData, css: cssCode },
      compiledCodeData: { ...compiledCodeData, css: cssCode },
    };

    const ecode = getECodeJson(data, true);

    onChange({
      ebcode: encodeURI(getECodeJson(data)),
      ecode: encodeURI(ecode),
    });

    importCssText(JSON.parse(ecode).css.compiledContent);
  };

  render() {
    const { sourceCode } = this.props;
    const { loading, hoveredDom, selectedDoms } = this.state;
    const { css } = getCodeData(sourceCode?.ebcode).codeData;

    return (
      <div className={`${prefixCls}-editor-css-panel`}>
        <When weId={`${this.props.weId || ''}_1lxacs`} condition={!loading}>
          <QuickSetting
            weId={`${this.props.weId || ''}_debr0h`}
            disable={!selectedDoms.length}
            insertCode={this.insertCode}
          />
        </When>
        <div className={`${prefixCls}-editor-css-editor`}>
          <CorsComponent
            weId={`${this.props.weId || ''}_kcbiti`}
            app="@weapp/ecode"
            compName="MonacoEditor"
            language="css"
            options={{
              minimap: {
                enabled: false, // 隐藏编辑器右侧小地图
              },
              scrollBeyondLastLine: false, // 超出编辑器高度后再出现滚动条
            }}
            src="/build/ecode/vs"
            tabSize={2}
            renderLoader={
              <Spin
                weId={`${this.props.weId || ''}_g0a7di`}
                className={`${prefixCls}-editor-css-editor-loading`}
              />
            }
            value={css}
            onMount={this.onMount}
            onChange={this.onCssChange}
          />
        </div>
        <When weId={`${this.props.weId || ''}_i2zoec`} condition={!loading}>
          <div className={`${prefixCls}-editor-css-tools`}>
            <When weId={`${this.props.weId || ''}_bwutbs`} condition={!!hoveredDom}>
              <Tools
                weId={`${this.props.weId || ''}_bs5byo`}
                type="hover"
                dom={hoveredDom!}
                onClick={this.onClick}
              />
            </When>
            {selectedDoms.map((dom, index) => (
              <Tools
                weId={`${this.props.weId || ''}_bs5byo`}
                // eslint-disable-next-line react/no-array-index-key
                key={String(index)}
                type="select"
                dom={dom}
              />
            ))}
          </div>
        </When>
      </div>
    );
  }
}
