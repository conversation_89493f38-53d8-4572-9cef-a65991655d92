import React, { useEffect } from 'react';
import { prefixCls } from '../../../../../constants';
import '../index.less';
import ToolBar from './ToolBar';

type ToolType = 'select' | 'hover';

interface ToolsProps extends React.Attributes {
  type: ToolType;
  dom: HTMLElement;
  onClick?: (e: MouseEvent) => void;
}

const Tools: React.FC<ToolsProps> = (props) => {
  const { dom, type, onClick } = props;

  useEffect(() => {
    dom.classList.add(`${prefixCls}-editor-css-${type}`);

    return () => {
      dom.classList.remove(`${prefixCls}-editor-css-${type}`);

      if (type === 'hover') {
        dom.removeEventListener('click', onClick!);
      }
    };
  }, [String(dom)]);

  return (
    <div>{type === 'hover' ? <ToolBar weId={`${props.weId || ''}_5366dw`} dom={dom} /> : null}</div>
  );
};

export default Tools;
