import React from 'react';
import { prefixCls } from '../../../../../constants';
import { position } from '../utils';

interface ToolBarProps extends React.Attributes {
  dom: HTMLElement;
}

const ToolBar: React.FC<ToolBarProps> = (props) => {
  const { dom } = props;

  let domClsNames = '';

  dom.classList.forEach((cls) => {
    if (cls === `${prefixCls}-editor-css-hover` || cls === `${prefixCls}-editor-css-select`) {
      domClsNames += '';
    } else {
      domClsNames += `.${cls}`;
    }
  });

  return (
    <div className={`${prefixCls}-editor-css-tools-toolbar`} style={{ ...position(dom) }}>
      {domClsNames || dom.dataset.style}
    </div>
  );
};

export default ToolBar;
