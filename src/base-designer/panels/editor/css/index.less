@import (reference) '../../../../style/prefix.less';

.@{prefix}-editor-css-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .@{prefix}-editor-css-editor {
    flex: 1;
    margin: 0px -15px;
    overflow: hidden;

    &-loading {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .monaco-editor {
      .monaco-scrollable-element canvas {
        display: none;
      }

      .margin-view-overlays {
        background-color: #f6f6f6;
      }

      .suggest-widget {
        left: auto !important;
        right: 0;
        max-width: 215px !important;

        & > div {
          margin-left: -40px;
        }
      }

      .margin {
        max-width: 40px;

        .line-numbers {
          width: 25px !important;
        }
      }

      .margin-view-overlays {
        max-width: 40px;
      }

      .monaco-scrollable-element {
        left: 42px !important;
        width: 210px !important;
      }

      .overflowingContentWidgets .monaco-hover {
        display: none;
      }
    }
  }
}

.@{prefix}-editor-css-tools {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  pointer-events: none;

  &-toolbar {
    position: absolute;
    line-height: 22px;
    background-color: rgba(255, 204, 102);
    padding: 0 4px;
    color: var(--base-white);
  }
}

.@{prefix}-flow-tools {
  &.hide {
    display: none;
  }
}

.@{prefix}-editor-css-hover,
.@{prefix}-editor-css-select {
  outline: 3px solid rgba(255, 204, 102, 0.2) !important;
  background-color: rgba(255, 204, 102, 0.1) !important;
  box-shadow: 0 0 8px 4px rgb(255 204 102 / 50%);
  z-index: 3;
}
