export const getHoveredDom = (dom: HTMLElement) => {
  let hoveredDom = dom;

  while (dom && !hoveredDom.dataset.style && hoveredDom.tagName !== 'BODY') {
    hoveredDom = hoveredDom.parentElement as HTMLElement;
  }

  return hoveredDom.tagName === 'BODY' ? null : hoveredDom!;
};

export const position = (dom: HTMLElement) => {
  const offset = dom.getBoundingClientRect();
  const { right, bottom } = offset;

  const style = {
    top: `${bottom + 3}px`,
    right: `${document.body.clientWidth - right}px`,
  };

  return style;
};

export default {};
