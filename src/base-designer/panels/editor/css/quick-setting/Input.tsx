import { Button, Input as UInput } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { useCallback, useState } from 'react';
import { prefixCls } from '../../../../../constants';

const { InputNumber } = UInput;

interface InputProps extends React.Attributes {
  onSure: (val: string) => void;
}

const Input: React.FC<InputProps> = (props) => {
  const [value, setValue] = useState<React.ReactText>(0);

  const onSure = useCallback(() => {
    props.onSure(`${value}px`);
  }, [value]);

  return (
    <div className={`${prefixCls}-editor-css-item-input`}>
      <InputNumber
        weId={`${props.weId || ''}_nfv138`}
        suffix="px"
        value={value}
        onChange={setValue}
      />
      <Button weId={`${props.weId || ''}_bhicp5`} type="primary" size="small" onClick={onSure}>
        {getLabel('40565', '确定')}
      </Button>
    </div>
  );
};

export default Input;
