@import (reference) '../../../../../style/prefix.less';

.@{prefix}-editor-css-quicksetting {
  margin-bottom: 5px;

  .@{prefix}-eitor-css-quick-item {
    margin: 3px 2px;

    &.disable {
      color: var(--base-white);
      cursor: not-allowed;
      background-color: #ccc;

      &:hover {
        border-color: var(--border-color);
        background-color: #ccc;

        &::before {
          background-color: #ccc;
        }
      }
    }
  }
}

.@{prefix}-editor-css-item-space {
  width: 300px;
  display: flex;
  align-items: center;

  & > div:first-of-type {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-end;
    margin-right: 9px;
    height: 100px;

    & > div {
      width: 68px;
    }

    & > button {
      margin-top: 9px;
    }
  }

  &-differ {
    display: flex;
    align-items: center;
    background: #efefef;
    border-radius: 3px;

    & > div {
      margin: 4px;
    }

    &-top > div:first-of-type {
      margin-bottom: 30px;
    }
  }
}

.@{prefix}-editor-css-item-input {
  width: 200px;
  display: flex;
  align-items: center;

  & > div {
    flex: 1;
  }

  & > button {
    margin-left: 10px;
  }
}

.@{prefix}-editor-css-item-buttons {
  width: 175px;
  overflow: hidden;

  & > div {
    margin-right: -5px;

    & > span {
      min-width: 85px;
      text-align: center;
      height: 24px;
      line-height: 23px;
      display: inline-block;
      color: #333;
      background: #fafafa;
      border: 1px solid #d9d9d9;
      border-radius: 3px;
      margin-right: 5px;
      margin-bottom: 5px;
      cursor: pointer;

      &:hover {
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.35);
      }

      &.selected {
        background: #aaa;
        color: #fff;
      }
    }
  }

  & > button {
    display: block;
    float: right;
  }
}

.@{prefix}-editor-css-item-color {
  width: 170px;
  display: flex;
  align-items: center;

  & > button {
    margin-left: 10px;
  }
}

.@{prefix}-editor-css-item-border {
  width: 250px;
  overflow: hidden;
  color: var(--regular-fc);

  & > div {
    height: 30px;
    margin-bottom: 8px;
    line-height: 30px;

    .ui-colorpicker {
      max-width: 100%;
    }

    .@{prefix}-editor-css-border-style-item {
      min-width: 52px;
      text-align: center;
      height: 24px;
      line-height: 23px;
      display: inline-block;
      color: #333;
      background: #fafafa;
      border: 1px solid #d9d9d9;
      border-radius: 3px;
      margin-right: 5px;
      margin-bottom: 5px;
      padding: 0 5px;
      cursor: pointer;

      &:last-of-type {
        margin-right: 0px;
      }

      &:hover {
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.35);
      }

      &.selected {
        background: #aaa;
        color: #fff;
      }
    }
  }

  & > button {
    float: right;
  }
}
