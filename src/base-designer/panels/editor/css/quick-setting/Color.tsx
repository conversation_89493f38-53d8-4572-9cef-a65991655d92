import { Button, ColorPicker } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { useCallback, useState } from 'react';
import { prefixCls } from '../../../../../constants';

interface ColorProps extends React.Attributes {
  onSure: (val: string) => void;
}

const Color: React.FC<ColorProps> = (props) => {
  const [value, setValue] = useState<string>('');

  const onSure = useCallback(() => {
    props.onSure(value);
  }, [value]);

  return (
    <div className={`${prefixCls}-editor-css-item-color`}>
      <ColorPicker weId={`${props.weId || ''}_186dhu`} onChange={setValue} />
      <Button weId={`${props.weId || ''}_7vfglq`} type="primary" size="small" onClick={onSure}>
        {getLabel('40565', '确定')}
      </Button>
    </div>
  );
};

export default Color;
