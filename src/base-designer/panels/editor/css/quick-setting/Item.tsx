import { Button, Popover } from '@weapp/ui';
import { classnames } from '@weapp/utils';
import React, { useCallback, useState } from 'react';
import { prefixCls } from '../../../../../constants';
import Border from './Border';
import Buttons from './Buttons';
import Color from './Color';
import Input from './Input';
import Space from './Space';

interface ItemProps extends React.Attributes {
  disable: boolean;
  type: string;
  onSure: (css: string) => void;
}

const AttributeCont: React.FC<Omit<ItemProps, 'disable'>> = (props) => {
  const { type, onSure } = props;

  if (type === 'margin' || type === 'padding') {
    return <Space weId={`${props.weId || ''}_8ag4cf`} onSure={onSure} />;
  }

  if (
    type === 'width'
    || type === 'height'
    || type === 'line-height'
    || type === 'font-size'
    || type === 'border-radius'
  ) {
    return <Input weId={`${props.weId || ''}_urwo8f`} onSure={onSure} />;
  }

  if (
    type === 'font-family'
    || type === 'font-weight'
    || type === 'text-align'
    || type === 'box-sizing'
    || type === 'position'
    || type === 'display'
    || type === 'float'
  ) {
    return <Buttons weId={`${props.weId || ''}_4gy7uc`} type={type} onSure={onSure} />;
  }

  if (type === 'color') {
    return <Color weId={`${props.weId || ''}_ugznj5`} onSure={onSure} />;
  }

  if (type === 'border') {
    return <Border weId={`${props.weId || ''}_lle8bc`} onSure={onSure} />;
  }

  return null;
};

const Item: React.FC<ItemProps> = (props) => {
  const [visible, setVisible] = useState<boolean>(false);
  const { type, disable } = props;

  const onSure = useCallback(
    (css: string) => {
      if (css) {
        props.onSure(`${type}: ${css};`);
      }

      setVisible(false);
    },
    [type],
  );

  const onVisibleChange = useCallback(
    (_visible?: boolean) => {
      setVisible(!disable && Boolean(_visible));
    },
    [type, disable],
  );

  return (
    <Popover
      weId={`${props.weId || ''}_j0u9zw`}
      placement="bottom"
      popup={
        visible && <AttributeCont weId={`${props.weId || ''}_fsphhe`} type={type} onSure={onSure} />
      }
      action={['click']}
      visible={visible}
      onVisibleChange={onVisibleChange}
      triggerProps={{ blacklist: ['.ui-trigger-popupInner-mask', '.ui-colorpicker-popover'] }}
    >
      <Button
        weId={`${props.weId || ''}_9fdmdr`}
        size="small"
        className={classnames(`${prefixCls}-eitor-css-quick-item`, {
          disable,
        })}
      >
        {type}
      </Button>
    </Popover>
  );
};

export default Item;
