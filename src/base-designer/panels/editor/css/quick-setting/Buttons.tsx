import { Button } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import React, { useCallback, useState } from 'react';
import { prefixCls } from '../../../../../constants';

interface ButtonsProps extends React.Attributes {
  type: string;
  onSure: (val: string) => void;
}

const getBtns = (type: string) => {
  if (type === 'font-family') {
    return [
      { text: '宋体', value: 'SimSun' },
      { text: '楷体', value: 'KaiTi' },
      { text: '黑体', value: 'SimHei' },
      { text: '隶书', value: 'LiSu' },
      { text: '微软雅黑', value: 'Microsoft YaHei' },
      { text: 'andaleMono', value: 'andale mono' },
      { text: 'arial', value: 'arial, helvetica, sans-serif' },
      { text: 'arialBlack', value: 'arial black, avant garde' },
      { text: 'comicSansMs', value: 'comic sans ms' },
      { text: 'impact', value: 'impact, chicago' },
      { text: 'timesNewRoman', value: 'times new roman' },
    ];
  }

  if (type === 'font-weight') {
    return [
      { text: 'bold', value: 'bold' },
      { text: 'normal', value: 'normal' },
      { text: 'light', value: 'light' },
    ];
  }

  if (type === 'text-align') {
    return [
      { text: 'left', value: 'left' },
      { text: 'center', value: 'center' },
      { text: 'right', value: 'right' },
    ];
  }

  if (type === 'box-sizing') {
    return [
      { text: 'border-box', value: 'border-box' },
      { text: 'content-box', value: 'content-box' },
    ];
  }

  if (type === 'position') {
    return [
      { text: 'relative', value: 'relative' },
      { text: 'absolute', value: 'absolute' },
      { text: 'fixed', value: 'fixed' },
    ];
  }

  if (type === 'display') {
    return [
      { text: 'block', value: 'block' },
      { text: 'none', value: 'none' },
      { text: 'inline-block', value: 'inline-block' },
    ];
  }

  if (type === 'float') {
    return [
      { text: 'left', value: 'left' },
      { text: 'right', value: 'right' },
    ];
  }

  return [];
};

const Buttons: React.FC<ButtonsProps> = (props) => {
  const [value, setValue] = useState<string>('');
  const { type } = props;
  const btns = getBtns(type);

  const onClick = useCallback(
    (_val: string) => () => {
      setValue(_val);
    },
    [type],
  );

  const onSure = useCallback(() => {
    props.onSure(value);
  }, [type, value]);

  return (
    <div className={`${prefixCls}-editor-css-item-buttons`}>
      <div>
        {btns.map((btn) => (
          <span
            key={btn.value}
            onClick={onClick(btn.value)}
            className={classnames(`${prefixCls}-editor-css-item-buttons-item`, {
              selected: value === btn.value,
            })}
          >
            {btn.text}
          </span>
        ))}
      </div>
      <Button weId={`${props.weId || ''}_7vfglq`} type="primary" size="small" onClick={onSure}>
        {getLabel('40565', '确定')}
      </Button>
    </div>
  );
};

export default Buttons;
