import {
  Button, ColorPicker, Input, Layout,
} from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import React, { useCallback, useState } from 'react';
import { prefixCls } from '../../../../../constants';

const { InputNumber } = Input;
const { Row, Col } = Layout;

const borderStyles = ['solid', 'dashed', 'dotted'];

interface BorderProps extends React.Attributes {
  onSure: (val: string) => void;
}

const Border: React.FC<BorderProps> = (props) => {
  const [color, setColor] = useState<string>('#fff');
  const [width, setWidth] = useState<React.ReactText>(1);
  const [style, setStyle] = useState<string>('solid');

  const onStyleChange = useCallback(
    (_style) => () => {
      setStyle(_style);
    },
    [],
  );

  const onSure = useCallback(() => {
    props.onSure(`${width}px ${style} ${color}`);
  }, [color, width, style]);

  return (
    <div className={`${prefixCls}-editor-css-item-border`}>
      <Row weId={`${props.weId || ''}_uz2l1h`}>
        <Col weId={`${props.weId || ''}_2oy7a8`} span={8}>
          Color
        </Col>
        <Col weId={`${props.weId || ''}_f45mc4`} span={16}>
          <ColorPicker weId={`${props.weId || ''}_z1pysm`} value={color} onChange={setColor} />
        </Col>
      </Row>
      <Row weId={`${props.weId || ''}_ks1znk`}>
        <Col weId={`${props.weId || ''}_dfnyfg`} span={8}>
          Border Width
        </Col>
        <Col weId={`${props.weId || ''}_31koc6`} span={16}>
          <InputNumber
            weId={`${props.weId || ''}_i7kz8q`}
            suffix="px"
            value={width}
            min={0}
            onChange={setWidth}
          />
        </Col>
      </Row>
      <Row weId={`${props.weId || ''}_zfqd27`}>
        <Col weId={`${props.weId || ''}_zqy49s`} span={8}>
          Border Style
        </Col>
        <Col weId={`${props.weId || ''}_6uujgs`} span={16}>
          {borderStyles.map((_style) => (
            <span
              key={_style}
              onClick={onStyleChange(_style)}
              className={classnames(`${prefixCls}-editor-css-border-style-item`, {
                selected: style === _style,
              })}
            >
              {_style}
            </span>
          ))}
        </Col>
      </Row>
      <Button weId={`${props.weId || ''}_7vfglq`} type="primary" size="small" onClick={onSure}>
        {getLabel('40565', '确定')}
      </Button>
    </div>
  );
};

export default Border;
