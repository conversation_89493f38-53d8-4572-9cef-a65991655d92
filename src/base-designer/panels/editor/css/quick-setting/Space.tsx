import { Button, Input } from '@weapp/ui';
import { difference, getLabel } from '@weapp/utils';
import React, { useCallback, useState } from 'react';
import { prefixCls } from '../../../../../constants';

const { InputNumber } = Input;

interface SpaceProps extends React.Attributes {
  onSure: (val: string) => void;
}

const Space: React.FC<SpaceProps> = (props) => {
  const [top, setTopVal] = useState<React.ReactText>(0);
  const [right, setRightVal] = useState<React.ReactText>(0);
  const [bottom, setBottomVal] = useState<React.ReactText>(0);
  const [left, setLeftVal] = useState<React.ReactText>(0);

  const onChange = useCallback((val: React.ReactText) => {
    setTopVal(val);
    setRightVal(val);
    setBottomVal(val);
    setLeftVal(val);
  }, []);

  const _value = difference([top, right, bottom, left], [top]).length ? 0 : top;

  const onSure = useCallback(() => {
    const value = difference([top, right, bottom, left], [top]).length
      ? [`${top}px`, `${right}px`, `${bottom}px`, `${left}px`].join(' ')
      : `${top}px`;

    props.onSure(value as string);
  }, [top, right, bottom, left]);

  return (
    <div className={`${prefixCls}-editor-css-item-space`}>
      <div>
        <InputNumber
          weId={`${props.weId || ''}_3e77u9`}
          suffix="px"
          value={_value}
          onChange={onChange}
        />
        <Button weId={`${props.weId || ''}_7vfglq`} type="primary" size="small" onClick={onSure}>
          {getLabel('40565', '确定')}
        </Button>
      </div>
      <div className={`${prefixCls}-editor-css-item-space-differ`}>
        <InputNumber
          weId={`${props.weId || ''}_3e77u9`}
          suffix="px"
          value={left}
          onChange={setLeftVal}
        />
        <div className={`${prefixCls}-editor-css-item-space-differ-top`}>
          <InputNumber
            weId={`${props.weId || ''}_3e77u9`}
            suffix="px"
            value={top}
            onChange={setTopVal}
          />
          <InputNumber
            weId={`${props.weId || ''}_3e77u9`}
            suffix="px"
            value={bottom}
            onChange={setBottomVal}
          />
        </div>
        <InputNumber
          weId={`${props.weId || ''}_3e77u9`}
          suffix="px"
          value={right}
          onChange={setRightVal}
        />
      </div>
    </div>
  );
};

export default Space;
