import React, { useCallback } from 'react';
import Collapse from '../../../../../common/collapse';
import { prefixCls } from '../../../../../constants';
import './index.less';
import Item from './Item';

interface QuickSettingProps extends React.Attributes {
  disable: boolean;
  insertCode: (code: string) => void;
}

const quickSettings = [
  'padding',
  'margin',
  'width',
  'height',
  'line-height',
  'font-family',
  'font-size',
  'font-weight',
  'text-align',
  'color',
  'border',
  'border-radius',
  'box-sizing',
  'position',
  'display',
  'float',
];

const QuickSetting: React.FC<QuickSettingProps> = (props) => {
  const onSure = useCallback((css: string) => {
    if (css) {
      props.insertCode(css);
    }
  }, []);

  return (
    <Collapse weId={`${props.weId || ''}_wfw1wx`} title="常用CSS">
      <div className={`${prefixCls}-editor-css-quicksetting`}>
        {quickSettings.map((_item) => (
          <Item
            weId={`${props.weId || ''}_32mxxx@${_item}`}
            key={_item}
            type={_item}
            disable={props.disable}
            onSure={onSure}
          />
        ))}
      </div>
    </Collapse>
  );
};

export default QuickSetting;
