import { Menu as _Menu, MenuItemData } from '@weapp/ui';
import { classnames } from '@weapp/utils';
import React from 'react';
import { prefixCls } from '../../../../constants';
import { IComDescription } from '../../../../types';
import _actions from '../../../decorator/actions';
import { ComListProps, ICategoryViewData } from '../types';
import Card from './Card';

const Menu: any = _Menu;

interface MenuListStates {
  menuData: MenuItemData[];
  selectedMenuId: string;
}

const getMenuData = (datas: ICategoryViewData[]) => datas.map((data) => ({
  id: data.id,
  content: data.title as string,
}));

@_actions.autobind
export default class MenuList extends React.PureComponent<ComListProps, MenuListStates> {
  constructor(props: ComListProps) {
    super(props);

    const { datas, defSelectedMenuId } = props;
    const menuData = getMenuData(datas);

    this.state = {
      menuData,
      selectedMenuId: defSelectedMenuId || menuData[0]?.id,
    };
  }

  static getDerivedStateFromProps(props: ComListProps, state: MenuListStates) {
    const { datas } = props;
    const { menuData: _menuData, selectedMenuId } = state;
    let changed = datas?.length !== _menuData?.length;
    const menuData = datas.map((data, index) => {
      const preData = _menuData[index];
      if (data.id !== preData?.id || data.title !== preData?.content) {
        changed = true;
      }
      return {
        id: data.id,
        content: data.title as string,
      };
    });

    // eslint-disable-next-line max-len
    const _selectedMenuId = menuData.find((data) => data.id === selectedMenuId)?.id || menuData[0]?.id;

    if (changed || _selectedMenuId !== selectedMenuId) {
      return { menuData, selectedMenuId: _selectedMenuId };
    }
  }

  @_actions.method('compList')
  selectMenu = (value: string) => {
    this.setState({ selectedMenuId: value });
  };

  renderAssociatedCard = (datas: IComDescription[], index: number) => {
    const data = datas[index];

    if (data.type === 'InternalTitle') {
      // datas不需要传入card，传入会造成不必要的对比
      const { datas: _datas, ...restProps } = this.props;
      return (
        <div className={`${prefixCls}-component-menu-list-content-associate`}>
          <Card
            key={data.id}
            weId={`${this.props.weId || ''}_nc5y61@${index}`}
            data={data}
            renderIcon={this.renderIcon}
            {...restProps}
          />
          <Card
            key={datas[index + 1].id}
            weId={`${this.props.weId || ''}_nc5y61@${index + 1}`}
            data={datas[index + 1]}
            renderIcon={this.renderIcon}
            {...restProps}
          />
        </div>
      );
    }
    return null;
  };

  renderContent = (datas?: IComDescription[]) => datas?.map((data, index) => {
    if (data.associatedId) {
      return this.renderAssociatedCard(datas, index);
    }

    // 不含内置标题的正常组件样式
    const normalClassNames = {
      [`${prefixCls}-component-normal`]: !data?.associatedId,
    };
    const className = classnames(this.props.className, { ...normalClassNames });

    return (
        <Card
          key={data.id}
          weId={`${this.props.weId || ''}_nc5y61@${index}`}
          data={data}
          className={className}
          renderIcon={this.renderIcon}
          {...this.props}
        />
    );
  });

  // 列表型面板不展示图标
  renderIcon = () => null;

  render() {
    const { datas } = this.props;
    const { menuData, selectedMenuId } = this.state;
    const data = datas.find((_data) => _data.id === selectedMenuId)!;

    return (
      <div className={`${prefixCls}-component-menu-list`}>
        <Menu
          weId={`${this.props.weId || ''}_nh2k2r`}
          data={menuData}
          value={selectedMenuId}
          renderAsSelectOverLimit={false}
          onChange={this.selectMenu}
        />
        <div className={`${prefixCls}-component-menu-list-content`}>
          {data?.columns?.map((column, index) => (
            <span key={`${index || ''}_nh2k2r`}>{column}</span>
          ))}
          <React.Fragment weId={`${this.props.weId || ''}_o5n3m2`} key={selectedMenuId}>
            {this.renderContent(data?.items)}
          </React.Fragment>
        </div>
      </div>
    );
  }
}
