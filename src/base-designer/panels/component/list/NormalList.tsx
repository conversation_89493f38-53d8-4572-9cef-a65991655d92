import { classnames } from '@weapp/utils';
import React, { memo, ReactNode } from 'react';
import Collapse from '../../../../common/collapse';
import { prefixCls } from '../../../../constants';
import { IComDescription } from '../../../../types';
import { CardProps, ComListProps, ICategoryViewData } from '../types';
import Card from './Card';

interface NormalCardProps extends CardProps {
  data: IComDescription;
  index: number;
}

const defaultRender = (_item: ICategoryViewData, v: ReactNode) => v;

const NormalCard: React.FC<NormalCardProps> = (props) => {
  const { data, index, ...cardProps } = props;

  // 是否是内置标题组件
  const isInternalTitle = data.type === 'InternalTitle';
  // 是否有关联的内置标题组件
  const hasInternalTitle = data?.associatedId && data.type !== 'InternalTitle';

  // 内置标题及其关联组件的卡片样式
  const InternalTitleClassNames = {
    [`${prefixCls}-component-img-title`]: isInternalTitle, // 组件内置的标题组件样式
    [`${prefixCls}-component-img-hastitle`]: hasInternalTitle, // 含有内置标题的组件样式
  };
  const className = classnames(props.className, { ...InternalTitleClassNames });

  return (
    <Card
      weId={`${props.weId || ''}_nc5y61@${index}`}
      data={data}
      className={className}
      {...cardProps}
    />
  );
};

const NormalList: React.FC<ComListProps> = (props) => {
  const {
    datas,
    type,
    renderCategory = defaultRender,
    secondCollapseClsName,
    ...cardProps
  } = props;

  return (
    <div className={`${prefixCls}-component-nlist`}>
      {datas.map((data, index) => (
        <Collapse key={data.id} weId={`${props.weId || ''}_wt3khp@${index}`} title={data.title}>
          {renderCategory(
            data,
            <>
              {data.items?.map((item, i) => (
                <NormalCard
                  key={item.id}
                  weId={`${props.weId || ''}_nc5y61@${i}`}
                  data={item}
                  index={i}
                  {...cardProps}
                />
              ))}
            </>,
          )}
          {data.childs?.map((child) => (
            <div key={child.id} className={`${prefixCls}-component-nlist-category`}>
              {renderCategory(
                child,
                <Collapse
                  weId={`${props.weId || ''}_wt3khp@${index}`}
                  className={secondCollapseClsName}
                  title={child.title}
                >
                  {child.items?.map((_item, _i) => (
                    <NormalCard
                      key={_item.id}
                      weId={`${props.weId || ''}_nc5y61@${_i}`}
                      data={_item}
                      index={_i}
                      {...cardProps}
                    />
                  ))}
                </Collapse>,
              )}
            </div>
          ))}
        </Collapse>
      ))}
    </div>
  );
};

export default memo(NormalList);
