import { Icon, <PERSON>u, Trigger } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { useCallback, useMemo, useState } from 'react';
import { ModeType, prefixCls } from '../../../../constants';
import ComponentStore from '../store';
import { ComponentPanelProps } from '../types';

interface ShowModeProps extends React.Attributes, ComponentPanelProps {
  store: ComponentStore;
}

/** 组件列表展示模式 */
const modeOptions = [
  {
    id: ModeType.Grid,
    content: getLabel('287082', '九宫格式'),
    icon: 'Icon-Triple-grid-view',
  },
  {
    id: ModeType.Tile,
    content: getLabel('287083', '平铺式'),
    icon: 'Icon-Two-Palace-Grid-View',
  },
  {
    id: ModeType.List,
    content: getLabel('84262', '列表式'),
    icon: 'Icon-Category-View01',
  },
];

const ShowMode = (props: ShowModeProps) => {
  const { store } = props;
  const { showMode: curMode } = store;
  const [visible, setVisible] = useState(false);

  const onChange = useCallback((val) => {
    store.setModeType(val);
    setVisible(false);
  }, []);

  const onPopupVisibleChange = useCallback((val: boolean) => {
    setVisible(val);
  }, []);

  const selectIcon = useMemo(
    () => modeOptions?.find((opt) => opt.id === curMode)?.icon || 'Icon-Triple-grid-view',
    [curMode],
  );

  const selectNode = useMemo(
    () => (
      <Icon
        className={`${prefixCls}-component-panel-search-mode-btn`}
        weId={`${props.weId || ''}_982jl1`}
        name={selectIcon}
        size="xs"
      />
    ),
    [selectIcon],
  );

  const popupContent = useMemo(
    () => (
      <Menu
        weId={`${props.weId || ''}_ydg0ql`}
        value={curMode}
        data={modeOptions}
        type="menu"
        mode="inline"
        popupPlacement="rightTop"
        childPopupPlacement="rightTop"
        onChange={onChange}
      />
    ),
    [curMode],
  );

  return (
    <Trigger
      weId={`${props.weId || ''}_3e6smd`}
      action="click"
      popup={popupContent}
      popupPlacement="bottomRight"
      popupClassName={`${prefixCls}-component-panel-search-mode-menu`}
      popupVisible={visible}
      onPopupVisibleChange={onPopupVisibleChange}
      enableEventEmitter
    >
      {selectNode}
    </Trigger>
  );
};

export default observer(ShowMode);
