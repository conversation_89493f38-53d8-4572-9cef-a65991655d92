import { Icon, Input } from '@weapp/ui';
import { OptionData } from '@weapp/ui/lib/components/select/types';
import { debounce, getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { useCallback, useMemo } from 'react';
import { When } from 'react-if';
import PureSelect from '../../../../common/select';
import { prefixCls } from '../../../../constants';
import { useDesigner } from '../../../hooks';
import ComponentStore from '../store';
import { ComponentPanelProps } from '../types';
import { getCategoryTypes, getCategoryViewDatas } from '../utils';
import './index.less';
import ShowMode from './ShowMode';

interface SearchProps extends React.Attributes, ComponentPanelProps {
  store: ComponentStore;
}

const Search = (props: SearchProps) => {
  const {
    placeholder = getLabel('54515', '请输入组件名称'),
    store,
    showCategorySelect = true,
    selectedCategoryType,
    // searchVal,
    type = 'flatten',
    clientType = 'PC',
    getOptions,
    onCategoryTypeSelect,
    onSearch,
    renderSearch = (v) => v,
    showMode,
    menuType,
  } = props;
  const {
    isCategoryTypeChanged,
    categories,
    selectedCategoryType: _selectedCategoryType,
    onCategoryTypeSelect: _onCategoryTypeSelect,
    // searchVal: _searchVal,
    onSearch: _onSearch,
    setIsCategoryTypeChanged,
  } = store;
  const { layoutType } = useDesigner();
  const viewDatas = useMemo(
    () => getCategoryViewDatas(toJS(categories), type, clientType, layoutType),
    [categories, type, clientType, layoutType],
  );
  const categoryTypes = useMemo(
    () => (getOptions ? getOptions(viewDatas) : getCategoryTypes(categories)),
    [categories, viewDatas],
  );

  const value = selectedCategoryType ?? _selectedCategoryType;

  const onSelect = useCallback(
    (categoryType: string | OptionData) => {
      (onCategoryTypeSelect ?? _onCategoryTypeSelect)(categoryType);

      if (!isCategoryTypeChanged) {
        setIsCategoryTypeChanged(true);
      }
    },
    [isCategoryTypeChanged],
  );

  const handleSearch = useCallback(
    debounce((_value) => {
      (onSearch ?? _onSearch)(_value);
    }, 200),
    [isCategoryTypeChanged, onSearch, _onSearch],
  );

  return renderSearch(
    <div className={`${prefixCls}-component-panel-search`}>
      <When weId={`${props.weId || ''}_29f8yl`} condition={showCategorySelect}>
        <PureSelect
          weId={`${props.weId || ''}_tfrv57`}
          data={categoryTypes}
          value={value}
          onSelect={onSelect}
        />
      </When>

      <Input
        weId={`${props.weId || ''}_lq8086`}
        // value={searchVal ?? _searchVal}
        allowClear
        suffix={
          <Icon
            weId={`${props.weId || ''}_jvcxez`}
            className="ui-searchAdvanced-input-icon"
            name="Icon-search"
          />
        }
        placeholder={placeholder}
        onChange={handleSearch}
      />

      <When weId={`${props.weId || ''}_i2hrhg`} condition={!menuType && !!showMode}>
        <ShowMode weId={`${props.weId || ''}_n4da4y`} {...props} />
      </When>
    </div>,
  );
};

export default observer(Search);
