@import (reference) '../../../../style/prefix.less';
@import (reference) '../../../../style/var.less';

@comp-width: 32%;
@comp-width-wider: 46%;
@comp-margin: 0;
@comp-margin-wider: 0 2%;

@tile-comp-width: 48%;

@list-comp-width: 100%;

.@{prefix}-component-panel {
  display: flex;
  height: 100%;
  flex-direction: column;

  .ui-collapse-panel__content > span {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    cursor: move;
    user-select: none;
    width: @comp-width;
    word-wrap: break-word;
    vertical-align: top;
  }

  &.tile .ui-collapse-panel__content > span {
    width: @tile-comp-width;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    border: var(--border-solid);
    border-radius: var(--border-radius-xs);
    box-sizing: border-box;
    height: 30px;

    &:nth-child(2n) {
      margin-left: 4% !important;
    }

    &:nth-child(3n + 2) {
      margin-right: 0;
      margin-left: 0;
    }

    &:hover {
      border: 1px solid var(--primary);
      box-shadow: 0 0 5px rgba(0, 0, 0, .15);
    } 

    & > .@{prefix}-component-img {
      flex-basis: 22px;
      width: 22px;
      height: 22px;
      border: none;
      background-color: transparent;
      padding-right: 0;

      & > .ebcoms-assets-item {
        width: 100%;
        height: 100%;

        .ui-icon {
          vertical-align: baseline;
        }
      }

      &:hover {
        box-shadow: none;
        border: none;
      }
    }

    & > .@{prefix}-component-name {
      display: inline;
      flex: 1;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      text-wrap: nowrap;
      color: var(--main-fc);
      margin: 0 8px 0 2px;
      line-height: normal;
    }
  }

  &.list .ui-collapse-panel__content > span {
    width: @list-comp-width;
    display: flex;
    flex-direction: row;
    margin: 0 !important;
    box-sizing: border-box;
    height: 30px;

    &:hover {
      background: #F7F7F7;
      border-radius: 3px;

      & > .@{prefix}-component-img {
        background: #F7F7F7;
      }
    }
    
    & > .@{prefix}-component-img {
      flex-basis: 22px;
      width: 22px;
      height: 22px;
      border: none;

      & > .ebcoms-assets-item {
        width: 100%;
        height: 100%;

        .ui-icon {
          vertical-align: baseline;
        }
      }


      &:hover {
        box-shadow: none;
        border: none;
      }
    }

    & > .@{prefix}-component-name {
      display: inline;
      flex: 1;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      text-wrap: nowrap;
      color: var(--main-fc);
      margin: 0 8px 0 5px;
      line-height: normal;
    }
  }

  .ui-collapse-panel__content > .@{prefix}-component-img-title {
    width: 10%;
  }

  .ui-collapse-panel__content > .@{prefix}-component-img-title:nth-child(4n-1) {
    margin: 0 2% 0 10%;
  }

  .ui-collapse-panel__content > .@{prefix}-component-img-title:nth-child(4n+1) {
    margin: 0 2% 0 0;
  }

  .ui-collapse-panel__content > span {
    margin: @comp-margin;
  }

  .ui-collapse-panel__content > span:nth-child(3n + 2) {
    margin-right: 2%;
    margin-left: 2%;
  }

  .ui-collapse-panel__content > .@{prefix}-component-img-hastitle:nth-child(n) {
    margin:0;
  }

  &-empty {
    align-items: center;
    padding-top: 2px;

    & > span {
      line-height: var(--btn-line-height);
    }

    & > .ui-btn {
      border: 0;
      display: inline;
    }

    &-en {
      display: flex;
      flex-direction: column;
    }
  }

  &-content-empty {
    transform: translateY(-20%);
    pointer-events: none;

     .ui-empty-description {
      pointer-events: auto;
    }
  }

  &-search {
    display: flex;
    padding: 10px;

    .ui-select-input {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      min-width: 60px;
      max-width: 85px;
    }

    & > div:first-child {
      width: auto;
    }

    & > div.ui-input-wrap {
      flex: 1 1 auto;
      width: 100%;
      margin-left: -1px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    .ui-input-wrap:hover {
      z-index: @ls-wrap-zIndex;
    }

    &-mode-btn {
      padding: 0 0 0 6px;
      color: var(--regular-fc);
      display: flex;
      align-self: center;
      cursor: pointer;

      &:hover {
        color: var(--primary)
      }
    }
  }

  .ui-spin {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-search-mode-menu {
    width: 120px;
    border: var(--border-solid);
    border-radius: var(--border-radius-sm);
    background-color: var(--select-dropdown-bc);
    box-shadow: var(--box-shadow);

    .ui-menu {
      width: 100%;
      padding: 10px 0;

      .ui-menu-list-item {
        height: calc(var(--hd)* 30);
        padding: 0 10px;

        &.ui-menu-list-item-active {
          border-color: transparent;
        }
      }
    }
  }
}

.@{prefix}-ls-content-wider {
  .@{prefix}-component-panel.grid {
    .ui-collapse-panel__content > span {
      width: @comp-width-wider;
      margin: @comp-margin-wider;
    }
  }
}

.@{prefix}-component-list {
  height: 100%;
  padding: 0 10px;
  overflow: auto;
}

.@{prefix}-component-nlist {
  height: 100%;
  padding-left: 10px;
  overflow: auto;

  .@{prefix}-collapse .ui-collapse-panel__content-box {
    padding-right: 10px;
  }

  &-category {
    border: 1px solid var(--border-color);
    margin-bottom: 10px;
    border-radius: 3px;

    .@{prefix}-collapse .ui-collapse-panel__content-box {
      padding-right: 0;
    }

    .weapp-de-collapse .ui-collapse-panel__title {
      padding-left: 10px;
      z-index: 1;
    }

    .ui-collapse-panel__content > span {
      margin-left: 20px;
    }

    .ui-collapse-panel__content > span:nth-child(3n + 2) {
      margin-left: 20px;
    }
  }
}

.@{prefix}-component-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 55px;
  padding: 2px;
  border-radius: 3px;
  background-color: #fff;
  border: 1px solid var(--border-color);
  transition: 0.225s box-shadow ease-in-out;

  &:hover {
    box-shadow: 0 0px 5px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--primary);
  }

  img {
    width: 90%;
    height: 90%;
    pointer-events: none;
    user-select: none;
    object-fit: contain;
  }

  & + span {
    font-size: var(--font-size-12);
    margin: 0 0 6px 0;
    user-select: none;
    opacity: 0.8;
    line-height: 15px;
    margin-top: 5px;
    width: 100%;
    text-align: center;
  }

  .ui-icon {
    width: 100%;
    height: 100%;
    margin-left: 0 !important;
  }

  & > div {
    height: 95%;
  }

  svg {
    width: 98%;
    height: 98%;
    pointer-events: none;
    user-select: none;
  }

  &-disabled {
    pointer-events: none;
    background-color: var(--de-disabled-icon-color);
    border-color: #c5c5c5;
  }
}

.@{prefix}-component-disabled{
    cursor: not-allowed !important;
    opacity: 0.3;
}

.@{prefix}-component-name {
  .lines-omitted;
}

.@{prefix}-component-menu-list {
  width: 100%;
  font-size: 12px;
  font-family: 'PingFangSC-Regular';
  overflow: auto;
  overflow-x: hidden;

  .ui-menu {
    padding: 0 10px;
  }

  &-content > span {
    width: 50%;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    padding: 7px 0;
    line-height: 18px;
  }

  &-content > span:nth-child(-n + 2) {
    background-color: #fbfbfb;
  }

  &-content > span:nth-child(n + 3) {
    cursor: move;
    color: #666666;
    border-top: 1px #f1f1f1 solid;
  }

  &-content > span:nth-child(n + 3):hover {
    background-color: #f1f1f1;
    color: #5D9CEC;
  }

  &-content-associate{
    cursor: move;
    color: #666666;
    border-top: 1px #f1f1f1 solid;

    .@{prefix}-component-name{
      padding: 0 5px;
    }

    & > span {
        width: 50%;
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        padding: 7px 0;
        line-height: 18px;
    }

    & > span:hover{
      color: #5D9CEC;
    }
  }
   &-content-associate:hover{
      background-color: #f1f1f1;
   }

  .@{prefix}-component-disabled{
    color: #BBBBBB !important;
  }

  .@{prefix}-component-normal{
    width: 100%;
    cursor: move;
    color: #666666;
  }

  .@{prefix}-component-normal:hover{
    background-color: #f1f1f1;
  }
}

.@{prefix}-highlight {
  background: #ffff33;
  text-transform: none;
}

body[page-rtl='true'] {
  .@{prefix}-component-list .ui-collapse-panel__title > span.ui-rtl {
    transform: none;
  }

  .@{prefix}-component-panel-search-mode-btn {
    padding: 0 6px 0 0;
  }
}