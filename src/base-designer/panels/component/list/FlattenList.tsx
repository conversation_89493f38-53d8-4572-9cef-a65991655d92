import React, { memo, useEffect, useRef } from 'react';
import Collapse from '../../../../common/collapse';
import MountedDefer from '../../../../common/components/MountedDefer';
import { prefixCls } from '../../../../constants';
import useInternal from '../../../hooks/useInternal';
import { ComListProps } from '../types';
import Card from './Card';

const ComList: React.FC<any> = (props) => {
  const { data, ...cardProps } = props;
  return data?.map((item: any, i: number) => (
    <MountedDefer comProps={{}} key={item.id} weId={`${props.weId || ''}_nc5y61@${i}`}>
      <Card weId={`${props.weId || ''}_nc5y61@${i}`} data={item} {...cardProps} />
    </MountedDefer>
  ));
};

const FlattenList: React.FC<ComListProps> = (props) => {
  const {
    datas, type, footer, ...cardProps
  } = props;
  const listRef = useRef<any>();

  const FlattenComList = useInternal()?.resolver?.FlattenComList || ComList;

  useEffect(() => {
    if (listRef.current) {
      listRef.current.scrollTop = 0;
    }
  }, [datas.length]);

  return (
    <div className={`${prefixCls}-component-list`} ref={listRef}>
      {datas.map((data, index) => {
        const { highlightTitle, title: dataTitle } = data;
        const highlightTitleSpan = highlightTitle || (dataTitle as string);
        const titleStyle: any = data.category === '-1' ? { textTransform: 'none' } : {};
        const title = (
          <span
            style={titleStyle}
            title={dataTitle as string}
            // 高亮展示需要渲染title中的span标签
            dangerouslySetInnerHTML={{ __html: highlightTitleSpan }}
          />
        );
        return (
          <Collapse key={data.id} weId={`${props.weId || ''}_wt3khp@${index}`} title={title}>
            <FlattenComList
              weId={`${props.weId || ''}_mgmv4h@${index}`}
              data={data.items}
              {...cardProps}
            />
          </Collapse>
        );
      })}
      {footer}
    </div>
  );
};

export default memo(FlattenList);
