import { CorsComponent, Icon, IconNames } from '@weapp/ui';
import {
  appInfo, classnames, isEqual, isString,
} from '@weapp/utils';
import React, { memo, ReactNode } from 'react';
import { prefixCls } from '../../../../constants';
import { IComDescription } from '../../../../core/types';
import json from '../../../../utils/json';
import './index.less';

interface CompIconProps extends React.Attributes {
  data: IComDescription;
}

const getImgPath = (data: IComDescription) => {
  // 自定义组件没有package，默认图标展示特殊处理
  if (data.ecodeId && data.icon?.indexOf('chart-default') > -1) {
    return `/build/ebdcoms/images/${data.icon}`;
  }

  if (!data.package) return '';

  const { buildFolder } = appInfo(data.package);

  return `${buildFolder}/images/${data.icon}`;
};

const CompIcon: React.FC<CompIconProps> = (props) => {
  const { data } = props;
  const { disabled } = data;
  const isObject = !!json.parse(data.icon);
  let src = '';
  let icon: ReactNode = null;

  if (isString(data.icon) && !isObject) {
    if (data.icon.indexOf('coms') === 0) {
      // 处理按规范(组件图片地址应该统一放到public/images/coms下)存放图片的方式
      src = getImgPath(data);
    } else if (data.icon.indexOf('/') === 0) {
      src = data.icon;
    }
  }

  if (src) {
    icon = <img src={src} alt="" />;
  } else if (isObject) {
    icon = (
      <CorsComponent
        weId={`${props.weId || ''}_g68imy`}
        app="@weapp/ebdcoms"
        compName="AssetsItem"
        {...json.parse(data.icon)}
      />
    );
  } else {
    icon = <Icon weId="aohrjo" name={data.icon as IconNames} />;
  }

  // 组件图标
  return (
    <span
      className={classnames(`${prefixCls}-component-img`, {
        [`${prefixCls}-component-img-disabled`]: disabled,
      })}
    >
      {icon}
    </span>
  );
};

export default memo(CompIcon, isEqual);
