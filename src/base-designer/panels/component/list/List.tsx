import { Spin } from '@weapp/ui';
import { isEqual } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, {
  useCallback, useEffect, useMemo, useState,
} from 'react';
import { prefixCls } from '../../../../constants';
import { useDesigner } from '../../../hooks';
import ComponentStore from '../store';
import { ComponentPanelProps, ICategoryViewData } from '../types';
import {
  filterUniqueComs,
  formatCompDatas,
  getCategoryViewDatas,
  getDisplayedDatas,
  handleComCustomHide,
} from '../utils';
import Empty from './Empty';
import FlattenList from './FlattenList';
import MenuList from './MenuList';
import NormalList from './NormalList';

interface ListProps extends React.Attributes, ComponentPanelProps {
  store: ComponentStore;
}

const defaultFilter = (datas: ICategoryViewData[]) => datas;
const defaultGetUniqueKeyData = (data: any) => data.type;

const List: React.FC<ListProps> = (props) => {
  const designer = useDesigner();
  const {
    id,
    fetchData,
    filter = defaultFilter,
    renderTitle,
    getUniqueKeyData = defaultGetUniqueKeyData,
    footer,
    datas,
    className,
    type = 'flatten',
    clientType = designer?.clientType || 'PC',
    placeholder,
    store,
    searchVal: _searchVal,
    loading,
    selectedCategoryType: _selectedCategoryType,
    onCategoryTypeSelect: onPropsCategoryTypeSelect,
    menuType,
    getReloadKey,
    ...listProps
  } = props;
  const {
    categories, selectedCategoryType, searchVal, onCategoryTypeSelect,
  } = store;

  const selectType = _selectedCategoryType ?? selectedCategoryType;
  const searchValue = (_searchVal as string) ?? searchVal;

  const coms = useMemo(() => designer?.layoutStore.coms || [], [toJS(designer?.layoutStore?.coms)]);

  const reloadKey = useMemo(() => getReloadKey?.(coms), [toJS(coms)]);

  const compDatas = useMemo(() => {
    const viewDatas = getCategoryViewDatas(
      toJS(categories),
      type,
      clientType,
      designer?.layoutType,
      menuType,
    );

    return filterUniqueComs(viewDatas, coms, getUniqueKeyData, filter);
  }, [
    categories,
    clientType,
    coms.length,
    reloadKey,
    _selectedCategoryType,
    getUniqueKeyData,
    filter,
  ]);

  const { displayedDatas: displayedComList, customHideComList } = useMemo(() => {
    const _compDatas = formatCompDatas(compDatas as ICategoryViewData[]);
    return getDisplayedDatas(_compDatas, categories, selectType, searchValue, renderTitle);
  }, [compDatas, categories, selectType, searchValue, renderTitle]);

  const [displayedDatas, setDisplayedDatas] = useState<ICategoryViewData[]>(displayedComList);

  useEffect(() => {
    if (!Array.isArray(customHideComList) || !customHideComList.length) {
      if (!isEqual(displayedComList, displayedDatas)) {
        setDisplayedDatas(displayedComList);
      }

      return;
    }
    handleComCustomHide(displayedComList, customHideComList, {
      clientType,
    }).then((handledComList) => {
      if (!isEqual(displayedComList, displayedDatas)) {
        setDisplayedDatas(handledComList);
      }
    });
  }, [displayedComList, customHideComList]);

  const handleChangeAll = useCallback(() => {
    const onChange = onPropsCategoryTypeSelect || onCategoryTypeSelect;
    onChange('ALL');
  }, [onPropsCategoryTypeSelect, onCategoryTypeSelect]);

  if (loading) {
    return (
      <div className={`${prefixCls}-component-panel`}>
        <Spin weId="cwk640" />
      </div>
    );
  }

  if (displayedDatas.length === 0) {
    return (
      <Empty
        weId={`${props.weId || ''}_gd6e7x`}
        hasSearchValue={!!searchValue}
        isSearchEmpty={compDatas.length > 0}
        selectType={selectType}
        onClick={handleChangeAll}
      />
    );
  }

  if (type === 'nested') {
    return (
      <NormalList
        weId={`${props.weId || ''}_rh79vy`}
        datas={displayedDatas}
        clientType={clientType}
        {...listProps}
      />
    );
  }

  if (type === 'menu') {
    return (
      <MenuList
        weId={`${props.weId || ''}_rh79vy`}
        datas={displayedDatas}
        clientType={clientType}
        type={type}
        {...listProps}
      />
    );
  }

  return (
    <FlattenList
      weId={`${props.weId || ''}_zjljwy`}
      datas={displayedDatas}
      clientType={clientType}
      footer={footer}
      {...listProps}
    />
  );
};

export default observer(List);
