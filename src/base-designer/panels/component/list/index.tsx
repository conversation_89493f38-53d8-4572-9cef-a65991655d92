import { Spin } from '@weapp/ui';
import { observer } from 'mobx-react';
import React, { useEffect, useMemo } from 'react';
import { prefixCls } from '../../../../constants';
import { IComCategory } from '../../../../core/types';
import ebdcoms from '../../../../utils/ebdcoms';
import { useDesigner } from '../../../hooks';
import ComponentStore from '../store';
import { ComponentPanelProps } from '../types';
import { fetchCategories } from '../utilsEager';
import './index.less';
import List from './List';
import Search from './Search';

const ComListContent = React.forwardRef((props: ComponentPanelProps, ref: any) => {
  const designer = useDesigner();
  const {
    fetchData = () => fetchCategories(designer?.page, props?.fetchParams),
    datas,
    className = '',
    showMode,
  } = props;

  /**
   * 外部传入datas，不再内部请求数据
   */
  const _fetchData = typeof datas !== 'undefined' ? () => Promise.resolve(datas) : fetchData;

  const store = useMemo(() => {
    const _store = new ComponentStore();

    ref.current = _store;

    return _store;
  }, []);

  const { hasInit } = store;

  useEffect(() => {
    if (!hasInit) {
      store.setModeType(showMode?.defaultValue);
      Promise.all([ebdcoms.load(), _fetchData()]).then((resp) => {
        const res = resp?.[1];
        if (res) {
          store.setCategories(res);
          store.setInit(true);
          designer?.initComDescriptions(res as IComCategory[]);
        }
      });
    }
  }, [hasInit]);

  useEffect(() => {
    if (datas) {
      store.setCategories(datas);

      if (!hasInit) {
        store.setInit(true);
      }
    }
  }, [datas]);

  if (!hasInit) {
    return (
      <div className={`${prefixCls}-component-panel`}>
        <Spin weId="cwk640" />
      </div>
    );
  }

  return (
    <div
      className={`${prefixCls}-component-panel ${store.showMode.toLocaleLowerCase()} ${className}`}
    >
      <Search weId={`${props.weId || ''}_of4kng`} store={store} {...props} />
      <List weId={`${props.weId || ''}_854s9e`} store={store} {...props} />
    </div>
  );
});

export default observer(ComListContent);
