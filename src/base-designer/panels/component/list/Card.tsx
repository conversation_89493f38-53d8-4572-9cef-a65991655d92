import { classnames, getLabel } from '@weapp/utils';
import React, {
  memo, ReactElement, ReactNode, SyntheticEvent, useCallback, useMemo,
} from 'react';
import { prefixCls } from '../../../../constants';
import { getComponent } from '../../../../core';
import { IComDescription } from '../../../../core/types';
import getViewType from '../../../../core/utils/getViewType';
import setTimeoutOnce from '../../../../utils/setTimeoutOnce';
import useInternal from '../../../hooks/useInternal';
import { CardProps } from '../types';
import CompIcon from './Icon';

const defaultRender = (_item: IComDescription, v: ReactNode) => v;

const Card: React.FC<CardProps> = (props) => {
  const {
    data: _data,
    clientType,
    className,
    draggable,
    onDragStart,
    renderIcon = defaultRender,
    renderName = defaultRender,
    renderComponent = defaultRender,
    renderCompExtra,
    customCardData,
  } = props;
  const wrapperProps: React.HTMLAttributes<HTMLSpanElement> = {};
  const { renderDragComponent = defaultRender } = useInternal()! || {};

  const data = useMemo(() => {
    if (typeof customCardData === 'function') {
      return customCardData(_data);
    }
    return _data;
  }, [_data, customCardData]);

  const { disabled, isCustomHide = false, highlightTitle } = data;

  // 组件图标
  const icon = <CompIcon weId={`${props.weId || ''}_v9z3mx`} data={data} />;

  // 高亮组件名称
  const highlightTitleSpan = highlightTitle || data.name;
  // 组件名称
  const name = (
    <span
      className={`${prefixCls}-component-name`}
      title={data.name}
      dangerouslySetInnerHTML={{ __html: highlightTitleSpan }}
    />
  );

  const handleDragStart = useCallback(
    (e: SyntheticEvent) => {
      if (clientType) {
        const designViewType = getViewType('Design', clientType);
        const configViewType = getViewType('Config', clientType);

        // 预加载组件的设计和配置视图
        // 添加setTimeout防止加载资源导致拖拽卡顿
        setTimeoutOnce(() => {
          // 模板组件和母版组件无需预加载
          if (data && data.type !== 'Compose' && data.type !== 'Master') {
            getComponent(data, designViewType);
            getComponent(data, configViewType);
          }
        });
      }
      onDragStart?.(e, data);
    },
    [clientType, JSON.stringify(data)],
  );

  if (draggable) {
    wrapperProps.draggable = draggable;
  }

  if (isCustomHide) {
    return null;
  }

  if (disabled) {
    return renderComponent(
      data,
      <span
        className={classnames(className, {
          [`${prefixCls}-component-disabled`]: disabled, // 组件唯一时设置 置灰效果
        })}
        title={getLabel('104759', '系统仅支持设置单个该类型组件')}
      >
        {renderIcon(data, icon)}
        {renderName(data, name)}
        {renderCompExtra?.(data)}
      </span>,
    ) as ReactElement;
  }

  return renderDragComponent(
    data,
    renderComponent(
      data,
      <span onDragStart={handleDragStart} className={className}>
        {renderIcon(data, icon)}
        {renderName(data, name)}
        {renderCompExtra?.(data)}
      </span>,
    ),
  ) as ReactElement;
};

export default memo(Card, (preProps, nextProps) => {
  const { data: preData, clientType: preClientType, draggable: preDraggable } = preProps;
  const { data, clientType, draggable } = nextProps;

  return (
    preData.disabled === data.disabled
    && preData.highlightTitle === data.highlightTitle
    && preData.name === data.name
    && preData.icon === data.icon
    && preClientType === clientType
    && preDraggable === draggable
  );
});
