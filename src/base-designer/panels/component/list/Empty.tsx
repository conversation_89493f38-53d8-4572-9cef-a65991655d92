import { CorsComponent } from '@weapp/ui';
import React, { memo } from 'react';
import { prefixCls } from '../../../../constants';

interface EmptyProps {
  weId: string;
  /** 选中类型 */
  selectType?: string;
  /** 是否存在搜索内容 */
  hasSearchValue?: boolean;
  /** 是否是搜索后的空状态 */
  isSearchEmpty?: boolean;
  /** 点击事件 */
  onClick?: () => void;
}

const Empty: React.FC<EmptyProps> = (props) => {
  const { isSearchEmpty } = props;
  // 当前是中文环境

  const emptyProps: any = {};
  return (
    <CorsComponent
      weId={`${props.weId || ''}_v1eqhx`}
      app="@weapp/ebdcoms"
      compName="Empty"
      type={isSearchEmpty ? 'search' : 'noData'}
      className={`${prefixCls}-component-panel-content-empty`}
      {...emptyProps}
    />
  );
};

export default memo(Empty);
