import { OptionData } from '@weapp/ui/lib/components/select/types';
import React, {
  ReactElement, ReactNode, ReactText, SyntheticEvent,
} from 'react';
import { ModeType } from '../../../constants';
import {
  ClientType,
  IComCategory,
  IComCategoryChild,
  IComConfig,
  IComData,
  IComDescription,
} from '../../../types';
import { RenderDragComponent } from '../../types';

/** 左侧面板类型，支持平级展示、嵌套展示和菜单展示 */
export type DisplayType = 'flatten' | 'nested' | 'menu';

/** 组件分类筛选格式 */
export type SelectedOption = {
  id: string;
  content: string;
};

/**
 * 组件列表接收外部传入的数据类型
 * 当数据与后端返回一致，此时childs有值，comps无值
 * 当传入非nested类型时，分类只有一级，此时comps有值，childs无值
 */
export interface ICategory extends Omit<IComCategory, 'childs'> {
  pid?: string;
  /** 组件数据 */
  comps?: IComDescription[];
  /** 子组件数据 */
  childs?: IComCategoryChild[];
  /** 列表型面板的列 */
  columns?: ReactNode[];
}

/**
 * 组件列表展示的数据类型
 */
export type ICategoryViewData = {
  id: string;
  /** 分类标题，当title为空时，不渲染标题 */
  title: ReactNode;
  /** 高亮标题 */
  highlightTitle?: string;
  /** 组件分类id，当组件无分类时不传category */
  category: string;
  /** 组件数据 */
  items?: IComDescription[];
  /** 子组件数据 */
  childs?: ICategoryViewData[];
  /** 列表型面板的列 */
  columns?: ReactNode[];
};

export type RenderTitle = (currCategory: IComCategory, comItem: ICategoryViewData) => string;
export interface CardProps extends React.Attributes {
  data: IComDescription;
  draggable?: boolean;
  clientType?: ClientType;
  className?: string;
  renderIcon?: (icon: ReactNode) => ReactNode;
  renderCategory?: (data: ICategoryViewData, node: ReactNode) => ReactNode;
  renderName?: (name: ReactNode) => ReactNode;
  renderComponent?: RenderDragComponent;
  renderCompExtra?: (data: IComDescription) => ReactNode;
  onDragStart?: (e: SyntheticEvent, data: IComDescription) => void;
  /** 自定义处理组件卡片数据 */
  customCardData?: HandleCustomComCardData;
}

export interface ComListProps extends Omit<CardProps, 'data'> {
  type?: DisplayType;
  datas: ICategoryViewData[];
  footer?: ReactNode;
  /** 第二级Collapse的样式类名 * */
  secondCollapseClsName?: string;
  defSelectedMenuId?: string;
}

export interface ComponentPanelProps extends React.Attributes {
  /** 组件面板唯一标志，用于区分多面板情况下的store */
  id?: string;
  className?: string;
  clientType?: ClientType;
  type?: DisplayType;
  datas?: ICategory[] | null;
  placeholder?: string;
  loading?: boolean;
  showCategorySelect?: boolean;
  /** 组件查询值 */
  searchVal?: ReactText;
  /** 选中组件分类的id */
  selectedCategoryType?: string;
  /** 自定义组件分类筛选项 */
  getOptions?: (viewDatas: ICategoryViewData[]) => SelectedOption[];
  /** 自定义组件分类选中事件 */
  onCategoryTypeSelect?: (categoryType: string | OptionData) => void;
  /** 自定义组件查询筛选事件 */
  onSearch?: (searchVal: ReactText) => void;
  getUniqueKeyData?: (data: any) => any;
  fetchData?: () => Promise<ICategory[]>;
  filter?: (datas: ICategoryViewData[]) => ICategoryViewData[];
  renderTitle?: RenderTitle;
  footer?: ReactNode;
  /** 第二级Collapse的样式类名 * */
  secondCollapseClsName?: string;
  menuType?: string;
  fetchParams?: any;
  getReloadKey?: (coms: IComData<IComConfig>[]) => string;
  renderSearch?: (search: ReactElement) => ReactElement;
  showMode?: { defaultValue: ModeType };
  /** menuList 默认选择menuId */
  defSelectedMenuId?: string;
}

/**
 * 处理组件自定义隐藏函数的 options
 */
export interface HandleComHideOpts {
  clientType?: ClientType;
}

export type TypeComCategory = IComCategory[] | ICategory[];

/**
 * 自定义处理左侧组件列表展示的数据
 */
export type HandleCustomComCardData = (data: IComDescription) => IComDescription;

export default {};
