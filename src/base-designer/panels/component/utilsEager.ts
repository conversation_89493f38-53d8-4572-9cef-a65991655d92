import { ModeType } from '../../../constants';
import { Page } from '../../../core';
import { IComCategory } from '../../../types';
import ebdcoms from '../../../utils/ebdcoms';

export const fetchCategories = (page?: Page, otherParams: any = {}): Promise<IComCategory[]> => {
  const { id, module = 'EB_PAGE' } = page || {};

  const params = {
    pageId: id,
    module,
    ...otherParams,
  };
  return new Promise((resolve) => {
    ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/component/list',
      params,
      success: resolve,
      error: () => {
        resolve([]);
        return true;
      },
    });
  });
};

/** 将组件列表展示类型存储在ls中 */
export const fetchShowMode = (mode: ModeType) => {
  localStorage.setItem('weapp_designer_comp_show_mode', mode);
};

export default {};
