import { observer } from 'mobx-react';
import React from 'react';
import _actions from '../../decorator/actions';
import List from './list';
import { ComponentPanelProps } from './types';

@_actions.autobind
class ComponentPanel extends React.PureComponent<ComponentPanelProps> {
  listRef = React.createRef<any>();

  @_actions.method('component')
  init = () => {
    const { datas, fetchData } = this.props;

    // 判断是不是组件面板，组件面板不会使用fetchData 和 外部维护datas
    if (!fetchData && !datas) {
      this.listRef.current.setInit(false);
    }
  };

  @_actions.method('component')
  getDatas = () => this.props.datas;

  render() {
    return <List weId={`${this.props.weId || ''}_3ufnzm`} ref={this.listRef} {...this.props} />;
  }
}

export default observer(ComponentPanel);
