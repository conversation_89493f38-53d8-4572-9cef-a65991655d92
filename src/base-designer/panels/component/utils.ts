import {
  cloneDeep, findLastIndex, forEachRight, getLabel, isArray, isNumber,
} from '@weapp/utils';
import { prefixCls } from '../../../constants';
import { ClientType, IComData, LayoutType } from '../../../core';
import getDesignInfo from '../../../core/utils/getDesignInfo';
import { IComCategory, IComDescription, ICustomHideComDesc } from '../../../types';
import ebdcoms from '../../../utils/ebdcoms';
import {
  DisplayType, HandleComHideOpts, ICategory, ICategoryViewData, RenderTitle,
} from './types';

const getHighlightSpan = (str: string) => `<span class="${prefixCls}-highlight">${str}</span>`;

// 模糊搜索（按内容顺序对字母进行查询）
const fuzzySearch = (searchVal: string, content: string) => {
  const searchList = searchVal.split('');
  // 起始位置
  let startPosition = 0;
  const highlightList: string | string[] = content.split('');
  const result = !searchList.some((search) => {
    // 忽略大小写
    const searchIndex = content
      .toLocaleLowerCase()
      .indexOf(search.toLocaleLowerCase(), startPosition);
    if (searchIndex === -1) {
      return true;
    }
    const focusValue = highlightList[searchIndex];
    highlightList.splice(searchIndex, 1, getHighlightSpan(focusValue));
    // console.log(searchIndex, focusValue, highlightList, startPosition);
    startPosition = searchIndex + 1;
    return false;
  });
  const highlightTitle: string = highlightList.join('');

  return {
    search: result,
    title: result ? highlightTitle : content,
  };
};

const filterLayoutScope = (layoutType?: LayoutType, layoutScope?: string[], menuType?: string) => {
  if (menuType || !layoutType || !layoutScope || !layoutScope.length) return true;
  return layoutScope.includes(layoutType);
};

/**
 * 格式化数组数据
 * @param formatFunc 格式化方法
 * @param array 数组
 * @param filterFunc 过滤数值方法
 */
const formatComData = <T, R>(
  array: T[],
  formatFunc: (item: T, index: number, arr: T[]) => { filter: boolean; changeData: R },
): R[] => {
  const result: R[] = [];
  array?.forEach((item, index, arr) => {
    const { filter, changeData } = formatFunc(item, index, arr);
    if (!filter) {
      return;
    }
    result.push(changeData);
  });
  return result;
};

const checkCompDisplay = (comp: any, opts: any) => {
  const { checkModuleDisplayStrict, checkModuleDisplay } = ebdcoms.get() || {};
  const _checkModuleDisplayStrict = checkModuleDisplayStrict || checkModuleDisplay;
  const { terminalScope, layoutScope } = comp;
  const { client, layoutType, menuType } = opts;

  return (
    (!client || terminalScope.includes(client))
    && _checkModuleDisplayStrict?.(comp.refModule)
    && filterLayoutScope(layoutType, layoutScope, menuType)
  );
};

const getPluginComps = (datas: ICategory[]) => {
  const pluginComps: IComDescription[] = [];

  datas.forEach((data) => {
    (data.childs || []).forEach((secondaryCategories) => {
      if (!(Array.isArray(secondaryCategories.comps) && secondaryCategories.comps.length)) return;

      secondaryCategories.comps.forEach((comp) => {
        if (Array.isArray(comp.pluginPackages) && comp.pluginPackages.length) {
          comp.pluginPackages.forEach((pluginPackage) => {
            pluginComps.push({
              ...comp,
              ...pluginPackage,
            });
          });
        }
      });
    });
  });

  return pluginComps;
};

export const getCategoryViewDatas = (
  datas: ICategory[],
  type: DisplayType,
  client: ClientType,
  layoutType?: LayoutType,
  menuType?: string,
): ICategoryViewData[] => {
  if (type === 'nested' || type === 'menu') {
    return datas.map((com) => {
      const items: IComDescription[] = [];

      com.comps?.forEach((comp) => {
        if (comp?.associatedId) {
          items.push({
            name: comp.name,
            type: 'InternalTitle',
            package: '@weapp/formbuilder',
            icon: 'Icon-Single-line-text-o',
            category: comp.category,
            terminalScope: comp?.terminalScope,
            moduleScope: comp?.moduleScope,
            unique: comp?.unique,
            associatedId: comp.associatedId,
            title: comp?.title,
          } as any);
        }
        items.push(comp);

        return comp;
      });

      return {
        id: com.id,
        title: com.name,
        category: com.id,
        items: items || [],
        columns: com?.columns || [],
        childs: (com.childs || []).map((_com) => ({
          id: _com.id,
          title: _com.name,
          category: _com.pid,
          items: _com.comps || [],
        })),
      };
    });
  }

  const categories: ICategory[] = []; // 组件分类列表
  const pluginComps = getPluginComps(datas);

  datas.forEach((primaryCategory) => {
    // 遍历一级分类
    const {
      name: primaryName,
      childs: secondaryCategories = [],
      comps: primaryComps = [],
    } = primaryCategory;

    // eslint-disable-next-line max-len
    const filterComps = primaryComps.filter((comp) => checkCompDisplay(comp, { client, layoutType, menuType }));

    if (filterComps.length) {
      // 一级分类下如果存在组件
      categories.push({
        ...primaryCategory,
        comps: filterComps,
        pid: primaryCategory.id, // 过滤组件时根据父类id来过滤的
      });
    }

    secondaryCategories.forEach((secondaryCategory) => {
      // 遍历二级分类
      const { name: secondaryName, comps: secondaryComps = [] } = secondaryCategory;

      const plgComps = pluginComps.filter((comp) => {
        if (comp.category === secondaryCategory.id) {
          return checkCompDisplay(comp, { client, layoutType, menuType });
        }
        return false;
      });

      // eslint-disable-next-line max-len
      const secFilterComps = secondaryComps.filter((comp) => checkCompDisplay(comp, { client, layoutType, menuType }));

      if (secFilterComps.length === 0 && plgComps.length === 0) return;

      categories.push({
        ...secondaryCategory,
        comps: secFilterComps,
        // 如果已选择一级分类，只展示二级分类，否则一二级分类名称都需要展示
        name: primaryName ? `${primaryName} / ${secondaryName}` : secondaryName,
      });
    });
  });

  const comps = categories.map((com) => ({
    id: com.id,
    title: com.name,
    category: com.pid,
    items: com.comps!,
  }));

  return comps as ICategoryViewData[];
};

/**
 * 隐藏插件包对应的业务列表
 * @param pluginData
 * @param items
 */
const hideOriginListCom = (pluginData: any, items: any[]) => {
  const plugins: any = {
    NList: 'List',
    rssReader: 'RSSReaderTpl',
    customer: 'CustomerListTpl',
    cowork: 'MyCollaboration',
    task: 'TaskListTpl',
    doc: 'DocumentListTpl',
    workflow: 'WorkflowListTpl',
    // blog: 'BlogStatusTpl',
  };
  const pluginType = pluginData?.plugins?.[0]?.resourceType;
  if (pluginType && plugins[pluginType]) {
    items.forEach((com) => {
      if (com.type === plugins[pluginType]) {
        com.deleteType = 1;
      }
    });
  }
};

/**
 *
 * 获取插件包信息
 *
 * @param dataItems 分类内的组件数据集合
 * @param currCategoryIndex 分类下标
 * @param datas 所有分类数据
 */
const getPluginCompDatas = (
  dataItems: IComDescription[],
  currCategoryIndex: number,
  datas: ICategoryViewData[],
) => {
  const isShowList = (location.href || '').indexOf('dlist=1') > 0;
  // TODO 新旧二维报表替换屏蔽逻辑。目前新二维报表显示条件：白名单内且有显示参数
  const isShowCrossReport = (location.href || '').indexOf('nreport=1') > 0;
  const defaultUrls = [
    'http://develop.yunteams.cn:6161',
    'http://localhost:6161',
    'https://weapp.yunteams.cn',
    'https://weapp.mulinquan.cn',
  ];
  const isInWhite = defaultUrls.some((url) => (location.origin || '') === url);
  if (!(isShowCrossReport && isInWhite)) {
    datas[currCategoryIndex].items?.forEach((item) => {
      if (item.type === 'CrossReport') {
        item.deleteType = 1;
      }
    });
  }

  dataItems.forEach((item) => {
    // 如果组件存在插件包数据，需要遍历显示出来
    if (Array.isArray(item.pluginPackages) && item.pluginPackages.length) {
      if (item.type === 'NList' && !isShowList) {
        hideOriginListCom(
          { plugins: [{ resourceType: 'NList' }] },
          datas[currCategoryIndex].items || [],
        );
      }

      // 过滤关联业务模块未启用的插件包
      const { checkModuleDisplayStrict, checkModuleDisplay } = ebdcoms.get() || {};
      const _checkModuleDisplayStrict = checkModuleDisplayStrict || checkModuleDisplay;
      const pluginPackages = item.pluginPackages.filter((pluginPackage) => {
        const isDisplay = _checkModuleDisplayStrict(pluginPackage.refModule);
        return isDisplay;
      });
      /** 插件包是否覆盖了所属组件，只能覆盖一次 */
      let isOverrideBelongComponent = false;

      // 需要forEachRight从右往左遍历，确保插件包原有顺序不变
      forEachRight(pluginPackages, (pluginPackage) => {
        let pluginCategory = pluginPackage.category || '';

        // 如果列表都不展示，那么建模列表也一定不能展示
        if (item.deleteType === 1 && ['933846626099339271'].includes(pluginPackage.id || '')) {
          return;
        }

        // 补全插件包信息
        const pluginData = {
          ...item, // 默认取所属组件的数据信息
          ...pluginPackage, // pluginPackage内返回的表示为插件包自身的数据
          /** 是否为插件包标识 */
          isPluginPackage: true,
          /** 插件包后端数据打包，供组件内使用 */
          pluginPack: pluginPackage,
          name: pluginPackage.packName || '',
        };

        if (!isOverrideBelongComponent && pluginPackage.overrideBelongComponent === 1) {
          isOverrideBelongComponent = true;
          pluginData.showOrder = item.showOrder;
          pluginData.category = item.category;
          pluginData.name = item.name;
          pluginCategory = item.category;
          datas[currCategoryIndex].items?.forEach((com) => {
            if (com.id === item.id) {
              com.deleteType = 1;
            }
          });
        }

        /** 插件包所属分类的下标 */
        const pluginCategoryIndex = pluginCategory
          ? datas.findIndex((_data) => _data.id === pluginCategory)
          : currCategoryIndex;

        if (!(isNumber(pluginCategoryIndex) && pluginCategoryIndex > -1)) return;

        // ------- 插件包有配置显示顺序时，按照显示顺序的值从小到大依次排列 -------

        if (pluginData.showOrder || pluginData.showOrder === 0) {
          /**
           * 插件包所在下标
           * - 排在比插件包showOrder值大的组件前面
           * - 如果没有则放在无showOrder字段的组件前面
           */
          let pluginIndex = datas[pluginCategoryIndex].items?.findIndex((_item) => {
            // 当有覆盖所属组件时，要替换组件本身位置
            if (pluginData.category === item.category && pluginData.showOrder === item.showOrder) {
              return _item.showOrder === pluginData.showOrder;
            }
            return (_item.showOrder || 0) > pluginData.showOrder! || !_item.showOrder;
          });

          if (isNumber(pluginIndex)) {
            // 如果都有showOrder且不存在比插件包大的情况，则放在组件最后
            if (pluginIndex < 0) {
              pluginIndex = datas[pluginCategoryIndex].items?.length || 0;
            }
            hideOriginListCom(pluginData, datas[pluginCategoryIndex].items || []);
            datas[pluginCategoryIndex].items?.splice(pluginIndex, 0, pluginData);
          }

          return;
        }

        // ------- 插件包没有配置显示顺序时，采用默认的兜底方案 -------------------

        // 插件包存在分类值，则将该插件包放入对应分类内
        if (pluginCategory) {
          /** 对应分类内最后一个组件的下标 */
          const lastCompDataIndex = findLastIndex(
            datas[pluginCategoryIndex].items,
            (_item) => !_item.packName,
          );

          if (isNumber(lastCompDataIndex) && lastCompDataIndex > -1) {
            hideOriginListCom(pluginData, datas[pluginCategoryIndex].items || []);
            datas[pluginCategoryIndex].items?.splice(lastCompDataIndex + 1, 0, pluginData);
          }
        } else {
          // 插件包没有分类值，默认排列在所属组件后面
          /** 所属组件最新的下标值 */
          const itemIndex = datas[pluginCategoryIndex].items?.findIndex(
            (_item) => _item.type === pluginPackage.componentType,
          );

          if (isNumber(itemIndex) && itemIndex > -1) {
            hideOriginListCom(pluginData, datas[pluginCategoryIndex].items || []);
            datas[pluginCategoryIndex].items?.splice(itemIndex + 1, 0, pluginData);
          }
        }
      });
    }
  });
};

/**
 * 组件数据预处理 - 获取插件包数据
 */
export const formatCompDatas = (datas: ICategoryViewData[]) => {
  const _datas = cloneDeep(datas);

  datas.forEach((data, index) => {
    if (!(Array.isArray(data.items) && data.items.length)) return;

    getPluginCompDatas(data.items, index, _datas);
  });

  return _datas;
};

export const getDisplayedDatas = (
  datas: ICategoryViewData[],
  categories: ICategory[],
  selectedCategoryType: string,
  searchVal: string,
  renderTitle?: RenderTitle,
) => {
  /**
   * 根据选中分类进行过滤
   * - id: 对应二级分类选中时的过滤
   */
  if (!searchVal) {
    datas = datas.filter(
      (data) => selectedCategoryType === 'ALL'
        || data.category === selectedCategoryType
        || data?.id === selectedCategoryType,
    );
  }

  const customHideComList: ICustomHideComDesc[] = [];

  const displayedDatas: ICategoryViewData[] = datas
    .map((_data) => {
      const { category, items, childs } = _data;
      const categoryType = categories.find(({ id }) => id === category) as IComCategory;
      const _childs = cloneDeep(childs);

      const categoryTitle = renderTitle ? renderTitle(categoryType, _data) : _data.title;
      const highlightTitle: string = categoryTitle as string;

      const getHideCom = (comp: IComDescription) => {
        const {
          customControll, name: comName, fullPinyin = '', deleteType,
        } = comp;
        const isCustomHide = customControll === 1;
        // 收集需要处理自定义隐藏的组件
        if (isCustomHide) {
          customHideComList.push({
            ...comp,
            isCustomHide,
            // 标记当前所属一级分类，用于后续遍历
            mainCategory: category,
          });
        }
        const changeData: IComDescription = {
          ...comp,
          isCustomHide,
        };

        /** 前端可通过deleteType隐藏组件，1：隐藏 0：显示 */
        const isHide = deleteType === 1;

        if (!searchVal) {
          return {
            changeData,
            filter: !isHide,
          };
        }

        let result = false;
        const langId = window.TEAMS?.locale?.lang;
        const chineseList = ['zh_CN', 'zh_TW'];

        // 判断是否中文环境
        if (chineseList.includes(langId)) {
          // 中文内容搜索
          const searchIndex = comName.toLocaleLowerCase().indexOf(searchVal.toLocaleLowerCase());
          const searchLength = searchVal.length;
          result = searchIndex > -1;
          if (result) {
            const baseData = comName.slice(searchIndex, searchIndex + searchLength);
            changeData.highlightTitle = comName.replace(baseData, getHighlightSpan(baseData));
          }
          // 中文模式下对拼音内容搜索
          const { search } = fuzzySearch(searchVal, fullPinyin);
          result = result || search;
        } else {
          // 英文模式下模糊搜索
          const { search: comNameSearch, title } = fuzzySearch(searchVal, comName);
          result = comNameSearch;
          changeData.highlightTitle = title;
        }

        return {
          changeData,
          filter: result && !isHide,
        };
      };

      const fresherItems: IComDescription[] = formatComData(items!, getHideCom);

      const fresherChildren = formatComData(_childs!, (child) => {
        child.items = formatComData(child.items!, getHideCom);
        return {
          changeData: child,
          filter: Boolean(child.items?.length),
        };
      });

      return {
        ..._data,
        items: fresherItems,
        title: categoryTitle,
        highlightTitle,
        childs: fresherChildren,
      };
    })
    .filter((_data) => _data.items?.length || _data.childs?.length);

  return {
    displayedDatas,
    customHideComList,
  };
};

export const getCategoryTypes = (compDatas: ICategory[]) => [
  { id: 'ALL', content: getLabel('54218', '全部') },
  ...compDatas.map((compData) => {
    const { id, name } = compData;

    return {
      id,
      content: name,
    };
  }),
];

export const filterUniqueComs = (
  compDatas: ICategoryViewData[],
  coms: IComData[],
  getUniqueKeyData?: (data: any) => any,
  filter = (v: ICategoryViewData[]) => v,
) => filter(
  compDatas.map((compData) => {
    if (!getUniqueKeyData) {
      return compData;
    }
    const comsKeys: string[] = [];

    coms.forEach((com) => {
      const uniqueKeyData = getUniqueKeyData(com);

      if (isArray(uniqueKeyData)) {
        comsKeys.push(...uniqueKeyData);
      } else {
        comsKeys.push(uniqueKeyData);
      }
    });

    compData.items = compData.items?.map((item) => {
      if (comsKeys.indexOf(getUniqueKeyData(item)) > -1 && item.unique) {
        return {
          ...item,
          disabled: true,
        };
      }
      return { ...item };
    });

    compData.childs = compData.childs?.map((child) => {
      child.items = child.items?.map((_item) => {
        if (comsKeys.indexOf(getUniqueKeyData(_item)) > -1 && _item.unique) {
          return {
            ..._item,
            disabled: true,
          };
        }
        return { ..._item };
      });

      return child;
    });

    return compData;
  }),
);

/**
 * 更新组件的 isCustomHide 信息
 * @param comList 所有组件
 * @param hideComList 收集起来的需要自定义隐藏的组件
 * @returns 更新后的组件
 */
export const updateCustomHideInfo = (
  comList: IComDescription[] | undefined,
  hideComList: ICustomHideComDesc[],
): IComDescription[] | undefined => {
  if (!Array.isArray(comList) || !comList.length) {
    return comList;
  }
  return comList.map((comInfo) => {
    const { id: comId } = comInfo;
    const hideComInfo = hideComList?.find((hideCom) => hideCom.id === comId);
    if (hideComInfo) {
      return {
        ...comInfo,
        isCustomHide: hideComInfo?.isCustomHide,
      };
    }
    return comInfo;
  });
};

/**
 * 处理组件自定义隐藏逻辑，更新自定义隐藏信息
 * @param displayDataList 所有展示的组件
 * @param customHideData 收集起来的需要自定义隐藏的组件
 * @param options 终端类型等其他信息
 */
export const handleComCustomHide = (
  displayDataList: ICategoryViewData[],
  customHideData: ICustomHideComDesc[],
  options?: HandleComHideOpts,
) => {
  const { clientType = 'PC' } = options || {};
  // 从组件的 defaultOpts 中获取自定义隐藏的配置
  const promiseComs = customHideData.map(
    (comp) => new Promise<ICustomHideComDesc>((resolve) => {
      getDesignInfo(comp, clientType)
        .then((comDesignInfo) => {
          const { customHide } = comDesignInfo?.defaultOpts?.compList || {};
          return Promise.resolve(customHide?.(comp, clientType) || false).then((isCustomHide) => {
            resolve({
              ...comp,
              isCustomHide,
            });
          });
        })
        .catch(() => {
          resolve(comp);
        });
    }),
  );

  return Promise.all(promiseComs).then((hideComList) => displayDataList.map((data) => {
    const { category, items, childs } = data;
    // 判断当前分类下有没有需要处理的组件
    const isCategory = hideComList.some((hideCom) => hideCom.mainCategory === category);
    // 没有的话就不用处理
    if (!isCategory) {
      return {
        ...data,
      };
    }
    // 更新当前分类下组件的信息
    const fresherItems = updateCustomHideInfo(items, hideComList);
    // 更新子类下组件的信息
    const fresherChilds = childs?.map((child) => {
      child.items = updateCustomHideInfo(child?.items, hideComList);
      return child;
    });
    return {
      ...data,
      items: fresherItems,
      childs: fresherChilds,
    };
  }));
};

export default {};
