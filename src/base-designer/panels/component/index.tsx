import { ComponentType } from 'react';
import Loadable from '../../../common/loadable';
import { ComponentPanelProps } from './types';
import { fetchCategories } from './utilsEager';

const ComponentPanel = Loadable({
  name: 'ComponentPanel',
  loader: () => import(/* webpackChunkName: "de_base_designer" */ './Panel'),
}) as ComponentType<ComponentPanelProps>;

const ComponentCard = Loadable({
  name: 'ComponentCard',
  loader: () => import(/* webpackChunkName: "de_base_designer" */ './list/Card'),
}) as ComponentType<ComponentPanelProps>;

ComponentPanel.displayName = 'ComponentPanel';
ComponentCard.displayName = 'ComponentCard';

export { ComponentCard, ComponentPanel, fetchCategories };

export default ComponentPanel;
