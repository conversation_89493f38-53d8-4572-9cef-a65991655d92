import { OptionData } from '@weapp/ui/lib/components/select/types';
import { debounce } from '@weapp/utils';
import { action, observable, runInAction } from 'mobx';
import { ReactText } from 'react';
import { ModeType } from '../../../constants';
import shallowObservable from '../../../utils/shallowObservable';
import { ICategory } from './types';
import { fetchShowMode } from './utilsEager';

export default class ComponentStore {
  /** 组件分类信息 */
  @shallowObservable categories: ICategory[] = [];

  /** 选中的组件类型 */
  @observable selectedCategoryType: string = 'ALL';

  /** 筛选值 */
  @observable searchVal: string = '';

  @observable hasInit: boolean = false;

  /** 组件分类类型是否发生过改变 */
  @observable isCategoryTypeChanged: boolean = false;

  @observable showMode: ModeType = ModeType.Grid;

  @action
  setInit = (hasInit: boolean) => {
    this.hasInit = hasInit;
  };

  @action
  setCategories = (categories: ICategory[]) => {
    this.categories = categories;
  };

  @action
  onCategoryTypeSelect = (categoryType: string | OptionData) => {
    this.selectedCategoryType = categoryType as string;
  };

  @action
  onSearch = (searchVal: ReactText) => {
    this.searchVal = searchVal as string;
  };

  @action
  setIsCategoryTypeChanged = (isCategoryTypeChanged: boolean) => {
    this.isCategoryTypeChanged = isCategoryTypeChanged;
  };

  /** 防抖处理，防止频繁发送切换请求 */
  @action
  setModeType = debounce((mode?: ModeType) => {
    if (mode) {
      runInAction(() => {
        this.showMode = mode;
        fetchShowMode(mode);
      });
    }
  }, 200);
}
