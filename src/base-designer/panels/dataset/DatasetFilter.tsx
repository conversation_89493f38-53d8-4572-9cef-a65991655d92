import { DataSet } from '@weapp/ebdcoms';
import { CorsComponent } from '@weapp/ui';
import { getLabel, isArray, isEmpty } from '@weapp/utils';
import { toJS } from 'mobx';
import React, { PureComponent } from 'react';
import { openConditionSet, transFormConditionValue } from './utils';

type FilterVal = any;

interface DatasetFilterProps extends React.Attributes {
  onChange: (val: FilterVal) => void;
  value?: FilterVal;
  datasource: DataSet;
  filterIsNull?: boolean;
}

export default class DatasetFilter extends PureComponent<DatasetFilterProps> {
  render() {
    const { datasource, onChange, filterIsNull } = this.props;

    let { value = [] } = this.props;
    const type = datasource?.type;
    const groupId = datasource?.groupId || '';

    const isOpenConditionSet = isEmpty(type) || openConditionSet(type, groupId);
    value = transFormConditionValue(isOpenConditionSet, value);

    if (isOpenConditionSet && !isArray(toJS(value))) {
      return (
        <CorsComponent
          weId={`${this.props.weId || ''}_as0mj0`}
          app="@weapp/components"
          compName="ConditionSet"
          dataSet={datasource}
          value={value}
          onChange={onChange}
          placeholder={getLabel('53952', '设置过滤条件')}
          title={getLabel('101819', '数据集固定查询条件')}
          filterIsNull={filterIsNull}
          showFilterIsNull
        />
      );
    }

    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_as0mj0`}
        app="@weapp/ebdcoms"
        compName="FilterInput"
        value={value}
        dataset={datasource}
        placeholder={getLabel('53952', '设置过滤条件')}
        title={getLabel('101819', '数据集固定查询条件')}
        onChange={onChange}
      />
    );
  }
}
