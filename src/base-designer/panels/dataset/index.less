@import (reference) '../../../style/prefix.less';
@import (reference) '../../../style/var.less';

.@{prefix}-panels-dataset {
  display: flex;
  flex-direction: column;
  height: 100%;

  .ui-input-wrap {
    width: calc(100% - 20px);
    margin: 10px 10px 6px 10px;
  }

  .ui-spin-nested-loading {
    height: 100%;
    width: 100%;

    & > div:nth-child(1) {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      overflow: auto;
    }
  }

  &-collapse {
    width: 100%;
    padding: 10px 10px 0 10px;
    border: none;
    overflow-y: auto;

    .ui-collapse-panel {
      border: var(--border-solid);
      margin-bottom: 10px;
    }

    .ui-collapse-panel__title {
      border: none !important;
      padding: 4px 6px;
    }

    .ui-collapse-panel__content {
      padding: 10px 0;
    }

    .ui-collapse-panel__content-box {
      padding: 0;
      border: none;
    }

    .ui-form-module > div,
    .ui-form-module > div > div {
      border: none !important;
    }

    &-title {
      width: 100%;
      font-size: 12px;
      display: grid;
      grid-template-columns: 36px auto 14px;

      & > span:nth-child(1) {
        font-weight: bold;
      }
    }

    &-name {
      margin-left: 5px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &-icon,
    &-delete {
      display: flex;
      align-items: center;
    }
  }

  &-scroll {
    padding-right: 2px;
  }

  &-form {
    .ui-formItem-module {
      padding: 4px 8px;
    }

    .ui-input {
      padding: 4px 6px 4px 11px;
    }
  }

  &-add {
    margin: 5px 10px;
    height: 30px;

    & > button {
      width: 100%;
    }
  }

  // 无数据集时，Empty组件存在button点击，所以需要提高层级
  &-empty {
    z-index: @zindex !important;
  }
}
