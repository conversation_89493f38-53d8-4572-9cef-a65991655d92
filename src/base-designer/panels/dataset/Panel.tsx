import {
  Button, Collapse, CorsComponent, ICollapsePanelProps, Icon, Input,
} from '@weapp/ui';
import { ValueType } from '@weapp/ui/lib/components/input';
import { getLabel, includes, isArray } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { prefixCls } from '../../../constants';
import DatasetItem from './DatasetItem';
import './index.less';
import { DatasetProps, DatasetStates, DatasetValue } from './types';
import { fetchGenerateId } from './utils';

class Dataset extends PureComponent<DatasetProps, DatasetStates> {
  constructor(props: DatasetProps) {
    super(props);

    const { datasetVals } = this.props;

    this.state = {
      datasetVals,
      searchValue: '',
      activeKey: this.getDftActiveKey(),
    };
  }

  // 默认展开没有填写名称的数据集
  getDftActiveKey = () => {
    const { datasetVals } = this.props;
    const activeKey = [] as string[];

    datasetVals.forEach((data) => {
      if (!data.name) {
        activeKey.push(data.id || '');
      }
    });

    return activeKey;
  };

  onChange = (formValue: DatasetValue) => {
    const { onChange } = this.props;
    const { datasetVals } = this.state;

    const dataIndex = datasetVals.findIndex((data) => data.id === formValue.id);

    datasetVals.splice(dataIndex, 1, formValue);

    this.setState({
      datasetVals,
    });

    onChange(datasetVals);
  };

  handleChange = (activeKey: string | string[]) => {
    this.setState({
      activeKey: activeKey as string[],
    });
  };

  onSearchChange = (value: ValueType) => {
    const { datasetVals } = this.props;

    this.setState({
      datasetVals: datasetVals.filter((item: DatasetValue) => includes(item.name, value)),
      searchValue: value as string,
    });
  };

  onAddClick = () => {
    const { onChange } = this.props;
    const { datasetVals, activeKey } = this.state;

    fetchGenerateId(1).then((ids) => {
      if (ids && ids.length) {
        const defaultDataset = {
          id: ids[0],
          name: '',
          datasource: undefined,
          filter: [],
          fields: [],
          remark: '',
        };

        datasetVals.push(defaultDataset);

        this.setState({
          datasetVals,
          activeKey: [...activeKey, ids[0]],
        });

        onChange(datasetVals);
      }
    });
  };

  onItemDelete = (dataset: DatasetValue) => () => {
    const { onChange } = this.props;
    const { datasetVals } = this.state;
    const dataIndex = datasetVals.findIndex((data) => data.id === dataset.id);

    datasetVals.splice(dataIndex, 1);

    this.setState({
      datasetVals,
    });

    onChange(datasetVals);
  };

  customCollapseTitleRender = (dataset: DatasetValue) => (
    <div className={`${prefixCls}-panels-dataset-collapse-title`}>
      <span>{getLabel('101804', '数据集')}</span>
      <span className={`${prefixCls}-panels-dataset-collapse-name`} title={dataset.name}>
        {!includes(this.state.activeKey, dataset.id) ? dataset.name : ''}
      </span>
      <CorsComponent
        weId={`${this.props.weId || ''}_vw1tg1`}
        app="@weapp/ebdcoms"
        compName="IconFont"
        wrapClassName={`${prefixCls}-panels-dataset-collapse-delete`}
        title={getLabel('53951', '删除')}
        name="Icon-delete04"
        onClick={this.onItemDelete(dataset)}
      />
    </div>
  );

  customIconRender = (props: ICollapsePanelProps) => (
    <Icon
      weId={`${this.props.weId || ''}_3v8l7i`}
      name={props.active ? 'Icon-Down-arrow04' : 'Icon-Right-arrow04'}
      className={`${prefixCls}-panels-dataset-collapse-icon`}
      style={{ marginRight: '5px' }}
      size="sm"
    />
  );

  render() {
    const { activeKey, searchValue, datasetVals } = this.state;

    const suffix = (
      <Icon
        className="ui-searchAdvanced-input-icon"
        weId={`${this.props.weId || ''}_7i01zy`}
        name="Icon-search"
      />
    );

    return (
      <div className={`${prefixCls}-panels-dataset`}>
        <div className={`${prefixCls}-tree-header`}>
          <Input
            weId={`${this.props.weId || ''}_xkdok5`}
            allowClear
            value={searchValue}
            placeholder={getLabel('101561', '请输入数据集名称')}
            onChange={this.onSearchChange}
            suffix={suffix}
          />
        </div>
        <If
          weId={`${this.props.weId || ''}_dgxjco`}
          condition={isArray(datasetVals) && datasetVals.length}
        >
          <Then weId={`${this.props.weId || ''}_2e0jfh`}>
            <Collapse
              weId={`${this.props.weId || ''}_9q049c`}
              panelTitleBackground="none"
              activeKey={activeKey}
              className={`${prefixCls}-panels-dataset-collapse`}
              onChange={this.handleChange}
              customIconRender={this.customIconRender}
            >
              {datasetVals.map((data, index) => {
                const key = data.id || index;

                return (
                  <Collapse.Panel
                    weId={`${this.props.weId || ''}_1t2bqv@${key}`}
                    title={this.customCollapseTitleRender(data)}
                    key={key}
                    id={data.id}
                    iconPosition="left"
                  >
                    <DatasetItem
                      weId={`${this.props.weId || ''}_i6uwt1@${key}`}
                      data={data}
                      datasetVals={this.props.datasetVals}
                      searchValue={searchValue}
                      onChange={this.onChange}
                    />
                  </Collapse.Panel>
                );
              })}
            </Collapse>
            <When weId={`${this.props.weId || ''}_6tfcmh`} condition={!searchValue}>
              <div className={`${prefixCls}-panels-dataset-add`}>
                <Button
                  weId={`${this.props.weId || ''}_x2hswm`}
                  type="default"
                  onClick={this.onAddClick}
                >
                  +{getLabel('101563', '添加数据集')}
                </Button>
              </div>
            </When>
          </Then>
          <Else weId={`${this.props.weId || ''}_pbmtk2`}>
            <CorsComponent
              weId={`${this.props.weId || ''}_xl1p5m`}
              app="@weapp/ebdcoms"
              compName="Empty"
              className={`${prefixCls}-panels-dataset-empty`}
              type={searchValue ? 'search' : 'noData'}
              btnText={searchValue ? null : getLabel('101563', '添加数据集')}
              onBtnClick={this.onAddClick}
            />
          </Else>
        </If>
      </div>
    );
  }
}
export default observer(Dataset);
