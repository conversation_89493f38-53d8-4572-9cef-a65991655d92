import { DataSet, Filter } from '@weapp/ebdcoms';
import { FormValue } from '@weapp/ui';
import { ReactText } from 'react';

/**
 * 数据集字段类型
 * ebdcoms不再导出类型Field，来源于 src/common/field/type.ts
 */
export interface Field {
  name: string;
  text: string;
  type: string;
  orderType: any;
  objId?: string;
  id: string;
  sumField?: string;
  statisticsField?: string[];
  isDetailtable?: boolean;
  fields?: Field[];
  isDetailMainKey?: boolean;
}

export interface DatasetValue {
  /** 数据集: id */
  id?: string;
  /** 数据集: 名称 */
  name: string;
  /** 数据集: 数据源 */
  datasource?: DataSet;
  /** 数据集: 字段 */
  fields?: Field[];
  /** 数据集: 条件 */
  filter?: Filter | Filter[];
  /** 数据集: 备注 */
  remark: string;
}

export interface DatasetItemProps {
  data: DatasetValue;
  datasetVals: DatasetValue[];
  searchValue: string;
  onChange: (formValue: DatasetValue) => void;
}

export interface DatasetStates {
  datasetVals: DatasetValue[];
  searchValue: string;
  activeKey: string[];
}

export interface DatasetProps {
  datasetVals: DatasetValue[];
  onChange: (datas: DatasetValue[]) => void;
}

export interface DatasetNameProps {
  value: FormValue;
  placeholder?: string;
  onBlur?: (value: ReactText) => void;
  onChange: (value: ReactText) => void;
}

export interface FilterFooterParams {
  filterIsNull?: boolean;
  isFilterSub?: boolean;
}
