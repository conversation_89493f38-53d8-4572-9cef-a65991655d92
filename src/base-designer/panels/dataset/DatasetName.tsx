import { Input } from '@weapp/ui';
import { observer } from 'mobx-react';
import React, { PureComponent, ReactText } from 'react';
import './index.less';
import { DatasetNameProps } from './types';

class DatasetName extends PureComponent<DatasetNameProps> {
  onInputChange = (value: ReactText) => {
    const { onChange } = this.props;

    onChange(String(value).replace(/\W/g, ''));
  };

  render() {
    const { value, placeholder, onBlur } = this.props;
    return (
      <Input
        weId={`${this.props.weId || ''}_kha36a`}
        value={value as string}
        placeholder={placeholder}
        onChange={this.onInputChange}
        onBlur={onBlur}
      />
    );
  }
}
export default observer(DatasetName);
