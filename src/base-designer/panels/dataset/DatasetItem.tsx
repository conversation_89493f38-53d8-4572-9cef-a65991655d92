import { DataSet } from '@weapp/ebdcoms';
import {
  CorsComponent,
  Form,
  FormDatas,
  FormItemProps,
  FormStore,
  FormSwitchProps,
} from '@weapp/ui';
import { cloneDeep, getLabel, includes } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { prefixCls } from '../../../constants';
import DatasetFilter from './DatasetFilter';
import DatasetName from './DatasetName';
import './index.less';
import { DatasetItemProps, DatasetValue, FilterFooterParams } from './types';

const items = (_this: any) => ({
  name: {
    itemType: 'CUSTOM',
    autoFocus: !_this.props.searchValue,
    errorType: 'popover',
    rules: 'required|isSame',
  },
  datasource: {
    itemType: 'CUSTOM',
    errorType: 'popover',
    required: true,
  },
  fields: {
    itemType: 'CUSTOM',
    errorType: 'popover',
  },
  filter: {
    itemType: 'CUSTOM',
    errorType: 'popover',
  },
  remark: {
    itemType: 'INPUT',
    placeholder: getLabel('101852', '请输入备注信息'),
    errorType: 'popover',
  },
} as FormItemProps);

const initLayout = [
  [
    {
      id: '1',
      label: getLabel('96066', '名称'),
      items: ['name'],
      labelSpan: 8,
      hide: false,
    },
  ],
  [
    {
      id: '2',
      label: getLabel('55058', '数据源'),
      items: ['datasource'],
      labelSpan: 8,
      hide: false,
    },
  ],
  [
    {
      id: '3',
      label: getLabel('56085', '字段'),
      items: ['fields'],
      labelSpan: 8,
      hide: false,
    },
  ],
  [
    {
      id: '4',
      label: getLabel('55481', '条件'),
      items: ['filter'],
      labelSpan: 8,
      hide: false,
    },
  ],
  [
    {
      id: '5',
      label: getLabel('53892', '备注'),
      items: ['remark'],
      labelSpan: 8,
      hide: false,
    },
  ],
];

class DatasetItem extends PureComponent<DatasetItemProps> {
  formStore: FormStore = new FormStore();

  componentDidMount() {
    const { data } = this.props;

    this.formStore.initForm({
      data: data as any,
      items: items(this),
      layout: initLayout,
      groups: [], // groups为空，则不分组显示
    });
  }

  onChange = (value?: FormDatas | DatasetValue, other?: FilterFooterParams) => {
    const { data, onChange } = this.props;
    const { datasource, name } = value as DatasetValue;
    const formData = this.formStore.getFormDatas();

    if (name) {
      this.validateName();
    }

    if (datasource) {
      this.formStore.updateDatas({ filter: [], fields: [] });
    }

    onChange({
      ...data,
      ...formData,
      ...value,
      filterIsNull: other?.filterIsNull,
    });
  };

  // 校验名称
  validateName = () => {
    const { data, datasetVals } = this.props;
    const datasetNames = cloneDeep(datasetVals)
      .filter((datasetVal) => datasetVal.id !== data.id)
      .map((datasetVal) => datasetVal.name);

    const customRules = this.formStore.getRules(); // 获取默认规则
    return this.formStore.validate(
      { name: customRules.name },
      {
        customRegister: [
          {
            name: 'isSame',
            rules: (val: string) => !includes(datasetNames, val),
            errorMessage: getLabel('105110', '该名称已存在'),
          },
        ],
      },
    );
  };

  // 校验数据源
  onDatasourceCancel = () => {
    const customRules = this.formStore.getRules(); // 获取默认规则

    return this.formStore.validate({ datasource: customRules.datasource });
  };

  customRenderFormSwitch = (key: string, props: FormSwitchProps) => {
    const comProps = props.props;
    const { onChange } = comProps;
    const {
      name, datasource, filter, fields, filterIsNull,
    } = this.formStore.datas;

    const fieldsProps = {
      type: 'field',
      config: {
        dataset: toJS(datasource),
      },
      placeholder: getLabel('87540', '点击设置字段'),
      value: fields,
      onChange,
      simple: true,
      fieldFilter:
        (datasource as DataSet)?.groupId === 'sql'
          ? (item: any) => {
            const showFields = ['measure'];
            return showFields.includes(item?.usage);
          }
          : () => true,
    };

    switch (key) {
      case 'name':
        return (
          <DatasetName
            weId={`${this.props.weId || ''}_tymdj1`}
            value={name}
            placeholder={getLabel('101851', '请输入英文名称')}
            onBlur={this.validateName}
            onChange={onChange}
          />
        );

      case 'datasource':
        return (
          <CorsComponent
            weId={`${this.props.weId || ''}_tymdj1`}
            app="@weapp/components"
            compName="DataSetView"
            showSqlDataset
            showInnerAPIData
            value={toJS(datasource)}
            placeholder={getLabel('56764', '请选择数据源')}
            onCancel={this.onDatasourceCancel}
            onChange={onChange}
          />
        );

      case 'fields':
        return (
          <CorsComponent
            weId={`${this.props.weId || ''}_v1eqhx`}
            app="@weapp/ebdcoms"
            compName="FieldView"
            {...fieldsProps}
          />
        );

      case 'filter':
        return (
          <DatasetFilter
            weId={`${this.props.weId || ''}_6jbuw6`}
            value={filter}
            datasource={toJS(datasource) as DataSet}
            onChange={onChange}
            filterIsNull={filterIsNull as boolean}
          />
        );

      default:
        return null;
    }
  };

  render() {
    const { data } = this.props;
    return (
      <div>
        <Form
          weId={`${this.props.weId || ''}_a51jqf@${data.id}`}
          store={this.formStore}
          onChange={this.onChange}
          className={`${prefixCls}-panels-dataset-form`}
          customRenderFormSwitch={this.customRenderFormSwitch}
        />
      </div>
    );
  }
}
export default observer(DatasetItem);
