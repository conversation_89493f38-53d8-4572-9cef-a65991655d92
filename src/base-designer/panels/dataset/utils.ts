import { isArray } from '@weapp/utils';
import { toJS } from 'mobx';
import ebdcoms from '../../../utils/ebdcoms';

export const openConditionSet = (type: string | undefined, groupId?: string) => groupId !== 'sql' && type !== 'BIZ';

export const transFormConditionValue = (isOpenConditionSet: boolean, value: any) => {
  if (!value || (isArray(toJS(value)) && toJS(value).length === 0)) {
    value = isOpenConditionSet ? {} : [];
  }
  return value;
};

// 生成数据集id（后端分布式id）
export const fetchGenerateId = (count: number): Promise<string[]> => new Promise((resolve) => {
  ebdcoms.asyncExcu('ajax', {
    url: '/api/ebuilder/common/distribution/generateId',
    params: {
      count,
    },
    success: resolve,
    error: () => {
      resolve(['']);
      return true;
    },
  });
});

export default {};
