import { corsImport } from '@weapp/utils';
import {
  action, computed, observable, runInAction, toJS,
} from 'mobx';
import { ReactText } from 'react';
import shallowObservable from '../../../utils/shallowObservable';
import { Page } from './types';

export enum DisplayType {
  Card = '0',
  Tree = '1',
}

export const HomepageType = '1';

class PageStore {
  /**
   * 页面展示方式，树形或卡片
   */
  @observable displayType = DisplayType.Card;

  @observable selectedPageId = '';

  @observable searchVal: ReactText = '';

  @observable loading: boolean = true;

  @shallowObservable pagelist: Page[] = [];

  @action.bound
  initPages(pageId: string, url: string) {
    if (pageId && url) {
      corsImport('@weapp/ebdcoms').then((mod) => {
        if (mod) {
          const { ajax } = mod;

          ajax({
            url,
            params: { pageId },
            ebBusinessId: pageId,
            success: (data: Page[]) => {
              runInAction(() => {
                this.pagelist = data;
              });
            },
            effect: () => {
              runInAction(() => {
                this.loading = false;
              });
            },
          });
        }
      });

      this.selectPage(pageId);
    } else {
      this.loading = false;
    }
  }

  @action.bound
  initDatas(pageId: string, datas: Page[]) {
    if (pageId && datas) {
      this.pagelist = datas;
      this.selectPage(pageId);
    }
    this.loading = false;
  }

  @computed get pages() {
    let pages: Page[] = [];
    const isTree = this.displayType === DisplayType.Tree;

    if (isTree) {
      pages = toJS(this.pagelist);
    } else {
      this.pagelist.forEach((page) => {
        if (!page.pages) {
          pages.push(page);

          return;
        }
        const children = toJS(page.pages).map((child) => {
          // 兼容老eb的建模页面，新建布局，显示布局，编辑布局
          if (child.type.indexOf('LAYOUT_') > -1) {
            child.name = `${page.name} / ${child.name}`;
          }
          return child;
        });

        pages.push(...children);
      });
    }

    return pages.filter((page) => page.name.indexOf(this.searchVal as string) > -1);
  }

  @action
  setDisplayType = (displayType: DisplayType) => {
    this.displayType = displayType;
  };

  @action
  onSearch = (searchVal: ReactText) => {
    this.searchVal = searchVal;
  };

  @action
  selectPage = (pageId: string) => {
    this.selectedPageId = pageId;
  };
}

const pageStore = new PageStore();

export default pageStore;
