import { Icon, ITreeData, Tree } from '@weapp/ui';
import { observer } from 'mobx-react';
import React from 'react';
import { prefixCls } from '../../../constants';
import { ClientType } from '../../../core';
import pageStore, { HomepageType } from './store';
import Title from './TreeTitle';
import { Page } from './types';

interface PageTreeProps {
  pages: Page[];
  clientType: ClientType;
  filter: (pages: Page[]) => Page[];
  onSelect?: (pageId: string) => void;
}

const getTreeData = (pagelist: Page[]): any[] => {
  if (!pagelist) return [];

  return pagelist.map((pNode: Page) => ({
    id: pNode.id,
    content: pNode.name,
    thumbnails: pNode.thumbnails,
    isLeaf: !pNode.pages,
    isHomepage: pNode.isHomePage === HomepageType,
    children: getTreeData(pNode?.pages || []),
  }));
};

class PageTree extends React.PureComponent<PageTreeProps> {
  onSelect = (data: ITreeData) => {
    // 不选中父节点
    if (data.children.length) {
      return;
    }

    const { onSelect } = this.props;
    const { selectPage } = pageStore;

    onSelect?.(data.id);
    selectPage(data.id);
  };

  getTreeNodeTitle = (data: ITreeData) => (
    <Title
      weId={`${this.props.weId || ''}_i3mvr2`}
      data={data}
      clientType={this.props.clientType}
    />
  );

  renderIcon = (root: ITreeData, isParent: boolean, isExpanded: boolean) => {
    if (root.isLeaf) {
      return (
        <Icon
          weId={`${this.props.weId || ''}_1mzgyw`}
          name={root.isHomepage ? 'Icon-Collection' : 'Icon-file'}
        />
      );
    }

    if (isParent) {
      return (
        <Icon
          weId={`${this.props.weId || ''}_25pefx`}
          name={isExpanded ? 'Icon-File-expansion' : 'Icon-folder'}
        />
      );
    }

    return null;
  };

  render() {
    const { pages, filter } = this.props;
    const { selectedPageId } = pageStore;
    const treeData = getTreeData(filter(pages));

    return (
      <div className={`${prefixCls}-page-tree-content`}>
        <Tree
          weId={`${this.props.weId || ''}_g5ju3s`}
          data={treeData}
          customRenderNode={this.getTreeNodeTitle}
          onSelect={this.onSelect}
          selectedKeys={[selectedPageId]}
          renderIcon={this.renderIcon}
        />
      </div>
    );
  }
}

export default observer(PageTree);
