import {
  CorsComponent, Icon, Input, Spin,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { DesignerContext } from '../..';
import { prefixCls } from '../../../constants';
import { UUID } from '../../../utils';
import CardList from './CardList';
import './index.less';
import pageStore from './store';
import Switch from './Switch';
import PageTree from './Tree';
import { DisplayType, Page, PageProps } from './types';

const suffix = (
  <span className={`${prefixCls}-slide-speed-suffix`}>
    <Icon className="ui-searchAdvanced-input-icon" weId="y3gv00" name="Icon-search" />
  </span>
);

class PagePanel extends PureComponent<PageProps> {
  static contextType = DesignerContext;

  componentDidMount() {
    const { pageId, url, datas } = this.props;
    const {
      pagelist, selectedPageId, selectPage, initPages, initDatas,
    } = pageStore;

    if (datas) {
      initDatas(pageId, datas);
    } else if (!pagelist.length) {
      initPages(pageId, url);
    }

    /**
     * 特殊处理：
     * grid布局页面切换到flow布局页面，pageId 必须从外部传入
     */

    if (pageId !== selectedPageId) {
      selectPage(pageId);
    }
  }

  // 只展示属于当前终端类型的页面
  filterPages = (pages: Page[]): Page[] => {
    const { clientType } = this.props;

    const scopedPages = pages.filter((page: Page) => {
      if (page.pages) {
        return this.filterPages(page.pages);
      }

      return page.terminalScope.indexOf(clientType) >= 0;
    });

    const groupedPages: Page[] = [];
    const _sortedGroupPages: Page[] = [];
    const _groupedPages: {
      [key: string]: Page[];
    } = {};
    scopedPages.forEach((page) => {
      if (['0', '-1', undefined].includes(page.group?.id)) {
        groupedPages.push(page);
      } else {
        const { group } = page;
        const { id: groupId } = group || {};
        if (!_groupedPages[groupId]) {
          _groupedPages[groupId] = [];
        }
        _groupedPages[groupId].push(page);
      }
    });
    Object.values(_groupedPages).forEach((_pages) => {
      const { group } = _pages[0] || {};
      const { id: groupId, name: groupName } = group || {};
      _sortedGroupPages.push({
        ..._pages[0],
        id: groupId || UUID(),
        name: groupName || '',
        pages: _pages,
      });
    });
    _sortedGroupPages.sort((a, b) => (a?.group?.showOrder || 0) - (b?.group?.showOrder || 0));
    groupedPages.push(..._sortedGroupPages);
    return groupedPages;
  };

  render() {
    const { onSelect, clientType, placeholder } = this.props;
    const {
      displayType, onSearch, loading, pages, searchVal, setDisplayType,
    } = pageStore;
    const isCard = displayType === DisplayType.Card;

    if (loading) {
      return (
        <div className={`${prefixCls}-component-panel`}>
          <Spin weId="cwk640" />
        </div>
      );
    }

    return (
      <div className={`${prefixCls}-panels-page`}>
        <div className={`${prefixCls}-pp-search`}>
          <Input
            weId={`${this.props.weId || ''}_1tyejy`}
            allowClear
            placeholder={placeholder || getLabel('54018', '请输入页面名称')}
            onChange={onSearch}
            suffix={suffix}
            defaultValue={searchVal}
          />
          <Switch
            weId={`${this.props.weId || ''}_4mpqp4`}
            displayType={displayType}
            onChange={setDisplayType}
          />
        </div>
        <When weId={`${this.props.weId || ''}_vutzt5`} condition={pages.length}>
          <If weId={`${this.props.weId || ''}_m72b2i`} condition={isCard}>
            <Then weId={`${this.props.weId || ''}_6dvx23`}>
              <CardList
                weId={`${this.props.weId || ''}_1qg8ja`}
                pages={pages}
                clientType={clientType}
                onSelect={onSelect}
                filter={this.filterPages}
              />
            </Then>
            <Else weId={`${this.props.weId || ''}_1wjkjf`}>
              <PageTree
                weId={`${this.props.weId || ''}_s3eqf4`}
                pages={pages}
                clientType={clientType}
                onSelect={onSelect}
                filter={this.filterPages}
              />
            </Else>
          </If>
        </When>
        <When weId={`${this.props.weId || ''}_vutzt5`} condition={!pages.length}>
          <CorsComponent
            weId="n9qc6y"
            app="@weapp/ebdcoms"
            compName="Empty"
            type={searchVal ? 'search' : 'noData'}
          />
        </When>
      </div>
    );
  }
}

export default observer(PagePanel);
