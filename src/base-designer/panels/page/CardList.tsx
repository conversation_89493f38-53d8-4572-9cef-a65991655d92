import { CorsComponent } from '@weapp/ui';
import React from 'react';
import { Else, If, Then } from 'react-if';
import { prefixCls } from '../../../constants';
import { ClientType } from '../../../core';
import Card from './Card';
import { Page } from './types';

interface CardListProps {
  pages: Page[];
  clientType: ClientType;
  filter: (pages: Page[]) => Page[];
  onSelect?: (pageId: string) => void;
  weId?:string;
}

const CardLsit: React.FC<CardListProps> = (props) => {
  const {
    pages, onSelect, clientType, filter,
  } = props;

  return (
    <div className={`${prefixCls}-page-card-content`}>
      <div>
        {filter(pages).map((page) => (
          <If
            weId={`${props.weId || ''}_0expue@${page.id}`}
            key={page.id}
            condition={!page.pages?.length}
          >
          <Then weId={`${props.weId || ''}_6vvcnd@${page.id}`}>
            <Card
              weId={`${props.weId || ''}_fjlkrl@${page.id}`}
              clientType={clientType}
              page={page}
              onSelect={onSelect}
            />
          </Then>
          <Else weId={`${props.weId || ''}_q6kqhv@${page.id}`}>
          <CorsComponent
            app="@weapp/ebdcoms"
            compName="Collapse"
            weId={`${props.weId || ''}_vo9vuc`}
            title={page.name}
          >
            {page.pages?.map((_page) => (
              <Card
                weId={`${props.weId || ''}_fjlkrl@${_page.id}`}
                key={_page.id}
                clientType={clientType}
                page={_page}
                onSelect={onSelect}
              />
            ))}
          </CorsComponent>
          </Else>
          </If>
        ))}
      </div>
    </div>
  );
};

export default CardLsit;
