import { Button, Icon, Popover } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React from 'react';
import { prefixCls } from '../../../constants';
import { DisplayType } from './types';

interface SwitchDisplayTypeProps extends React.Attributes {
  displayType: DisplayType;
  onChange: (displayType: DisplayType) => void;
}

const Switch: React.FC<SwitchDisplayTypeProps> = (props) => {
  const { displayType, onChange } = props;
  const isCard = displayType === DisplayType.Card;

  const onDisplayTypeChange = (type: DisplayType) => () => {
    onChange(type);
  };

  return (
    <span className={`${prefixCls}-page-switch-suffix`}>
      <div className={`${prefixCls}-pss-button-group`}>
        <Popover
          weId={`${props.weId || ''}_hwk6zg`}
          popup={getLabel('54411', '显示卡片切换')}
          placement="bottom"
          popoverType="tooltip"
        >
          <Button
            weId={`${props.weId || ''}_so9hwx`}
            className={`${prefixCls}-grid`}
            onClick={onDisplayTypeChange(DisplayType.Card)}
            type={isCard ? 'primary' : 'default'}
          >
            <Icon weId={`${props.weId || ''}_qzeyv4`} name="Icon-application-o" size="sm" />
          </Button>
        </Popover>
        <Popover
          weId={`${props.weId || ''}_4zu5fr`}
          popup={getLabel('54412', '显示树形切换')}
          placement="bottom"
          popoverType="tooltip"
        >
          <Button
            weId={`${props.weId || ''}_8nwg55`}
            className={`${prefixCls}-list`}
            onClick={onDisplayTypeChange(DisplayType.Tree)}
            type={isCard ? 'default' : 'primary'}
          >
            <Icon weId={`${props.weId || ''}_vhbdr3`} name="Icon-task-o" size="sm" />
          </Button>
        </Popover>
      </div>
    </span>
  );
};

export default Switch;
