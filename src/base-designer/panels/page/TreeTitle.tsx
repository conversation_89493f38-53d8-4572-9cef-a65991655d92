import { ITreeData, Popover } from '@weapp/ui';
import { classnames } from '@weapp/utils';
import React, { useCallback, useState } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import { ClientType } from '../../../core/types';
import CardExtra from './CardExtra';
import './index.less';

interface PageTreeTitleProps extends React.Attributes {
  data: ITreeData;
  clientType: ClientType;
}

const PageTreeTitle: React.FC<PageTreeTitleProps> = (props) => {
  const { data, clientType } = props;
  const { thumbnails = [] } = data || {};
  const [pcThumb, mobileThumb] = thumbnails;
  const isMobile = clientType === 'MOBILE';
  const thumbUrl = isMobile ? mobileThumb?.url : pcThumb?.url;
  const [hovering, setHovering] = useState(false);

  const onMouseEnter = useCallback(() => {
    // 文件夹不展示缩略图
    if (data && data.isLeaf === true) {
      setHovering(true);
    }
  }, [data]);

  const onMouseLeave = useCallback(() => {
    // 文件夹不展示缩略图
    if (data && data.isLeaf === true) {
      setHovering(false);
    }
  }, []);

  return (
    <div onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave}>
      <When weId="e59vgs" condition={thumbUrl}>
        <Popover
          weId="g7hmc6"
          popup={
            <div
              className={classnames({
                [`${prefixCls}-thumb-nail-pc`]: !isMobile,
                [`${prefixCls}-thumb-nail-mobile`]: isMobile,
              })}
            >
              <img src={thumbUrl} alt="" />
            </div>
          }
          className={`${prefixCls}-thumb-nail`}
          placement="right"
          popoverType="popover"
          visible={hovering}
        />
      </When>
      <span>{data.content}</span>
      <CardExtra
        weId={`${props.weId || ''}_wdjp2e`}
        pageId={data.id}
        className={`${prefixCls}-tree-btn-more`}
      />
    </div>
  );
};

export default PageTreeTitle;
