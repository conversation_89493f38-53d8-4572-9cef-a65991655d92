import { ClientType } from '../../../core/types';

export enum DisplayType {
  Card = '0',
  Tree = '1',
}

export interface ThumbnailsType {
  type: ClientType;
  url?: string;
}

export interface PageProps {
  /** 页面id */
  pageId: string;
  /** 请求页面列表url */
  url: string;
  /** 客户端类型 */
  clientType: ClientType;
  /** 选中事件回调 */
  onSelect?: (pageId: string) => void;
  /** 页面列表 * */
  datas?: Page[];
  placeholder?: string;
}
export interface IPageGroup {
  /** 分组id */
  id: string;
  /** 分组名称 */
  name?: string;
  /** 分组排序 */
  showOrder?: number;
}

export interface Page {
  /** 页面id */
  id: string;
  /** 应用id */
  appid: string;
  /** 页面名称 */
  name: string;
  /** 页面类型 */
  type: string;
  /** 是否是首页 */
  isHomePage?: string;
  /** 页面支持的客户端类型 */
  terminalScope: ClientType[];
  /** 缩略图 */
  thumbnails: ThumbnailsType[];
  /** 分组信息 */
  group: IPageGroup;
  /**
   * 老eb数据，建模表格页面
   */
  pages?: Page[];
}
