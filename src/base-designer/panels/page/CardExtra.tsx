import {
  CorsComponent, Icon, Menu, MenuItemData,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { useCallback } from 'react';
import copyTextToClipboard from '../../../common/utils/copyTextToClipboard';
import { prefixCls } from '../../../constants';

interface CardExtraProps extends React.Attributes {
  pageId: string;
  className?: string;
}

const options = [
  {
    id: 'copyID',
    content: getLabel('236804', '复制页面ID'),
    icon: <CorsComponent weId="ps3sei}" app="@weapp/ebdcoms" compName="IconFont" name="Icon-id2" />,
  },
];

const CardExtra: React.FC<CardExtraProps> = (props) => {
  const { pageId, className } = props;
  const onMenuChange = useCallback(
    (value: string, _item: MenuItemData, e: React.MouseEvent<Element, MouseEvent>) => {
      if (value === 'copyID') {
        copyTextToClipboard(pageId);
      }

      e.stopPropagation();
    },
    [pageId],
  );

  return (
    <span className={`${prefixCls}-card-btn-more ${className}`}>
      <Menu
        weId={`${props.weId || ''}_h993h0`}
        data={options}
        type="select"
        value=""
        customSelectContent={
          <span>
            <Icon weId={`${props.weId || ''}_3eyiyt`} name="Icon-more" />
          </span>
        }
        onChange={onMenuChange}
      />
    </span>
  );
};

export default CardExtra;
