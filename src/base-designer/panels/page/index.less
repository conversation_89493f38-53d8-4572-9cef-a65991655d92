@import (reference) '../../../style/prefix.less';
@import (reference) '../../../style/var.less';

.@{prefix}-panels-page {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;

  .ui-spin-nested-loading {
    height: 100%;
    width: 100%;

    & > div:nth-child(1) {
      height: 100%;
    }
  }

  .@{prefix}-pp-search {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 10px 6px 10px 10px;

    .@{prefix}-page-switch-suffix {
      display: flex;
      color: var(--de-icon-color);
      cursor: pointer;
      padding-left: 5px;

      &:hover {
        color: var(--main-fc);
      }

      .@{prefix}-pss-button-group {
        display: flex;

        button:first-child {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }

        button:last-child {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }

        button {
          width: 30px;
          height: 30px;
          text-align: center;
          padding-left: 0;
          padding-right: 0;
        }
      }
    }
  }

  .@{prefix}-pp-content-wrapper {
    height: 100%;
  }

  .@{prefix}-page-card-content {
    height: calc(100% - 50px);
    padding: 0 10px;
    overflow-y: auto;
    text-transform: capitalize;

    & > div,
    .ui-collapse-panel__content {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      & > div {
        position: relative;
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        width: 48%;

        span.page-thumbnail:first-child {
          display: flex;
          align-items: center;
          position: relative;
          justify-content: center;
          width: 100%;
          height: 80px;
          padding: 2px;
          border-radius: 3px;
          background-color: var(--de-panels-page-card-bgColor);
          cursor: pointer;

          &:hover,
          &.selected {
            border: 1px solid var(--border-color-active);
          }

          img {
            pointer-events: none;
            user-select: none;
          }

          .@{prefix}-card-pc-thumb {
            width: 70px;
            height: 50px;
            object-fit: cover;
            background-color: #f9f9f9;
            box-shadow: 0 0 10px 0 rgb(0 0 0 / 4%);
          }

          .@{prefix}-card-mobile-thumb {
            width: 40px;
            height: 60px;
            object-fit: cover;
            background-color: #f7f7f7;
            box-shadow: 0 0 10px 0 rgb(0 0 0 / 4%);
          }

          span.@{prefix}-pcc-homepage-icon svg {
            position: absolute;
            left: 2px;
            bottom: 2px;
            width: 12px;
            height: 12px;
            color: #ff666a;
          }
        }

        span.page-name {
          width: 100%;
          color: var(--main-fc);
          font-size: var(--font-size-base);
          .lines-omitted;
        }

        span.page-name:last-child {
          font-size: var(--font-size-12);
          margin: 6px 0;
          user-select: none;
          text-transform: none;
        }

        &.ui-collapse {
          width: 100%;
          .ui-collapse-panel {
            width: 100%;
          }
        }
      }
    }

    .@{prefix}-card-btn-more {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      right: 0;
      top: 0;
      width: 24px;
      height: 16px;
      cursor: pointer;

      & > .ui-icon {
        cursor: pointer;
        color: #666;
      }
    }
  }
}

.@{prefix}-thumb-nail {
  .ui-popover-arrow {
    display: none;
  }

  .ui-popover-content {
    box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.12);
  }

  &-pc {
    width: 230px;
    min-height: 170px;
  }

  &-mobile {
    width: 140px;
    min-height: 230px;
  }

  img {
    width: 100%;
    height: 100%;
  }
}

.@{prefix}-page-tree-content {
  text-transform: capitalize;

  height: calc(100% - 50px);
  overflow-y: auto;

  .ui-tree {
    width: 100%;
  }

  .ui-tree-bar {
    padding: 0 0 0 7px !important;
    cursor: pointer;

    &.isSelected,
    &:hover {
      color: #fff;

      .ui-tree-node-content {
        color: #fff;
      }

      background-color: var(--primary);
    }

    & > span {
      width: auto !important;
    }

    .ui-icon {
      .Icon-folder,
      .Icon-File-expansion {
        color: #f6bd16;
      }
    }
  }

  .ui-tree-node {
    overflow: hidden;
  }

  .ui-tree-child .ui-tree-bar {
    padding: 0px 0px 0px 22px !important;
  }

  .@{prefix}-tree-btn-more {
    position: absolute;
    right: 5px;
    top: 5px;
    cursor: pointer;

    & > .ui-icon {
      cursor: pointer;
      color: #666;
    }
  }

  .ui-tree-node-content {
    padding: 2px 0 2px 4px;
    transition: background-color 0.3s ease-in-out;

    span {
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      text-transform: none;
    }
  }

  .ui-tree-bar-switcher-warp {
    display: none;
  }

  li {
    color: var(--main-fc);
    margin-bottom: 1px;
  }

  .Icon-Collection {
    color: #ff666a;
  }
}

body[page-rtl='true'] {
  .@{prefix}-card-mobile-thumb,
  .@{prefix}-card-pc-thumb {
    transform: none;
  }

  .@{prefix}-thumb-nail-pc,
  .@{prefix}-thumb-nail-mobile,
  .@{ebPrefix}-thumb-nail-pc,
  .@{ebPrefix}-thumb-nail-mobile {
    & > img {
      transform: none;
    }
  }

  .@{prefix}-page-tree-content .ui-tree-node-content span.ui-rtl {
    text-align: right;
    padding-left: 35px;
  }
}
