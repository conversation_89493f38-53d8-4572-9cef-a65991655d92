import { Icon } from '@weapp/ui';
import { classnames } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { useCallback } from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { prefixCls } from '../../../constants';
import { ClientType } from '../../../core/types';
import CardExtra from './CardExtra';
import pageStore from './store';
import { Page } from './types';

interface CardListProps extends React.Attributes {
  page: Page;
  clientType: ClientType;
  onSelect?: (pageId: string) => void;
}

const Card: React.FC<CardListProps> = (props) => {
  const { page, onSelect, clientType } = props;
  const { selectPage: _selectPage, selectedPageId } = pageStore;
  const { isHomePage, thumbnails = [] } = page;
  const isMobile = clientType === 'MOBILE';
  const [pcThumb, mobileThumb] = thumbnails;
  const imgUrl = isMobile ? mobileThumb?.url : pcThumb?.url;
  const selectPage = useCallback(() => {
    onSelect?.(page.id);
    _selectPage(page.id);
  }, [page.id]);

  return (
    <div onClick={selectPage}>
      <span
        className={classnames('page-thumbnail', {
          selected: page.id === selectedPageId,
        })}
      >
        <If weId="29miuz" condition={imgUrl}>
          <Then weId="1mpap4">
            <img
              src={imgUrl}
              className={`${prefixCls}-card-${isMobile ? 'mobile' : 'pc'}-thumb`}
              alt=""
            />
          </Then>
          <Else weId="aui0vg">
            <div className={`${prefixCls}-card-${isMobile ? 'mobile' : 'pc'}-thumb`} />
          </Else>
        </If>

        <When weId="vcl2d4" condition={isHomePage}>
          <Icon
            weId="nthszw"
            name="Icon-Collection"
            size="md"
            className={`${prefixCls}-pcc-homepage-icon`}
          />
        </When>
      </span>
      <span className="page-name">{page.name}</span>
      <CardExtra weId={`${props.weId || ''}_wdjp2e`} pageId={props.page.id} />
    </div>
  );
};

export default observer(Card);
