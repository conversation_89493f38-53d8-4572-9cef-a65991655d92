import { PermissionMember } from '@weapp/ebdcoms/lib/common/hrm/types';
import { CorsComponent, FormDatas } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { useCallback } from 'react';
import { prefixCls } from '../../../constants';
import { useLayout } from '../../hooks';
import RightsContent from './Content';
import './index.less';
import MultiRights from './MultiRights';
import { PageRightData, RightsProps } from './types';
import { getMatrixDatas, setMatrixDatas } from './utils';

const Rights: React.FC<RightsProps> = (props) => {
  const layoutStore = useLayout();
  const { selectedCom } = layoutStore;
  const rights = selectedCom?.config?.rights && selectedCom?.config?.rights?.length
    ? selectedCom?.config?.rights
    : [];
  const disableRights = selectedCom?.config?.disableRights || [];
  const matrixRights = getMatrixDatas(selectedCom?.config?.matrixRights || []);

  const pageId = (layoutStore as any)?.parent?.pageId;

  const onChange = useCallback(
    (key: string) => (value: PermissionMember[]) => {
      const changes = { [key]: value } as FormDatas;

      layoutStore.updateComConfig(changes, layoutStore.selectedCom?.id!);
    },
    [selectedCom?.id],
  );

  const onMultiRightsChange = useCallback(
    (_data: PageRightData) => {
      const changes = {
        rights: _data?.viewRightData?.common || [],
        disableRights: _data?.disableRightData || [],
        matrixRights: setMatrixDatas(_data?.viewRightData?.matrix || []),
      };

      layoutStore.updateComConfig(changes, layoutStore.selectedCom?.id!);
    },
    [selectedCom?.id],
  );

  const clearData = useCallback(
    (key: string) => () => {
      onChange(key)([]);
    },
    [selectedCom?.id],
  );

  if (props.enableMulti && props.appId) {
    return (
      <MultiRights
        weId={`${props.weId || ''}_2yo422`}
        pageId={pageId}
        appId={props.appId}
        data={{
          viewRightData: {
            common: rights,
            matrix: matrixRights,
          },
          disableRightData: disableRights,
        }}
        onChange={onMultiRightsChange}
      />
    );
  }

  return (
    <div className={`${prefixCls}-rights-config-panel`}>
      <CorsComponent
        app="@weapp/ebdcoms"
        compName="Collapse"
        weId={`${props.weId || ''}_1lqh9l`}
        title={getLabel('116646', '使用范围')}
      >
        <RightsContent
          weId={`${props.weId || ''}_jjg4dm`}
          onChange={onChange('rights')}
          onClear={clearData('rights')}
          rights={rights}
          pageId={pageId}
        />
      </CorsComponent>

      <CorsComponent
        app="@weapp/ebdcoms"
        compName="Collapse"
        weId={`${props.weId || ''}_1lqh9l`}
        title={getLabel('54135', '使用范围中禁用')}
      >
        <RightsContent
          weId={`${props.weId || ''}_jjg4dm`}
          onChange={onChange('disableRights')}
          onClear={clearData('disableRights')}
          rights={disableRights}
          memberTypeExclude={['all', 'allexternal']}
          pageId={pageId}
        />
      </CorsComponent>
    </div>
  );
};

export default observer(Rights);
