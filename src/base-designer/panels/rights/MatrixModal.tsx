import { But<PERSON>, CorsComponent, Dialog } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import {
  ComponentType, FC, useCallback, useState,
} from 'react';
import { dlgIconName, prefixCls } from '../../../constants';
import { UUID } from '../../../utils';
import './index.less';
import { MatrixModalProps } from './types';
import { formatMatrixDatas, getListData } from './utils';

const MatrixModal: FC<MatrixModalProps> = (props) => {
  const {
    visible, data, appId = '', terminal, onClose, onOk,
  } = props;

  const [matrixFormRef, setMatrixFormRef] = useState<any>({});

  const onMatrixFormRef = useCallback(
    (ref: any) => {
      setMatrixFormRef(ref);
    },
    [matrixFormRef],
  );

  const onSubmit = useCallback(() => {
    const { handleValidate, getFormDatas, resetForm } = matrixFormRef?.store || {};

    handleValidate?.().then(() => {
      const matrixDatas: any = formatMatrixDatas(getFormDatas());
      let [_data] = getListData([matrixDatas]);

      if (data?.id) {
        _data = {
          ..._data,
          id: data?.id,
        };
      }

      onOk({
        ..._data,
        uuid: data?.uuid || UUID(),
      });
      resetForm?.();
    });
  }, [matrixFormRef, data]);

  return (
    <Dialog
      weId={`${props.weId || ''}_19scgr`}
      visible={visible}
      className={`${prefixCls}-eb-matrix-form-dlg`}
      title={data ? getLabel('116704', '编辑权限') : getLabel('56785', '添加权限')}
      icon={dlgIconName}
      onClose={onClose}
      width={600}
      closable
      mask
      destroyOnClose
      footer={[
        <Button weId={`${props.weId || ''}_xj6vs5`} onClick={onSubmit} key="sure" type="primary">
          {getLabel('40565', '确定')}
        </Button>,
        <Button weId={`${props.weId || ''}_bm5pkr`} onClick={onClose} key="cancel">
          {getLabel('53937', '取消')}
        </Button>,
      ]}
    >
      <CorsComponent
        weId={`${props.weId || ''}_qvlaka`}
        app="@weapp/ebdform"
        compName="EbMatrixForm"
        onRef={onMatrixFormRef}
        isCommonUse
        appId={appId}
        conditionParams={{ needFormField: false }}
        tableType="none"
        authBaseInfo={{
          isShowMatrixConditionSystemParams: true,
        }}
        datas={data}
        terminal={terminal}
      />
    </Dialog>
  );
};

export default MatrixModal as unknown as ComponentType<MatrixModalProps>;
