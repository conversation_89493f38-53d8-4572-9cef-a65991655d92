import { PermissionMember } from '@weapp/ebdcoms/lib/common/hrm/types';
import { AnyObj, FormStore } from '@weapp/ui';
import React from 'react';
import { RightScope } from './constants';

export interface RightsProps extends React.Attributes {
  enableMulti?: boolean;
  appId?: string;
}

export interface MultiRightsProps extends React.Attributes {
  /** 页面id */
  pageId: string;
  /** 应用id */
  appId: string;
  /** 使用列表、禁用列表数据 */
  data?: PageRightData;
  /** 更新事件 */
  onChange: (data: PageRightData) => void;
}

export interface MultiRightsContentProps extends React.Attributes {
  pageId: string;
  appId: string;
  data: PageRightData;
  rightScopeValue: RightScope;
  detailProps?: AnyObj;
  terminal?: string;
  onMount?: (formStore: FormStore) => void;
  onChange: (data: PageRightData) => void;
}

export interface MatrixModalProps extends React.Attributes {
  data?: RightListData;
  visible: boolean; // modal显隐
  appId?: string;
  terminal?: string;
  onOk: (data: RightListData) => void; // 确定事件
  onClose: () => void; // 取消事件
}

/** 权限数据格式 */
export type ViewRightDataType = {
  common?: PermissionMember[];
  matrix?: RightListData[];
  customApi?: RightListData[];
};

export type PageRightData = {
  /** 使用列表 */
  viewRightData?: ViewRightDataType;
  /** 禁用列表 */
  disableRightData?: PermissionMember[];
};

/** 添加的权限类型 */
export enum AddRightType {
  RightsCommon = 'rightsCommon', // 使用范围-通用
  RightsMatrix = 'rightsMatrix', // 使用范围-矩阵
  RightsForbid = 'rightsForbid', // 禁用范围-通用
}

/** 使用列表、禁用列表-数据类型 */
export interface RightListData {
  rightsType?: AddRightType;
  rightsTypeSpan?: string; // 类型
  rightsName?: string; // 对象
  // 接口分布式id
  id?: string;
  // 前端逻辑处理
  uuid?: string;
  [key: string]: any;
}

export default {};
