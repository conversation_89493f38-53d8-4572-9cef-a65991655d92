@import (reference) '../../../style/prefix.less';

.@{prefix}-rights-config-panel {
  .ebcoms-hrm-browser {
    padding: 0;

    & .ui-icon {
      margin-left: 0 !important;
    }
  }

  .ui-browser-associative-selected-list {
    max-height: initial !important;
    overflow: auto;
    padding-right: 0;
  }

  .ui-browser-associative-selected-btn {
    display: none;
  }

  & > a {
    margin-top: 4px;
  }

  &-matrix {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow-y: hidden;
    position: relative;

    &-group {
      width: 100%;
      height: auto;
      background-color: #f2f2f2;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-right: 19px;

      & > div:first-child {
        height: 100%;
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        padding-top: 7px;

        & > span {
          font-size: 12px;
          color: var(--regular-fc);
          line-height: 12px;
          display: inline-block;
          padding: 8px 20px;
          cursor: pointer;

          &:hover {
            color: var(--primary);
          }

          &.active {
            background-color: #fff;
            color: var(--primary);
            font-weight: bold;
          }
        }
      }

      & > span {
        display: flex;
      }

      .ui-icon {
        cursor: pointer;
        color: var(--regular-fc);
      }
    }

    &-content {
      padding: 5px 15px 0;
      overflow-y: auto;

      &-matrix-item {
        display: flex;
        align-items: center;
        justify-content: left;
        margin-bottom: 8px;

        .ui-tag {
          width: 195px;
          background-color: unset;
          text-align: start;
          cursor: pointer;
          margin-right: 4px;
        }

        &-delete {
          display: flex;
          cursor: pointer;
        }
      }
    }
  }
}

.@{prefix}-rights-config-panel-browser-wraper {
  .ui-browser-associative-dropdown {
    max-width: 110px;
    min-width: 0 !important;
  }
}
