import { getLabel } from '@weapp/utils';
import { prefixCls } from '../../../constants';

export const matrixClassName = `${prefixCls}-rights-config-panel-matrix`;

/** 权限设置范围 */
export enum RightScope {
  Rights = 'rights', // 使用范围
  RightsForbid = 'rightsForbid', // 禁用范围
}

/** 使用范围-权限类型 */
export enum RightsType {
  Common = 'Common', // 通用
  Matrix = 'Matrix', // 矩阵相关
}

/** 添加的权限类型 */
export enum AddRightType {
  RightsCommon = 'rightsCommon', // 使用范围-通用
  RightsMatrix = 'rightsMatrix', // 使用范围-矩阵
  RightsForbid = 'rightsForbid', // 禁用范围-通用
}

export const rightScopeMenuData = () => [
  { id: RightScope.Rights, content: getLabel('116646', '使用范围') },
  { id: RightScope.RightsForbid, content: getLabel('54135', '使用范围中禁用') },
];

export default {};
