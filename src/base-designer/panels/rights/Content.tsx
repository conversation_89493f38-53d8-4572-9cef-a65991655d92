import { PermissionMember } from '@weapp/ebdcoms/lib/common/hrm/types';
import { Button, CorsComponent } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { PureComponent } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import './index.less';
import { getRightsList } from './utils';

interface RightsProps {
  rights: PermissionMember[];
  onClear: () => void;
  onChange: (value: PermissionMember[]) => void;
  memberTypeExclude?: string[];
  pageId?: string;
}

class RightsContent extends PureComponent<RightsProps> {
  render() {
    const {
      rights, onClear, onChange, memberTypeExclude, pageId,
    } = this.props;

    const _rights = getRightsList(rights);

    return (
      <div className={`${prefixCls}-rights-config-panel`}>
        <CorsComponent
          app="@weapp/ebdcoms"
          compName="HrmBrowser"
          weId={`${this.props.weId || ''}_hd40vr`}
          data={_rights || []}
          onChange={onChange}
          editable
          hasExternal
          useShareBrowser
          memberTypeExclude={memberTypeExclude}
          browserAssociativeProps={{
            triggerProps: { popupClassName: `${prefixCls}-rights-config-panel-browser-wraper` },
          }}
          detailProps={{
            useCustomDetail: true,
            permissionType: 1,
            pageId,
            tableName: 'ebdd_layout_c',
            permissionId: 'ebdcomm_view',
            comServicePath: 'ebuilder/designer',
            type: 'PAGE',
            objId: pageId,
          }}
        />
        <When weId={`${this.props.weId || ''}_zeros8`} condition={rights && rights.length > 1}>
          <Button weId={`${this.props.weId || ''}_1j3n76`} type="link" onClick={onClear}>
            {getLabel('117716', '清空所有')}
          </Button>
        </When>
      </div>
    );
  }
}

export default RightsContent;
