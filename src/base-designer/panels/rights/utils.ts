import { PermissionMember } from '@weapp/ebdcoms/lib/common/hrm/types';
import { AnyObj } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { AddRightType } from './constants';
import { RightListData } from './types';

/**
 * 统一数组内数据格式，targetType字段和_entityType字段
 * - 人员浏览框通过 _entityType 字段过滤已选中的数据
 * - 回传给后端需要 targetType 字段，不传会引起接口报错
 * @param rights 组件权限数组
 */
export const getRightsList = (rights: PermissionMember[]) => rights?.map((rightInfo) => {
  const { _entityType, targetType, ...restInfo } = rightInfo;

  let entityType = _entityType;
  /**
     * 保存接口有 targetType，没有 _entityType
     * 后端反馈可以取 targetType
     */
  if (_entityType === undefined && targetType && typeof targetType === 'string') {
    entityType = targetType.toLowerCase();
  }

  return {
    ...restInfo,
    targetType,
    _entityType: entityType,
  };
});

export const relationArr = () => [
  { id: 'SELF', content: getLabel('55724', '本人') },
  { id: 'IMMEDIATE_SUPERIOR', content: getLabel('55726', '直接上级') },
  { id: 'IMMEDIATE_SUBORDINATE', content: getLabel('63655', '直接下属') },
];

export const depRelationArr = () => [
  { id: 'DEPARTMENT', content: getLabel('63657', '本部门') },
  { id: 'DEPARTMENT_ALLSUBORDINTE', content: getLabel('100917', '本部门及所有下级部门') },
  { id: 'ALLSUBORDINTEDEPARTMENT', content: getLabel('100919', '所有下级部门') },
];

// 部门字段已配置上级部门相关的依然展示，新配置都不需要上级部门相关
export const getDepSuperRelationArr = () => [
  { id: 'DEPARTMENT_ALLSUPER', content: getLabel('100918', '本部门及所有上级部门') },
  { id: 'ALLSUPERDEPARTMENT', content: getLabel('100921', '所有上级部门') },
];

export const employeeShareArr = () => [
  { id: 'HRM_FIELD', content: getLabel('259732', '人力字段') },
];

export const employeeScopeArr = () => [
  { id: 'HRM_RANGE_SELECT', content: getLabel('152596', '人员范围选择') },
];

export const employeeOrganizationArr = () => [
  { id: 'HRM_ORG_MUTI', content: getLabel('152597', '人员组织多选') },
];

export const getAimContent = (data: any) => {
  const matrixValueRelation = typeof data.matrixValueRelation === 'string'
    ? JSON.parse(data.matrixValueRelation || '[]')
    : data.matrixValueRelation || [];

  const valFieldStr = matrixValueRelation
    .map(
      ({ valueName, type }: any) => `${valueName}:${
        [
          ...relationArr(),
          ...[...depRelationArr(), ...getDepSuperRelationArr()],
          ...employeeShareArr(),
          ...employeeOrganizationArr(),
          ...employeeScopeArr(),
        ].find((r) => r.id === type)?.content || ''
      }`,
    )
    .join(';');
  const str = `${data.name}(${valFieldStr})`;

  return str;
};

export const getListData = (rightDatas?: any) => {
  if (isEmpty(rightDatas)) return [];

  const listData: RightListData[] = [];

  if (Array.isArray(rightDatas) && rightDatas.length) {
    rightDatas.forEach((item) => {
      const [dataid] = item?.dataid || [];

      listData.push({
        ...item,
        rightsType: AddRightType.RightsMatrix,
        rightsTypeSpan: getLabel('156702', '矩阵相关'), // 类型
        rightsName: getAimContent({
          ...item,
          name: dataid.name,
        }), // 对象
      });
    });
  }

  return listData;
};

// 权限表单组件兼容：数据结构转换成通用结构
export const formatMatrixDatas = (formDatas: AnyObj) => {
  const {
    orgType, orgId, matrixValueRelation = [], matrixConditionData,
  } = formDatas;
  const _relation = matrixValueRelation.filter((r: AnyObj) => !!r.valueId);
  const data = {
    selectedType: orgType,
    dataid: orgId,
    matrixValueRelation: JSON.stringify(_relation),
    matrixConditionData: matrixConditionData ? JSON.stringify(matrixConditionData) : '',
  };

  return data;
};

/** 获取矩阵相关数据 - 补充uuid字段 */
export const getMatrixDatas = (matrixDatas: RightListData[]) => matrixDatas.map((item) => ({
  ...item,
  uuid: item.id || item.uuid,
}));

/** 保存矩阵相关数据 - 去除id字段 */
export const setMatrixDatas = (matrixDatas: RightListData[]) => {
  const _matrixDatas: RightListData[] = JSON.parse(JSON.stringify(matrixDatas));

  return _matrixDatas.map((item) => {
    delete item.id;

    return item;
  });
};

export default {};
