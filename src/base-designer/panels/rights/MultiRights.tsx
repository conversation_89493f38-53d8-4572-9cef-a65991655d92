import { classnames } from '@weapp/utils';
import React, {
  ComponentType, FC, useCallback, useState,
} from 'react';
import { matrixClassName, RightScope, rightScopeMenuData } from './constants';
import './index.less';
import MultiRightsContent from './MultiRightsContent';
import { MultiRightsProps } from './types';

const MultiRights: FC<MultiRightsProps> = (props) => {
  const {
    pageId = '', appId = '', data = {}, onChange,
  } = props;

  const [rightScopeValue, setRightScopeValue] = useState(RightScope.Rights);

  const onRightScopeChange = useCallback(
    (value: string) => () => {
      setRightScopeValue(value as RightScope);
    },
    [],
  );

  return (
    <div className={matrixClassName}>
      <div className={`${matrixClassName}-group`}>
        <div>
          {rightScopeMenuData().map((el) => (
            <span
              className={classnames({ active: el.id === rightScopeValue })}
              key={el.id}
              onClick={onRightScopeChange(el.id)}
            >
              {el.content}
            </span>
          ))}
        </div>
      </div>
      <MultiRightsContent
        weId={`${props.weId || ''}_hrlvon`}
        data={data}
        rightScopeValue={rightScopeValue}
        pageId={pageId}
        appId={appId}
        onChange={onChange}
      />
    </div>
  );
};

export default MultiRights as unknown as ComponentType<MultiRightsProps>;
