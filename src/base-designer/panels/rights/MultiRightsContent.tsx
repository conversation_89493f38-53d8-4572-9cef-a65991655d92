import { PermissionMember } from '@weapp/ebdcoms/lib/common/hrm/types';
import { CorsComponent, Tag } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import {
  ComponentType, FC, useCallback, useState,
} from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { matrixClassName, RightScope } from './constants';
import RightsContent from './Content';
import './index.less';
import MatrixModal from './MatrixModal';
import { MultiRightsContentProps, RightListData } from './types';

const MultiRightsContent: FC<MultiRightsContentProps> = (props) => {
  const {
    data, rightScopeValue, pageId, appId, terminal, onChange,
  } = props;

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailData, setDetailData] = useState<RightListData | undefined>(undefined);

  const { viewRightData = {}, disableRightData = [] } = data;
  const { matrix = [], common = [] } = viewRightData;

  /** 是否为使用范围 */
  const isRights = rightScopeValue === RightScope.Rights;
  /** 是否为禁用范围 */
  const isRightsForbid = rightScopeValue === RightScope.RightsForbid;

  const onPageRightChange = (_data: RightListData[], key: string) => {
    let _pageRightData = JSON.parse(JSON.stringify(data || {}));

    switch (key) {
      case RightScope.Rights:
        _pageRightData = {
          ..._pageRightData,
          viewRightData: {
            ..._pageRightData.viewRightData,
            common: toJS(_data),
          },
        };
        break;

      case RightScope.RightsForbid:
        _pageRightData = {
          ..._pageRightData,
          disableRightData: toJS(_data),
        };
        break;

      case 'matrix':
        _pageRightData = {
          ..._pageRightData,
          viewRightData: {
            ..._pageRightData.viewRightData,
            matrix: _data,
          },
        };
        break;

      default:
        break;
    }

    onChange?.(_pageRightData);
  };

  const onCommonChange = (key: string) => (value: PermissionMember[]) => {
    onPageRightChange(value, key);
  };

  const clearData = (key: string) => () => {
    onPageRightChange([], key);
  };

  /** 删除 */
  const onDelete = (listItem?: RightListData) => () => {
    let _data = [];

    if (listItem) {
      _data = matrix.filter((item: RightListData) => item.uuid !== listItem.uuid);
    } else {
      _data = matrix.filter((item: RightListData) => !selectedRowKeys.includes(item.uuid || ''));
      setSelectedRowKeys([]);
    }

    onPageRightChange(_data, 'matrix');
  };

  /** 添加矩阵 */
  const addMatrixRight = useCallback(() => {
    setDetailData(undefined);
    setDetailVisible(true);
  }, []);

  /** 编辑 */
  const editRightInfo: any = useCallback(
    (_data?: RightListData) => (listItem: RightListData) => {
      setDetailData(_data || listItem);
      setDetailVisible(true);
    },
    [],
  );

  const onMatrixModalClose = useCallback(() => {
    setDetailVisible(false);
  }, []);

  const onMatrixModalOk = useCallback(
    (_data: RightListData) => {
      const matrixData = JSON.parse(JSON.stringify(matrix));
      const dataIndex = matrixData.findIndex((item: RightListData) => item.uuid === _data.uuid);

      if (dataIndex > -1) {
        matrixData.splice(dataIndex, 1, _data);

        onPageRightChange(matrixData, 'matrix');
      } else {
        matrixData.push(_data);

        onPageRightChange(matrixData, 'matrix');
      }

      onMatrixModalClose();
    },
    [data],
  );

  return (
    <div
      className={classnames(`${matrixClassName}-content`, {
        hide: isRightsForbid,
      })}
    >
      <If weId={`${props.weId || ''}_5ggnrz`} condition={rightScopeValue === RightScope.Rights}>
        {/* 使用范围 - 通用 */}
        <Then weId={`${props.weId || ''}_zyregb`}>
          <CorsComponent
            app="@weapp/ebdcoms"
            compName="Collapse"
            weId={`${props.weId || ''}_1lqh9l`}
            title={getLabel('56756', '通用')}
          >
            <RightsContent
              weId={`${props.weId || ''}_jjg4dm`}
              onChange={onCommonChange(RightScope.Rights)}
              onClear={clearData(RightScope.Rights)}
              rights={common}
              pageId={pageId}
            />
          </CorsComponent>
        </Then>
        {/* 使用范围中禁用 */}
        <Else weId={`${props.weId || ''}_o72fr7`}>
          <RightsContent
            weId={`${props.weId || ''}_jjg4dm`}
            onChange={onCommonChange(RightScope.RightsForbid)}
            onClear={clearData(RightScope.RightsForbid)}
            rights={disableRightData}
            memberTypeExclude={['all', 'allexternal']}
            pageId={pageId}
          />
        </Else>
      </If>
      {/* 矩阵相关 */}
      <When weId={`${props.weId || ''}_d0rjlr`} condition={isRights}>
        <CorsComponent
          app="@weapp/ebdcoms"
          compName="Collapse"
          weId={`${props.weId || ''}_1lqh9l`}
          title={getLabel('156702', '矩阵相关')}
        >
          {matrix.map((item) => (
            <div key={item.id || item.uuid} className={`${matrixClassName}-content-matrix-item`}>
              <Tag
                weId={`${props.weId || ''}_uy2w3j`}
                type="primary"
                title={item.rightsName}
                onCheck={editRightInfo(item)}
              >
                {getLabel('212394', '矩阵')}：{item.rightsName}
              </Tag>
              <CorsComponent
                weId={`${props.weId || ''}_oap0if`}
                app="@weapp/ebdcoms"
                compName="IconFont"
                wrapClassName={`${matrixClassName}-content-matrix-item-delete`}
                title={getLabel('53951', '删除')}
                name="Icon-delete04"
                onClick={onDelete(item)}
              />
            </div>
          ))}
          <CorsComponent
            weId={`${props.weId || ''}_zd9bat`}
            app="@weapp/ebdcoms"
            compName="ButtonEx"
            inline={false}
            action="add"
            style={{ marginTop: 10 }}
            onClick={addMatrixRight}
          >
            {getLabel('310377', '添加矩阵')}
          </CorsComponent>
        </CorsComponent>
        <When weId={`${props.weId || ''}_3rtoto`} condition={detailVisible}>
          <MatrixModal
            weId={`${props.weId || ''}_rwqxo2`}
            visible={detailVisible}
            data={detailData}
            appId={appId}
            terminal={terminal}
            onOk={onMatrixModalOk}
            onClose={onMatrixModalClose}
          />
        </When>
      </When>
    </div>
  );
};

export default MultiRightsContent as unknown as ComponentType<MultiRightsContentProps>;
