import { Icon, IconNames, ITreeData } from '@weapp/ui';
import { inject, observer } from 'mobx-react';
import React, {
  ComponentType, useCallback, useEffect, useState,
} from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import getDesignInfo from '../../../core/utils/getDesignInfo';
import BaseDesignerStore from '../../store';
import IconDelete from './icon/Delete';
import IconHide from './icon/Hide';

interface ElementTreeNodeProps extends React.Attributes {
  node: ITreeData;
  onHide: (visible: boolean, comId: string) => void;
  onDelete: (comId: string) => void;
  onNodeHover: (nodeId: string, hover: boolean) => void;
}

type InternalElementTreeNodeProps = ElementTreeNodeProps & {
  designStore: BaseDesignerStore;
};

interface TreeNodeIconProps extends React.Attributes {
  icon?: string;
}

const regImg = /.+\.(jpg|jpeg|gif|bmp|png)$/;

const TreeNodeIcon: React.FC<TreeNodeIconProps> = (props) => {
  const { icon } = props;
  const isImg = regImg.test(icon as string);

  if (isImg) {
    return (
      <span>
        <img src={icon} alt="" />
      </span>
    );
  }

  return (
    <span>
      <When weId={`${props.weId || ''}_pg7su1`} condition={!!icon}>
        <Icon weId={`${props.weId || ''}_94u8jc`} name={icon as IconNames} />
      </When>
    </span>
  );
};

const ElementTreeItem: React.FC<InternalElementTreeNodeProps> = (props) => {
  const elementItemRef: React.RefObject<any> = React.useRef();

  const {
    node, onHide, onNodeHover, onDelete, designStore,
  } = props;
  const {
    hidden, content, iconpath, nodeId, canHide, id, canDelete,
  } = node;

  const [canShowOrHide, setCanShowOrHide] = useState<boolean>(false);

  const onMouseEnter = useCallback(() => {
    onNodeHover(nodeId, true);
  }, [nodeId]);

  const onMouseleave = useCallback(() => {
    onNodeHover(nodeId, false);
  }, [nodeId]);

  useEffect(() => {
    onNodeHover(nodeId, !hidden);
  }, [hidden]);

  useEffect(() => {
    if (!canHide) {
      const parentCom = designStore.layoutStore.getParentCom(id);
      if (parentCom) {
        getDesignInfo(parentCom, designStore.clientType).then((res: any) => {
          if (res?.defaultOpts?.children?.canShowOrHide === true) {
            setCanShowOrHide(true);
          }
        });
      }
    }
  }, [canHide, id]);

  useEffect(() => {
    const elementDom = elementItemRef.current;

    if (elementDom) {
      elementDom.addEventListener('mouseenter', onMouseEnter);
      elementDom.addEventListener('mouseleave', onMouseleave);
    }

    return () => {
      elementDom.removeEventListener('mouseenter', onMouseEnter);
      elementDom.removeEventListener('mouseleave', onMouseleave);
    };
  }, [elementItemRef.current, onNodeHover]);

  return (
    <div className={`${prefixCls}-tree-node`} ref={elementItemRef}>
      <TreeNodeIcon weId="f1jvmf" icon={iconpath} />
      <div>{content}</div>
      <When weId={`${props.weId || ''}_cjv4j9`} condition={canHide || canShowOrHide}>
        <IconHide
          weId={`${props.weId || ''}_8428ym`}
          onHide={onHide}
          nodeId={nodeId}
          hidden={hidden}
        />
        <When weId={`${props.weId || ''}_cjv4j9`} condition={canDelete !== false}>
          <IconDelete weId={`${props.weId || ''}_hkyccb`} nodeId={nodeId} onDelete={onDelete} />
        </When>
      </When>
    </div>
  );
};

export default inject('designStore')(
  observer(ElementTreeItem),
) as unknown as ComponentType<ElementTreeNodeProps>;
