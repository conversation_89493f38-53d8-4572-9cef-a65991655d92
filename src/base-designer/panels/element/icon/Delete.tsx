import { CorsComponent } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { PureComponent, SyntheticEvent } from 'react';

interface IconDeleteProps {
  nodeId: string;
  onDelete: (comId: string) => void;
}

class IconDelete extends PureComponent<IconDeleteProps> {
  deleteElement = (e: SyntheticEvent) => {
    const { onDelete, nodeId } = this.props;
    e.stopPropagation();

    onDelete(nodeId);
  };

  render() {
    return (
      <span onClick={this.deleteElement}>
        <CorsComponent
          weId={`${this.props.weId || ''}_kdx2qf`}
          app="@weapp/ebdcoms"
          compName="IconFont"
          name="Icon-delete02"
          title={getLabel('53951', '删除')}
        />
      </span>
    );
  }
}

export default IconDelete;
