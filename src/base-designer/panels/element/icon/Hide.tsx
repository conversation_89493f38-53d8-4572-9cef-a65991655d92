import { CorsComponent } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, {
  SyntheticEvent, useCallback, useEffect, useState,
} from 'react';
import { Else, If, Then } from 'react-if';

interface IconHideProps extends React.Attributes {
  nodeId: string;
  hidden: boolean;
  onHide: (visible: boolean, comId: string) => void;
}

const IconHide: React.FC<IconHideProps> = (props) => {
  const { onHide, nodeId } = props;
  const [hidden, setHide] = useState<boolean>(props.hidden);

  useEffect(() => {
    if (props.hidden !== hidden) {
      setHide(props.hidden);
    }
  }, [props.hidden]);

  const setElementVisible = useCallback(
    (e: SyntheticEvent) => {
      e.stopPropagation();

      setHide(!hidden);
      onHide(hidden, nodeId);
    },
    [nodeId, hidden],
  );

  return (
    <span onClick={setElementVisible}>
      <If weId={`${props.weId || ''}_sqnfdt`} condition={!hidden}>
        <Then weId={`${props.weId || ''}_alpwbr`}>
          <CorsComponent
            weId={`${props.weId || ''}_bs4vxq`}
            app="@weapp/ebdcoms"
            compName="IconFont"
            type="eye"
            title={getLabel('54034', '隐藏')}
          />
        </Then>
        <Else weId={`${props.weId || ''}_gd98tg`}>
          <CorsComponent
            weId={`${props.weId || ''}_bs4vxq`}
            app="@weapp/ebdcoms"
            compName="IconFont"
            style={{ display: 'inline' }}
            title={getLabel('54039', '显示')}
            type="eye-hide"
          />
        </Else>
      </If>
    </span>
  );
};

export default IconHide;
