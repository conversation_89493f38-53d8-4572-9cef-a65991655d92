@import (reference) '../../../style/prefix.less';
@import (reference) '../../../style/var.less';

.@{prefix}-panels-element {
  display: flex;
  flex-direction: column;
  height: 100%;

  .ui-input-wrap {
    width: calc(100% - 20px);
    margin: 10px 10px 6px 10px;
  }

  .ui-spin-nested-loading {
    height: 100%;
    width: 100%;

    & > div:nth-child(1) {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      overflow: auto;
    }
  }
}

.@{prefix}-tree-content-wrapper {
  min-height: 0;
  text-transform: capitalize;

  .@{prefix}-pecw-page {
    height: 28px;
    padding-left: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: var(--font-size-sm);

    & > span:last-child {
      margin-left: 4px;
    }
  }

  .ui-tree-node-content {
    padding-right: 0;
  }

  .page-selected,
  .ui-tree-bar.isSelected,
  .ui-tree-bar.isSelected:hover {
    background-color: #edf4ff;
  }

  .ui-tree-bar > span {
    margin-left: 13px;
  }

  .ui-tree-child .ui-tree-bar > span {
    margin-left: 12px;
  }

  .ui-tree-node {
    .icon-eye,
    .Icon-delete02 {
      visibility: hidden;

      &:hover {
        color: var(--de-base-fc);
      }
    }
    &:hover {
      .icon-eye,
      .Icon-delete02 {
        visibility: visible;
      }
    }
  }

  .@{prefix}-tree-node {
    position: relative;
    width: 100%;
    display: flex;
    z-index: @tree-node-zIndex;
    align-items: center;

    & > div {
      flex: 1 1;
      max-width: 80px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    & > span:first-child {
      display: flex;
      width: 14px;
      height: 12px;
      border-radius: 1px;
      background-color: var(--de-body-bgColor);
      justify-items: center;
      align-items: center;
      margin-right: 5px;

      img {
        max-width: 95%;
        max-height: 95%;
        min-height: 30%;
      }
    }

    & > span:not(:first-child) {
      color: #666;
      padding: 0 4px;
    }
  }
}

.@{prefix}-element-highlight {
  border: 1px dashed var(--primary);
  background-color: rgba(93, 156, 236, 0.1); // var(--primary)的0.1透明度色
}
