import {
  CorsComponent, Icon, Input, ITreeData, Tree,
} from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { PureComponent, ReactNode } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import { ROOT_NODE_ID } from '../../../constants/flow';
import { useLayout } from '../../hooks';
import ElementTreeItem from './Element';
import './index.less';
import {
  ElementNode, ElementPanelStates, ElementTreeProps, TreeContentProps,
} from './types';

const TreeContent: React.FC<TreeContentProps> = (props) => {
  const {
    treeData,
    renderElementNode,
    onSelect,
    searchValue,
    isCustomEmpty = false,
    emptyText,
  } = props;
  const { selectedCom } = useLayout()!;
  const comId = selectedCom?.id || '';

  if ((!treeData.length && searchValue) || isCustomEmpty) {
    return (
      <CorsComponent
        weId="v1eqhx"
        app="@weapp/ebdcoms"
        compName="Empty"
        type="search"
        description={emptyText}
      />
    );
  }

  return (
    <Tree
      defaultExpandedAutoByChecked
      weId="dqvdju"
      data={treeData}
      defaultExpandedLeavel={4}
      onSelect={onSelect}
      selectedKeys={[comId]}
      customRenderNode={renderElementNode}
    />
  );
};

class ElementTree extends PureComponent<ElementTreeProps, ElementPanelStates> {
  constructor(props: ElementTreeProps) {
    super(props);

    this.state = {
      searchValue: '',
      isRoot: false,
    };
  }

  renderElementNode = (node: ITreeData): ReactNode => (
    <ElementTreeItem
      weId={`${this.props.weId || ''}_ouvn95`}
      node={node}
      onHide={this.props.onHide}
      onDelete={this.props.onDelete}
      onNodeHover={this.props.onNodeHover}
    />
  );

  getTreeData = (treeData: ElementNode[]): ElementNode[] => treeData.filter((item: ElementNode) => {
    if (item.content.indexOf(this.state.searchValue) > -1 || item.id === this.state.searchValue) {
      return true;
    }

    if (item?.children.length) {
      return !!this.getTreeData(item?.children).length;
    }

    return false;
  });

  selectNode = (node: ITreeData) => {
    const { onSelect } = this.props;

    this.setState({ isRoot: false });
    onSelect(node.nodeId);
  };

  selectPage = () => {
    const { onSelect } = this.props;

    this.setState((prev) => ({
      isRoot: !prev.isRoot,
    }));
    onSelect(ROOT_NODE_ID);
  };

  onChange = (value: any) => {
    this.setState({ searchValue: value });
  };

  render() {
    const { searchValue, isRoot } = this.state;
    const { isCustomEmpty = false, emptyText } = this.props;
    const pageclassnames = classnames(`${prefixCls}-pecw-page`, {
      'page-selected': isRoot,
    });
    const treeData = this.getTreeData(this.props.treeData);
    const suffix = (
      <span className={`${prefixCls}-slide-speed-suffix`}>
        <Icon
          className="ui-searchAdvanced-input-icon"
          weId={`${this.props.weId || ''}_7i01zy`}
          name="Icon-search"
        />
      </span>
    );

    return (
      <div className={`${prefixCls}-panels-element`}>
        <div className={`${prefixCls}-tree-header`}>
          <Input
            weId={`${this.props.weId || ''}_xkdok5`}
            allowClear
            value={searchValue}
            placeholder={getLabel('54515', '请输入组件名称')}
            onChange={this.onChange}
            suffix={suffix}
          />
        </div>
        <div className={`${prefixCls}-tree-content-wrapper`}>
          <When weId="5ibf6u" condition={treeData.length || !searchValue}>
            <div className={`${pageclassnames}`} onClick={this.selectPage}>
              {this.props.rootNode}
            </div>
          </When>
          <TreeContent
            weId={`${this.props.weId || ''}_owhupj`}
            treeData={treeData}
            searchValue={searchValue}
            onSelect={this.selectNode}
            renderElementNode={this.renderElementNode}
            emptyText={emptyText}
            isCustomEmpty={isCustomEmpty}
          />
        </div>
      </div>
    );
  }
}
export default observer(ElementTree);
