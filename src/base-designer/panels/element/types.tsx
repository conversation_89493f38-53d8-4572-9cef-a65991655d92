import { ITreeData } from '@weapp/ui';
import { ReactNode } from 'react';

export interface ElementNode extends ITreeData {
  /** 子节点数据 */
  children: ElementNode[];
  /** 组件结点的id */
  id: string;
  /** 组件结点的nodeId */
  nodeId: string;
  /** 树节点是否可以隐藏 */
  canHide: boolean;
  /** 是否隐藏结点 */
  hidden: boolean;
  /** 元素树图标名称，支持path和iconNames */
  iconpath: string;
  /** 元素树中节点名称 */
  content: string;
}

export interface ElementTreeProps {
  /** 根节点 */
  rootNode: ReactNode;
  /** 元素树数据 */
  treeData: ElementNode[];
  /** 需要自定义展示空状态 */
  isCustomEmpty?: boolean;
  /** 空状态文案 */
  emptyText?: string;
  /** 隐藏事件 */
  onHide: (visible: boolean, comId: string) => void;
  /** 删除事件 */
  onDelete: (comId: string) => void;
  /** 鼠标点击事件 */
  onSelect: (node: string) => void;
  /** 鼠标移入移出事件 */
  onNodeHover: (nodeId: string, hover: boolean) => void;
}

export interface ElementPanelStates {
  searchValue: string;
  isRoot: boolean;
}

export interface TreeContentProps {
  treeData: ElementNode[];
  searchValue: string;
  onSelect: (node: ITreeData) => void;
  renderElementNode: any;
  /** 需要自定义的空状态展示 */
  isCustomEmpty?: boolean;
  /** 空状态文案 */
  emptyText?: string;
}
