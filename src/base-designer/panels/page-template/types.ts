import React, { <PERSON>actNode, SyntheticEvent } from 'react';
import { LayoutType, PageModuleType } from '../../../core/types';
import { ClientType } from '../../../types';
import { RenderDragComponent } from '../../types';

type TemplatePanelStore = {
  reload: () => void;
};

export interface PageTempPanelProps extends React.Attributes {
  /** 独立使用时，需要以下属性 */
  /** 所属模块 */
  moduleScope?: PageModuleType;
  /** 终端类型 */
  clientType?: ClientType;
  /** 设计器布局类型 */
  layoutType?: LayoutType;
  /** 无权使用 */
  useDisabled?: boolean;
  /** 组件didmount的回调 */
  onDidMount?: (store: TemplatePanelStore) => void;
  /** 页面模板使用回调 */
  onUsed?: (templateId: string) => void;
}

export interface PageTempDataType {
  id: string;
  isSys: number;
  name: string;
  showOrder: number;
  templates: IPageTemplateData[];
}

export type RenderTitle = (currCategory: IPageCategory, comItem: ICategoryViewData) => string;

export interface PagePanelProps extends React.Attributes {
  /** 组件面板唯一标志，用于区分多面板情况下的store */
  id?: string;
  className?: string;
  clientType?: ClientType;
  datas?: IPageCategory[] | null;
  placeholder?: string;
  /**  无权使用 */
  useDisabled?: boolean;
  getUniqueKeyData?: (data: any) => any;
  fetchData?: () => Promise<IPageCategory[]>;
  filter?: (datas: ICategoryViewData[]) => ICategoryViewData[];
  renderTitle?: RenderTitle;
}

export interface CardProps extends React.Attributes {
  data: IPageDescription;
  draggable?: boolean;
  className?: string;
  /** 无权使用 */
  useDisabled?: boolean;
  renderIcon?: (icon: ReactNode) => ReactNode;
  renderCategory?: (data: ICategoryViewData, node: ReactNode) => ReactNode;
  renderName?: (name: ReactNode) => ReactNode;
  renderComponent?: RenderDragComponent;
  renderCompExtra?: (data: IPageDescription) => ReactNode;
  onDragStart?: (e: SyntheticEvent, data: IPageDescription) => void;
  onUsed?: (templateId: string) => void;
}

/**
 * 页面列表展示的数据类型
 */
export type ICategoryViewData = {
  id: string;
  /** 分类标题，当title为空时，不渲染标题 */
  title: ReactNode;
  /** 页面分类id，当页面无分类时不传category */
  category: string;
  /** 页面数据 */
  items?: IPageDescription[];
  /** 子页面数据 */
  childs?: ICategoryViewData[];
};

export interface PageListProps extends Omit<CardProps, 'data'> {
  datas: ICategoryViewData[];
}

export interface ICategory extends Omit<IPageCategory, 'childs'> {
  pid?: string;
  /** 组件数据 */
  comps?: IPageDescription[];
  /** 子组件数据 */
  childs?: IPageCategoryChild[];
}

/** 后端返回的页面模板列表数据类型 */
export interface IPageCategory {
  /** 分类id */
  id: string;
  /** 分类名称 */
  name: string;
  /** 排序字段 */
  showOrder: number;
  /** 二级分类组件 */
  childs: IPageCategoryChild[];
}

/** 页面的子分类数据类型 */
export interface IPageCategoryChild {
  id: string;
  name: string;
  pid: string;
  showOrder: number;
  /** 分类下的所有组件列表 */
  comps: IPageDescription[];
}

/**
 * 页面模板数据
 */
export interface IPageTemplateData {
  /** 所属模块 */
  belongModule: string;
  /** 模板分类 */
  category: string;
  /** 移动端页面模板配置信息 */
  configMobile: string;
  /** pc端页面模板配置信息 */
  configPc: string;
  /** 创建时间 */
  createTime: number;
  /** 创建人 */
  creator: string;
  /** 模板图标路径 */
  cover: string;
  /** 模板id */
  id: string;
  /** 模板名称 */
  name: string;
  /** 页面模板说明 */
  remark: string;
  /** 显示顺序 */
  showOrder: number;
  /** 页面模板布局类型 */
  layoutType: LayoutType;
  /** 租户key */
  tenantKey: string;
  /** 更新时间 */
  updateTime: number;
}

/**
 * 页面模板基本信息
 */
export interface IPageDescription {
  id: string;
  /** 页面模板名称 */
  name: string;
  /** 页面模板分类id */
  category: string;
  /** 页面模板类型 */
  type: string;
  /** 页面模板图片路径 */
  icon: string;
  /** 页面模板适用端 */
  terminalScope?: ClientType[];
  /** 图标文件 */
  cover: string;
  /** 页面模板布局类型 */
  layoutType: LayoutType;
  /** 页面模板说明 */
  remark: string;
}
