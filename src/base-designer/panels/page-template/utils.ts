import { cloneDeep } from '@weapp/utils';
import { Page } from '../../../core';
import { ClientType } from '../../../core/types';
import ebdcoms from '../../../utils/ebdcoms';
import json from '../../../utils/json';
import {
  ICategory,
  ICategoryViewData,
  IPageCategory,
  IPageDescription,
  IPageTemplateData,
  RenderTitle,
} from './types';

export const isValidTempConfig = (configStr: string) => {
  if (configStr === '1') {
    return true;
  }

  return false;
};

export function TemplateToComp(tplData: IPageTemplateData): IPageDescription {
  // 终端作用域
  const terminalScope: ClientType[] = [];
  const { configPc: configPCStr, configMobile: configMobileStr, cover: icon } = tplData;
  const iconPath = icon || '';
  // 模板图标设置了样式后，icon为json string，用于在组件AssetItem上显示
  const tplIcon = json.parse(iconPath) || ebdcoms.excu('getImgUrl', iconPath);

  if (isValidTempConfig(configPCStr)) {
    terminalScope.push('PC');
  } else {
    tplData.configPc = '';
  }

  if (isValidTempConfig(configMobileStr)) {
    terminalScope.push('MOBILE');
  } else {
    tplData.configMobile = '';
  }

  return {
    ...tplData,
    terminalScope,
    icon: tplIcon,
    type: 'Compose',
  };
}

export const fetchCategories = (page: Page): Promise<IPageCategory[]> => {
  const { id, module } = page;

  return new Promise((resolve) => {
    ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/component/list',
      params: {
        pageId: id,
        module,
      },
      success: resolve,
      error: () => {
        resolve([]);
        return true;
      },
    });
  });
};

export const getCategoryViewDatas = (
  datas: ICategory[],
  client: ClientType,
  singleClient: boolean,
): ICategoryViewData[] => {
  const categories: ICategory[] = []; // 组件分类列表

  datas.forEach((primaryCategory) => {
    // 遍历一级分类
    const { name: primaryName, childs: secondaryCategories = [] } = primaryCategory;

    secondaryCategories.forEach((secondaryCategory) => {
      // 遍历二级分类
      const { name: secondaryName, comps: secondaryComps = [] } = secondaryCategory;
      const secFilterComps = secondaryComps.filter((comp: any) => {
        const { terminalScope } = comp;
        const isSingleClientPageTemp = terminalScope.length === 1;

        // 单个终端页面模板不允许导入双终端页面
        return (
          (!client || terminalScope.includes(client)) && (!isSingleClientPageTemp || singleClient)
        );
      });
      if (secFilterComps.length === 0) return;
      categories.push({
        ...secondaryCategory,
        comps: secFilterComps,
        // 如果已选择一级分类，只展示二级分类，否则一二级分类名称都需要展示
        name: primaryName ? `${primaryName} / ${secondaryName}` : secondaryName,
      });
    });
  });

  const comps = categories.map((com) => ({
    id: com.id,
    title: com.name,
    category: com.pid,
    items: com.comps!,
  }));

  return comps as ICategoryViewData[];
};

const filterCom = (searchVal: string) => (com: IPageDescription) => com.name.includes(searchVal);

export const getDisplayedDatas = (
  datas: ICategoryViewData[],
  categories: ICategory[],
  searchVal: string,
  renderTitle?: RenderTitle,
) => datas
  .map((_data) => {
    const { category, items, childs } = _data;
    const categoryType = categories.find(({ id }) => id === category) as IPageCategory;
    const _childs = cloneDeep(childs);

    return {
      ..._data,
      items: items?.filter(filterCom(searchVal)),
      title: renderTitle ? renderTitle(categoryType, _data) : _data.title,
      childs: _childs
        ?.map((child) => {
          child.items = child.items?.filter(filterCom(searchVal));

          return child;
        })
        .filter((_child) => _child.items?.length),
    };
  })
  .filter((_data) => _data.items?.length || _data.childs?.length);

export default {};
