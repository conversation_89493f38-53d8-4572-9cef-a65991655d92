import { getLabel } from '@weapp/utils';
import { action } from 'mobx';
import { ClientType, LayoutType, PageModuleType } from '../../../core/types';
import ebdcoms from '../../../utils/ebdcoms';
import shallowObservable from '../../../utils/shallowObservable';
import { IPageCategory, IPageCategoryChild, PageTempDataType } from './types';
import { TemplateToComp } from './utils';

const getDftCategories = (): IPageCategory[] => [
  {
    id: '1',
    name: get<PERSON>abel('55626', '系统'),
    showOrder: 1,
    childs: [],
  },
  {
    id: '100',
    name: get<PERSON>abel('54002', '自定义'),
    showOrder: 100,
    childs: [],
  },
];

export type Params = {
  module: PageModuleType;
  layoutType: LayoutType;
  templateType: string;
  clientType?: ClientType;
};

export class TemplatePanelStore {
  @shallowObservable categories: IPageCategory[] | null = null;

  @action
  init(params: Params, reload?: boolean, comServicePath: string = 'ebuilder/designer') {
    if (this.categories && !reload) {
      return;
    }

    this.categories = null;

    ebdcoms
      .asyncExcu('ajax', {
        url: `/api/bs/${comServicePath}/page/tmpl/listAll`,
        params,
      })
      .then(this.initCategories);
  }

  @action
  initCategories = (tplDatas: PageTempDataType[]) => {
    const categories = getDftCategories();
    const [sysCategory, customCategory] = categories;

    tplDatas.forEach((tplData) => {
      const {
        id, name, isSys, templates,
      } = tplData;
      const comps = templates.map(TemplateToComp);
      const { id: pid, childs, showOrder } = isSys ? sysCategory : customCategory;
      const category: IPageCategoryChild = {
        id,
        pid,
        name,
        showOrder,
        comps,
      };

      childs!.push(category);
    });

    this.categories = categories;
  };
}

export default new TemplatePanelStore();
