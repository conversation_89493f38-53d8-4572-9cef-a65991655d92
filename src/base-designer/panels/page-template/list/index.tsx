import { observer } from 'mobx-react';
import React from 'react';
import _actions from '../../../decorator/actions';
import { PagePanelProps } from '../types';
import Content from './Content';

@_actions.autobind
class PagePanel extends React.PureComponent<PagePanelProps> {
  listRef = React.createRef<any>();

  @_actions.method('page')
  init = () => {
    const { datas, fetchData } = this.props;

    if (!fetchData && !datas) {
      this.listRef.current.setInit(false);
    }
  };

  render() {
    return <Content weId={`${this.props.weId || ''}_3ufnzm`} ref={this.listRef} {...this.props} />;
  }
}

export default observer(PagePanel);
