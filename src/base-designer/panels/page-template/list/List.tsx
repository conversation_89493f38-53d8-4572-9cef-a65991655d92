import React from 'react';
import Collapse from '../../../../common/collapse';
import { prefixCls } from '../../../../constants';
import { PageListProps } from '../types';
import Card from './Card';

const PageList: React.FC<PageListProps> = (props) => {
  const { datas, ...cardProps } = props;

  return (
    <div className={`${prefixCls}-page-list`}>
      {datas.map((data, index) => (
        <Collapse key={data.id} weId={`${props.weId || ''}_wt3khp@${index}`} title={data.title}>
          {data.items?.map((item, i) => (
            <Card
              key={item.id}
              weId={`${props.weId || ''}_nc5y61@${i}`}
              data={item}
              {...cardProps}
            />
          ))}
        </Collapse>
      ))}
    </div>
  );
};

export default PageList;
