import { action, observable } from 'mobx';
import { ReactText } from 'react';
import { IPageCategory } from '../types';

export default class PageStore {
  /** 页面分类信息 */
  @observable categories: IPageCategory[] = [];

  /** 筛选值 */
  @observable searchVal: string = '';

  @observable hasInit: boolean = false;

  @action
  setInit = (hasInit: boolean) => {
    this.hasInit = hasInit;
  };

  @action
  setCategories = (categories: IPageCategory[]) => {
    this.categories = categories;
  };

  @action
  onSearch = (searchVal: ReactText) => {
    this.searchVal = searchVal as string;
  };
}
