import {
  But<PERSON>, Cors<PERSON><PERSON>po<PERSON>, Dialog, Icon, IconNames, Popover,
} from '@weapp/ui';
import { getLabel, isEqual, isString } from '@weapp/utils';
import React, {
  memo, ReactNode, SyntheticEvent, useCallback,
} from 'react';
import { Else, If, Then } from 'react-if';
import { prefixCls } from '../../../../constants';
import json from '../../../../utils/json';
import { useDesigner } from '../../../hooks';
import { CardProps, IPageDescription } from '../types';

const { confirm } = Dialog;

const defaultRender = (_item: IPageDescription, v: ReactNode) => v;

const Card: React.FC<CardProps> = (props) => {
  const {
    data,
    className,
    draggable,
    onDragStart,
    renderIcon = defaultRender,
    renderName = defaultRender,
    renderCompExtra,
    onUsed,
    useDisabled = false,
  } = props;
  const wrapperProps: React.HTMLAttributes<HTMLSpanElement> = {};
  const { remark, id } = data;
  const designer = useDesigner();
  const isObject = !!json.parse(data.icon);
  let src = '';

  if (isString(data.icon) && !isObject) {
    if (data.icon.indexOf('/') === 0) {
      src = data.icon;
    }
  }

  const onUsedPageTpl = useCallback(() => {
    if (useDisabled) return;

    if (designer) {
      const { coms } = designer.layoutStore;

      if (coms?.length) {
        confirm({
          mask: true,
          content: getLabel('112460', '使用模板会覆盖掉当前页面的内容，确定要使用模板吗？'),
          onOk: () => onUsed?.(id),
        });
      } else {
        onUsed?.(id);
      }
    } else {
      onUsed?.(id);
    }
  }, []);

  const button = (
    <CorsComponent
      weId={`${props.weId || ''}_2x15bo`}
      app="@weapp/components"
      compName="Disabled"
      condition={useDisabled}
      className={`${prefixCls}-page-button-disabled`}
      hideMask
    >
      <Button
        weId={`${props.weId || ''}_b2j1az`}
        type="primary"
        size="small"
        className={`${prefixCls}-page-button`}
        onClick={onUsedPageTpl}
      >
        {getLabel('112433', '使用')}
      </Button>
    </CorsComponent>
  );

  // 页面图标
  const icon = (
    <span className={`${prefixCls}-page-img`}>
      <If weId="y64fa0" condition={!!src}>
        <Then weId="qzqkz9">
          <img src={src} alt="" />
        </Then>
        <Else weId="hrojvq">
          <If weId={`${props.weId || ''}_cmk0ze`} condition={isObject}>
            <Then weId={`${props.weId || ''}_zpwcqh`}>
              <CorsComponent
                weId={`${props.weId || ''}_g68imy`}
                app="@weapp/ebdcoms"
                compName="AssetsItem"
                {...json.parse(data.icon)}
              />
            </Then>
            <Else weId={`${props.weId || ''}_tnfida`}>
              <Icon weId="aohrjo" name={data.icon as IconNames} />
            </Else>
          </If>
        </Else>
      </If>
      <If weId={`${props.weId || ''}_zm2ogo`} condition={remark}>
        <Then weId={`${props.weId || ''}_guvoek`}>
          <Popover
            weId={`${props.weId || ''}_pa39w1`}
            popup={remark}
            placement="bottomLeft"
            popoverType="tooltip"
          >
            {button}
          </Popover>
        </Then>
        <Else weId={`${props.weId || ''}_o76fgg`}>{button}</Else>
      </If>
    </span>
  );

  // 页面名称
  const name = (
    <span className={`${prefixCls}-page-name`} title={data.name}>
      {data.name}
    </span>
  );

  const handleDragStart = useCallback((e: SyntheticEvent) => {
    onDragStart?.(e, data);
  }, []);

  if (draggable) {
    wrapperProps.draggable = draggable;
  }

  return (
    <span {...wrapperProps} onDragStart={handleDragStart} className={className}>
      {renderIcon(data, icon)}
      {renderName(data, name)}
      {renderCompExtra?.(data)}
    </span>
  );
};

export default memo(Card, isEqual);
