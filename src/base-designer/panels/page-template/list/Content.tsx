import {
  CorsComponent, Icon, Input, Spin,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { useEffect, useMemo } from 'react';
import { Else, If, Then } from 'react-if';
import { prefixCls } from '../../../../constants';
import { useDesigner } from '../../../hooks';
import { ICategoryViewData, PagePanelProps } from '../types';
import { fetchCategories, getCategoryViewDatas, getDisplayedDatas } from '../utils';
import './index.less';
import PageList from './List';
import PageStore from './store';

/**
 * 缓存组件面板的store，用于避免组件面板之前切换造成的重复请求
 */
const stores: Record<string, PageStore> = {};

const defaultFilter = (datas: ICategoryViewData[]) => datas;
const defaultGetUniqueKeyData = (data: any) => data.type;

const ComListContent = React.forwardRef((props: PagePanelProps, ref: any) => {
  const designer = useDesigner();
  const {
    id,
    fetchData = () => fetchCategories(designer?.page),
    filter = defaultFilter,
    renderTitle,
    getUniqueKeyData = defaultGetUniqueKeyData,
    datas,
    className = '',
    clientType = designer?.clientType || 'PC',
    placeholder = getLabel('99296', '请输入模板名称'),
    ...listProps
  } = props;

  /**
   * 外部传入datas，不再内部请求数据
   */
  const _fetchData = typeof datas !== 'undefined' ? () => Promise.resolve(datas) : fetchData;

  const store = useMemo(() => {
    /**
     * 如果是外部维护的datas，每次重新实例化ComponentStore, 不会造成重复请求
     */
    if (datas && !id) {
      return new PageStore();
    }

    const key = id || _fetchData.toString();

    if (!stores[key]) {
      stores[key] = new PageStore();
    }

    ref.current = stores[key];

    return stores[key];
  }, []);

  const {
    hasInit, categories, searchVal, onSearch,
  } = store;

  useEffect(() => {
    if (!hasInit) {
      _fetchData().then((res) => {
        if (res) {
          store.setCategories(res);
          store.setInit(true);
        }
      });
    }
  }, [hasInit]);

  useEffect(() => {
    if (datas) {
      store.setCategories(datas);

      if (!hasInit) {
        store.setInit(true);
      }
    }
  }, [datas]);

  const compDatas = useMemo(
    () => getCategoryViewDatas(toJS(categories), clientType, designer.singleClient),
    [categories, clientType, getUniqueKeyData, filter],
  );

  if (!hasInit) {
    return (
      <div className={`${prefixCls}-page-panel`}>
        <Spin weId="cwk640" />
      </div>
    );
  }

  const displayedDatas = getDisplayedDatas(compDatas, categories, searchVal, renderTitle);

  return (
    <div className={`${prefixCls}-page-panel ${className}`}>
      <div className={`${prefixCls}-page-panel-search`}>
        <Input
          weId={`${props.weId || ''}_lq8086`}
          value={searchVal}
          allowClear
          suffix={
            <Icon
              weId={`${props.weId || ''}_jvcxez`}
              className="ui-searchAdvanced-input-icon"
              name="Icon-search"
            />
          }
          placeholder={placeholder}
          onChange={onSearch}
        />
      </div>
      <If weId={`${props.weId || ''}_j6xdpx`} condition={displayedDatas.length > 0}>
        <Then weId={`${props.weId || ''}_grhx5f`}>
          <PageList weId={`${props.weId || ''}_uqfbzq`} datas={displayedDatas} {...listProps} />
        </Then>
        <Else weId={`${props.weId || ''}_ki264o`}>
          <CorsComponent
            weId={`${props.weId || ''}_v1eqhx`}
            app="@weapp/ebdcoms"
            compName="Empty"
            type={searchVal ? 'search' : 'noData'}
          />
        </Else>
      </If>
    </div>
  );
});

export default observer(ComListContent);
