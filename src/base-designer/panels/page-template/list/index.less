@import (reference) '../../../../style/prefix.less';
@import (reference) '../../../../style/var.less';

.@{prefix}-page-panel {
  display: flex;
  height: 100%;
  flex-direction: column;

  .ui-collapse-panel__content > span {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    user-select: none;
    width: 46%;
   word-wrap: break-word;
  }

  .ui-collapse-panel__content > span:nth-child(2n + 1) {
    margin-right: 6%;
  }

  &-search {
    display: flex;
    padding: 10px;

    .ui-select-input {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      min-width: 60px;
      max-width: 80px;
    }

    & > div:first-child {
      width: auto;
    }

    & > div:last-child {
      flex: 1 1 auto;
      width: 100%;
      margin-left: -1px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    .ui-input-wrap:hover {
      z-index: @ls-wrap-zIndex;
    }
  }

  .ui-spin {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.@{prefix}-page-list {
  height: 100%;
  padding: 0 10px;
  overflow: auto;

  .ui-collapse-panel__title {
    text-transform: none !important;
  }
}

.@{prefix}-page-nlist {
  height: 100%;
  padding-left: 10px;
  overflow: auto;

  .@{prefix}-collapse .ui-collapse-panel__content-box {
    padding-right: 10px;
  }

  &-category {
    border: 1px solid var(--border-color);
    margin-bottom: 10px;
    border-radius: 3px;

    .@{prefix}-collapse .ui-collapse-panel__content-box {
      padding-right: 0;
    }

    .weapp-de-collapse .ui-collapse-panel__title {
      padding-left: 10px;
    }

    .ui-collapse-panel__content > span {
      margin-left: 20px;
    }

    .ui-collapse-panel__content > span:nth-child(2n + 1) {
      margin-left: 20px;
    }
  }
}

.@{prefix}-page-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 60px;
  padding: 2px;
  border-radius: 3px;
  background-color: #fff;
  border: 1px solid var(--border-color);
  transition: 0.225s box-shadow ease-in-out;
  position: relative;

  &:hover {
    box-shadow: 0 0px 5px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--primary);

    .@{prefix}-page-button {
      display: block;
    }
  }

  .@{prefix}-page-button {
    display: none;
    position: absolute;
  }

  .@{prefix}-page-button-disabled {
    position: absolute;

    .@{prefix}-page-button {
      position: unset;
    }
  }

  img {
    width: 95%;
    height: 95%;
    object-fit: contain;
    pointer-events: none;
    user-select: none;
  }

  & + span {
    font-size: var(--font-size-12);
    margin: 0 0 10px 0;
    user-select: none;
    opacity: 0.8;
    line-height: 15px;
    text-align: center;
    margin-top: 8px;
  }

  .ui-icon {
    width: 100%;
    height: 100%;
    margin-left: 0 !important;
  }

  & > div {
    height: 95%;
  }

  svg {
    width: 98%;
    height: 98%;
    pointer-events: none;
    user-select: none;
  }

  &-disabled {
    pointer-events: none;
    background-color: var(--de-disabled-icon-color);
  }
}

.@{prefix}-page-disabled{
    color: var(--de-disabled-font-color);
    cursor: not-allowed !important;
}

.@{prefix}-page-name {
  .lines-omitted;
}
