import { observer } from 'mobx-react';
import React, { useEffect } from 'react';
import { anonymousLoginModule, EVENT_PAGE_TEMP_RELOAD, prefixCls } from '../../../constants';
import { useDesigner } from '../../hooks';
import getPageModule from '../../utils/getPageModule';
import './index.less';
import PagePanel from './list';
import tplStore, { Params } from './store';
import { PageTempPanelProps } from './types';

const PageTemplatePanel: React.FC<PageTempPanelProps> = (props) => {
  const { moduleScope, layoutType, clientType } = props;
  const { categories } = tplStore;
  // 提供给外部单独使用，不在设计器内
  const designer = useDesigner();

  useEffect(() => {
    const init = (reload = true) => {
      const _module = moduleScope || designer?.moduleScope;
      const _layoutType = layoutType || designer?.layoutType;
      const isAnonymous = anonymousLoginModule.includes(_module || '');
      const params = {
        module: getPageModule(_module),
        templateType: 'PAGE',
        layoutType: _layoutType,
        // 登录页，网格布局下可以查看流式布局和网格布局的模板
        ...(isAnonymous && _layoutType === 'GRID' ? { isAll: true } : {}),
      } as Params;
      // 网格布局设计器才有这个属性
      const { isFrontDeskEntry = false } = (designer || {}) as any;

      const isDoubleClients = designer?.terminalScopes?.length === 2;

      // 双端页面下不用传终端类型
      if (moduleScope !== 'EB_PAGE' && !isDoubleClients) {
        params.clientType = clientType;
      }

      if (isFrontDeskEntry) {
        params.module = moduleScope || params.module;
      }

      tplStore.init(params, reload, designer?.comServicePath);
    };

    designer?.events.off(EVENT_PAGE_TEMP_RELOAD);
    designer?.events.on(EVENT_PAGE_TEMP_RELOAD, init);

    init(false);

    props.onDidMount?.({
      reload: init,
    });

    designer?.events.on('update.pagetemp', () => {
      init(true);
    });

    return () => {
      designer?.events.off('update.pagetemp');
      designer?.events.off(EVENT_PAGE_TEMP_RELOAD);
    };
  }, []);

  return (
    <PagePanel
      weId={`${props.weId || ''}_5gixmu`}
      className={`${prefixCls}-page-temp`}
      {...props}
      datas={categories}
    />
  );
};

export default observer(PageTemplatePanel);
