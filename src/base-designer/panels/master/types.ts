import { ClientType, Page } from '../../../core/types';
import { IComTemplateData, LayoutType, PageModuleType } from '../../../types';
import { ComponentPanelProps } from '../component/types';

type MasterMaskPanelStore = {
  reload: () => void;
};

export interface MasterMaskPanelProps extends ComponentPanelProps {
  /** 独立使用时，需要以下属性 */
  /** 所属模块 */
  moduleScope?: PageModuleType;
  /** 设计器布局类型 */
  layoutType?: LayoutType;
  /** 页面信息 */
  pageInfo?: Page;
  clientType?: ClientType;
  /** 模板管理是否显示 */
  visible: boolean;
  /** 接口支持额外参数 */
  params?: any;
  /** 组件didmount的回调 */
  onDidMount?: (store: MasterMaskPanelStore) => void;
  /** 母版管理关闭的回调 */
  closeMasterDia: () => void;
  /** 无权保存、修改配置 */
  changeDisabled?: boolean;
}

export interface CompMasterDataType {
  id: string;
  isSys: number;
  name: string;
  showOrder: number;
  templates: IComTemplateData[];
}

export default {};
