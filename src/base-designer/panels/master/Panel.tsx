import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { useCallback, useEffect, useMemo } from 'react';
import { EVENT_MASTER_RELOAD, prefixCls } from '../../../constants';
import { IComConfig, IComData } from '../../../types';
import json from '../../../utils/json';
import { MasterDialogView } from '../../dialogs';
import { useDesigner } from '../../hooks';
import getPageModule from '../../utils/getPageModule';
import { ComponentPanel } from '../component';
import '../template/index.less';
import masterStore from './store';
import { MasterMaskPanelProps } from './types';

const MasterMaskPanel: React.FC<MasterMaskPanelProps> = (props) => {
  const {
    visible,
    moduleScope = 'EB_PAGE',
    layoutType,
    params: _params,
    changeDisabled = false,
    closeMasterDia,
  } = props;
  const { categories } = masterStore;
  // 提供给外部单独使用，不在设计器内
  const designer = useDesigner();

  const {
    clientType, comDescriptions, layoutStore, singleClient, isUnqiue,
  } = designer;

  const init = (reload = true) => {
    const params = {
      module: getPageModule(moduleScope || designer?.moduleScope),
      layoutType: layoutType || designer?.layoutType,
      ..._params,
    };

    masterStore.init(params, designer, reload);
  };

  useEffect(() => {
    designer?.events.off(EVENT_MASTER_RELOAD);
    designer?.events.on(EVENT_MASTER_RELOAD, init);

    designer?.events.on('update.master', () => {
      init();
    });

    init(false);

    return () => {
      designer?.events.off('update.master');
      designer?.events.off(EVENT_MASTER_RELOAD);
    };
  }, []);

  const formatCardData = useCallback(
    (data: any) => {
      const { configPC, configMobile } = data;
      const config = clientType === 'PC' ? configPC : configMobile;
      const coms = config && json.parse(config) ? JSON.parse(config) : [];
      const unqiueCom = coms.find((com: any) => isUnqiue(com.type));
      const isUnqiueMaster = toJS(layoutStore.coms)?.find(
        (com: any) => com?.master?.id === data?.id,
      );

      const disabled = unqiueCom
        ? toJS(layoutStore.coms)?.find((com) => unqiueCom.type === com.type)
        : false;

      return { ...data, disabled: Boolean(disabled || isUnqiueMaster) };
    },
    [clientType, comDescriptions, toJS(layoutStore.coms)],
  );

  const getCategories = useMemo(() => {
    if (!categories) return categories;

    const _categories = toJS(categories);
    const [customCategory] = _categories;
    const { childs = [] } = customCategory;

    childs.forEach((child: any) => {
      child.comps = child.comps
        ?.filter((comp: any) => {
          const { terminalScope } = comp;

          // 单一终端不能匹配多个终端的母版，多个终端的母版不能匹配单一的终端
          // eslint-disable-next-line max-len
          const unMatched = singleClient && (terminalScope.length !== 1 || !terminalScope.includes(clientType));

          return !unMatched;
        })
        ?.map((comp: any) => formatCardData(comp));
    });

    return _categories;
  }, [clientType, comDescriptions, toJS(layoutStore.coms), toJS(categories), singleClient]);

  // 生成refreshKey，用于刷新母版的状态
  const getReloadKey = useCallback(
    (coms: IComData<IComConfig>[]) => coms.map((com) => com?.master?.id)?.join(','),
    [],
  );

  return (
    <>
      <ComponentPanel
        weId={`${props.weId || ''}_5gixmu`}
        className={`${prefixCls}-temp`}
        {...props}
        placeholder={getLabel('118354', '请输入母版名称')}
        datas={getCategories}
        menuType="master"
        getReloadKey={getReloadKey}
      />
      <MasterDialogView
        weId="h2muv1"
        visible={visible}
        pageScope={moduleScope}
        pageInfo={designer.page || { layoutType: 'FLOW' }}
        clientType={designer.clientType}
        events={designer?.events}
        changeDisabled={changeDisabled}
        onClose={closeMasterDia}
        isSingleClient={singleClient}
      />
    </>
  );
};

export default observer(MasterMaskPanel);
