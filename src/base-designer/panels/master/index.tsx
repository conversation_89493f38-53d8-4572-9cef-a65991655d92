import { ComponentType } from 'react';
import Loadable from '../../../common/loadable';
import { MasterMaskPanelProps } from './types';

const MasterMaskPanel = Loadable({
  name: 'MasterMaskPanel',
  loader: () => import(/* webpackChunkName: "de_master_mask_panel" */ './Panel'),
}) as ComponentType<MasterMaskPanelProps>;

MasterMaskPanel.displayName = 'MasterMaskPanel';

export default MasterMaskPanel;
