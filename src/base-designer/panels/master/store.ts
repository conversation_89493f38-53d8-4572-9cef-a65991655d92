import { action } from 'mobx';
import {
  IComCategory, IComCategoryChild, LayoutType, PageModuleType,
} from '../../../core/types';
import ebdcoms from '../../../utils/ebdcoms';
import shallowObservable from '../../../utils/shallowObservable';
import BaseDesignerStore from '../../store';
import { TemplateToComp } from '../template/utils';
import { CompMasterDataType } from './types';

const getDftCategories = (): IComCategory[] => [
  {
    id: '100',
    name: '',
    showOrder: 100,
    childs: [],
  },
];

export type Params = {
  module: PageModuleType;
  layoutType: LayoutType;
  [key: string]: any;
};

export class MasterMaskPanelStore {
  designStore: BaseDesignerStore | null = null;

  @shallowObservable categories?: IComCategory[] | null = null;

  @action
  init(params: Params, designStore: BaseDesignerStore, reload?: boolean) {
    this.designStore = designStore;

    if (this.categories && !reload) {
      return;
    }

    const servicePath = designStore?.comServicePath || 'ebuilder/designer';

    ebdcoms
      .asyncExcu('ajax', {
        url: `/api/bs/${servicePath}/master/listWithCategory`,
        params,
      })
      .then(this.initCategories);
  }

  @action
  initCategories = (tplDatas: CompMasterDataType[]) => {
    const { clientType } = this.designStore!;
    const categories = getDftCategories();
    const [customCategory] = categories;
    tplDatas.forEach((tplData) => {
      const { id, name, templates = [] } = tplData;
      const comps = templates?.map((com) => TemplateToComp(com, clientType, 'Master'));
      const { id: pid, childs, showOrder } = customCategory;
      const category: IComCategoryChild = {
        id,
        pid,
        name,
        showOrder,
        comps,
      };

      childs!.push(category);
    });

    this.categories = categories;
  };
}

export default new MasterMaskPanelStore();
