import { ClientType } from '../../../types';
import { TemplateToComp } from '../template/utils';
import { CompPreTempType } from './types';

export const PreTemplateToComp = (data: CompPreTempType[], clientType: ClientType) => {
  const newData = data
    .map((el) => {
      const item = {
        ...el,
        ...TemplateToComp(el as any, clientType),
        icon: el.icon,
      };
      if (item.terminalScope?.includes(clientType)) {
        return item;
      }
      return null;
    })
    .filter(Boolean);
  return newData;
};

export default {};
