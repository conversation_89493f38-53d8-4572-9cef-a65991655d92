import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { useEffect, useState } from 'react';
import { prefixCls } from '../../../constants';
import { ClientType } from '../../../types';
import List from './List';
import './index.less';
import { CompPreTempType } from './types';
import { PreTemplateToComp } from './utils';
import setTimeoutOnce from '../../../utils/setTimeoutOnce';

export interface PreTemplatePanelProps extends React.Attributes {
  visible: boolean;
  clientType: ClientType;
  data: CompPreTempType[];
  name: string;
  onClose: () => void;
}

const TemplatePanel: React.FC<PreTemplatePanelProps> = (props) => {
  const {
    visible, data, clientType, name, onClose,
  } = props;
  const [datas, changeDatas] = useState<CompPreTempType[]>([]);

  const isParent = (obj: any, parentObj: any) => {
    while (obj !== undefined && obj !== null && obj.tagName.toUpperCase() !== 'BODY') {
      if (obj === parentObj) {
        return true;
      }
      obj = obj.parentNode;
    }
    return false;
  };

  const onCloseDialog = (e: any) => {
    const {
      type, pageX, pageY, target,
    } = e;
    const dom = document.querySelector(`.${prefixCls}-com-styles-dia`);
    const { left = 200, top = 42, width = 200 } = dom!.getBoundingClientRect();
    if (
      type === 'dragstart'
      || (!isParent(target, document.querySelector(`.${prefixCls}-ls`)) // 如果点击的左边侧边栏 ，不需要隐藏
        && (pageX > left + width || pageY < top)) // 防止点击的元素不属于左侧边栏的子元素 ，但是又在左侧边栏上 而造成页面被隐藏
    ) {
      // 定时300ms是为了拖拽出预置模版之后，有个缓冲期关闭预置模版modal
      setTimeoutOnce(() => {
        onClose();
      }, 300);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', onCloseDialog);

    return () => {
      document.removeEventListener('mousedown', onCloseDialog);
    };
  }, []);

  useEffect(() => {
    const newData: any[] = PreTemplateToComp(toJS(data), clientType);
    changeDatas(newData);
  }, [data, clientType]);

  return (
    <List
      weId={`${props.weId || ''}_5gixmu`}
      visible={visible}
      clientType={clientType}
      data={datas}
      name={name}
      onClose={onClose}
    />
  );
};

export default observer(TemplatePanel);
