@import (reference) '../../../style/prefix.less';

.@{prefix}-com-styles-dia {
  position: absolute;
  top: 0px;
  left: 0;
  background-color: var(--de-box-bgColor);
  display: flex;
  flex-direction: column;
  transition: translate3d(0, 0, 0);
  flex-shrink: 0;
  transform-origin: 0;
  z-index: 100;
  transform: translateX(-211px);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  height: 100%;
  border-right: 1px solid var(--de-box-lineColor);

  &.show {
    transform: translateX(200px);
  }

  & > span {
    display: block;
    color: var(--regular-fc);
    font-size: 12px;
    line-height: 12px;
    padding: 11px 8px;
  }

  & > div {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding-bottom: 12px;
    width: 210px;
    padding: 0 15px 12px 15px;

    & > .ui-empty.ui-empty.ui-empty {
      justify-content: flex-start;
      padding-top: 100px;
      .ui-empty-title {
        font-size: 12px;
      }
    }
  }

  .@{prefix}-style-card-m {
    height: 102px;
    min-height: 102px;
    width: 100%;
    margin-bottom: 8px;
    & > span {
      height: 100%;
      padding: 5px 8px;
      border: 1px solid #e5e5e5;
      border-radius: 3px;
      width: 100%;
      background-color: #fff;
      cursor: move;
      display: block;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      &:hover {
        border-color: var(--primary);
      }
    }
  }

  .@{prefix}-style-card {
    height: 102px;
    min-height: 102px;
    padding: 5px 8px;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    margin-bottom: 8px;
    width: 100%;
    background-color: #fff;
    cursor: move;
    display: block;

    &:hover {
      border-color: var(--primary);
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}
