import { CorsComponent } from '@weapp/ui';
import { classnames } from '@weapp/utils';
import React, { ReactNode, useCallback } from 'react';
import { Else, If, Then } from 'react-if';
import { prefixCls } from '../../../constants';
import { ClientType } from '../../../types';
import useInternal from '../../hooks/useInternal';
import Card from '../component/list/Card';
import { CompPreTempType } from './types';
import setTimeoutOnce from '../../../utils/setTimeoutOnce';

interface PreCompTempListProps extends React.Attributes {
  visible: boolean;
  clientType: ClientType;
  data: CompPreTempType[];
  name: string;
  onClose: () => void;
  customItem?: (node: ReactNode, data: CompPreTempType) => ReactNode;
}

const List: React.FC<PreCompTempListProps> = (props) => {
  const {
    visible, data, name, onClose,
  } = props;

  const PreTempPanel = useInternal()?.resolver?.PreTempPanel;

  const onDragStart = useCallback(() => {
    // 定时300ms是为了拖拽出预置模版之后，有个缓冲期关闭预置模版modal
    setTimeoutOnce(() => {
      onClose();
    }, 300);
  }, []);

  const renderIcon = useCallback(() => null, []);

  const renderName = useCallback(() => null, []);

  const renderCompExtra = useCallback(
    (item: any) => (
      <div key={item.id} className={`${prefixCls}-style-card`}>
        <img src={item.icon} alt="" />
      </div>
    ),
    [],
  );

  const renderContent = useCallback((compPreTempData: CompPreTempType[]) => {
    if (PreTempPanel) {
      return <PreTempPanel weId={`${props.weId || ''}_zq7mil`} {...props} data={compPreTempData} />;
    }

    const content = compPreTempData.map((item) => (
      <Card
        key={item.id}
        weId={`${props.weId || ''}_nc5y61@${item.id}`}
        data={item as any}
        onDragStart={onDragStart}
        renderIcon={renderIcon}
        renderName={renderName}
        renderCompExtra={renderCompExtra}
      />
    ));

    return content;
  }, []);

  return (
    <div className={classnames([`${prefixCls}-com-styles-dia`], { show: visible })}>
      <span>{name}</span>
      <div>
        <If weId={`${props.weId || ''}_ff7bdg`} condition={data.length}>
          <Then weId={`${props.weId || ''}_u4vgmw`}>{renderContent(data)}</Then>
          <Else weId={`${props.weId || ''}_tx6q1h`}>
            <CorsComponent
              app="@weapp/ebdcoms"
              compName="Empty"
              weId={`${props.weId || ''}_rvzkz9`}
              type="noData"
              icon="Icon-empty-file"
            />
          </Else>
        </If>
      </div>
    </div>
  );
};

export default List;
