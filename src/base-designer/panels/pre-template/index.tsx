import { ComponentType } from 'react';
import Loadable from '../../../common/loadable';
import { PreTemplatePanelProps } from './Panel';

const PreTemplatePanel = Loadable({
  name: 'PreTemplatePanel',
  loader: () => import(/* webpackChunkName: "de_pre_template_panel" */ './Panel'),
}) as ComponentType<PreTemplatePanelProps>;

PreTemplatePanel.displayName = 'PreTemplatePanel';

export default PreTemplatePanel;
