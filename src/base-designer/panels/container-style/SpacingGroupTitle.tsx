import { CorsComponent } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import React, { SyntheticEvent, useCallback } from 'react';
import { prefixCls } from '../../../constants';
import { StyleFormData } from './types';

interface SpacingGroupTitleProps extends React.Attributes {
  styleForm: StyleFormData;
}

const fixSpacingVal = (val: string) => {
  if (!val || val === '0') {
    return '0px 0px 0px 0px';
  }
  const spacing = val.trim().split(' ');
  switch (spacing.length) {
    case 1:
      return `${val} ${val} ${val} ${val}`;
    case 2:
      return `${val} ${val}`;
    case 3:
      return `${val} ${spacing[1]}`;
    default:
      return val;
  }
};

const SpacingGroupTitle: React.FC<SpacingGroupTitleProps> = (props) => {
  const { styleForm } = props;

  const autoCentering = useCallback(
    (e: SyntheticEvent) => {
      const formDatas: any = styleForm.getFormDatas() || {};
      // 容器样式初加载后修改margin padding时，对应值在formDatas.spacing中获取
      let { margin, padding } = formDatas.spacing || formDatas;

      margin = fixSpacingVal(margin);
      padding = fixSpacingVal(padding);

      const changedSpacingValue = margin
        .split(' ')
        .map((item: string, index: number) => {
          if (index === 1 || index === 3) {
            item = 'auto';
          }
          return item;
        })
        .join(' ');

      styleForm.onChange({ spacing: { margin: changedSpacingValue, padding } });
      e.stopPropagation();
    },
    [toJS(styleForm)],
  );

  return (
    <>
      <span className={`${prefixCls}-title-center-title`}>{getLabel('181260', '间距')}</span>
      <div className={`${prefixCls}-title-center-icon`}>
        <CorsComponent
          weId={`${props.weId || ''}_jvpogx`}
          app="@weapp/ebdcoms"
          compName="IconFont"
          type="spacing-center"
          onClick={autoCentering}
          title={getLabel('181296', '水平居中，需要固定宽度')}
          placement="topRight"
        />
      </div>
    </>
  );
};

export default SpacingGroupTitle;
