import { CorsComponent } from '@weapp/ui';
import React, { useMemo } from 'react';

const Height = (props: any) => {
  const { styleData } = props;
  const disabled = useMemo(() => !!styleData?.heightAutoFill, [styleData?.heightAutoFill]);

  return (<CorsComponent
    weId={`${props.weId || ''}_51sakm`}
    {...props}
    app="@weapp/ebdcoms"
    compName="InputSize"
    disabled={disabled}
  />);
};

export default Height;
