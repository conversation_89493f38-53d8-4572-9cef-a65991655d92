@import (reference) '../../../style/prefix.less';

@height: 24px;
@st-box-size: 4px;

.@{prefix}-fl {
  &-value,
  &-inherit {
    padding: 2px 3px;
    border-radius: 2px;
    cursor: default;
  }

  &-value {
    color: #4c98f1;
    background: rgba(76, 152, 241, 0.14901960784313725);
  }

  &-initial {
    color: var(--regular-fc);
  }

  &-inherit {
    color: rgb(240, 139, 72);
    background: rgba(240, 139, 72, 0.14901960784313725);
  }

  &-value-tip,
  &-inherit-tip {
    font-size: var(--font-size-12);
    color: #ccc;

    div {
      padding: 4px 0px;
    }
  }

  &-value-from {
    margin: 0 8px;

    .ui-icon {
      margin-right: 8px;
    }

    span:last-child {
      background: #b5518c;
      color: #fff;
      padding: 2px 4px;
      display: inline-block;
      vertical-align: middle;
      border-radius: 2px;
    }
  }

  &-value-tip &-value-reset {
    padding: 0 8px;
  }

  &-value-reset {
    color: #fff;
    margin: -8px -8px 4px;
    height: 28px;
    line-height: 28px;
    transition: 0.225s background ease;
    cursor: pointer;
    border-bottom: 1px solid #444;

    &:hover {
      background: #5e5e5e;
    }

    .ui-icon {
      margin-right: 8px;
      margin-top: -3px;
    }
  }
}

.@{prefix}-style-form {
  // 原样式值overflow: unset;
  // 全部收缩容器分组样式以后，会在底部显示部分FormItem内容
  .ui-collapse {
    overflow: hidden;
  }

  &-loading {
    min-height: 100px;
  }

  padding-bottom: var(--form-item-v-spacing);

  .ui-collapse-group {
    .ui-collapse-panel {
      .ui-collapse-panel__title {
        padding-right: 10px;
      }

      .ui-collapse-panel__content-box {
        border-bottom: 0px;
      }

      .ui-formItem-label {
        height: @height;
        line-height: @height;

        & > span {
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          position: relative;
        }
      }

      .ui-formItem-wrapper {
        min-height: 0;
        height: auto;
      }

      .ui-formItem-label-col {
        line-height: @height;
      }
    }
  }

  .ui-select-input-wrap {
    height: @height;
  }

  .ui-input,
  .ui-input-wrap,
  .ui-textarea,
  .ui-select-input-wrap > div {
    min-height: @height;
    height: @height;
    padding-top: 0;
    padding-bottom: 0px;
    width: 100%;
  }

  .ui-input-wrap > input {
    min-height: @height - 2;
    height: @height - 2;
  }

  .ui-colorpicker {
    background-color: #fff;

    .ui-colorpicker-btn {
      height: @height - 4px;
      width: @height - 4px;
    }
  }

  .ui-slider-track {
    background-color: #ccc;
  }

  .ui-slider-rail {
    background-color: #ddd;
  }

  .ui-slider-handle {
    border-color: #aaa;
  }

  .ui-form-row {
    padding: 0 6px;
    background-color: #f5f5f5;

    &:first-child {
      padding-top: 8px;
    }

    & > div {
      padding-right: 2px;

      &:last-child {
        padding-right: 0;
      }
    }

    & > div + div {
      padding-left: 2px;
    }
  }

  /**
   * 分割线样式处理
   * 1. 大小：溢出处理
   * 2. 文字排版： 文本阴影
   * 3. 边框
   * 4. 其他，所有项
  */
  .ui-collapse-panel:nth-child(3) .ui-form-row:nth-child(4),
  .ui-collapse-panel:nth-child(6) .ui-collapse-panel__content > .ui-form-row:nth-child(11),
  .ui-collapse-panel:nth-child(8) .ui-form-row:not(:first-child),
  .ui-collapse-panel:nth-child(9) .ui-form-row:not(:first-child) {
    margin-top: 1px;

    & > div {
      padding-top: 8px;
    }
  }

  /**
   * spacing 标题剧中按钮
  */
  .ui-collapse-panel
    .ui-collapse
    .ui-collapse-panel--inactive
    .@{prefix}-title-center-icon
    .ui-icon {
    display: none;
  }

  /**
   * size fit显示位置
  */
  .ui-collapse-panel__content {
    .@{comsPrefix}-fit {
      .ui-icon {
        margin-left: 5px;
      }
    }
  }
}

.@{prefix}-style-toggle {
  height: 20px;
  background-color: #ececec;

  .ui-icon {
    transform: rotate(-90deg);
    transition: transform 0.225s ease;

    &.active {
      transform: rotate(0);
    }
  }

  .st-box {
    display: flex;
    align-items: center;

    span {
      width: @st-box-size;
      height: @st-box-size;
      border-radius: @st-box-size;
      margin-left: 2px;
    }

    &-inherit {
      background-color: rgb(232, 145, 83);

      & + span {
        margin-left: -1px;
      }
    }

    &-value {
      background-color: rgb(107, 176, 255);
    }
  }
}

.@{comsPrefix}-fl-style-popover {
  right: 0;
  left: auto !important;

  // popver 统一去掉小箭头
  .ui-popover-arrow {
    display: none;
  }

  .ui-slider-track {
    background-color: #ccc;
  }

  .ui-slider-rail {
    background-color: #ddd;
  }

  .ui-slider-handle {
    border-color: #aaa;
  }

  .ui-popover.ui-popover-undefined.ui-popover-bottom {
    padding: 6px 0 !important;
  }
}

.@{prefix}-title-center-icon {
  position: absolute;
  right: 8px;
  top: 9px;
  color: #666666;
}

.@{prefix}-title-center-icon :hover {
  color: #444444;
}

.@{comsPrefix}-collapse {
  .@{prefix}-collapse {
    .ui-collapse-panel--active {
      .ui-collapse-panel__title .ui-icon {
        transform: rotate(0);
      }
    }

    .ui-collapse-panel--inactive {
      .ui-icon {
        transform: rotate(-90deg);
      }
    }
  }
  transform: rotate(0);
}

.@{prefix}-autofill-switch {
  display: flex;
  align-items: center;
  .ui-switch {
    margin-top: 0;
    margin-right: 5px;
  }
}

body[page-rtl='true'] {
  .@{prefix}-title-center-icon {
    left: 4px;
    right: auto;
  }

  .@{prefix}-title-center-title {
    transform: none;
  }

  .@{prefix}-fl {
    &-value,
    &-inherit {
      display: inline-block;
    }
  }
}
