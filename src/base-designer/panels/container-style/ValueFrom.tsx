import { CorsComponent } from '@weapp/ui';
import { observer } from 'mobx-react';
import React, { useEffect, useMemo } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import './index.less';
import { CssProperty } from './types';

interface ValueFromProps extends React.Attributes {
  property: CssProperty;
  onChange?: (valueFrom: string) => void;
}

const ValueFrom: React.FC<ValueFromProps> = (props) => {
  const { property, onChange } = props;
  const valueFrom = useMemo(() => property.valueFrom, [property]);

  useEffect(() => {
    onChange?.(valueFrom);
  }, [valueFrom]);

  return (
    <When weId={`${props.weId || ''}_rx3e31`} condition={!!valueFrom}>
      <div className={`${prefixCls}-fl-value-from`}>
        <CorsComponent
          weId={`${props.weId || ''}_0k7wgu`}
          app="@weapp/ebdcoms"
          compName="IconFont"
          type="desktop"
        />
        <span>{valueFrom}</span>
      </div>
    </When>
  );
};

export default observer(ValueFrom);
