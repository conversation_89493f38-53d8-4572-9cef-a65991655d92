import { ComponentType } from 'react';
import Loadable from '../../../common/loadable';

export const ContainerStyle = Loadable({
  name: 'ContainerStyle',
  loader: () => import(/* webpackChunkName: "de_container_style" */ './ContainerStyle'),
}) as ComponentType<any>;

ContainerStyle.displayName = 'ContainerStyle';

export const ContainerContentStyle = Loadable({
  name: 'ContainerContentStyle',
  loader: () => import(/* webpackChunkName: "de_container_content_style" */ './Content'),
}) as ComponentType<any>;

ContainerContentStyle.displayName = 'ContainerContentStyle';

export default {};
