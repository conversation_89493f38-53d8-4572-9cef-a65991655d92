import { CorsComponent, Icon } from '@weapp/ui';
import { classnames } from '@weapp/utils';
import React, { useCallback, useRef, useState } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import { ValueStatus } from './types';

interface ToggleProps extends React.Attributes {
  value: boolean;
  onChange: (value: boolean) => void;
}

type LabelStatus = Record<ValueStatus, boolean>;

const initialLabelStatus: LabelStatus = {
  initial: false,
  inherit: false,
  value: false,
};

const getParentByClass = (ele: HTMLElement, selector: string) => {
  let parentEle = ele.parentElement;

  while (parentEle?.tagName !== 'BODY') {
    if (parentEle?.classList.contains(selector)) {
      return parentEle;
    }

    parentEle = parentEle?.parentElement as any;
  }

  return null;
};
const getSiblings = (ele: HTMLElement) => {
  const siblings = [];
  let { nextSibling } = ele;

  while (nextSibling) {
    siblings.push(nextSibling);
    nextSibling = nextSibling.nextSibling;
  }

  return siblings as HTMLElement[];
};
const getAllLabelStatus = (rowItems: HTMLElement[]) => {
  const labelStatus: Record<ValueStatus, boolean> = {
    initial: false,
    inherit: false,
    value: false,
  };

  rowItems.forEach((rowItem) => {
    const rowLabelTips: any = rowItem.querySelectorAll('[data-status]');

    rowLabelTips?.forEach((labelTip: HTMLElement) => {
      const valueStatus: ValueStatus = labelTip.getAttribute('data-status') as any;

      labelStatus[valueStatus] = true;
    });
  });

  return labelStatus;
};
const getLabelStatus = (tipContainer: HTMLElement) => {
  const parentRowItem = getParentByClass(tipContainer, 'ui-form-row');
  const siblingRowItems = getSiblings(parentRowItem!);

  return getAllLabelStatus(siblingRowItems);
};

const Toggle: React.FC<ToggleProps> = (props) => {
  const { value = false } = props;
  const tipRef = useRef<HTMLSpanElement>(null);
  const [labelStatus, setLabelStatus] = useState(initialLabelStatus);
  const toggle = useCallback(() => {
    if (value) {
      setLabelStatus(getLabelStatus(tipRef.current!));
    } else {
      setLabelStatus(initialLabelStatus);
    }
    props.onChange(!value);
  }, [value]);

  return (
    <CorsComponent
      weId="b7ueaw"
      app="@weapp/ebdcoms"
      compName="ButtonEx"
      inline={false}
      className={`${prefixCls}-style-toggle`}
      icon={
        <Icon weId="g2b2cx" name="Icon-Down-arrow04" className={classnames({ active: value })} />
      }
      onClick={toggle}
    >
      {props.children}
      <span ref={tipRef} className="st-box">
        <When weId={`${props.weId || ''}_6tb4k0`} condition={labelStatus.inherit}>
          <span className="st-box-inherit" />
        </When>
        <When weId={`${props.weId || ''}_8bo0jq`} condition={labelStatus.value}>
          <span className="st-box-value" />
        </When>
      </span>
    </CorsComponent>
  );
};

export default Toggle;
