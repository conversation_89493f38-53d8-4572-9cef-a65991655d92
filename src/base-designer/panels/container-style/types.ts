import { PopupPlacementSingle } from '@weapp/ebdcoms/lib/common/icon/types';
import { FormDatas } from '@weapp/ui/lib/components/form/types';
import { ReactNode } from 'react';

export type CustomRenderComs = { [key: string]: any };

/** 值的状态，继承，初始化，赋值 */
export type ValueStatus = 'inherit' | 'initial' | 'value';

/** css属性对象 */
export type CssProperty = {
  status: ValueStatus;
  /** 取值 */
  value: string;
  /** 值来源于 */
  valueFrom: string;
};

export interface LabelProps {
  itemKey: string;
  content: ReactNode;
}

export interface StyleFormData {
  getFormDatas: () => FormDatas;
  onChange: (formDatas: FormDatas) => void;
}

export interface SpacingTitleProps {
  name: string;
  type: string;
  onClick: any;
  title: string;
  placement?: PopupPlacementSingle;
}

export type ConfigAttrs = {
  [key: string]: string | number;
};

export enum CustomStyleType {
  Config = '1',
  Css = '2',
}
export interface StyleConfigData {
  id: string;
  category: string;
  libStyle?: ConfigAttrs;
  customStyle?: ConfigAttrs;
  cssSnippet?: string;
  useCustom?: boolean;
  customType?: CustomStyleType;
}

export enum StyleType {
  // 样式分组id对应关系，用于对应不同样式的config内容
  BASIC = '1',
  BASIC_CONTAINER = '7',
  CHARTS = '2',
  LIST = '3',
  NAV = '4',
  TABS = '5',
  DigitalPanel = '6',
  PAGE = '-1',
  SLIDE = '8',
}
