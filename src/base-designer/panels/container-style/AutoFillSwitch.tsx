import { Help, Switch } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { useCallback } from 'react';
import { EVENT_HEIGHT_AUTOFILL_CHANGE, prefixCls } from '../../../constants';
import './index.less';

const title = `${getLabel('231168', '1、根据页面剩余高度自动撑开；')}
${getLabel('231169', '2、如果组件不在可视范围（一屏内），高度自适应不生效；')}
${getLabel('231170', '3、仅支持页面中一级组件（不包括子组件）；')}
${getLabel('251209', '4、不受外层自定义样式总开关影响；')}`;

const AutoFillSwitch: React.FC<any> = (props) => {
  const {
    value, events, dom, onChange,
  } = props;

  const handleChange = useCallback((val: boolean) => {
    onChange(val);

    events?.emit(EVENT_HEIGHT_AUTOFILL_CHANGE, val, dom);
  }, []);

  return (
    <div className={`${prefixCls}-autofill-switch`}>
      <Switch weId={`${props.weId || ''}_zrvueu`} size="sm" value={value} onChange={handleChange} />
      <Help weId={`${props.weId || ''}_6ell9x`} title={title} placement="bottomRight" />
    </div>
  );
};

export default AutoFillSwitch;
