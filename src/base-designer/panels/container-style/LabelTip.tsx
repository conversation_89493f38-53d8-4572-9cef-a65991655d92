import { Icon } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { useState } from 'react';
import { prefixCls } from '../../../constants';
import { CssProperty } from './types';
import ValueFrom from './ValueFrom';

interface LabelTipProps {
  property?: CssProperty;
  onReset?: () => void;
}

const LabelTip: React.FC<LabelTipProps> = (props) => {
  const { property, onReset } = props;
  const [valueFrom, setValueFrom] = useState('');

  if (!property) {
    return null;
  }

  if (property.status === 'inherit' && property) {
    return (
      <div className={`${prefixCls}-fl-inherit-tip`}>
        <div>{getLabel('181299', '值来源于:')}</div>
        <ValueFrom weId="elxo2y" property={property} />
      </div>
    );
  }

  if (property.status === 'value' && property) {
    const tip = valueFrom
      ? getLabel('181297', '重置后，值将来源于')
      : getLabel('181300', '重置后，值将恢复到初始值');
    return (
      <div className={`${prefixCls}-fl-value-tip`}>
        <div className={`${prefixCls}-fl-value-reset`} onClick={onReset}>
          <Icon weId="nrj3i3" size="xs" name="Icon-Left-refresh" />
          {getLabel('56004', '重置')}
        </div>
        <div>{tip}</div>
        <ValueFrom weId="grbj92" property={property} onChange={setValueFrom} />
      </div>
    );
  }

  return null;
};

export default observer(LabelTip);
