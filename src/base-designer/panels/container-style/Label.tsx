import { Popover, utils } from '@weapp/ui';
import { debounce } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { useCallback, useEffect, useState } from 'react';
import { prefixCls } from '../../../constants';
import setTimeoutOnce from '../../../utils/setTimeoutOnce';
import LabelTip from './LabelTip';
import { isInherited, refProperties } from './Rule';
import { CssProperty, LabelProps, ValueStatus } from './types';
import { useContainerStyle } from './useStore';

const getItemValue = function getItemValue(itemKey: string, value: any) {
  return utils.isObject(value) ? value[itemKey] : value;
};

const Label: React.FC<LabelProps> = (props) => {
  const { itemKey, content } = props;
  const { form, cssRule, triggerFormChange } = useContainerStyle()!;
  const itemValue = form!.datas[itemKey];
  const [property, setProperty] = useState(cssRule!.properties?.[itemKey]);
  const [visible, setVisible] = useState(false);
  const status: ValueStatus = property?.status || 'initial';
  const labelNode = (
    <span className={`${prefixCls}-fl-${status}`} data-status={status} title={content as string}>
      {content}
    </span>
  );

  const updateProperty = useCallback((prop: CssProperty, value: string) => {
    // 复杂自定义组件如object-fit，border等值可能为对象，需要处理下
    const _itemValue = getItemValue(itemKey, value);
    const refItemKeys = refProperties[itemKey] || [];

    setProperty(prop);
    cssRule?.setProperty(itemKey, prop);

    refItemKeys.forEach((key) => {
      cssRule?.setProperty(key, cssRule!.getProperty(key, _itemValue));
    });
  }, []);

  const onValueChange = useCallback(
    debounce((value: string) => {
      // 样式更新到dom节点上后，再去获取prop
      setTimeoutOnce(() => {
        const _itemValue = getItemValue(itemKey, value);
        const prop = cssRule!.getProperty(itemKey, _itemValue);

        updateProperty(prop, value);
      });
    }, 500),
    [],
  );

  const getInitialProp = (_itemKey: string): CssProperty => ({
    value: cssRule!.initialStyles[_itemKey],
    status: 'initial',
    valueFrom: '',
  });

  const onReset = useCallback(() => {
    const inherited = isInherited(itemKey);
    let prop = { ...property };
    let refValues: Record<string, string> = {};

    if (inherited) {
      prop = cssRule!.getInheritedProperty(itemKey);
    } else {
      const refItemKeys = refProperties[itemKey] || [];
      const refProps = refItemKeys.map(getInitialProp);

      prop = getInitialProp(itemKey);
      // 重置关联属性的cssproperty
      if (refProps.length) {
        refValues = refProps.reduce((_value: any, refProp: CssProperty, index: number) => {
          const key = refItemKeys[index];

          cssRule?.setProperty(key, refProp);
          _value[key] = refProp.value;
          return _value;
        }, {});
      }
    }

    updateProperty(prop, itemValue as string);

    form?.updateDatas({ [itemKey]: prop.value, ...refValues });

    // 触发config改变，重新生成样式类
    triggerFormChange({ [itemKey]: prop.value });

    setVisible(false);
  }, [itemValue]);

  useEffect(() => {
    if (typeof property?.value !== 'undefined' && property?.value !== itemValue) {
      onValueChange(itemValue as string);
    }
  }, [itemValue]);

  if (!property || property.status === 'initial') {
    return <span>{labelNode}</span>;
  }

  return (
    <span>
      <Popover
        weId="dscy0a"
        popoverType="tooltip"
        placement="bottom"
        action={['click']}
        visible={visible}
        popup={<LabelTip weId="vsjuvb" property={property} onReset={onReset} />}
        onVisibleChange={setVisible as any}
      >
        {labelNode}
      </Popover>
    </span>
  );
};

export default observer(Label);
