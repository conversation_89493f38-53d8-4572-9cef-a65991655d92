import { FormItemProps } from '@weapp/ui';
import { observer } from 'mobx-react';
import React, { useCallback } from 'react';
import { EVENT_HIGHLIGHT_UPDATE } from '../../../constants';
import { IComData } from '../../../types';
import setTimeoutOnce from '../../../utils/setTimeoutOnce';
import { useDesigner } from '../../hooks';
import Content from './Content';
import { StyleConfigData } from './types';

export interface ContainerStyleProps extends React.Attributes {
  getBasicContainerStyle: (styles: StyleConfigData[]) => StyleConfigData[];
  itemsFilter?: (items: FormItemProps) => Promise<FormItemProps>;
}

const ContainerStyle: React.FC<ContainerStyleProps> = (props) => {
  const { getBasicContainerStyle, itemsFilter } = props;
  const designerStore = useDesigner();
  const { layoutStore, events, clientType } = designerStore;
  const { selectedComDom, selectedCom, updateComConfig } = layoutStore;

  const onChange = useCallback(
    (val: { styles: StyleConfigData[] }, comId: string, otherParams?: any) => {
      updateComConfig(val, comId, otherParams);
      // 组件尺寸改变时，选择框跟随调整
      setTimeoutOnce(() => {
        events?.emit(EVENT_HIGHLIGHT_UPDATE, layoutStore?.selectedComDom, 'selected');
      });
    },
    [layoutStore],
  );

  return (
    <Content
      weId={`${props.weId || ''}_wgsy0z`}
      selectedComDom={selectedComDom as HTMLElement}
      selectedCom={selectedCom as IComData}
      events={events}
      clientType={clientType}
      onChange={onChange}
      getBasicContainerStyle={getBasicContainerStyle}
      itemsFilter={itemsFilter}
    />
  );
};

export default observer(ContainerStyle);
