import { FormItemProps } from '@weapp/ui';
import { observer, Provider as MobxProvier } from 'mobx-react';
import React from 'react';
import { EventEmitter } from '../../../core/utils';
import { ClientType, IComData } from '../../../types';
import { Group } from './form';
import StyleForm from './StyleForm';
import { StyleConfigData } from './types';
import containerStyleStore, { Provider } from './useStore';

interface ContainerContentStyleProps extends React.Attributes {
  selectedCom: IComData;
  selectedComDom: HTMLElement;
  events?: EventEmitter;
  clientType?: ClientType;
  getBasicContainerStyle?: (styles: StyleConfigData[]) => StyleConfigData[];
  groupFilter?: (val: Group[]) => Group[];
  itemsFilter?: (items: FormItemProps) => Promise<FormItemProps>;
  onChange: (value: { styles: StyleConfigData[] }, comId: string) => void;
}

const ContainerContentStyle: React.FC<ContainerContentStyleProps> = (props) => {
  const {
    selectedCom,
    selectedComDom,
    events,
    clientType,
    onChange,
    groupFilter,
    getBasicContainerStyle,
    itemsFilter,
  } = props;

  return (
    <MobxProvier weId={`${props.weId || ''}_jjd5xs`} containerStyleStore={containerStyleStore}>
      <Provider weId={`${props.weId || ''}_e8v2lj`} value={containerStyleStore}>
        <StyleForm
          weId={`${props.weId || ''}_731vdo`}
          selectedCom={selectedCom}
          selectedComDom={selectedComDom}
          events={events}
          clientType={clientType}
          onChange={onChange}
          groupFilter={groupFilter}
          itemsFilter={itemsFilter}
          getBasicContainerStyle={getBasicContainerStyle}
        />
      </Provider>
    </MobxProvier>
  );
};

export default observer(ContainerContentStyle);
