import { CorsComponent, FormItemProps } from '@weapp/ui';
import { SingleItemProps } from '@weapp/ui/lib/components/form-item/types';
import { FormLayoutProps, FormLayoutType } from '@weapp/ui/lib/components/form/types';
import { getLabel } from '@weapp/utils';
import React from 'react';
import AutoFillSwitch from './AutoFillSwitch';
import Height from './Height';
import Label from './Label';
import SpacingGroupTitle from './SpacingGroupTitle';
import Toggle from './Toggle';
import { StyleFormData } from './types';

export enum Group {
  Layout = 'layout',
  Typography = 'typography',
  Space = 'spacing',
  Size = 'size',
  Position = 'postion',
  Background = 'background',
  Border = 'border',
  Effects = 'Effects',
}

// 缓存组件样式Item
const cacheEbdCom: Record<string, any> = {};

const loaderEbdCom = (compName: string, customProps?: any) => (props: any) => {
  if (cacheEbdCom[compName]) {
    const Com = cacheEbdCom[compName];

    return React.cloneElement(Com, { ...customProps, ...props });
  }

  const ebdCom = (
    <CorsComponent
      weId={`${props.weId || ''}_51sakm`}
      app="@weapp/ebdcoms"
      compName={compName}
      {...customProps}
      {...props}
    />
  );

  cacheEbdCom[compName] = ebdCom;

  return ebdCom;
};

export function getItems(
  itemsFilter = (v: any) => v,
  isOutsideGridContainer?: boolean,
): Promise<FormItemProps> | FormItemProps {
  const items: FormItemProps = {
    display: {
      label: getLabel('54039', '显示'),
      labelSpan: 6,
      itemType: 'CUSTOM',
      groupId: Group.Layout,
      // 网格布局最外层容器不能选择inline、inline-block，只能选择block、flex
      disabledValue: isOutsideGridContainer ? ['inline', 'inline-block'] : [],
      defaultValue: 'block',
      customRender: loaderEbdCom('Display'),
      toggleItems: ['flexDirection', 'alignItems', 'justifyContent', 'flexWrap', 'alignContent'],
      setHide: (id: string, value: string, formDatas: any) => {
        if (id === 'alignContent') {
          return formDatas.flexWrap === 'nowrap' || !formDatas.flexWrap || value !== 'flex';
        }

        return value !== 'flex';
      },
    },
    flexDirection: {
      label: getLabel('181314', '方向'),
      labelSpan: 6,
      itemType: 'CUSTOM',
      groupId: Group.Layout,
      customRender: loaderEbdCom('FlexDirection'),
    },
    alignItems: {
      label: '',
      labelSpan: 0,
      itemType: 'CUSTOM',
      groupId: Group.Layout,
      customRender: loaderEbdCom('AlignItems'),
    },
    justifyContent: {
      label: '',
      labelSpan: 0,
      itemType: 'CUSTOM',
      groupId: Group.Layout,
      customRender: loaderEbdCom('JustifyContent'),
    },
    flexWrap: {
      label: getLabel('57330', '换行'),
      labelSpan: 6,
      itemType: 'CUSTOM',
      groupId: Group.Layout,
      customRender: loaderEbdCom('FlexWrap'),
      toggleItems: ['alignContent'],
      setHide: (id: string, value: string, formDatas: any) => formDatas.display !== 'flex' || value === 'nowrap' || !value,
    },
    alignContent: {
      label: getLabel('56275', '对齐'),
      labelSpan: 6,
      itemType: 'CUSTOM',
      groupId: Group.Layout,
      customRender: loaderEbdCom('AlignContent'),
    },
    fontFamily: {
      label: getLabel('102271', '字体'),
      labelSpan: 6,
      itemType: 'CUSTOM',
      groupId: Group.Typography,
      customRender: loaderEbdCom('FontFamily'),
    },
    fontWeight: {
      label: getLabel('102272', '字重'),
      labelSpan: 6,
      itemType: 'CUSTOM',
      groupId: Group.Typography,
      customRender: loaderEbdCom('FontWeight'),
    },
    fontSize: {
      label: getLabel('55012', '大小'),
      itemType: 'CUSTOM',
      groupId: Group.Typography,
      labelSpan: 12,
      customRender: loaderEbdCom('InputSize', { exclude: ['auto'] }),
      layoutGroup: 'layout7',
    },
    lineHeight: {
      label: getLabel('94486', '行高'),
      itemType: 'CUSTOM',
      groupId: Group.Typography,
      labelSpan: 12,
      customRender: loaderEbdCom('InputSize', {
        custom: ['px', '%', 'em', 'rem', 'vw', 'vh', '-'],
      }),
      layoutGroup: 'layout7',
    },
    color: {
      label: getLabel('55013', '颜色'),
      labelSpan: 6,
      itemType: 'CUSTOM',
      groupId: Group.Typography,
      otherParams: {
        style: { maxWidth: '100%' },
      },
      customRender: (props: any) => {
        const { value, onChange } = props;
        return (
          <CorsComponent
            weId={`${props.weId || ''}_zw0xw7`}
            app="@weapp/ebdcoms"
            compName="ColorPickerTheme"
            value={value}
            onChange={onChange}
          />
        );
      },
    },
    textAlign: {
      label: getLabel('56275', '对齐'),
      labelSpan: 6,
      itemType: 'CUSTOM',
      groupId: Group.Typography,
      customRender: loaderEbdCom('TextAlign'),
    },
    fontStyle: {
      label: getLabel('54504', '样式'),
      labelSpan: 12,
      labelCom: <Label weId="k3jnd0" content={getLabel('55682', '斜体')} itemKey="fontStyle" />,
      itemType: 'CUSTOM',
      groupId: Group.Typography,
      customRender: loaderEbdCom('FontStyle'),
      layoutGroup: 'layout4',
    },
    textDecoration: {
      label: '',
      labelSpan: 0,
      labelCom: (
        <Label weId="srz7v9" content={getLabel('102274', '文本修饰')} itemKey="textDecoration" />
      ),
      itemType: 'CUSTOM',
      groupId: Group.Typography,
      customRender: loaderEbdCom('TextDecoration'),
      layoutGroup: 'layout4',
    },
    moreTypography: {
      label: '',
      labelSpan: 0,
      itemType: 'CUSTOM',
      toggleItems: [
        'letterSpacing',
        'textIndent',
        'textTransform',
        'direction',
        'whiteSpace',
        'textShadows',
      ],
      setHide: (id: string, value: boolean) => !value,
      groupId: Group.Typography,
      customRender: (props: any) => (
        <Toggle weId={`${props.weId || ''}_v8tssh`} {...props}>
          {getLabel('102275', '更多类型选项')}
        </Toggle>
      ),
    },
    letterSpacing: {
      label: '',
      labelSpan: 0,
      labelCom: (
        <Label weId="g3v9ng" content={getLabel('102276', '字母间距')} itemKey="letterSpacing" />
      ),
      itemType: 'CUSTOM',
      groupId: Group.Typography,
      customRender: loaderEbdCom('InputSize', {
        custom: ['px', 'em', 'rem', 'ch', 'vw', 'vh', 'normal'],
      }),
      layoutGroup: 'layout5',
    },
    textIndent: {
      label: '',
      labelSpan: 2,
      labelCom: (
        <Label weId="p2d0qy" content={getLabel('102277', '首行缩进')} itemKey="textIndent" />
      ),
      itemType: 'CUSTOM',
      groupId: Group.Typography,
      customRender: loaderEbdCom('InputSize', { exclude: ['auto'] }),
      layoutGroup: 'layout5',
    },
    textTransform: {
      label: '',
      labelSpan: 0,
      labelCom: (
        <Label weId="q1cpiu" content={getLabel('102278', '文本大小写')} itemKey="textTransform" />
      ),
      itemType: 'CUSTOM',
      colSpan: 16,
      groupId: Group.Typography,
      customRender: loaderEbdCom('TextTransform'),
      layoutGroup: 'layout6',
    },
    direction: {
      label: '',
      labelSpan: 12,
      labelCom: (
        <Label weId="232p6m" content={getLabel('102279', '文本方向')} itemKey="direction" />
      ),
      itemType: 'CUSTOM',
      colSpan: 8,
      groupId: Group.Typography,
      customRender: loaderEbdCom('Direction'),
      layoutGroup: 'layout6',
    },
    whiteSpace: {
      label: getLabel('102280', '空白处理'),
      labelSpan: 6,
      itemType: 'CUSTOM',
      groupId: Group.Typography,
      customRender: loaderEbdCom('WhiteSpace'),
    },
    textShadows: {
      label: getLabel('102281', '文本阴影'),
      itemType: 'CUSTOM',
      labelSpan: 24,
      groupId: Group.Typography,
      customRender: loaderEbdCom('TextShadow'),
      specialStyle: true,
    },
    spacing: {
      label: '',
      labelSpan: 0,
      itemType: 'CUSTOM',
      groupId: Group.Space,
      valueItems: ['margin', 'padding'],
      customRender: loaderEbdCom('Spacing'),
    },
    margin: {
      label: '',
      itemType: 'CUSTOM',
      // specialStyle: true,
    },
    padding: {
      label: '',
      itemType: 'CUSTOM',
      // specialStyle: true,
    },
    width: {
      label: getLabel('54578', '宽度'),
      itemType: 'CUSTOM',
      groupId: Group.Size,
      labelSpan: 12,
      customRender: loaderEbdCom('InputSize'),
      value: 'auto',
      layoutGroup: 'layout1',
      initialValue: 'auto',
      specialStyle: true,
    },
    height: {
      label: getLabel('54597', '高度'),
      itemType: 'CUSTOM',
      groupId: Group.Size,
      labelSpan: 12,
      customRender: (props: any) => <Height weId={`${props.weId || ''}_qo4e5u`} {...props} />,
      value: 'auto',
      layoutGroup: 'layout1',
      initialValue: 'auto',
      specialStyle: true,
    },
    heightAutoFill: {
      label: getLabel('253105', '高度自动填充'),
      itemType: 'CUSTOM',
      groupId: Group.Size,
      labelSpan: 18,
      value: false,
      customRender: (props: any) => (
        <AutoFillSwitch weId={`${props.weId || ''}_thtug1`} {...props} />
      ),
    },
    minWidth: {
      label: getLabel('98859', '最小宽度'),
      itemType: 'CUSTOM',
      groupId: Group.Size,
      labelSpan: 12,
      customRender: loaderEbdCom('InputSize'),
      layoutGroup: 'layout2',
      initialValue: 'auto',
      specialStyle: true,
    },
    minHeight: {
      label: getLabel('181315', '最小高度'),
      itemType: 'CUSTOM',
      groupId: Group.Size,
      labelSpan: 12,
      customRender: loaderEbdCom('InputSize'),
      layoutGroup: 'layout2',
      initialValue: 'auto',
      specialStyle: true,
    },
    maxWidth: {
      label: getLabel('181316', '最大宽度'),
      itemType: 'CUSTOM',
      groupId: Group.Size,
      labelSpan: 12,
      initialValue: 'none',
      specialStyle: true,
      customRender: loaderEbdCom('InputSize', {
        custom: ['px', 'em', 'rem', 'ch', 'vw', 'vh', 'none'],
        placeholder: 'none',
      }),
      layoutGroup: 'layout3',
    },
    maxHeight: {
      label: getLabel('114912', '最大高度'),
      itemType: 'CUSTOM',
      groupId: Group.Size,
      labelSpan: 12,
      specialStyle: true,
      initialValue: 'none',
      customRender: loaderEbdCom('InputSize', {
        custom: ['px', 'em', 'rem', 'ch', 'vw', 'vh', 'none'],
        placeholder: 'none',
      }),
      layoutGroup: 'layout3',
    },
    overflow: {
      label: getLabel('181317', '溢出处理'),
      itemType: 'CUSTOM',
      groupId: Group.Size,
      customRender: loaderEbdCom('Overflow'),
    },
    objectPosition: {
      label: '',
      itemType: 'CUSTOM',
    },
    objectFit: {
      label: getLabel('181318', '显示适应'),
      itemType: 'CUSTOM',
      groupId: Group.Size,
      valueItems: ['objectFit', 'objectPosition'],
      customRender: loaderEbdCom('ObjectFit'),
    },
    position: {
      label: getLabel('181319', '定位类型'),
      itemType: 'CUSTOM',
      groupId: Group.Position,
      valueItems: ['position', 'inset', 'zIndex'],
      customRender: loaderEbdCom('Position'),
    },
    morePosition: {
      label: '',
      labelSpan: 0,
      itemType: 'CUSTOM',
      toggleItems: ['float', 'clear'],
      setHide: (id: string, value: boolean) => !value,
      groupId: Group.Position,
      customRender: (props: any) => (
        <Toggle weId={`${props.weId || ''}_v8tssh`} {...props}>
          {getLabel('181320', '浮动和清除')}
        </Toggle>
      ),
    },
    float: {
      label: getLabel('147354', '浮动'),
      itemType: 'CUSTOM',
      groupId: Group.Position,
      customRender: loaderEbdCom('Float'),
    },
    clear: {
      label: getLabel('181321', '清除浮动'),
      itemType: 'CUSTOM',
      groupId: Group.Position,
      customRender: loaderEbdCom('Clear'),
    },
    borderRadius: {
      label: getLabel('54599', '圆角'),
      itemType: 'CUSTOM',
      groupId: Group.Border,
      valueItems: [
        'borderRadius',
        'borderTopLeftRadius',
        'borderTopRightRadius',
        'borderBottomRightRadius',
        'borderBottomLeftRadius',
      ],
      customRender: loaderEbdCom('BorderRadiusNew'),
    },
    borderTopLeftRadius: {
      label: '',
      itemType: 'CUSTOM',
    },
    borderTopRightRadius: {
      label: '',
      itemType: 'CUSTOM',
    },
    borderBottomRightRadius: {
      label: '',
      itemType: 'CUSTOM',
    },
    borderBottomLeftRadius: {
      label: '',
      itemType: 'CUSTOM',
    },
    border: {
      label: getLabel('54598', '边框'),
      labelSpan: 24,
      itemType: 'CUSTOM',
      groupId: Group.Border,
      valueItems: ['border', 'borderLeft', 'borderTop', 'borderRight', 'borderBottom'],
      customRender: loaderEbdCom('BorderNew'),
    },
    borderLeft: {
      label: '',
      itemType: 'CUSTOM',
    },
    borderTop: {
      label: '',
      itemType: 'CUSTOM',
    },
    borderRight: {
      label: '',
      itemType: 'CUSTOM',
    },
    borderBottom: {
      label: '',
      itemType: 'CUSTOM',
    },
    imageAndGradient: {
      label: getLabel('102292', '图片和渐变色'),
      labelCom: (
        <Label weId="tw5h33" content={getLabel('102292', '图片和渐变色')} itemKey="background" />
      ),
      labelSpan: 0,
      itemType: 'CUSTOM',
      groupId: Group.Background,
      customRender: loaderEbdCom('Backgrounds'),
      specialStyle: true,
    },
    backgroundColor: {
      label: getLabel('55013', '颜色'),
      itemType: 'CUSTOM',
      groupId: Group.Background,
      otherParams: {
        style: { maxWidth: '100%' },
      },
      customRender: (props: any) => {
        const { value, onChange } = props;
        return (
          <CorsComponent
            weId={`${props.weId || ''}_zw0xw7`}
            app="@weapp/ebdcoms"
            compName="ColorPickerTheme"
            value={value}
            onChange={onChange}
          />
        );
      },
    },
    backgroundClip: {
      label: getLabel('181322', '裁剪'),
      itemType: 'CUSTOM',
      groupId: Group.Background,
      customRender: loaderEbdCom('BackgroundClip'),
    },
    opacity: {
      label: getLabel('102267', '不透明度'),
      labelSpan: 7,
      itemType: 'CUSTOM',
      groupId: Group.Effects,
      customRender: loaderEbdCom('Opacity'),
    },
    boxShadows: {
      label: getLabel('54602', '阴影'),
      itemType: 'CUSTOM',
      labelSpan: 24,
      groupId: Group.Effects,
      customRender: loaderEbdCom('BoxShadow'),
      specialStyle: true,
    },
    filter: {
      label: getLabel('142328', '滤镜'),
      itemType: 'CUSTOM',
      labelSpan: 24,
      groupId: Group.Effects,
      customRender: loaderEbdCom('Filters'),
    },
    cursor: {
      label: getLabel('181323', '光标'),
      itemType: 'CUSTOM',
      groupId: Group.Effects,
      customRender: loaderEbdCom('Cursor'),
    },
  };
  return itemsFilter(items);
}

function getLayoutItem(itemKey: string, item: SingleItemProps) {
  const {
    label, labelSpan, groupId, layoutGroup, labelCom,
  } = item;
  const labelNode = label ? (
    <Label key="itemKey" weId={`9q1w9i_${itemKey}`} content={label} itemKey={itemKey} />
  ) : null;
  const layoutItem: FormLayoutProps = {
    label: labelNode as any,
    groupId,
    labelSpan,
    layoutGroup,
    id: itemKey,
    items: [itemKey],
    hide: false, // 公共组件加了判断，不加该字段，组件无法显示
  };

  if (typeof layoutItem.labelSpan === 'undefined') {
    if (item.itemType === 'SWITCH') {
      layoutItem.labelSpan = 20;
    } else {
      layoutItem.labelSpan = 6;
    }
  }

  if (labelCom) {
    layoutItem.label = label;
  }

  return layoutItem;
}

export function getLayout(items: FormItemProps): FormLayoutType[] {
  const layout: FormLayoutType[] = [];
  let count = 0;

  Object.keys(items).forEach((itemKey: string) => {
    const item = getLayoutItem(itemKey, items[itemKey]);

    if ('layoutGroup' in items[itemKey]) {
      count = 0;

      layout.forEach((layItem) => {
        if (layItem[0].layoutGroup === item.layoutGroup) {
          layItem.push(item);
        } else {
          count += 1;
        }
      });

      if (count === layout.length) {
        layout.push([item]);
      }
    } else {
      layout.push([item]);
    }
  });

  return layout;
}

// eslint-disable-next-line max-len
export const getGroups = (styleForm: StyleFormData, groupFilter: (val: any[]) => any[] = (v) => v) => () => {
  let groups = [
    {
      id: Group.Layout,
      title: getLabel('90422', '布局'),
      custom: false,
      visible: true,
    },
    {
      id: Group.Space,
      title: (<SpacingGroupTitle weId="67vna9" styleForm={styleForm} />) as any,
      custom: false,
      visible: true,
    },
    {
      id: Group.Size,
      title: getLabel('55012', '大小'),
      custom: false,
      visible: true,
    },
    {
      id: Group.Position,
      title: getLabel('179216', '定位'),
      custom: false,
      visible: true,
    },
    {
      id: Group.Typography,
      title: getLabel('102282', '排版'),
      custom: false,
      visible: true,
    },
    {
      id: Group.Background,
      title: getLabel('54525', '背景'),
      custom: false,
      visible: true,
    },
    {
      id: Group.Border,
      title: getLabel('54598', '边框'),
      custom: false,
      visible: true,
    },
    {
      id: Group.Effects,
      title: getLabel('55756', '其他'),
      custom: false,
      visible: true,
    },
  ];
  groups = groupFilter(groups);
  return groups;
};

export const references = {
  display: ['spacing'],
  flexDirection: ['alignItems', 'justifyContent', 'alignContent'],
  flexWrap: ['alignContent'],
  position: ['float', 'clear'],
  margin: ['spacing'],
  padding: ['spacing'],
  objectPosition: ['objectFit'],
  inset: ['position'],
  zIndex: ['position'],
  borderLeft: ['border'],
  borderTop: ['border'],
  borderRight: ['border'],
  borderBottom: ['border'],
  borderTopLeftRadius: ['borderRadius'],
  borderTopRightRadius: ['borderRadius'],
  borderBottomRightRadius: ['borderRadius'],
  borderBottomLeftRadius: ['borderRadius'],
  heightAutoFill: ['height'],
};

export default {};
