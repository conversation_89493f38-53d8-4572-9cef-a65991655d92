import { compare } from 'specificity';
import { prefixCls } from '../../../constants';
import { IComData } from '../../../core/types';
import cssSupports from '../../../utils/polyfill/cssSupports';
import { CssProperty } from './types';

/** 支持继承的css属性 */
const inheritedProperties = [
  'fontSize',
  'fontWeight',
  'fontFamily',
  'color',
  'lineHeight',
  'textAlign',
  'textIndent',
  'textTransform',
  'whiteSpace',
];

/** 需要转换格式的css属性 */
const transformProperties = ['margin', 'padding'];

/** 属性值为对象的情况下，存在一个主字段，主字段更像，相关联的其他字段也将更新 */
export const refProperties: Record<string, string[]> = {
  objectFit: ['objectPosition'],
  borderRadius: [
    'borderTopLeftRadius',
    'borderTopRightRadius',
    'borderBottomRightRadius',
    'borderBottomLeftRadius',
  ],
  border: ['borderTop', 'borderRight', 'borderBottom', 'borderLeft'],
  textShadows: ['textShadow'],
  position: ['inset', 'zIndex'],
};

/** 是否为paper元素 */
const isPaper = (el: HTMLElement) => el.classList.contains(`${prefixCls}-paper`);

/**
 * comid开头的样式类，组件设置过容器样式会有此类 (comId模式)
 * 是否包含sl-开头的类，组件设置过样式会添加此类 (sl-模式)
 */
const hasStyledClass = (className: string) => className.indexOf('#') > -1 || className.indexOf('sl-') > -1;

/** 是否包含组件公共样式 */
const hasCommonCompClass = (className: string) => className.indexOf('ebdcoms-com') > -1;

/** 是否为可继承属性 */
export const isInherited = (propName: string) => inheritedProperties.indexOf(propName) > -1;

/** 组件是否设置过样式，设置过的会有sl-开头的类 */
const isStyledElement = (el: HTMLElement) => hasStyledClass(el.classList.value);

/** 将驼峰的css属性转为css style中的属性 */
const toStyleName = (name: string) => name.replace(/([A-Z])/g, '-$1').toLowerCase();

const rgbaRegEXp = /^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+\.{0,1}\d*))?\)$/;
/** 将rgba颜色转为hex */
const rgba2hex = (value: string) => value.replace(
  /rgba?\([^)]+\)/g,
  (rgba: string) => `#${rgba
    .match(rgbaRegEXp)!
    .slice(1)
    .map((n, i) => (i === 3 ? Math.round(parseFloat(n) * 255) : parseFloat(n))
      .toString(16)
      .padStart(2, '0')
      .replace('NaN', ''))
    .join('')}`,
);

/** 将3位的hexcolor转成6位 */
const hexcolor3to6 = (value: string) => value.replace(/#([\da-f]+)/g, (replaceValue: string, matchValue: string) => {
  if (matchValue.length === 3) {
    const hexcolor = matchValue
      .split('')
      .map((v) => v + v)
      .join('');

    return `#${hexcolor}`;
  }

  return replaceValue;
});

/** 解析从规则中取出来的值（这个值可能会被浏览器解析成另外的规则） */
const parseValue = (value: string) => {
  // 对于非字符串的属性值，统一转为string，如opacity
  if (typeof value !== 'string') {
    return String(value);
  }
  // 组件中的值可能存在前后空格，统一处理，消除差异
  value = value.trim();

  // 统一处理属性值中的","格式，浏览器的格式为xx, xx，组件的传值可能缺少空格，如xx,xx
  if (value.indexOf(',') > -1) {
    value = value
      .split(',')
      .map((v) => v.trim())
      .join(', ');
  }

  if (value.indexOf('#') > -1) {
    return hexcolor3to6(value);
  }

  // 通过style.getPropertyValue获取的颜色为rgb，配置中设置的为hex，故需要将其转为相同进制进行比较
  if (value.indexOf('rgb') > -1) {
    return rgba2hex(value);
  }

  return value;
};

/**
 * 解析类似于margin padding的缩略值
 * 如margin：10px 20px; -> margin: 10px 20px 10px 20px
 * 避免 isStyledRule 中判断 hasStyled 时异常
 */
const transformStyleValue = (value: string, propName: string) => {
  let result = '';

  if (transformProperties.includes(propName)) {
    const values = value.split(' ');

    switch (values.length) {
      case 1:
        result = `${values[0]} ${values[0]} ${values[0]} ${values[0]}`;
        break;
      case 2:
        result = `${values[0]} ${values[1]} ${values[0]} ${values[1]}`;
        break;
      case 3:
        result = `${values[0]} ${values[1]} ${values[2]} ${values[1]}`;
        break;
      case 4:
        result = value;
        break;

      default:
        break;
    }
  } else {
    result = value;
  }

  // css属性值包含空格的场景，getPropertyValue返回的样式值首位两端存在双引号
  // 需去除样式首位两端的引号，否则会影响isEffectiveRule方法的比较
  return result.replace(/^"|"$/g, '');
};

/** 样式规则是否为生效的样式规则，即改属性的值与生效的值是一致的 */
const isEffectiveRule = (rule: CSSStyleRule, propName: string, value?: string) => {
  const styleValue = transformStyleValue(
    rule.style.getPropertyValue(toStyleName(propName)),
    propName,
  );
  const isEmptyValue = typeof value === 'undefined' || value === '';
  // 这个属性是否设置过值, 如果value与styleValue不等，则说明该rule的样式优先级不够，未应用上
  const hasStyled = !!styleValue && (isEmptyValue || parseValue(styleValue) === parseValue(value!));

  // 特殊处理border属性
  // 设置border-left后，getPropertyValue获取的border值为空
  let hasRefItemStyled = false; // border对应的其他属性是否设置过值
  if (propName === 'border' && styleValue === '') {
    const refItemKeys = refProperties[propName] || [];
    // eslint-disable-next-line max-len
    const refItemStyleVals = refItemKeys.map((key) => rule.style.getPropertyValue(toStyleName(key)));
    // 其他属性有值即为设置过
    hasRefItemStyled = refItemStyleVals.findIndex((val) => !!val) > -1;
  }

  return hasStyled || hasRefItemStyled;
};

type EffectiveRule = {
  rule: CSSStyleRule;
  /** 是否设置过容器样式 */
  styled: boolean;
};

const getEffectiveRule = (rules: CSSStyleRule[], propName: string, value?: string) => {
  const _rules: EffectiveRule[] = [];

  rules.forEach((rule) => {
    const effective = isEffectiveRule(rule, propName, value);
    // 是否为容器样式的类名
    let styled = false;

    if (effective) {
      styled = hasStyledClass(rule.selectorText);

      _rules.push({ rule, styled });
    }
  });

  // 取样式优先级最高的那个，这里不考虑!impoartant的情况
  const sorted = _rules.sort((s1, s2) => {
    // compare直接收单个选择器，不支持xx,xx的形式
    // 所以需要将这种组合的选择器分割，取优先级最高的，单独再进行比较
    const [selectorText1] = s1.rule.selectorText.split(',').sort(compare).reverse();
    const [selectorText2] = s2.rule.selectorText.split(',').sort(compare).reverse();

    return compare(selectorText1, selectorText2);
  });

  return sorted.pop();
};

/** 样式规则中是否设置过该属性的样式 */
const isStyledRule = (rule: CSSStyleRule, propName: string, value?: string) => {
  const isStyledClass = hasStyledClass(rule.selectorText);

  return isStyledClass && isEffectiveRule(rule, propName, value);
};
/** 获取设置过propName属性的样式规则 */
const getStyledRule = (rules: CSSStyleRule[], propName: string, value?: string) => {
  const styledRules = rules.filter((rule) => isStyledRule(rule, propName, value));
  // 取样式优先级最高的那个，这里不考虑!impoartant的情况
  const sortedRules = styledRules.sort((s1, s2) => compare(s1.selectorText, s2.selectorText));

  return sortedRules.pop()!;
};

const getStyles = (el: HTMLElement, propNames: string[]): Record<string, string> => {
  const computedStyle = getComputedStyle(el);

  return propNames.reduce((style: any, propName: string) => {
    const name = toStyleName(propName);
    // 使用了css变量的需要通过getPropertyValue获取实际的值
    const _value = computedStyle.getPropertyValue(name);
    const value = parseValue(_value);

    if (value) {
      style[propName] = value;
    }

    return style;
  }, {});
};

/** 获取元素样式属性的初始化值 */
const getInitialStyles = (el: HTMLElement, propNames: string[]): Record<string, string> => {
  const cloned = el.cloneNode(true) as HTMLElement;
  const styledClsName = Array.from(cloned.classList).find(hasStyledClass);
  let styles = null;

  // 样式作用会通过组件id方式去作用，所以获取初始值的时候先把克隆组件的id给置为无效id
  cloned.setAttribute('id', 'demo_id');
  if (styledClsName) {
    cloned.classList.remove(styledClsName);
  }

  cloned.style.display = '';
  cloned.innerHTML = '';
  el.parentElement?.appendChild(cloned);
  styles = getStyles(cloned, propNames);
  cloned.remove();

  return styles;
};

const getCssRules = (el: any): CSSStyleRule[] => {
  const sheets = Array.from(document.styleSheets);
  const cssRules: CSSRule[] = [];

  el.matches = el.matches
    || el.webkitMatchesSelector
    || el.mozMatchesSelector
    || el.msMatchesSelector
    || el.oMatchesSelector;

  sheets.forEach((sheet) => {
    try {
      const rules = sheet.rules || sheet.cssRules;

      Array.from(rules).forEach((rule: CSSRule) => {
        if (el.matches((rule as any).selectorText)) {
          cssRules.push(rule);
        }
      });
    } catch (error) {
      // 可能存在跨域的样式，会抛出异常
    }
  });

  return cssRules as CSSStyleRule[];
};

/** 根节点的可继承样式 */
const rootInheritedStyles = (() => {
  const root = document.getElementById('root') as HTMLElement;
  const rules = getCssRules(root);
  const computedStyle = getComputedStyle(root);
  const inheritedStyles: Record<string, string> = {
    // rules中不存在，但是需要内置的默认样式
    fontWeight: '400',
  };

  inheritedProperties.forEach((propName) => {
    const name = toStyleName(propName);
    const propValue = computedStyle.getPropertyValue(name);
    const value = parseValue(propValue);
    const rule = rules.find((r) => !!r.style.getPropertyValue(name));

    if (rule) {
      inheritedStyles[propName] = parseValue(value);
    }
  });

  return inheritedStyles;
})();

window.getCssRules = getCssRules;
export default class CssRule {
  el: HTMLElement;

  com: IComData;

  propNames: string[] = [];

  parents: HTMLElement[] = [];

  properties: Record<string, CssProperty> = {};

  /** 所有属于继承类(组件的样式，body的样式)的样式规则 */
  inheritedProperties: Record<string, CssProperty> = {};

  cssRules: CSSStyleRule[] = [];

  parentsCssRules: CSSStyleRule[][] = [];

  /** 组件样式的初始值 */
  initialStyles: Record<string, string>;

  constructor(com: IComData, el: HTMLElement, propNames: string[]) {
    this.el = el;
    this.com = com;
    this.propNames = propNames;
    this.initialStyles = getInitialStyles(el, propNames);
  }

  init(initialValues: Record<string, string>) {
    this.parents = this.getParents();
    this.parentsCssRules = this.parents.map(getCssRules);
    this.properties = this.getProperites(initialValues);
    this.inheritedProperties = this.getInheritProperites();
  }

  getValues() {
    const props = this.properties;

    return Object.keys(props).reduce((values: any, propName: string) => {
      values[propName] = props[propName].value;

      return values;
    }, {});
  }

  getInheritProperites() {
    const properties = this.getProperites({});
    const inheritProps: Record<string, CssProperty> = {};

    Object.keys(properties).forEach((propName) => {
      const prop = properties[propName];

      if (prop.status === 'inherit') {
        inheritProps[propName] = prop;
      }
    });

    return inheritProps;
  }

  getProperites(values: Record<string, string>) {
    const computedStyle = getComputedStyle(this.el);
    const cssRules = getCssRules(this.el);

    return this.propNames
      .map((propName) => this.getProperty(propName, values[propName], computedStyle, cssRules))
      .reduce((properties, property: CssProperty, index: number) => {
        const propName = this.propNames[index];

        properties[propName] = property;

        return properties;
      }, {} as Record<string, CssProperty>);
  }

  getProperty(
    propName: string,
    changedValue: string,
    computedStyle = getComputedStyle(this.el),
    cssRules = getCssRules(this.el),
  ) {
    const { tagName } = this.el;
    const styleName = toStyleName(propName);
    const computedValue = computedStyle.getPropertyValue(styleName);

    const value = cssSupports(styleName, changedValue) ? changedValue : parseValue(computedValue);
    const styledRule = getEffectiveRule(cssRules, propName, value);
    let prop: CssProperty = {
      value,
      status: 'initial',
      valueFrom: '',
    };

    if (!styledRule && isInherited(propName)) {
      const inheritedProp = this.getInheritedProperty(propName, changedValue);

      prop = { ...prop, ...inheritedProp };
    } else if (styledRule) {
      const { selectorText } = styledRule.rule;

      // 继承自reset样式，如img { max-width: 100%; }，即以标签名为选择器的样式规则
      if (styledRule.styled) {
        if (isInherited(propName)) {
          // 获取重置后的valueFrom
          const _prop = this.getInheritedProperty(propName);

          prop.valueFrom = _prop.valueFrom;
        }

        prop.status = 'value';
      } else if (selectorText.toLowerCase() === tagName.toLowerCase()) {
        prop.status = 'inherit';
        prop.valueFrom = `All ${tagName}`;
      } else if (hasCommonCompClass(selectorText)) {
        prop.status = 'inherit';
        prop.valueFrom = 'All Component';
      } else {
        prop.status = 'inherit';
        prop.valueFrom = `All ${this.com.type}`;
      }
    }

    return prop;
  }

  setProperty(propName: string, property: CssProperty) {
    this.properties[propName] = property;
  }

  getParents() {
    let { el } = this;
    const parents: HTMLElement[] = [];

    if (isPaper(el)) {
      return parents;
    }

    while (el.parentElement && !isPaper(el.parentElement)) {
      if (isStyledElement(el.parentElement)) {
        parents.push(el.parentElement);
      }

      el = el.parentElement;
    }

    return parents;
  }

  getInheritedProperty(propName: string, changedValue?: string): CssProperty {
    const prop: CssProperty = {
      status: 'inherit',
      valueFrom: '',
      value: '',
    };
    let styledRule: CSSStyleRule | null = null;

    this.parents.some((parentEle, index) => {
      const parentCssRules = this.parentsCssRules![index];

      styledRule = getStyledRule(parentCssRules, propName, changedValue);

      return !!styledRule;
    });

    if (!styledRule) {
      const inheritProp = this.inheritedProperties[propName];

      if (inheritProp && inheritProp.valueFrom) {
        prop.value = inheritProp.value;
        prop.valueFrom = inheritProp.valueFrom;
      } else if (rootInheritedStyles[propName]) {
        prop.value = rootInheritedStyles[propName];
        prop.valueFrom = 'Body(All Pages)';
      } else {
        prop.status = 'initial';
      }
    } else {
      const styleName = toStyleName(propName);

      prop.value = (styledRule as CSSStyleRule).style.getPropertyValue(styleName);
      // getPropertyValue的取值与组件用的值存在不一致的情况，需要转化下
      // 如通过getPropertyValue取的颜色为rgb的，但是组件需要的为hex方式的
      prop.value = parseValue(prop.value);
      prop.valueFrom = 'Parent';
    }

    return prop;
  }
}
