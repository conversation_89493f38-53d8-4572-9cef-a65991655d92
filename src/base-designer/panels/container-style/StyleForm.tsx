import {
  FormDatas,
  FormItemProps,
  FormLayoutProps,
  FormLayoutType,
  FormStore,
  Spin,
} from '@weapp/ui';
import { GroupProps } from '@weapp/ui/lib/components/form/types';
import {
  cloneDeep, eventEmitter, isEqual, isPlainObject, memoize,
} from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import Form from '../../../common/form';
import { FormInitAllDatasEx, FormItemPropsEx } from '../../../common/form/types';
import { EVNET_COM_STYLE_EXPAND, EVNET_COM_STYLE_SHRINK, prefixCls } from '../../../constants';
import getDefaultOpts from '../../../core/loader/com/getDefaultOpts';
import { ClientType, IComData } from '../../../core/types';
import { EventEmitter } from '../../../core/utils';
import setTimeoutOnce from '../../../utils/setTimeoutOnce';
import {
  getGroups, getItems, getLayout, Group, references,
} from './form';
import './index.less';
import CssRule from './Rule';
import { StyleConfigData, StyleType } from './types';
import { ContainerStyleStore } from './useStore';

function transformStyle(changes: any) {
  const clonedChanges = cloneDeep(changes);
  const baseStyle = clonedChanges.styles?.find(
    (style: any) => style.id === StyleType.BASIC_CONTAINER,
  );
  // @ts-ignore
  const refConfig = this.getClonedRefConfig();
  const refBaseStyle = refConfig.styles?.find(
    (style: any) => style.id === StyleType.BASIC_CONTAINER,
  );

  if (
    baseStyle?.customStyle?.container?.heightAutoFill !== undefined
    || refBaseStyle?.customStyle?.container?.heightAutoFill !== undefined
  ) {
    // eslint-disable-next-line max-len
    baseStyle.customStyle.container.heightAutoFill = !!refBaseStyle?.customStyle?.container?.heightAutoFill;
  }
  return clonedChanges;
}

const transform = {
  toMobile: transformStyle,
  toPC: transformStyle,
};

interface StyleFormProps extends React.Attributes {
  selectedCom: IComData;
  selectedComDom: HTMLElement;
  events?: EventEmitter;
  clientType?: ClientType;
  onChange: (value: { styles: StyleConfigData[] }, comId: string, otherParams?: any) => void;
  groupFilter?: (val: Group[]) => Group[];
  itemsFilter?: (items: FormItemProps) => Promise<FormItemProps>;
  getBasicContainerStyle?: (styles: StyleConfigData[]) => StyleConfigData[];
}

interface StyleFormsStates {
  loading: boolean;
  data: FormDatas;
  oldStyles: StyleConfigData[];
  nodeDOM: HTMLElement | null;
}
@observer
@inject('containerStyleStore')
class StyleForm extends PureComponent<
  StyleFormProps & { containerStyleStore: ContainerStyleStore },
  StyleFormsStates
> {
  constructor(props: any) {
    super(props);

    this.state = {
      loading: true,
      data: (toJS(this.getBasicStyles()[0]?.customStyle?.container) || {}) as FormDatas,
      oldStyles: [],
      nodeDOM: null,
    };
  }

  form: FormStore = new FormStore();

  selectedComId: string = '';

  items: FormItemPropsEx = {};

  layout: FormLayoutType[] = [];

  groups: GroupProps[] = [];

  initialDatas = {
    items: this.items,
    layout: this.layout,
    groups: this.groups,
  } as unknown as FormInitAllDatasEx;

  store: ContainerStyleStore = this.props.containerStyleStore; // 存放自定义渲染组件Swicth组件的实例

  nodeCom: IComData | null = null;

  componentDidMount() {
    const { selectedComDom, selectedCom, clientType = 'PC' } = this.props;

    getDefaultOpts(selectedCom, clientType, true).then((Design: any) => {
      const { containerStyleSelector } = Design?.defaultOpts || {};
      const nodeDOM: any = containerStyleSelector
        ? selectedComDom.querySelector(containerStyleSelector)
        : selectedComDom;

      this.setState({ nodeDOM }, this.init);
    });

    // 全部收缩、展开容器内的分组样式
    eventEmitter.on('@weapp/designer', EVNET_COM_STYLE_EXPAND, this.expandAllGroups);
    eventEmitter.on('@weapp/designer', EVNET_COM_STYLE_SHRINK, this.shrinkAllGroups);
  }

  componentWillUnmount() {
    eventEmitter.off('@weapp/designer', EVNET_COM_STYLE_EXPAND, this.expandAllGroups);
    eventEmitter.off('@weapp/designer', EVNET_COM_STYLE_SHRINK, this.shrinkAllGroups);
  }

  expandAllGroups = () => {
    const { groups } = this.form;
    const activeKeys: string[] = [];

    groups.forEach((group) => {
      activeKeys.push(group.id);
    });

    const newGroup = groups.map((group) => ({
      ...group,
      visible: true,
      // batchEvent强制更新分组状态
      batchEvent: `expand_${Math.random() * 10000}`,
    }));

    this.form.setState({
      groups: newGroup,
      activeKey: activeKeys,
    });
  };

  shrinkAllGroups = () => {
    const { groups } = this.form;

    const newGroup = groups.map((group) => ({
      ...group,
      visible: false,
      // batchEvent强制更新分组状态
      batchEvent: `shrink_${Math.random() * 10000}`,
    }));

    this.form.setState({
      groups: newGroup,
      activeKey: [],
    });
  };

  init = () => {
    const { selectedCom, events } = this.props;

    this.nodeCom = selectedCom;
    this.selectedComId = selectedCom?.id || '';

    this.initForm();

    this.initStore();

    if (this.selectedComId) {
      // 初始化样式对象
      const data = this.getStyleObj();

      const { heightAutoFill } = (this.getBasicStyles()[0]?.customStyle?.container as any) || {};

      this.updateFormDatas({ ...data, heightAutoFill });
    }
    events?.on('unredo', this.unredo);
  };

  getSnapshotBeforeUpdate(preProps: StyleFormProps) {
    const { selectedCom: preSelectedCom } = preProps;
    const { selectedCom } = this.props;
    const preBasicStyle = preSelectedCom?.config?.styles?.find(
      (el: any) => el.category === StyleType.BASIC,
    );
    const basicStyle = selectedCom?.config?.styles?.find(
      (el: any) => el.category === StyleType.BASIC,
    );
    // 系统样式中有容器样式，作用时直接同步到流式容器样式内
    if (!isEqual(toJS(preBasicStyle), toJS(basicStyle))) {
      const styles = toJS(this.getBasicStyles()[0]?.customStyle?.container) || {};
      this.setState({
        data: styles,
      });
    }
  }

  unredo = () => {
    // 需要回撤、撤销改完config再获取数据
    setTimeoutOnce(() => {
      const containerStyle = (toJS(this.getBasicStyles()[0]?.customStyle?.container)
        || {}) as FormDatas;
      const { properties } = this.store.cssRule || { properties: {} };
      const items = getItems() as FormItemProps;

      const restDatas: FormDatas = {};

      Object.keys(items).forEach((itemKey) => {
        if (items[itemKey].valueItems) {
          items[itemKey].valueItems.forEach((_itemKey: string) => {
            const itemValue = containerStyle[_itemKey] || properties[_itemKey]?.value;

            restDatas[_itemKey] = itemValue;
          });
        } else {
          restDatas[itemKey] = containerStyle[itemKey] || properties[itemKey]?.value;
        }
      });

      this.setState({ data: restDatas });
    });
  };

  getStyleObj = () => {
    const { data } = this.state;
    const { selectedCom } = this.props;
    let { properties } = this.store.cssRule || { properties: {} };

    if (!selectedCom?.type) {
      properties = {};
    }

    return Object.keys(properties).reduce(
      (datas: any, propName: string) => {
        datas[propName] = properties[propName].value;

        return datas;
      },
      { ...data },
    );
  };

  initStore() {
    const { nodeDOM } = this.state;

    if (nodeDOM && this.nodeCom) {
      const items = getItems() as FormItemProps;
      const { data } = this.state;

      const cssRule = new CssRule(
        this.nodeCom,
        nodeDOM,
        Object.keys(items).filter((item) => !items[item].specialStyle),
      );

      this.store.init({
        cssRule,
        form: this.form,
        triggerFormChange: this.onChange(this.selectedComId),
      });

      cssRule.init(data as Record<string, string>);
    }
  }

  initLayout = (items: FormItemProps) => {
    const { groupFilter } = this.props;
    this.items = items;
    this.layout = getLayout(this.items);

    this.initialDatas = {
      items: this.items,
      layout: this.layout,
      getGroups: getGroups(
        {
          onChange: this.updateDatas,
          getFormDatas: this.form.getFormDatas,
        },
        groupFilter,
      ),
    };

    this.setState({ loading: false });
  };

  initForm() {
    const { itemsFilter } = this.props;
    const { config } = this.nodeCom || {};
    // 是否是网格布局最外层容器
    const isOutsideGridContainer = !config?.flow?.parent && config?.type === 'GridContainer';
    const filterItems = getItems(itemsFilter, isOutsideGridContainer) as Promise<FormItemProps>;
    const isPromiseInstance = filterItems instanceof Promise;
    if (isPromiseInstance) {
      filterItems.then((items) => {
        this.initLayout(items);
      });
    } else {
      this.initLayout(filterItems);
    }
  }

  // eslint-disable-next-line max-len
  customRenderFormSwitch = (CustomFormSwitch: React.ReactElement) => {
    const initialValue = this.store.cssRule!.initialStyles[CustomFormSwitch.props.id];

    return React.cloneElement(CustomFormSwitch, {
      initialValue,
      ...CustomFormSwitch?.props,
      styleData: this.form.getFormDatas(),
      dom: this.state.nodeDOM,
      // 新建样式时，无选中card dom ，传入id来判断是否为新建样式
      domId: this.state.nodeDOM?.getAttribute('id') || this.nodeCom?.id,
    });
  };

  customHide = (col: FormLayoutProps) => {
    const itemKey = col.id;
    const formDatas = this.form.getFormDatas();
    const itemValue = formDatas[itemKey];
    const { toggleItems, setHide } = this.items[itemKey];

    if (toggleItems) {
      toggleItems.forEach((id: string) => {
        this.form.setHide(id, setHide(id, itemValue, formDatas));
      });
    }

    return col;
  };

  getStyles() {
    const { selectedCom } = this.props;
    const { styles } = selectedCom.config;

    return cloneDeep(toJS(styles)) || [];
  }

  getBasicStyles(): [StyleConfigData, number] {
    const { getBasicContainerStyle = (v) => v } = this.props;
    let styles = this.getStyles();
    styles = getBasicContainerStyle(styles);
    const index = styles.findIndex(
      (style: StyleConfigData) => style.category === StyleType.BASIC_CONTAINER,
    );

    return [toJS(styles[index]), index];
  }

  onChange = memoize((comId: string) => (changes?: FormDatas, otherParams?: any) => {
    changes = toJS(changes);

    /**
     * 组件样式更改行高等input组件时，切换组件会失焦触发onChange，改变当前选中组件的样式
     */
    if (comId !== this.selectedComId) return;

    const { onChange } = this.props;
    const { oldStyles } = this.state;

    let styles = this.getStyles();
    const [_basicStyles, index] = this.getBasicStyles();
    let containerStyles = { ...changes };
    const changedKeys = Object.keys(changes || {});
    let basicConatainerStyles = _basicStyles;

    if (changedKeys.length) {
      const changedKey = changedKeys[0];
      const prop = this.store.cssRule!.properties[changedKey];

      if (prop && prop.status === 'inherit' && prop.value === changes![changedKey]) {
        containerStyles![changedKey] = '';
      }

      // 将如{spacing: {margin: xxx, padding: xxx}}这种结构的值解构
      // 便于初始化(transform.getData)的时候能够取到里面margin，padding的值
      if (isPlainObject(changes![changedKey])) {
        const changedValue: any = changes![changedKey];

        changes = changedValue;
        containerStyles = changedValue;
      }
    }

    if (!basicConatainerStyles) {
      basicConatainerStyles = {
        id: StyleType.BASIC_CONTAINER,
        category: StyleType.BASIC_CONTAINER,
        customStyle: {},
        useCustom: true,
      };
      styles = [...styles, basicConatainerStyles];
    }

    basicConatainerStyles.customStyle = {
      ...basicConatainerStyles.customStyle,
      container: {
        ...(basicConatainerStyles.customStyle?.container as any),
        ...containerStyles,
      },
    };
    if (index >= 0) {
      styles[index] = basicConatainerStyles;
    }

    if (!isEqual(oldStyles, styles)) {
      const basic = styles.find((el: any) => el.category === StyleType.BASIC);
      if (basic?.libStyle && !basic?.customStyle) {
        // 同步系统样式（操作：先选系统样式，然后改变容器样式，然后删除系统样式，需要保留容器和基础样式）
        basic.customStyle = {
          ...basic.libStyle,
        };
      }
      onChange({ styles }, this.selectedComId, {
        ...otherParams,
        transform,
      });
      this.setState({ oldStyles: [...styles] });
      this.updateFormDatas(changes);
    }
  });

  /** 主动改变item的值 */
  updateDatas = (changes?: FormDatas) => {
    // 改变config中的值
    this.onChange(this.selectedComId)(changes);
    // 改变form内部维护的formdatas
    this.updateFormDatas(changes);
  };

  updateFormDatas = (changes?: FormDatas) => {
    // 改变form内部维护的formdatas
    this.form.updateDatas(changes);

    const data = this.form.getFormDatas();

    this.setState({ data });
  };

  render() {
    const { loading, data } = this.state;
    const { events } = this.props;

    if (loading) {
      return (
        <Spin
          weId={`${this.props.weId || ''}_4mb3fn`}
          className={`${prefixCls}-style-form-loading`}
          spinning
        />
      );
    }

    return (
      <Form
        className={`${prefixCls}-style-form`}
        store={this.form}
        weId={`${this.props.weId || ''}_pfqkww`}
        initialDatas={this.initialDatas}
        references={references}
        customRenderFormSwitch={this.customRenderFormSwitch}
        customHide={this.customHide}
        onChange={this.onChange(this.selectedComId)}
        data={data}
        events={events}
      />
    );
  }
}

export default StyleForm as unknown as ComponentType<StyleFormProps>;
