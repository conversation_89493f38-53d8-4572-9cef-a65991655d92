import { FormDatas, FormStore } from '@weapp/ui';
import React from 'react';
import CssRule from './Rule';

export const Context = React.createContext<ContainerStyleStore | null>(null);

export const useContainerStyle = () => React.useContext(Context);

export const { Provider } = Context;

type Datas = {
  form: FormStore;
  cssRule: CssRule;
  triggerFormChange: (formDatas: FormDatas) => void;
};
export class ContainerStyleStore {
  form: FormStore | null = null;

  cssRule: CssRule | null = null;

  triggerFormChange: (formDatas: FormDatas) => void = () => {};

  init(datas: Datas) {
    this.form = datas.form;
    this.cssRule = datas.cssRule;

    this.triggerFormChange = datas.triggerFormChange;
  }
}

export default new ContainerStyleStore();
