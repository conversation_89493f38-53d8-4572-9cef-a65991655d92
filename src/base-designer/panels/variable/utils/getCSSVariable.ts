import { isEmpty } from '@weapp/utils';

const cssVariable: Record<string, string> = {};

const getRealValue = (key: string, computedStyle: any): string => {
  const value = computedStyle.getPropertyValue(key);

  // eslint-disable-next-line no-useless-escape
  const varValue = /^var\((--[\da-z\-]+)\)$/.exec(value);
  if (varValue) {
    return getRealValue(varValue[1], computedStyle);
  }

  // eslint-disable-next-line no-useless-escape
  const numValue = /^(\d+(.\d+)?)px$/.exec(value);
  if (numValue) {
    return numValue[1];
  }

  return value;
};

/**
 * @param keys 需要配置的样式名称
 * @returns
 */
export default function getCSSVariable(keys?: string[]) {
  if (isEmpty(cssVariable)) {
    const root = document.querySelector('html');
    if (!root) return {};
    const computedStyle = window.getComputedStyle(root);
    if (keys?.length) {
      keys.forEach((key: string) => {
        cssVariable[key] = getRealValue(key, computedStyle);
      });
    }
  }
  return cssVariable;
}
