import { inspectorStyleId } from '../../../../constants/inspector';

export { default as getCSSVariable } from './getCSSVariable';

type STYLEElement = HTMLElement & { type?: string } | null

export const upsertStyle = (styleVariable: Record<string, string>) => {
  let inspectorStyle: STYLEElement = document.getElementById(inspectorStyleId);
  if (!inspectorStyle) {
    inspectorStyle = document.createElement('style');
    inspectorStyle.type = 'text/css';
    inspectorStyle.id = inspectorStyleId;
    document.getElementsByTagName('head').item(0)?.appendChild(inspectorStyle);
  }
  const innerText = `:root{${Object
    .keys(styleVariable)
    .map((key) => {
      const wittSuffix = ['--hd', '--font-size-md'];
      if (!styleVariable[key] || styleVariable[key] === 'default') return '';
      if (wittSuffix.includes(key)) {
        return `${key}:${styleVariable[key]}px!important;`;
      }
      return `${key}:${styleVariable[key]}!important;`;
    })
    .join('')
  }}`;
  inspectorStyle.innerHTML = innerText;
};

// export const getVariableCodeTpl = (styleVariable: FormDatas) => `
//         let inspectorStyle: STYLEElement = document.getElementById('${inspectorStyleId}');
//         if (!inspectorStyle) {
//           inspectorStyle = document.createElement('style');
//           inspectorStyle.type = 'text/css';
//           inspectorStyle.id = '${inspectorStyleId}';
//           document.getElementsByTagName('head').item(0)?.appendChild(inspectorStyle);
//         }
//         const innerText = ':root{${Object
//     .keys(styleVariable)
//     .map((key) => {
//       const wittSuffix = ['--hd', '--font-size-md'];
//       if (!styleVariable[key] || styleVariable[key] === 'default') return '';
//       if (wittSuffix.includes(key)) {
//         return `${key}:${styleVariable[key]}px!important;`;
//       }
//       return `${key}:${styleVariable[key]}!important;`;
//     })
//     .join('')
// }}';
//         inspectorStyle.innerHTML = innerText;
//       `;
