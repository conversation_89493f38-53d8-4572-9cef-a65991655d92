import { FormItemProps } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React from 'react';

export const getGroups = () => [
  {
    id: 'basic',
    title: getLabel('181281', '基本'),
    custom: false,
    visible: true,
  },
  {
    id: 'secondary',
    title: get<PERSON>abel('181282', '辅助色'),
    custom: false,
    visible: true,
  },
  {
    id: 'neutral',
    title: get<PERSON>abel('181283', '中性色'),
    custom: false,
    visible: true,
  },
  {
    id: 'font',
    title: getLabel('102271', '字体'),
    custom: false,
    visible: true,
  },
];

export const groups = getGroups();

export const getItems = (): FormItemProps => ({
  '--hd': {
    label: getLabel('181285', '基本单位'),
    itemType: 'INPUTNUMBER',
    labelSpan: 7,
    groupId: 'basic',
    suffix: <span>px</span>,
  },
  '--primary': {
    label: getLabel('181286', '主题色'),
    itemType: 'COLORPICKER',
    labelSpan: 7,
    groupId: 'basic',
  },
  '--bubble-color': {
    label: getLabel('181287', '聊天气泡背景'),
    itemType: 'COLORPICKER',
    labelSpan: 7,
    groupId: 'basic',
  },
  '--success': {
    label: getLabel('181288', '成功色'),
    itemType: 'COLORPICKER',
    labelSpan: 7,
    groupId: 'secondary',
  },
  '--warning': {
    label: getLabel('181289', '普通警告色'),
    itemType: 'COLORPICKER',
    labelSpan: 7,
  },
  '--danger': {
    label: getLabel('181290', '强烈警告色'),
    itemType: 'COLORPICKER',
    labelSpan: 7,
    groupId: 'secondary',
  },
  '--info': {
    label: getLabel('181291', '提示色'),
    itemType: 'COLORPICKER',
    labelSpan: 7,
    groupId: 'secondary',
  },
  '--invalid': {
    label: getLabel('181292', '禁用色'),
    itemType: 'COLORPICKER',
    labelSpan: 7,
    groupId: 'secondary',
  },
  '--base-white': {
    label: getLabel('181293', '基础白色'),
    itemType: 'COLORPICKER',
    labelSpan: 7,
    groupId: 'neutral',
  },
  '--base-black': {
    label: getLabel('181294', '基础黑色'),
    itemType: 'COLORPICKER',
    labelSpan: 7,
    groupId: 'neutral',
  },
  '--transparent': {
    label: getLabel('181295', '透明色'),
    itemType: 'COLORPICKER',
    labelSpan: 7,
    groupId: 'neutral',
  },
  '--regular-ff': {
    label: getLabel('102271', '字体'),
    itemType: 'SELECT',
    labelSpan: 7,
    groupId: 'font',
    data: [{ id: 'default', content: getLabel('103013', '默认') }],
  },
  '--font-weight-base': {
    label: getLabel('102272', '字重'),
    itemType: 'SELECT',
    labelSpan: 7,
    groupId: 'font',
    data: [
      { id: 'default', content: getLabel('103013', '默认') },
      { id: '400', content: '400' },
      { id: '500', content: '500' },
    ],
  },
  '--font-size-md': {
    label: getLabel('55012', '大小'),
    itemType: 'INPUTNUMBER',
    labelSpan: 7,
    groupId: 'font',
    suffix: <span>px</span>,
  },
  '--regular-fc': {
    label: getLabel('55013', '颜色'),
    itemType: 'COLORPICKER',
    labelSpan: 7,
    groupId: 'font',
  },
});

export const items: FormItemProps = getItems();
