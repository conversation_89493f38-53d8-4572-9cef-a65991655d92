import { FormDatas } from '@weapp/ui';
import { debounce } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, {
  useCallback, useEffect, useMemo, useState,
} from 'react';
import Form from '../../../common/form/Form';
import { inspectorRSCls } from '../../../constants/inspector';
import { getGroups, getItems } from './constans';
import './index.less';
import store from './store';
import { getCSSVariable, upsertStyle } from './utils';

function Variable(props: any) {
  const [data, setData] = useState({});

  const { items, groups } = useMemo(
    () => ({
      items: getItems(),
      groups: getGroups(),
    }),
    [],
  );

  const initialDatas = useMemo(() => ({ items, groups }), [items, groups]);

  useEffect(() => {
    const cssVariable = getCSSVariable(Object.keys(items));
    const _data = { ...(cssVariable as FormDatas), ...store.styleVariable };
    setData(_data);
    upsertStyle(_data as Record<string, string>);
  }, []);

  useEffect(() => {
    const cssVariable = getCSSVariable();
    const _data = { ...(cssVariable as FormDatas), ...store.styleVariable };
    setData(_data);
    upsertStyle(_data as Record<string, string>);
  }, [store.styleVariable]);

  const onChange = useCallback(
    debounce((changes) => {
      store.updateStyleVariable(changes);
    }, 500),
    [],
  );

  return (
    <div className={`${inspectorRSCls}-variable`}>
      <Form
        weId={`${props.weId || ''}_8vuo1z`}
        initialDatas={initialDatas}
        data={data}
        onChange={onChange}
      />
    </div>
  );
}

export default observer(Variable);
