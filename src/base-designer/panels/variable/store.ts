import { FormDatas } from '@weapp/ui';
import { action } from 'mobx';
import shallowObservable from '../../../utils/shallowObservable';
import { upsertStyle } from './utils';

export class VariableStore {
  @shallowObservable styleVariable: FormDatas = {};

  @action.bound
  updateStyleVariable(changes: FormDatas) {
    let changed = false;
    // 颜色选择器一次操作会onchange两次
    Object.keys(changes).forEach((key) => {
      if (changes[key] !== this.styleVariable[key]) {
        changed = true;
      }
    });
    if (changed) {
      const _styleVariable = { ...this.styleVariable, ...changes };
      this.styleVariable = _styleVariable;
      upsertStyle(this.styleVariable as Record<string, string>);
      //   this.operationRecord.push({ type: 'variable', changes: _styleVariable });
    }
  }

  @action.bound
  resetStyleVariable(changes: FormDatas) {
    this.styleVariable = changes;
    upsertStyle(this.styleVariable as Record<string, string>);
  }
}

export default new VariableStore();
