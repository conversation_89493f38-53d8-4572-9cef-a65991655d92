import { Dialog } from '@weapp/ui';
import {
  cloneDeep, getLabel, isArray, isEmpty,
} from '@weapp/utils';
import { toJS } from 'mobx';
import { ILayoutData } from '../../../types';
import ebdcoms from '../../../utils/ebdcoms';
import { DatasetValue } from '../dataset/types';

const { message } = Dialog;

// 生成数据集id（后端分布式id）
const fetchGenerateId = (count: number): Promise<string[]> => new Promise((resolve) => {
  ebdcoms.asyncExcu('ajax', {
    url: '/api/ebuilder/common/distribution/generateId',
    params: {
      count,
    },
    success: resolve,
    error: () => {
      resolve(['']);
      return true;
    },
  });
});

/** 数据集配置更新 */
export const onDatasetChange = (options: any) => {
  if (isEmpty(options)) return;

  const {
    datasetVal = {},
    type = '',
    datasetVals = [],
    setDatasetVals = () => {},
    userCustom,
  } = options;

  if (userCustom) {
    message({
      type: 'info',
      content: getLabel('151439', '自定义页面不支持修改数据集'),
    });

    return;
  }

  const _datasetVals = [...toJS(datasetVals)];

  if (type === 'add') {
    fetchGenerateId(1).then((ids) => {
      if (ids && ids.length) {
        _datasetVals.push({
          ...datasetVal,
          id: ids[0],
        });
      }

      setDatasetVals(_datasetVals);
    });
  } else if (type === 'edit') {
    const dataIndex = _datasetVals.findIndex((data) => data.id === datasetVal?.id);

    if (!!dataIndex || dataIndex === 0) {
      _datasetVals.splice(dataIndex, 1, datasetVal);
    }

    setDatasetVals(_datasetVals);
  } else if (type === 'delete') {
    const dataIndex = _datasetVals.findIndex((data) => data.id === datasetVal?.id);

    if (!!dataIndex || dataIndex === 0) {
      _datasetVals.splice(dataIndex, 1);
    }

    setDatasetVals(_datasetVals);
  }
};

/** 保存更新数据集数据 */
export const updateDatasetVals = (datas: ILayoutData[], datasetVals: DatasetValue[]) => {
  const _datasetVals = cloneDeep(toJS(datasetVals));

  _datasetVals.map((data: any) => {
    if (data.type === 'add') {
      delete data.id;
      delete data.type;
    }

    return data;
  });

  if (isArray(datas) && datas.length > 0) {
    datas[0].config = {
      ...datas[0].config,
      dataset: _datasetVals,
    };
  }
};

export default {};
