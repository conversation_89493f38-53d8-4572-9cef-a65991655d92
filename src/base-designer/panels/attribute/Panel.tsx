import { Cors<PERSON><PERSON>ponent, FormDatas, Spin } from '@weapp/ui';
import { eventEmitter, getLabel, throttle } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, {
  useCallback, useEffect, useMemo, useState,
} from 'react';
import { EVENT_COM_CONFIG_RELOAD, EVNET_DATASET_UPDATE, prefixCls } from '../../../constants';
import { CoreConfig } from '../../../core/config';
import { UUID } from '../../../utils';
import ebdcoms from '../../../utils/ebdcoms';
import { useDesigner } from '../../hooks';
import useInternal from '../../hooks/useInternal';
import useLazySelectedCom from '../../hooks/useLazySelectedCom';
import useMemoProps from '../../hooks/useMemoProps';
import { DatasetValue } from '../dataset/types';
import './index.less';
import { onDatasetChange as onDataChange } from './utils';

export interface AttributePanelProps extends React.Attributes {
  customSwitchProps?: Record<string, any>;
  [key: string]: any;
}

const AttributePanel: React.FC<AttributePanelProps> = (props) => {
  const [key, setKey] = useState('');
  const { onDatasetChange } = props; /** 更新数据集，需要的是最外层基础设计器的更新方法 */
  const designer = useDesigner();
  const {
    layoutDatas, datasetVals, events, setDatasetVals,
  } = designer;
  const userCustom = !!(designer as any).userCustom;
  const { updateCom } = designer.layoutStore;
  const memoProps = useMemoProps();
  const { page } = memoProps;
  const { loading, selectedCom } = useLazySelectedCom();
  const { renderConfig } = useInternal()!;
  const throttleChange = useCallback(
    throttle((changes: FormDatas, comId: string, otherParams: any) => {
      const { layoutStore } = designer;

      return layoutStore.updateComConfig(changes, comId, otherParams);
    }, 30),
    [],
  );

  const reloadPanel = useCallback(() => {
    setKey(UUID());
  }, []);

  useEffect(() => {
    events.on(EVENT_COM_CONFIG_RELOAD, reloadPanel);

    eventEmitter.on('@weapp/designer', EVENT_COM_CONFIG_RELOAD, reloadPanel);

    return () => {
      events.off(EVENT_COM_CONFIG_RELOAD);
      eventEmitter.off('@weapp/designer', EVENT_COM_CONFIG_RELOAD, reloadPanel);
    };
  }, []);

  const onChange = useCallback(
    // 当组件内部同时调用或多次调用onChange,onConfigChange时，会出现更新顺序错乱的问题，导致配置不对
    // 原因是由于craftjs setProp更新节点config为异步的操作
    // 两次更新同时发生时，上次的更新可能还没有应用到节点上，下次的更新就已经发生了
    // 故这里使用throttle来拉开两次更新间的时间频率，来解决以上问题
    (changes: FormDatas, otherParams: any) => {
      const { layoutStore } = designer;
      const comId = otherParams?.comId || layoutStore.selectedCom!.id;

      if (otherParams?.comId && otherParams?.comId !== layoutStore.selectedCom!.id) {
        return layoutStore.updateComConfig(changes, comId, otherParams);
      }

      return throttleChange(changes, comId, otherParams);
    },
    [],
  );

  const _onDatasetChange = useCallback(
    (datasetVal?: any, type?: string) => {
      if (!layoutDatas?.length) return;

      const cb = (v: DatasetValue[]) => {
        setDatasetVals(v);
        ebdcoms.excu('setEbParams', 'pageParams', page?.id, {
          datasetVals: v || [],
        });

        events.emit(EVNET_DATASET_UPDATE, v || []);
      };

      onDataChange({
        datasetVal,
        type,
        datasetVals: designer.datasetVals,
        setDatasetVals: cb,
        userCustom,
      });
    },
    [layoutDatas?.length, page?.id, userCustom],
  );

  const _page = useMemo(
    () => ({
      ...page,
      ...props.page,
      datasetVals: toJS(datasetVals) || [],
    }),
    [page, props.page, datasetVals],
  );

  if (!selectedCom) {
    return (
      <CorsComponent
        weId={`${props.weId || ''}_v1eqhx`}
        app="@weapp/ebdcoms"
        compName="Empty"
        type="noData"
        description={getLabel('116397', '请在左侧区域选择组件')}
      />
    );
  }

  return (
    <Spin
      weId={`${props.weId || ''}_oro8tx`}
      className={`${prefixCls}-attr-panel`}
      spinning={loading}
      wrapperClassName={`${prefixCls}-attr-panel-container`}
    >
      <CoreConfig
        weId={`${props.weId || ''}_y9n9wz`}
        key={`${selectedCom.id}_${key}`}
        com={selectedCom}
        onChange={onChange}
        onComChange={updateCom}
        renderConfig={renderConfig}
        onDatasetChange={onDatasetChange || _onDatasetChange}
        {...memoProps}
        {...props}
        page={_page}
      />
    </Spin>
  );
};

export default observer(AttributePanel);
