import { CorsComponent, FormDatas, Switch } from '@weapp/ui';
import {
  getLabel, isBoolean, isEmpty, isEqual,
} from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { useCallback, useMemo } from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { useDesigner, useLayout } from '../..';
import Form from '../../../common/form';
import { prefixCls } from '../../../constants';
import { PageStyleProps } from '../style/types';
import './index.less';

export const dftBackground = {
  backgroundColor: '',
  backgroundImage: '',
  backgroundPosition: 'center center',
  backgroundRepeat: 'no-repeat',
  backgroundSize: 'auto',
  positionType: 'grid',
  imgUrl: '',
};

const PageStyle: React.FC<PageStyleProps> = (props) => {
  const { clientType } = useDesigner();
  const { pageConfig, updatePageConfig } = useLayout();
  const {
    className,
    items,
    layout,
    groups,
    themeStyle = {},
    getGroups,
    getItems,
    onChange,
    customRenderBackground,
    customThemeApi,
  } = props;
  const _onChange = onChange || updatePageConfig;

  const useCustom = useMemo(
    () => (isBoolean(pageConfig?.style?.useCustom) ? pageConfig?.style?.useCustom : true),
    [pageConfig?.style?.useCustom],
  );

  const data = useMemo(() => {
    if (
      useCustom === false
      || isEmpty(pageConfig?.style?.page)
      || (Object.keys(pageConfig?.style.page || {}).length === 1
        && isEqual(pageConfig?.style.page.background, dftBackground))
    ) {
      return { ...themeStyle.styleConfig };
    }
    return pageConfig?.style?.page || {};
  }, [pageConfig?.style, themeStyle, useCustom]);

  const _items = useMemo(
    () => (getItems ? getItems(clientType) : items),
    [getItems, items, clientType],
  );

  const onPageStyleChange = useCallback(
    (changes?: FormDatas) => {
      const style = {
        ...pageConfig?.style,
        useCustom,
        page: { ...data, ...changes },
      };

      _onChange({ style });
    },
    [pageConfig, clientType, data, useCustom],
  );

  const onUseCustomChange = useCallback(
    (v: boolean) => {
      const style = {
        ...pageConfig?.style,
        useCustom: v,
      };
      _onChange({ style });
    },
    [pageConfig],
  );

  if (!_items) {
    return (
      <CorsComponent
        weId={`${props.weId || ''}_0jk0jn`}
        app="@weapp/ebdcoms"
        compName="Empty"
        type="noData"
        description={getLabel('181327', '暂无页面样式')}
      />
    );
  }

  const renderForm = () => (
    <Form
      key={clientType}
      weId={`${props.weId || ''}_rt89au`}
      className={className}
      initialDatas={{
        items: _items,
        layout,
        groups,
        getGroups,
      }}
      data={data}
      onChange={onPageStyleChange}
    />
  );

  return (
    <div className={`${prefixCls}-flow-style-page-config`}>
      <CorsComponent
        weId={`${props.weId || ''}_e5hh5x`}
        app="@weapp/ebddesigner"
        compName="StylePageThemeConfig"
        customThemeApi={customThemeApi}
      />
      <div className={`${prefixCls}-style-page-custom-title`}>
        <div>
          <span>{getLabel('132429', '自定义样式')}</span>
        </div>
        <Switch
          weId={`${props.weId || ''}_99qv4p`}
          size="sm"
          value={useCustom}
          onChange={onUseCustomChange}
        />
      </div>
      <When weId={`${props.weId || ''}_vg70k2`} condition={useCustom}>
        {/* customRenderBackground： 动态背景-显示在背景下面
        因为原本在页面属性处迁移至样式下，数据存储逻辑和背景样式不同，所以通过定位实现，避免大规模改动原先逻辑 */}
        <If weId={`${props.weId || ''}_sazw25`} condition={!!customRenderBackground}>
          <Then weId={`${props.weId || ''}_sg6ctv`}>
            <div className={`${prefixCls}-style-page-background`}>
              {renderForm()}
              {customRenderBackground}
            </div>
          </Then>
          <Else weId={`${props.weId || ''}_h9k21j`}>{renderForm()}</Else>
        </If>
      </When>
    </div>
  );
};

export default observer(PageStyle);
