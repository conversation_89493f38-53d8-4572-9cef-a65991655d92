@import (reference) '../../../style/prefix.less';

.@{prefix}-flow-style-page-config {
  margin-top: -5px;

  .@{prefix}-style-page-custom-title {
    width: calc(100% + 30px);
    height: 35px;
    background-color: #f2f2f2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 8px;
    margin-left: -15px;

    & > div:first-child {
      display: flex;
      align-items: center;
      & > span {
        font-size: 12px;
        color: var(--regular-fc);
        line-height: 12px;
        padding-top: 1px;
        margin-right: 5px;
      }
    }

    .ui-icon {
      font-size: 12px;
      color: var(--regular-fc);
      line-height: 12px;
      padding: 4px;
      border-radius: var(--border-radius-xs);
      cursor: pointer;
    }
  }

  .@{prefix}-style-page-background {
    position: relative;

    .@{prefix}-form > div:first-child {
      margin-bottom: 38px;
    }

    .weapp-ebde-background {
      position: absolute;
      width: 100%;
      top: 38px;
      left: 0;

      & > div {
        padding: 0;
        padding-top: 10px;
      }
    }

    //流式中缺少这部分样式，样式代码从网格中迁移一份到流式
    .@{comsPrefix}-c-mg-c-wrapper {
      width: 100%;
      display: block;
      margin: 0 0 0 -30px;
      padding-left: 0;
      padding-right: 0;
      padding-bottom: 0;

      & > div {
        .ui-formItem-label {
          margin-left: -20px;
        }

        .ui-input-wrap {
          width: calc(100% + 30px);
        }
      }

      & > .ui-layout-row:last-child {
        padding-bottom: 0;
      }

      .ui-formItem {
        padding: 0 0 var(--form-item-v-spacing) 0;
      }
    }
  }
}
