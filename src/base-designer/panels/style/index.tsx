import { ComponentType } from 'react';
import Loadable from '../../../common/loadable';
import { StylePanelProps, StylePanelType } from './types';

export const StylePanel = Loadable({
  name: 'Style',
  loader: () => import(
    /* webpackChunkName: "de_style_panel" */
    './Style'
  ),
}) as ComponentType<StylePanelProps<StylePanelType>>;

StylePanel.displayName = 'StylePanel';

export const StylePanelContent = Loadable({
  name: 'StyleContent',
  loader: () => import(
    /* webpackChunkName: "de_style_panel_content" */
    './StyleContent'
  ),
}) as ComponentType<StylePanelProps<StylePanelType>>;

StylePanelContent.displayName = 'StylePanelContent';

export default {};
