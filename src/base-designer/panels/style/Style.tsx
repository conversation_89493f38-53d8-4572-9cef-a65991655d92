import { observer } from 'mobx-react';
import React from 'react';
import { useDesigner, useLayout } from '../..';
import useLazySelectedCom from '../../hooks/useLazySelectedCom';
import StyleContent from './StyleContent';
import { StylePanelProps, StylePanelType } from './types';

const StylePanel = <T extends StylePanelType>(props: StylePanelProps<T>) => {
  const { clientType, coms, updateComConfig } = useLayout();
  const { loading, selectedCom } = useLazySelectedCom();
  const { comDescriptions, page } = useDesigner();
  const comType = selectedCom?.type || '';
  const applyStyle = comDescriptions[comType]?.applyStyle || selectedCom?.applyStyle || '';

  return (
    <StyleContent
      weId={`${props.weId || ''}_r8f8jt`}
      {...(props as any)}
      clientType={clientType}
      coms={coms}
      loading={loading}
      selectedCom={selectedCom}
      page={{ ...page, subModule: props.filterModule }}
      applyStyle={applyStyle}
      updateComConfig={updateComConfig}
    />
  );
};

export default observer(StylePanel);
