import { CorsComponent, FormDatas, Spin } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { useCallback } from 'react';
import { prefixCls } from '../../../constants';
import { ComponentStyle } from '../../../core/style/component';
import { HideStyleConfig, StyleCardDlgConfig } from '../../../core/style/types';
import { ClientType, IComData, Page } from '../../../types';
import { ContainerStyle } from '../container-style';
import PageStylePanel from '../page-style';
import './index.less';

// eslint-disable-next-line max-len
const findCom = (coms: IComData[], selectedComId?: string) => coms.find((com) => com.id === selectedComId);

interface StyleContentProps extends React.Attributes {
  clientType: ClientType;
  coms: IComData[];
  selectedCom?: IComData;
  page: Page;
  applyStyle: string;
  updateComConfig?: Function;
  loading?: boolean;
  type?: string;
  styleCardDlgConfig?: StyleCardDlgConfig;
  hideStylesConfig?: HideStyleConfig[];
  /** 自定义请求参数 */
  fetchParams?: any;
  hideStyleLibMenu?: boolean;
}

const StyleContent: React.FC<StyleContentProps> = (props) => {
  const {
    clientType,
    coms = [],
    selectedCom,
    page,
    applyStyle,
    updateComConfig,
    loading = false,
    type,
    hideStylesConfig,
    styleCardDlgConfig,
    fetchParams,
    hideStyleLibMenu,
    ...restProps
  } = props;

  const onComStyleChange = useCallback(
    (changes: FormDatas, otherParams?: any) => {
      updateComConfig?.(changes, selectedCom!.id, otherParams);
    },
    [selectedCom],
  );

  if (type === 'page') {
    return <PageStylePanel weId={`${props.weId || ''}_z7dmn2`} {...(restProps as any)} />;
  }

  const com = findCom(coms, selectedCom?.id);

  if (!com) {
    return (
      <CorsComponent
        weId={`${props.weId || ''}_0jk0jn`}
        app="@weapp/ebdcoms"
        compName="Empty"
        type="noData"
        description={getLabel('116397', '请在左侧区域选择组件')}
      />
    );
  }

  if (type === 'container') {
    return <ContainerStyle weId={`${props.weId || ''}_ylpjqs`} />;
  }

  return (
    <Spin
      weId={`${props.weId || ''}_9w06uc`}
      wrapperClassName={`${prefixCls}-com-style-loading`}
      spinning={loading}
    >
      <ComponentStyle
        weId={`${props.weId || ''}_c9ld45`}
        // key={com.id}
        page={page}
        com={com}
        coms={coms}
        client={clientType}
        applyStyle={applyStyle}
        hideStylesConfig={hideStylesConfig}
        styleCardDlgConfig={styleCardDlgConfig}
        onChange={onComStyleChange}
        fetchParams={fetchParams}
        hideStyleLibMenu={hideStyleLibMenu}
      />
    </Spin>
  );
};

export default observer(StyleContent);
