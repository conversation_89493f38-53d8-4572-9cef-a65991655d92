import { FormDatas, FormLayoutProps } from '@weapp/ui';
import React, { ComponentProps } from 'react';
import Form from '../../../common/form/Form';
import { FormInitAllDatasEx, FormItemPropsEx } from '../../../common/form/types';
import { HideStyleConfig } from '../../../core/style/types';
import { ClientType } from '../../../core/types';

export type ComponentStyleProps = {} & React.Attributes;

export interface ICustomThemeApi {
  /** 主题列表接口 */
  list: string;
  /** 关联主题接口 */
  refTheme: string;
}

export type PageStyleProps = Omit<FormInitAllDatasEx, 'transform'> & {
  className?: string;
  value?: Record<string, any>;
  themeStyle?: any;
  getItems?: (client: ClientType) => FormItemPropsEx;
  customHide?: (this: Form, col: FormLayoutProps) => FormLayoutProps;
  /** 外部更新pageStyle */
  onChange?: (changes: FormDatas) => void;
  customRenderBackground?: React.ReactNode;
  /** 自定义调用主题接口 */
  customThemeApi?: ICustomThemeApi;
} & React.Attributes;

/**
 * container - 容器样式
 * component - 组件样式
 * page - 页面样式
 */
export type StylePanelType = 'container' | 'component' | 'page';

type StylePropsMap = {
  container: ComponentProps<'div'>;
  component: ComponentStyleProps;
  page: PageStyleProps;
};

interface ICustomFetchParams {
  module?: string;
  [key: string]: any;
}

export type StylePanelProps<T extends StylePanelType> = {
  type: T;
  /** 自定义请求参数 */
  fetchParams?: Partial<ICustomFetchParams>;
  /** 需要隐藏的样式配置（源码样式、tab） */
  hideStylesConfig?: HideStyleConfig[];
  filterModule?: string;
} & StylePropsMap[T];

export default {};
