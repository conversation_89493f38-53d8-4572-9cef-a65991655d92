import { ComponentType } from 'react';
import Loadable from '../../common/loadable';
import { MasterDlgProps } from './master-lib/types';
import { TempDiaProps } from './temp-lib/types';

export * from './com-temp';
export { default as MasterLib } from './master-lib/lib';
export { default as TempLib } from './temp-lib/lib';

export const TempDialogView = Loadable({
  name: 'CompTempDlg',
  loader: () => import(
    /* webpackChunkName: "de_temp_lib_dlg" */
    './temp-lib/DialogView'
  ),
}) as ComponentType<TempDiaProps>;

export const MasterDialogView = Loadable({
  name: 'MasterTemDlg',
  loader: () => import(
    /* webpackChunkName: "de_master_lib_dlg" */
    './master-lib/DialogView'
  ),
}) as ComponentType<MasterDlgProps>;

export default {};
