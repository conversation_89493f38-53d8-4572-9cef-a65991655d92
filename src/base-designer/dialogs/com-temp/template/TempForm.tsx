import {
  CorsComponent, Form, FormItemProps, FormLayoutProps, FormSwitchProps,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { Component, ComponentType } from 'react';
import { extraProps, prefixCls } from '../../../../constants';
import html2base64 from '../../../../utils/html2base64';
import setTimeoutOnce from '../../../../utils/setTimeoutOnce';
import { InternalDesignerContext } from '../../../Context';
import BaseDesignerStore from '../../../store';
import AssetsSelect from '../AssetsSelect';
import CategorySelect from '../CategorySelect';
import { ComTempType } from '../constants';
import CompTempStore from '../store';
import TempSelect from './TempSelect';

interface TempFormProps extends React.Attributes {
  comDom?: HTMLElement | null;
  type: ComTempType;
}

interface InternalTempFormProps extends TempFormProps {
  compTempStore: CompTempStore;
  designStore: BaseDesignerStore;
}

interface TempFormState {
  useThumbnail: boolean;
}

const assetsSelect = (props: any, onSelectIcon: () => void) => (
  <AssetsSelect weId={`${props.weId || ''}_iuid3x`} {...props.props} onSelectIcon={onSelectIcon} />
);

const categorySelect = (props: any) => (
  <CategorySelect weId={`${props.weId || ''}_v1dhhx`} {...props.props} />
);

const templateSelect = (props: any, onCoverIdChange: (temp: any) => void) => (
  <TempSelect
    weId={`${props.weId || ''}_kejhxe`}
    {...props.props}
    onSelectTempChange={onCoverIdChange}
  />
);

const appItems = (localization: Record<string, any>): FormItemProps => {
  const {
    placeholder = getLabel('99296', '请输入模板名称'),
    generate = getLabel('99293', '生成新模板'),
    cover = getLabel('99294', '覆盖已有模板'),
  } = localization;

  return {
    name: {
      itemType: 'CUSTOM',
      placeholder,
      required: true,
      autoFocus: true,
      errorType: 'popover',
    },
    fileId: {
      itemType: 'CUSTOM',
      required: true,
      errorType: 'popover',
    },
    category: {
      itemType: 'CUSTOM',
      required: true,
      errorType: 'popover',
    },
    isNew: {
      itemType: 'RADIO',
      canReversedChoose: false,
      data: [
        { id: '1', content: generate },
        { id: '0', content: cover },
      ],
    },
    coverId: {
      itemType: 'CUSTOM',
      required: true,
      errorType: 'popover',
    },
  };
};

const appInitLayout = [
  [
    {
      id: 'name',
      label: getLabel('53866', '名称'),
      items: ['name'],
      labelSpan: 6,
      hide: false,
      className: `${prefixCls}-comp-temp-add-name`,
    },
  ],
  [
    {
      id: 'fileId',
      label: getLabel('56345', '图标'),
      items: ['fileId'],
      labelSpan: 6,
      hide: false,
      className: `${prefixCls}-comp-temp-add-fileId`,
    },
  ],
  [
    {
      id: 'category',
      label: getLabel('99292', '分类'),
      items: ['category'],
      labelSpan: 6,
      hide: false,
      className: `${prefixCls}-comp-temp-add-category`,
    },
  ],
  [
    {
      id: 'isNew',
      label: getLabel('87816', '类型'),
      items: ['isNew'],
      labelSpan: 6,
      hide: false,
    },
  ],
  [
    {
      id: 'coverId',
      label: getLabel('99295', '模板'),
      items: ['coverId'],
      labelSpan: 6,
      hide: false,
      className: `${prefixCls}-comp-temp-add-template`,
    },
  ],
];

export const getThumbnail = (dom?: HTMLElement, _filter = (node: HTMLElement) => !!node) => {
  if (!dom) return Promise.resolve('');
  return html2base64(dom, _filter);
};

const initTempFormValue = {
  name: '',
  fileId: '',
  category: '-1',
  isNew: '1',
};

@inject('compTempStore', 'designStore')
@observer
class TempForm extends Component<InternalTempFormProps, TempFormState> {
  static contextType = InternalDesignerContext;

  hasInitThumbnail = false;

  state = {
    useThumbnail: true,
  };

  componentDidMount() {
    const { compTempStore, designStore, type } = this.props;
    const { templateStore } = compTempStore;
    const { formStore, initData } = templateStore;
    const items = appItems(this.context?.localization.toolbar.template.dialog || {});
    const layout = appInitLayout;

    formStore.initForm({
      data: initTempFormValue,
      items,
      layout,
      groups: [],
    });

    initData(designStore);

    if (type === ComTempType.TEMP) {
      // 生成缩略图会卡顿，待表单加载完成后
      setTimeoutOnce(this.initThumbnail, 100);
    }
  }

  componentDidUpdate() {
    const { type } = this.props;

    if (!this.hasInitThumbnail && type === ComTempType.TEMP) {
      // 生成缩略图会卡顿，待表单加载完成后
      setTimeoutOnce(this.initThumbnail, 100);
    }
  }

  initThumbnail = () => {
    this.hasInitThumbnail = true;

    const { formStore } = this.props.compTempStore.templateStore;
    const { comDom } = this.props;

    if (comDom) {
      getThumbnail(comDom)?.then((file) => {
        if (this.state.useThumbnail) {
          formStore.updateDatas({
            fileId: toJS({ path: file }),
          });
          this.forceUpdate();
        }
      });
    }
  };

  onSelectIcon = () => {
    this.setState({ useThumbnail: false });
  };

  // 字段联动
  customHide = (col: FormLayoutProps) => {
    const { formStore } = this.props.compTempStore.templateStore;
    const value = formStore.getFormDatas();
    const { isNew } = value;

    // 覆盖已有模板
    const _isNew = isNew === '1';

    formStore.setHide('coverId', _isNew);

    return col;
  };

  nameLocaleSet = (props: any) => {
    const { formStore } = this.props.compTempStore.templateStore;
    const formData = formStore.getFormDatas();
    const { name, nameLabelId } = formData;

    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_uuv09z`}
        app="@weapp/ebdcoms"
        compName="LocaleAnother"
        module="ebuilder_designer"
        tablefield="ebdd_component_tmpl.name"
        value={name}
        targetId={nameLabelId}
        style={{ minWidth: '100%' }}
        {...props.props}
      />
    );
  };

  customRenderFormSwitch = (key: string, props: FormSwitchProps) => {
    switch (key) {
      case 'name':
        return this.nameLocaleSet(props);
      case 'category':
        return categorySelect(props);
      case 'coverId':
        return templateSelect(props, this.onCoverIdChange);
      default:
        return assetsSelect(props, this.onSelectIcon);
    }
  };

  onCoverIdChange = (tempData: any) => {
    const { formStore } = this.props.compTempStore.templateStore;
    const { content, type } = tempData;
    formStore.updateDatas({
      name: content,
      category: type,
    });
    this.forceUpdate();
  };

  render() {
    const { formStore } = this.props.compTempStore.templateStore;

    return (
      <div className={`${prefixCls}-content`}>
        <Form
          weId={`${this.props.weId || ''}_mz3o3v`}
          {...extraProps}
          store={formStore}
          customHide={this.customHide}
          customRenderFormSwitch={this.customRenderFormSwitch}
        />
      </div>
    );
  }
}

export default TempForm as unknown as ComponentType<TempFormProps>;
