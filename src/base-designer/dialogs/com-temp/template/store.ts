import { Dialog, FormStore } from '@weapp/ui';
import { getLabel, isString } from '@weapp/utils';
import FormData from 'form-data';
import {
  action, observable, runInAction, toJS,
} from 'mobx';
import { ClientType, IComData } from '../../../../core/types';
import getDesignInfo from '../../../../core/utils/getDesignInfo';
import dataURLtoFile from '../../../../utils/dataURLtoFile';
import ebdcoms from '../../../../utils/ebdcoms';
import { CompTempDataType } from '../../../panels/template/types';
import { isValidTempConfig } from '../../../panels/template/utils';
import BaseDesignerStore from '../../../store';
import getPageModule from '../../../utils/getPageModule';
import CompTempStore from '../store';
import { ComTempCategory, ComTempData } from '../types';

const { message } = Dialog;

export const isBase64File = (source: string) => isString(source) && source.startsWith('data:');

export default class CompTemplateStore {
  constructor(parent: CompTempStore) {
    this.parent = parent;
  }

  parent: CompTempStore | null = null;

  designStore: BaseDesignerStore | null = null;

  /** 模板新建，编辑的FormStore */
  formStore = new FormStore();

  @observable hasInit: boolean = false;

  /**
   *  模板分类列表
   */
  @observable categories: ComTempCategory[] = [];

  /**
   *  模板列表
   */
  @observable templates: CompTempDataType[] = [];

  get params() {
    const { moduleScope, layoutType = 'GRID' } = this.designStore!;

    return {
      layoutType,
      module: getPageModule(moduleScope),
    };
  }

  @action
  initData = (designStore: BaseDesignerStore) => {
    if (this.hasInit) return;

    this.designStore = designStore;
    this.hasInit = true;
    this.getCategories();
    this.getTemplates();
  };

  @action
  getCategories = () => {
    const { params } = this;
    const { moduleScope = params?.module } = this.designStore || {};
    // 网格布局设计器才有这个属性
    const { isFrontDeskEntry = false } = (this.designStore || {}) as any;

    // 独立部署相关逻辑
    if (isFrontDeskEntry) {
      // module需要调整
      params.module = moduleScope;
    }

    ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/tmpl/category/list',
      params,
      success: (data: ComTempCategory[]) => {
        runInAction(() => {
          this.categories = data;
          this.templates = data.map((item: any) => {
            item.templates = [];

            return item;
          });
        });
      },
    });
  };

  @action.bound
  getTemplates = () => {
    const params = this.params as any;
    const { clientType, singleClient, moduleScope = params?.module } = this.designStore! || {};
    const { isFrontDeskEntry = false } = (this.designStore || {}) as any;
    // 和后端沟通确认，pageSize传100000，可返回所有数据
    const maxSize = 100000;

    if (singleClient) {
      params.clientType = clientType;
    }

    // 独立部署相关逻辑
    if (isFrontDeskEntry) {
      // module需要调整
      params.module = moduleScope;
    }

    ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/component/tmpl/list',
      params: { ...params, pageSize: maxSize, isAll: false },
      success: (res: ComTempData) => {
        runInAction(() => {
          res.templates.forEach((data) => {
            this.templates.find((template) => {
              if (template.id === data.category) {
                template.templates.push(data);
              }

              return template.id === data.category;
            });
          });

          this.templates = this.templates.filter((tpl) => !!tpl.templates.length);
        });
      },
    });
  };

  handleConfig = async (config: string, clientType: ClientType) => {
    const { refreshLayoutByRowSpace } = this.designStore as any;
    const parseConfig = JSON.parse(config);
    // map方法中是异步操作，所以此处需要用Promise.all包裹
    const tempConfig = await Promise.all(
      parseConfig.map(async (com: IComData) => {
        const comDesign = await getDesignInfo(com, clientType);
        // 在保存模板前，可以通过该方法对相应的参数进行处理
        const { onBeforeSaveTemp } = comDesign?.defaultOpts || {};

        // 模板组件去除母版信息
        const { master, masterComponent, ...restCom } = com;

        /**
         * 以间距配置 10 为基准，重新计算组件布局
         * - 解决在间距配置不是10的情况下拖入的母版，高度计算不对的问题
         * - 只处理网格布局PC端
         */
        if (typeof refreshLayoutByRowSpace === 'function') {
          restCom.config.layout = refreshLayoutByRowSpace(restCom, clientType);
        }

        if (onBeforeSaveTemp) {
          return onBeforeSaveTemp({ ...restCom });
        }
        return { ...restCom };
      }),
    );
    return JSON.stringify(tempConfig);
  };

  @action
  createCategroy = (categoryName: string, nameLabelId?: string) => {
    const { params } = this;
    const { moduleScope = params?.module } = this.designStore || {};
    const { isFrontDeskEntry = false } = (this.designStore || {}) as any;
    const belongModule = isFrontDeskEntry === true ? moduleScope : params?.module;

    return ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/tmpl/category/create',
      method: 'post',
      params: {
        ...this.params,
        module: belongModule,
        name: categoryName,
        nameLabelId,
      },
      success: (data: ComTempCategory) => {
        runInAction(() => {
          this.categories.push(data);
        });

        message({
          type: 'success',
          content: getLabel('54117', '保存成功'),
        });
      },
    });
  };

  @action
  save = async () => {
    const { params } = this;
    const { moduleScope = params?.module } = this.designStore || {};
    const { isFrontDeskEntry = false } = (this.designStore || {}) as any;
    const tempFormData = toJS(this.formStore.getFormDatas());
    const {
      category, coverId, fileId, isNew, name, nameLabelId,
    } = tempFormData;
    const { configMobile, configPC } = this.parent!.tempConfig;
    const formData = new FormData();
    formData.append('category', category);

    const isStringValue = typeof name === 'string';
    const nameVal = isStringValue ? { nameAlias: name } : name;
    const { nameAlias, nameAliasLabelId = nameLabelId } = nameVal;
    const belongModule = isFrontDeskEntry === true ? moduleScope : params?.module;

    if (isValidTempConfig(configMobile)) {
      const tempConfig = await this.handleConfig(configMobile, 'MOBILE');
      formData.append('configMobile', tempConfig);
    }

    if (isValidTempConfig(configPC)) {
      const tempConfig = await this.handleConfig(configPC, 'PC');
      formData.append('configPc', tempConfig);
    }

    formData.append('isNew', isNew);
    formData.append('module', belongModule);
    formData.append('layoutType', params.layoutType);
    formData.append('name', nameAlias);
    formData.append('nameLabelId', nameAliasLabelId || '');

    if (coverId) {
      formData.append('coverId', coverId);
    }

    if (fileId?.path && isBase64File(fileId?.path)) {
      const file = dataURLtoFile(fileId?.path, 'thumbnail.png');
      const fileFormData = new FormData();

      // 获取缩略图id
      fileFormData.append('file', file);
      await ebdcoms.asyncExcu('ajax', {
        url: '/api/bs/ebuilder/common/image-lib/common/upload',
        method: 'post',
        data: fileFormData,
        success: (res: any) => {
          runInAction(() => {
            formData.append('fileId', res.id);
          });
        },
      });
    } else {
      formData.append('fileId', fileId);
    }

    const successMsg = coverId
      ? getLabel('100857', '模板覆盖成功！')
      : getLabel('100837', '模板创建成功！');

    return ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/component/tmpl/create',
      method: 'post',
      data: formData,
      success: successMsg,
    });
  };
}
