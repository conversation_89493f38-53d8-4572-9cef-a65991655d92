import { Select } from '@weapp/ui';
import { inject, observer } from 'mobx-react';
import React, { Component } from 'react';
import { prefixCls } from '../../../../constants';
import { CompTempDataType } from '../../../panels/template/types';

export type TempOpts = {
  id: string;
  content: string;
  type: string;
  icon: string;
};

export const getGroupOptions = (cats: CompTempDataType[]) => {
  const data: TempOpts[] = [];

  cats.forEach((_cat) => {
    const { id, isSys } = _cat;

    if (isSys) return;
    const temps = _cat.templates.map((_temp) => ({
      id: _temp.id,
      content: _temp.name,
      icon: _temp.icon || '',
      type: id,
    }));

    data.push(...temps);
  });

  return data;
};

const getGroupTitle = (cats: CompTempDataType[]) => cats.map((_cat) => ({
  id: _cat.id,
  content: _cat.name,
}));

@inject('compTempStore')
@observer
class TempSelect extends Component<any> {
  handleSelect = (value: any) => {
    const { onChange, onSelectTempChange } = this.props;
    const { id } = value;

    onSelectTempChange(value);
    onChange(id);
  };

  render() {
    const { value } = this.props;
    const { templates } = this.props.compTempStore.templateStore;
    const groupTitle = getGroupTitle(templates);
    const groupOptions = getGroupOptions(templates);

    return (
      <div>
        <Select
          weId={`${this.props.weId || ''}_90byj1`}
          showDataGroupByType
          style={{ width: 200 }}
          dropdownClassName={`${prefixCls}-comp-temp-cover-dropdown`}
          data={groupOptions}
          dataGroupTitle={groupTitle}
          value={value}
          showSearch
          onSelect={this.handleSelect}
        />
      </div>
    );
  }
}

export default TempSelect;
