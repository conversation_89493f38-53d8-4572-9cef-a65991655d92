import {
  CorsComponent, Form, FormItemProps, FormSwitchProps,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { Component, ComponentType } from 'react';
import { extraProps, prefixCls } from '../../../../constants';
import html2base64 from '../../../../utils/html2base64';
import setTimeoutOnce from '../../../../utils/setTimeoutOnce';
import BaseDesignerStore from '../../../store';
import AssetsSelect from '../AssetsSelect';
import CategorySelect from '../CategorySelect';
import { ComTempType } from '../constants';
import CompTempStore from '../store';

interface TempFormProps extends React.Attributes {
  comDom?: HTMLElement | null;
  type: ComTempType;
}

interface InternalTempFormProps extends TempFormProps {
  compTempStore: CompTempStore;
  designStore: BaseDesignerStore;
}

interface TempFormState {
  useThumbnail: boolean;
}

const assetsSelect = (props: any, onSelectIcon: () => void) => (
  <AssetsSelect weId={`${props.weId || ''}_iuid3x`} {...props.props} onSelectIcon={onSelectIcon} />
);

const categorySelect = (props: any) => (
  <CategorySelect weId={`${props.weId || ''}_v1dhhx`} {...props.props} />
);

const appItems = (): FormItemProps => ({
  name: {
    itemType: 'CUSTOM',
    placeholder: getLabel('118354', '请输入母版名称'),
    required: true,
    autoFocus: true,
    errorType: 'popover',
  },
  fileId: {
    itemType: 'CUSTOM',
    required: true,
    errorType: 'popover',
  },
  category: {
    itemType: 'CUSTOM',
    required: true,
    errorType: 'popover',
  },
});

const appInitLayout = [
  [
    {
      id: 'name',
      label: getLabel('53866', '名称'),
      items: ['name'],
      labelSpan: 6,
      hide: false,
      className: `${prefixCls}-comp-temp-add-name`,
    },
  ],
  [
    {
      id: 'fileId',
      label: getLabel('56345', '图标'),
      items: ['fileId'],
      labelSpan: 6,
      hide: false,
      className: `${prefixCls}-comp-temp-add-fileId`,
    },
  ],
  [
    {
      id: 'category',
      label: getLabel('99292', '分类'),
      items: ['category'],
      labelSpan: 6,
      hide: false,
      className: `${prefixCls}-comp-temp-add-category`,
    },
  ],
];

export const getThumbnail = (dom?: HTMLElement) => {
  if (!dom) return Promise.resolve('');
  return html2base64(dom);
};

const initTempFormValue = {
  name: '',
  fileId: '',
  category: '-1',
};

@inject('compTempStore', 'designStore')
@observer
class TempForm extends Component<InternalTempFormProps, TempFormState> {
  hasInitThumbnail = false;

  state = {
    useThumbnail: true,
  };

  componentDidMount() {
    const { type, compTempStore, designStore } = this.props;
    const { masterStore } = compTempStore;
    const { formStore, initData } = masterStore;
    const items = appItems();
    const layout = appInitLayout;

    formStore.initForm({
      data: initTempFormValue,
      items,
      layout,
      groups: [],
    });

    initData(designStore);
    if (type === ComTempType.MASTER) {
      // 生成缩略图会卡顿，待表单加载完成后
      setTimeoutOnce(this.initThumbnail, 100);
    }
  }

  componentDidUpdate() {
    const { type } = this.props;

    if (!this.hasInitThumbnail && type === ComTempType.MASTER) {
      // 生成缩略图会卡顿，待表单加载完成后
      setTimeoutOnce(this.initThumbnail, 100);
    }
  }

  initThumbnail = () => {
    this.hasInitThumbnail = true;

    const { comDom } = this.props;
    const { formStore } = this.props.compTempStore.masterStore;

    if (comDom) {
      getThumbnail(comDom)?.then((file) => {
        if (this.state.useThumbnail) {
          formStore.updateDatas({
            fileId: toJS({ path: file }),
          });
          this.forceUpdate();
        }
      });
    }
  };

  onSelectIcon = () => {
    this.setState({ useThumbnail: false });
  };

  nameLocaleSet = (props: any) => {
    const { formStore } = this.props.compTempStore.templateStore;
    const formData = formStore.getFormDatas();
    const { name, nameLabelId } = formData;

    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_7jas5t`}
        app="@weapp/ebdcoms"
        compName="LocaleAnother"
        module="ebuilder_designer"
        tablefield="ebdd_master_component.master_name"
        value={name}
        targetId={nameLabelId}
        style={{ minWidth: '100%' }}
        {...props.props}
      />
    );
  };

  customRenderFormSwitch = (key: string, props: FormSwitchProps) => {
    switch (key) {
      case 'name':
        return this.nameLocaleSet(props);
      case 'category':
        return categorySelect(props);
      default:
        return assetsSelect(props, this.onSelectIcon);
    }
  };

  onCoverIdChange = (tempData: any) => {
    const { formStore } = this.props.compTempStore.masterStore;
    const { content, type } = tempData;
    formStore.updateDatas({
      name: content,
      category: type,
    });

    this.forceUpdate();
  };

  render() {
    const { formStore } = this.props.compTempStore.masterStore;

    return (
      <div className={`${prefixCls}-content`}>
        <Form
          weId={`${this.props.weId || ''}_mz3o3v`}
          {...extraProps}
          store={formStore}
          customRenderFormSwitch={this.customRenderFormSwitch}
        />
      </div>
    );
  }
}

export default TempForm as unknown as ComponentType<TempFormProps>;
