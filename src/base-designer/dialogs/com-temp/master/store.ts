import { Dialog, FormStore } from '@weapp/ui';
import { eventEmitter, getLabel, isString } from '@weapp/utils';
import {
  action, observable, runInAction, toJS,
} from 'mobx';
import getDesignInfo from '../../../../core/utils/getDesignInfo';
import { ClientType, IComData } from '../../../../types';
import dataURLtoFile from '../../../../utils/dataURLtoFile';
import ebdcoms from '../../../../utils/ebdcoms';
import { CompTempDataType } from '../../../panels/template/types';
import { isValidTempConfig } from '../../../panels/template/utils';
import BaseDesignerStore from '../../../store';
import getPageModule from '../../../utils/getPageModule';
import { ComTempType } from '../constants';
import CompTempStore from '../store';
import { ComTempCategory, ICreateMasterResponse } from '../types';

const { message } = Dialog;

export const isBase64File = (source: string) => isString(source) && source.startsWith('data:');

export default class CompMasterMaskStore {
  constructor(parent: CompTempStore) {
    this.parent = parent;
  }

  parent: CompTempStore | null = null;

  designStore: BaseDesignerStore | null = null;

  /** 模板新建，编辑的FormStore */
  formStore = new FormStore();

  @observable hasInit: boolean = false;

  /**
   *  模板分类列表
   */
  @observable categories: ComTempCategory[] = [];

  /**
   *  模板列表
   */
  @observable templates: CompTempDataType[] = [];

  get params() {
    const { moduleScope, layoutType = 'GRID' } = this.designStore!;

    return {
      layoutType,
      module: getPageModule(moduleScope),
      templateType: ComTempType.MASTER,
      isAll: false,
    };
  }

  @action
  initData = (designStore: BaseDesignerStore) => {
    if (this.hasInit) return;

    this.designStore = designStore;

    this.getCategories();
  };

  @action
  getCategories = () => {
    const { params } = this;
    const { moduleScope = params?.module } = this.designStore || {};
    // 网格布局设计器才有这个属性
    const { isFrontDeskEntry = false } = (this.designStore || {}) as any;

    // 独立部署相关逻辑
    if (isFrontDeskEntry) {
      // module需要调整
      params.module = moduleScope;
    }

    ebdcoms.asyncExcu('ajax', {
      url: '/api/bs/ebuilder/designer/tmpl/category/list',
      params,
      success: (data: ComTempCategory[]) => {
        runInAction(() => {
          this.categories = data;
        });
      },
    });
  };

  @action.bound
  getTemplates = () => {
    const params = this.params as any;
    const { clientType, singleClient, moduleScope = params?.module } = this.designStore! || {};
    const { isFrontDeskEntry = false } = (this.designStore || {}) as any;

    if (singleClient) {
      params.clientType = clientType;
    }

    // 独立部署相关逻辑
    if (isFrontDeskEntry) {
      // module需要调整
      params.module = moduleScope;
    }

    ebdcoms.asyncExcu('ajax', {
      url: '/api/bs/ebuilder/designer/tmpl/listAll',
      params,
      success: (templates: CompTempDataType[]) => {
        runInAction(() => {
          this.templates = templates.filter((tpl) => !!tpl.templates.length);
        });
      },
    });
  };

  handleConfig = async (config: string, clientType: ClientType) => {
    const { refreshLayoutByRowSpace } = this.designStore as any;
    const parseConfig = JSON.parse(config);
    const comIdList: string[] = [];
    // map方法中是异步操作，所以此处需要用Promise.all包裹
    const tempConfig = await Promise.all(
      parseConfig.map(async (com: IComData) => {
        const comDesign = await getDesignInfo(com, clientType);
        // 在保存模板前，可以通过该方法对相应的参数进行处理
        const { onBeforeSaveTemp } = comDesign?.defaultOpts || {};

        // 模板组件去除母版信息
        const { master, masterComponent, ...restCom } = com;

        if (com?.id) {
          comIdList.push(com.id);
        }

        /**
         * 以间距配置 10 为基准，重新计算组件布局
         * - 解决在间距配置不是10的情况下拖入的母版，高度计算不对的问题
         * - 只处理网格布局PC端
         */
        if (typeof refreshLayoutByRowSpace === 'function') {
          restCom.config.layout = refreshLayoutByRowSpace(restCom, clientType);
        }

        if (onBeforeSaveTemp) {
          return onBeforeSaveTemp({ ...restCom });
        }
        return { ...restCom };
      }),
    );
    return {
      comIdList,
      tempConfig: JSON.stringify(tempConfig),
    };
  };

  @action
  save = async () => {
    const { params } = this;
    const tempFormData = toJS(this.formStore.getFormDatas());
    const {
      category, fileId, name, nameLabelId,
    } = tempFormData;
    const { configMobile, configPC } = this.parent!.tempConfig;
    const { moduleScope = params?.module } = this.designStore || {};
    const { isFrontDeskEntry = false } = (this.designStore || {}) as any;
    const formData = new FormData();

    const isStringValue = typeof name === 'string';
    const nameVal = isStringValue ? { nameAlias: name } : name;
    const { nameAlias, nameAliasLabelId = nameLabelId } = nameVal;
    const belongModule = isFrontDeskEntry === true ? moduleScope : params?.module;

    let comIdList: string[] = [];

    formData.append('category', category);

    if (isValidTempConfig(configMobile)) {
      const { tempConfig, comIdList: pcComIdList } = await this.handleConfig(
        configMobile,
        'MOBILE',
      );
      formData.append('configMobile', tempConfig);
      comIdList = comIdList.concat(pcComIdList);
    }

    if (isValidTempConfig(configPC)) {
      const { tempConfig, comIdList: mobileComIdList } = await this.handleConfig(configPC, 'PC');
      formData.append('configPC', tempConfig);
      comIdList = comIdList.concat(mobileComIdList);
    }

    formData.append('module', belongModule);
    formData.append('layoutType', params.layoutType);
    formData.append('name', nameAlias);
    formData.append('nameLabelId', nameAliasLabelId || '');

    if (fileId?.path && isBase64File(fileId?.path)) {
      const file = dataURLtoFile(fileId?.path, 'thumbnail.png');

      formData.append('thumbnail', file);
      formData.append('fileId', '');
    } else {
      formData.append('fileId', fileId);
    }

    return ebdcoms
      .asyncExcu('ajax', {
        url: '/api/bs/ebuilder/designer/master/create',
        method: 'post',
        encrys: ['configPC', 'configMobile'],
        data: formData,
        success: getLabel('143341', '母版创建成功！'),
      })
      .then((result: ICreateMasterResponse) => {
        if (comIdList.length) {
          /**
           * 创建母版后，通知样式缓存更新缓存的母版数据
           * - 样式缓存的地方不希望缓存母版样式
           * - ebdcoms - createCssText.ts
           */
          comIdList.forEach((comId) => {
            eventEmitter.emit('@weapp/ebdcoms', 'update.cachedMasterInfo', {
              origin: 'create',
              comId,
            });
          });
        }

        return result;
      });
  };

  @action
  createCategroy = (categoryName: string, nameLabelId?: string) => ebdcoms.asyncExcu('ajax', {
    url: '/api/bs/ebuilder/designer/tmpl/category/create',
    method: 'post',
    params: {
      ...this.params,
      name: categoryName,
      nameLabelId,
    },
    success: (data: ComTempCategory) => {
      runInAction(() => {
        this.categories.push(data);
      });

      message({
        type: 'success',
        content: getLabel('54117', '保存成功'),
      });
    },
  });
}
