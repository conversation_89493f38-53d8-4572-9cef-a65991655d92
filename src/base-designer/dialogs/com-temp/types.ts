import React from 'react';
import { IComTemplateData } from '../../../core';
import { ComTempType } from './constants';

export type ComTempCategory = {
  creator: string;
  deleteType: number;
  id: string;
  isSys: number;
  name: string;
  showOrder: number;
  nameLabelId?: string;
  defaultName?: string;
};

export type ComTempData = {
  templates: IComTemplateData[];
  totalPage: number;
};

export type ComTempConfig = {
  configPC: string;
  configMobile: string;
  pageId?: string;
  /** 是否支持存为母版 */
  supportMaster?: boolean;
};

export type CompTempDlgProps = React.Attributes & {
  /** 控制弹窗显隐 */
  visible?: boolean;
  /** 用于生成组件缩略图 */
  comDom?: HTMLElement | null;
  /** 生成组件模板的配置数据 */
  tempConfig?: ComTempConfig;
  /** 是否展示存为母版选项 */
  showMaster?: boolean;
  /** 弹窗点击取消的回调 */
  onCancel?: (visible: boolean) => void;
  /** 弹窗点击确认的回调 */
  onOk?: (type: ComTempType, id: string, dom?: HTMLElement | null) => void;
};

/** 创建母版成功后的响应数据 */
export interface ICreateMasterResponse {
  id: string;
  name: string;
  category: string;
  configMobile: string;
  configPC: string;
  deleteType: number;
  icon: string;
  module: string;
  layoutType: 'GRID' | 'FLOW';
}
