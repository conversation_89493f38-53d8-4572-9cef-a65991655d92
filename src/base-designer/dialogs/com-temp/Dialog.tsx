import {
  <PERSON><PERSON>, Cors<PERSON>ompo<PERSON>, Di<PERSON>, FormDatas, FormItem, FormItemProps, Spin,
} from '@weapp/ui';
import {
  classnames, getLabel, isEmpty, middleware,
} from '@weapp/utils';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import { When } from 'react-if';
import { appName, dlgIconName, prefixCls } from '../../../constants';
import { InternalDesignerContext } from '../../Context';
import BaseDesignerStore from '../../store';
import { ComTempType } from './constants';
import './index.less';
import AddMasterTemp from './master/TempForm';
import CompTempStore from './store';
import AddCompTemp from './template/TempForm';
import { CompTempDlgProps } from './types';

type InternalCompTempDlgProps = CompTempDlgProps & {
  compTempStore: CompTempStore;
  designStore: BaseDesignerStore;
};

interface States {
  visible: boolean;
  loading: boolean;
}

class CompTempDialog extends PureComponent<InternalCompTempDlgProps, States> {
  static contextType = InternalDesignerContext;

  state = {
    visible: this.props.visible || false,
    loading: false,
  };

  static getDerivedStateFromProps(nextProps: InternalCompTempDlgProps, prevState: States) {
    const hasVisible = typeof nextProps.visible !== 'undefined';

    if (hasVisible && nextProps.visible !== prevState.visible) {
      return {
        visible: nextProps.visible,
      };
    }

    return null;
  }

  componentDidMount() {
    const { compTempStore, designStore, tempConfig } = this.props;
    const { setComServicePath, setTempConfig } = compTempStore;
    const { comServicePath } = designStore;

    if (comServicePath) {
      setComServicePath(comServicePath);
    }

    setTempConfig(tempConfig);
  }

  componentDidUpdate() {
    const { tempConfig, compTempStore } = this.props;
    const { setTempConfig } = compTempStore;

    if (tempConfig) {
      setTempConfig(tempConfig);
    }
  }

  onClose = () => {
    this.setState({ visible: false });
    this.props.onCancel?.(false);
  };

  onOk = () => {
    const { type, addComTempStore } = this.props.compTempStore;
    const { formStore, save } = addComTempStore;
    this.setState({ loading: true });
    formStore
      .validate()
      .then((res: any) => {
        const { errors } = res;

        if (isEmpty(errors)) {
          save()
            .then((data) => {
              this.setState({
                loading: false,
                visible: false,
              });

              this.props.onOk?.(type, data.id, this.props?.comDom);
            })
            .catch(() => {
              this.setState({
                loading: false,
                visible: false,
              });
            });
        } else {
          this.setState({ loading: false });
        }
      })
      .catch(() => {
        this.setState({ loading: false });
      });
  };

  onTypeChange = (datas?: FormDatas) => {
    const { setComTempType } = this.props.compTempStore;

    if (!datas!.type) return;

    setComTempType(datas!.type as ComTempType);
  };

  render() {
    const {
      compTempStore, comDom, showMaster = false, tempConfig,
    } = this.props;
    const { type } = compTempStore;
    const { visible, loading } = this.state;
    const templateTitle = this.context?.localization.toolbar.template.dialog.title || getLabel('99291', '存为模板');

    const item: FormItemProps = {
      type: {
        itemType: 'RADIO',
        fieldName: 'type',
        data: [
          { id: ComTempType.TEMP, content: getLabel('99291', '存为模板') },
          {
            id: ComTempType.MASTER,
            content: tempConfig?.supportMaster ? (
              getLabel('118623', '存为母版')
            ) : (
              <span className={`${prefixCls}-comp-temp-type-select-disable`}>
                {getLabel('118623', '存为母版')}
                <CorsComponent
                  weId={`${this.props.weId || ''}_6ym6wd`}
                  app="@weapp/ebdcoms"
                  compName="IconFont"
                  name="Icon-remind05"
                  size="s"
                  title={getLabel('181280', '当前组件下存在子组件为母版，无法另存为母版')}
                />
              </span>
            ),
            disabled: !tempConfig?.supportMaster,
          },
        ],
        value: type,
      },
    };

    const footer = [
      <Button
        disabled={loading}
        weId="w30m24@submit"
        key="submit"
        type="primary"
        className={`${prefixCls}-comp-temp-save-btn`}
        onClick={this.onOk}
      >
        {!loading ? (
          getLabel('97216', '保存')
        ) : (
          <>
            <Spin
              weId={`${this.props.weId || ''}_p74awc}`}
              style={{ marginRight: '5px' }}
              size="small"
            />
            <span> {getLabel('97216', '保存')}</span>
          </>
        )}
      </Button>,
      <Button weId="l5w8u3@close" key="close" type="default" onClick={this.onClose}>
        {getLabel('53937', '取消')}
      </Button>,
    ];

    return (
      <When weId={`${this.props.weId || ''}_edyr28`} condition={visible}>
        <Dialog
          weId={`${this.props.weId || ''}_q5rhy1`}
          wrapClassName={`${prefixCls}-comp-temp-add`}
          visible={visible}
          title={showMaster ? getLabel('118361', '另存为') : templateTitle}
          footer={footer}
          onClose={this.onClose}
          width={500}
          destroyOnClose
          draggable
          closable
          mask
          icon={dlgIconName}
        >
          <div className={`${prefixCls}-comp-temp`}>
            <When weId={`${this.props.weId || ''}_afg52b`} condition={showMaster}>
              <FormItem
                weId={`${this.props.weId || ''}_gipm5l`}
                label={getLabel('118365', '保存类型')}
                labelSpan={6}
                item={item}
                com={['type']}
                onChange={this.onTypeChange}
              />
            </When>

            <div
              className={classnames(`${prefixCls}-comp-temp-cat`, {
                [`${prefixCls}-comp-temp-cat-show`]: type === ComTempType.TEMP,
              })}
            >
              <AddCompTemp weId={`${this.props.weId || ''}_gj8kab`} type={type} comDom={comDom} />
            </div>
            <When weId={`${this.props.weId || ''}_2nq23r`} condition={showMaster}>
              <div
                className={classnames(`${prefixCls}-comp-temp-cat`, {
                  [`${prefixCls}-comp-temp-cat-show`]: type === ComTempType.MASTER,
                })}
              >
                <AddMasterTemp
                  weId={`${this.props.weId || ''}_cgx23i`}
                  type={type}
                  comDom={comDom}
                />
              </div>
            </When>
          </div>
        </Dialog>
      </When>
    );
  }
}

// 使用static getDerivedStateFromProps后，无法使用装饰器，故作以下处理
const ObserverDialog = observer(CompTempDialog as any);
const OverWritableDialog = middleware(appName, 'CompTempDialog')(ObserverDialog);
const InjectDialog = inject('compTempStore', 'designStore')(OverWritableDialog);

export default InjectDialog as unknown as ComponentType<CompTempDlgProps>;
