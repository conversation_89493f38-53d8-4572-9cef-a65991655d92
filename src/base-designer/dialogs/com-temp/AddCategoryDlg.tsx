import {
  Button,
  CorsComponent,
  Dialog,
  Form,
  FormItemProps,
  FormStore,
  FormSwitchProps,
  Spin,
} from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import React, { PureComponent } from 'react';
import { When } from 'react-if';
import { dlgIconName, extraProps, prefixCls } from '../../../constants';

export interface AddCategoryDlgProps {
  visible: boolean;
  placeholder?: string;
  saveLoading?: boolean;
  onAdd: (id: string, cateName: string, nameLabelId?: string) => void;
  onClose?: () => void;
  categoryData?: any;
}

const initTempFormValue: any = {
  name: '',
};

const InitLayout = [
  [
    {
      id: 'name',
      label: getLabel('53866', '名称'),
      items: ['name'],
      labelSpan: 6,
      hide: false,
      className: `${prefixCls}-comp-temp-add-name`,
    },
  ],
];

const getInitItems = (placeholder: string): FormItemProps => ({
  name: {
    itemType: 'CUSTOM',
    placeholder,
    required: true,
    autoFocus: true,
    errorType: 'popover',
  },
});

class AddCategoryDlg extends PureComponent<AddCategoryDlgProps> {
  store = new FormStore();

  componentDidUpdate() {
    if (this.props.visible) {
      this.initForm();
    }
  }

  initForm = () => {
    const { placeholder = getLabel('101594', '请输入模板分类名称'), categoryData } = this.props;
    const { defaultName, name } = categoryData || {};

    this.store.initForm({
      data: {
        ...initTempFormValue,
        ...categoryData,
        name: defaultName || name,
      },
      items: getInitItems(placeholder),
      layout: InitLayout,
      groups: [],
    });
  };

  onOk = () => {
    this.store.validate().then((res: any) => {
      const { errors } = res;
      const { id, name, nameLabelId } = this.store.getFormDatas();
      const isStringValue = typeof name === 'string';
      const nameVal = isStringValue ? { nameAlias: name } : name;
      const { nameAlias, nameAliasLabelId = nameLabelId } = nameVal;

      if (isEmpty(errors)) {
        this.props.onAdd(id, nameAlias, nameAliasLabelId);
      }
    });
  };

  customRenderFormSwitch = (key: string, props: FormSwitchProps) => {
    const formData = this.store.getFormDatas();
    const { name, nameLabelId } = formData;

    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_0t295r`}
        app="@weapp/ebdcoms"
        compName="LocaleAnother"
        module="ebuilder_designer"
        tablefield="ebdd_tmpl_category.name"
        value={name}
        targetId={nameLabelId}
        {...props.props}
      />
    );
  };

  render() {
    const {
      onClose, visible, saveLoading, categoryData,
    } = this.props;

    const footer = [
      <Button
        weId="w30m24@submit"
        key="submit"
        type="primary"
        disabled={saveLoading}
        onClick={this.onOk}
      >
        {!saveLoading ? (
          getLabel('97216', '保存')
        ) : (
          <>
            <Spin
              weId={`${this.props.weId || ''}_p74awc}`}
              style={{ marginRight: '5px' }}
              size="small"
            />
            <span> {getLabel('97216', '保存')}</span>
          </>
        )}
      </Button>,
      <Button weId="l5w8u3@close" key="close" type="default" onClick={onClose}>
        {getLabel('53937', '取消')}
      </Button>,
    ];

    return (
      <When weId={`${this.props.weId || ''}_9dx7sl`} condition={visible}>
        <Dialog
          weId={`${this.props.weId || ''}_q5rhy1`}
          wrapClassName={`${prefixCls}-comp-temp-add-cate`}
          visible={visible}
          title={!categoryData ? getLabel('101591', '新建分类') : getLabel('124142', '编辑分类')}
          footer={footer}
          onClose={onClose}
          width={500}
          destroyOnClose
          draggable
          closable
          mask
          icon={dlgIconName}
        >
          <Form
            weId={`${this.props.weId || ''}_c790x9`}
            {...extraProps}
            store={this.store}
            customRenderFormSwitch={this.customRenderFormSwitch}
          />
        </Dialog>
      </When>
    );
  }
}

export default AddCategoryDlg;
