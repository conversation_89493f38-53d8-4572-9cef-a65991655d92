import {
  <PERSON><PERSON>, <PERSON><PERSON>, Input, <PERSON><PERSON>er, Trigger,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { inject, observer } from 'mobx-react';
import React, { Component, SyntheticEvent } from 'react';
import { prefixCls } from '../../../constants';
import { InternalDesignerContext } from '../../Context';
import AddCateDlg from './AddCategoryDlg';
import CompTempStore from './store';
import { ComTempCategory } from './types';
import { ComTempType } from './constants';

export type CatOpts = {
  id: string;
  content: string;
};

const transform = (category: ComTempCategory[]) => category
  .filter((item) => !item.isSys)
  .map((item) => ({
    id: item.id,
    content: item.name,
  }));

interface CategorySelectProps {
  value: string;
  onChange: (tempId: string) => void;
}

@inject('compTempStore')
@observer
class CategorySelect extends Component<
  CategorySelectProps & {
    compTempStore: CompTempStore;
  }
> {
  static contextType = InternalDesignerContext;

  state = {
    visible: false,
    trigVisible: false,
  };

  onChange = (value: CatOpts) => () => {
    const { onChange } = this.props;
    const { id } = value;

    if (id) {
      this.setState({ trigVisible: false });
    }

    onChange(id);
  };

  onAddCate = (id: string, cateName: string, nameLabelId?: string) => {
    const { compTempStore, onChange } = this.props;
    const { createCategroy } = compTempStore.addComTempStore;

    if (!cateName) return;

    createCategroy(cateName, nameLabelId).then((res: any) => {
      onChange(res?.id);
      this.setState({ visible: false });
    });
  };

  openAddDlg = (e: SyntheticEvent) => {
    e.stopPropagation();
    this.setState({
      visible: true,
      trigVisible: false,
    });
  };

  togglePopup = () => {
    const { trigVisible } = this.state;
    this.setState({
      trigVisible: !trigVisible,
    });
  };

  setTrigVisible = (trigVisible: boolean) => {
    this.setState({ trigVisible });
  };

  setVisible = (visible: boolean) => () => {
    this.setState({ visible });
  };

  render() {
    const { compTempStore, value } = this.props;
    const { addComTempStore, type } = compTempStore;
    const { categories } = addComTempStore;
    const { visible, trigVisible } = this.state;

    const data = transform(categories);
    const catName = data.find((item) => item.id === value)?.content || '';
    const { categoryPlaceholder = getLabel('101594', '请输入模板分类名称') } = this.context?.localization.toolbar.template.dialog || {};

    return (
      <div className={`${prefixCls}-comp-temp-add-cat`}>
        <Trigger
          weId={`${this.props.weId || ''}_dm5w0h`}
          action={['click']}
          popupVisible={trigVisible}
          onPopupVisibleChange={this.setTrigVisible}
          popup={
            <div className={`${prefixCls}-comp-temp-add-cat-dropdown ui-select-dropdown`}>
              <Scroller className="list" direction="v" weId={`${this.props.weId || ''}_8ppoqb`}>
                {data.map((cat) => (
                  <li
                    className="ui-select-option"
                    key={cat.id}
                    title={cat.content}
                    onClick={this.onChange(cat)}
                  >
                    {cat.content}
                  </li>
                ))}
              </Scroller>

              <div className="add-box">
                <Button
                  weId={`${this.props.weId || ''}_1qrgli`}
                  type="link"
                  onClick={this.openAddDlg}
                >
                  <Icon weId={`${this.props.weId || ''}_ijigkt`} name="Icon-add-to01" />
                  {getLabel('54007', '新建')}
                </Button>
              </div>
            </div>
          }
        >
          <Input
            weId={`${this.props.weId || ''}_l1d4s1`}
            value={catName}
            className={`${prefixCls}-comp-temp-add-cat-input`}
            style={{ width: 200 }}
            placeholder={getLabel('40502', '请选择')}
            // eslint-disable-next-line react/jsx-no-bind
            onBeforeFocusCheck={() => false}
            suffix={
              <Icon
                weId={`${this.props.weId || ''}_4erzd4`}
                name="Icon-Down-arrow01"
                onClick={this.togglePopup}
              />
            }
          />
        </Trigger>
        <AddCateDlg
          weId={`${this.props.weId || ''}_rcofvl`}
          key={type}
          visible={visible}
          placeholder={
            type === ComTempType.TEMP
              ? categoryPlaceholder
              : getLabel('118359', '请输入母版分类名称')
          }
          onAdd={this.onAddCate}
          onClose={this.setVisible(false)}
        />
      </div>
    );
  }
}

export default CategorySelect;
