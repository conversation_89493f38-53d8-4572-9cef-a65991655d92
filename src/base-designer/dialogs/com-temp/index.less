@import (reference) '../../../style/prefix.less';

.@{prefix}-comp-temp {
  background-color: var(--base-white);
  border: var(--border-solid);
  border-bottom: 0px;

  .ui-form {
    .ui-form-row-first {
      border-top: var(--form-item-border-module);
    }

    .ui-layout-row > .ui-layout-col {
      border-left: 0px;
      border-right: 0px;
    }
  }

  &-cat {
    display: none;

    &-show {
      display: block;
    }
  }

  &-type-select-disable {
    display: flex;
    align-items: center;
    justify-content: center;

    & > span {
      margin-left: 5px;
      cursor: pointer;
    }
  }
}

.@{prefix}-comp-temp-add-cat {
  .ui-btn {
    vertical-align: middle;
    margin-left: 10px;
    text-decoration: underline;
  }

  &-input {
    width: 200;

    input {
      cursor: pointer;
    }

    .ui-input-suffix {
      color: var(--secondary-fc);
      cursor: pointer;
    }
  }

  &-dropdown {
    width: 200px;
    max-height: 313px;
    display: flex;
    flex-direction: column;

    .ui-input-group {
      width: 90%;
      margin: 0 10px;

      div {
        color: var(--primary);
      }
    }

    .add-box {
      flex-basis: 8%;
      display: flex;
      padding: 3px 0 0 12px;

      .ui-icon {
        margin-right: 3px;
        margin-top: -2px;
        vertical-align: middle;
      }
    }

    .list {
      flex: 1;
      padding: 0;
      margin: 0 0 3px 0;
      position: relative;

      .ui-scroller__wrap {
        max-height: 270px;
      }

      li {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        display: block;
      }
    }
  }
}

.@{prefix}-comp-temp-cover-dropdown .ui-select-group {
  .ui-select-option {
    padding: 6px 16px 6px 32px;
  }

  .ui-select-group-title {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}

.@{prefix}-comp-temp-save-btn {
  vertical-align: middle;

  .ui-spin .ui-spin-dot {
    display: block;
  }
}

body[page-rtl='true'] {
  .@{prefix}-comp-temp-add-cat-dropdown .list li.ui-rtl {
    text-align: right;
  }
}
