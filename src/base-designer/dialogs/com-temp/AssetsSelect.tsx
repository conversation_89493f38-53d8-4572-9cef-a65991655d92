import { CorsComponent } from '@weapp/ui';
import { toJS } from 'mobx';
import React, { PureComponent } from 'react';
import { prefixCls } from '../../../constants';
import json from '../../../utils/json';

export enum LibType {
  Icon = 'ICON',
  Pic = 'PIC',
}

export interface AssetsSelectProps extends React.Attributes {
  value: string;
  onChange: (value: string) => void;
}

export default class AssetsSelect extends PureComponent<any> {
  onChange = (val: any) => {
    const { onChange } = this.props;
    const { path } = val;

    const result = path || '';

    onChange(result);
  };

  render() {
    const {
      value, onSelectIcon, eidtorClassName, previewStyle,
    } = this.props;

    const iconValue = json.parse(value) || value;

    return (
      <div className={`${prefixCls}-comp-temp-add-icon`}>
        <CorsComponent
          app="@weapp/ebdcoms"
          compName="IconEidtor"
          weId={`${this.props.weId || ''}_p55nn1`}
          value={toJS(iconValue)}
          type={LibType.Pic}
          onSelect={onSelectIcon}
          onChange={this.onChange}
          editConfig={{ isHideFontSize: true, isHideRadius: true }}
          previewStyle={previewStyle}
          className={eidtorClassName}
        />
      </div>
    );
  }
}
