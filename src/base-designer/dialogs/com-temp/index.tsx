import { ComponentType } from 'react';
import Loadable from '../../../common/loadable';
import { AddCategoryDlgProps } from './AddCategoryDlg';
import { AssetsSelectProps } from './AssetsSelect';
import { CompTempDlgProps } from './types';

export const CompTempDlg = Loadable({
  name: 'CompTempDlg',
  loader: () => import(
    /* webpackChunkName: "de_temp_dialog" */
    './Main'
  ),
}) as ComponentType<CompTempDlgProps>;

export const CompTempAddCategoryDlg = Loadable({
  name: 'CompTempAddCategoryDlg',
  loader: () => import(
    /* webpackChunkName: "de_temp_addcategory_dialog" */
    './AddCategoryDlg'
  ),
}) as ComponentType<AddCategoryDlgProps>;

export const AssetsSelect = Loadable({
  name: 'AssetsSelect',
  loader: () => import(
    /* webpackChunkName: "de_temp_assets_select" */
    './AssetsSelect'
  ),
}) as ComponentType<AssetsSelectProps>;

export default {};
