import { action, computed, observable } from 'mobx';
import CompMasterMaskStore from './master/store';
import CompTemplateStore from './template/store';
import { ComTempConfig } from './types';
import { ComTempType } from './constants';

const defTempConfig = {
  configPC: '',
  configMobile: '',
};

export default class CompTempStore {
  /** 模板store */
  templateStore = new CompTemplateStore(this);

  /** 母版store */
  masterStore = new CompMasterMaskStore(this);

  @observable tempConfig: ComTempConfig = defTempConfig;

  @observable type: ComTempType = ComTempType.TEMP;

  @observable comServicePath: string = 'ebuilder/designer';

  @computed get addComTempStore() {
    if (this.type === ComTempType.TEMP) {
      return this.templateStore;
    }

    return this.masterStore;
  }

  @action
  setComServicePath = (comServicePath: string) => {
    this.comServicePath = comServicePath;
  };

  @action
  setTempConfig = (tempConfig?: ComTempConfig) => {
    this.tempConfig = tempConfig || defTempConfig;
  };

  @action
  setComTempType = (type: ComTempType) => {
    this.type = type;
  };
}
