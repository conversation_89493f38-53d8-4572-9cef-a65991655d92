import { Provider } from 'mobx-react';
import React, { useMemo } from 'react';
import Content from './Dialog';
import './index.less';
import CompTempStore from './store';
import { CompTempDlgProps } from './types';

export default function CompTempMain(props: CompTempDlgProps) {
  const store = useMemo(() => new CompTempStore(), []);

  return (
    <Provider weId={`${props.weId || ''}_0s665h`} compTempStore={store}>
      <Content weId={`${props.weId || ''}_ptd1bo`} {...props} />
    </Provider>
  );
}
