import {
  Bread<PERSON>rumb, Button, Dialog, Icon, Input,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { ReactText, useCallback } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import { HeaderProps } from './types';

const { confirm } = Dialog;

const Header: React.FC<HeaderProps> = (props) => {
  const onBatchDelete = useCallback(() => {
    const deleteObj = props.getBatchDeleteIds();
    let nameBox: string = '';
    const idBox: string[] = [];
    deleteObj.forEach((temp) => {
      nameBox += `「${temp.name}」`;
      idBox.push(temp.id);
    });
    confirm({
      mask: true,
      content: (
        <div>
          {getLabel('118371', '确定要删除母板')}
          <span style={{ color: 'var(--danger)' }}>{nameBox}</span>？
        </div>
      ),
      onOk: () => {
        props.onBatchDelete(idBox);
      },
    });
  }, []);

  const onComNameSearch = useCallback((value: ReactText) => {
    const { onSearch } = props;
    onSearch(`${value}`);
  }, []);

  function getBreadCrumbItems(breadCrumbItems: string[]) {
    return breadCrumbItems.map((t: string) => <span key={t}>{t}</span>);
  }

  const data = getBreadCrumbItems(props.breadCrumbItems);

  return (
    <div className={`${prefixCls}-tempLib-header-content`}>
      <div className={`${prefixCls}-tempLib-header-left`}>
        <BreadCrumb weId={`${props.weId || ''}_jrzunv`} data={data} />
      </div>
      <div className={`${prefixCls}-tempLib-header-right`}>
        <When weId={`${props.weId || ''}_jost4x`} condition={props.canOperate}>
          <Button weId={`${props.weId || ''}_g0b4fk`} type="danger" onClick={onBatchDelete}>
            {getLabel('74407', '批量删除')}
          </Button>
        </When>
        <Input
          className="ui-searchAdvanced-input"
          weId={`${props.weId || ''}_9cs2aj`}
          style={{ width: 200 }}
          allowClear
          suffix={
            <Icon
              className="ui-searchAdvanced-input-icon"
              weId={`${props.weId || ''}_2px8ru`}
              name="Icon-search"
            />
          }
          placeholder={getLabel('118354', '请输入母版名称')}
          onChange={onComNameSearch}
        />
      </div>
    </div>
  );
};

export default Header;
