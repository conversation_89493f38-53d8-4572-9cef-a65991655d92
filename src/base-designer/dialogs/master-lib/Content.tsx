import {
  CorsComponent, Dialog, Layout, List, ListData, Spin,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { prefixCls } from '../../../constants';
import { IComTemplateData } from '../../../types';
import Card from '../temp-lib/Card';
import EditTempDialog from '../temp-lib/components/edit/editTempDialog';
import Header from './Header';
import LeftMenu from './LeftMenu';
import { MasterStoreProps, Operation, TreeNode } from './types';
import UserInfoDlg from './UserInfoDlg';

const { confirm } = Dialog;

interface MasterLibContentState {
  editDiaVisible: boolean;
  editDiaData: ListData | null;
}

const operationOptions = (disabled?: boolean) => [
  {
    id: Operation.Modify,
    content: getLabel('54009', '编辑'),
    icon: 'Icon-Invoice---batch-edit-o',
  },
  {
    id: Operation.UserInfo,
    content: getLabel('118360', '使用情况'),
    icon: 'Icon-Line-chart-switching',
  },
  {
    id: Operation.Delete,
    content: getLabel('53951', '删除'),
    icon: 'Icon-delete',
    disabled,
  },
];

@inject('masterLibStore')
@observer
class MasterLibContent extends PureComponent<MasterStoreProps, MasterLibContentState> {
  constructor(props: MasterStoreProps) {
    super(props);

    this.state = {
      editDiaVisible: false,
      editDiaData: null,
    };
  }

  componentDidMount() {
    const {
      masterLibStore,
      pageScope,
      pageInfo,
      clientType,
      events,
      isSingleClient = false,
    } = this.props;
    const layoutType = pageInfo?.layoutType;

    masterLibStore.init(pageScope, clientType, layoutType, events, isSingleClient);
  }

  componentWillUnmount() {
    this.props.masterLibStore.reset();
  }

  onCheckChange = (rowData: ListData, value: any) => {
    const { onCheckChange: _onCheckChange, masterLibStore } = this.props;
    const { onCheckChange } = masterLibStore;

    onCheckChange(rowData as IComTemplateData, value);

    if (_onCheckChange) {
      _onCheckChange(rowData);
    }
  };

  onClickIcon = (type: string, data: ListData) => {
    const { masterLibStore } = this.props;

    switch (type) {
      case Operation.Delete:
        this.onDeleteTemp(data);
        break;
      case Operation.Modify:
        this.setState({
          editDiaVisible: true,
          editDiaData: data,
        });
        break;
      case Operation.UserInfo:
        this.setState(
          {
            editDiaData: data,
          },
          () => {
            masterLibStore.setUserInfoDlgVisible(true);
          },
        );

        break;
      default:
        break;
    }
  };

  onDeleteTemp = (data: ListData) => {
    const { onBatchDelete } = this.props.masterLibStore;
    confirm({
      mask: true,
      content: (
        <div className={`${prefixCls}-temp-delete-confirm-content`}>
          <span>{getLabel('98883', '确定要删除模板')}</span>
          <span style={{ color: 'var(--danger)' }}>「{data.name}」</span>？
        </div>
      ),
      onOk: () => {
        onBatchDelete([data.id!]);
      },
    });
  };

  onCloseEditDia = () => {
    this.setState({ editDiaVisible: false });
  };

  customRenderCard = (rowData: ListData | ListData[], rowID: number) => {
    const { changeDisabled = false, masterLibStore } = this.props;
    const { isSearching, isDetailCategory, pageScope } = masterLibStore;
    return (
      <Card
        weId={`${this.props.weId || ''}_z4uiva`}
        rowData={rowData}
        rowID={rowID}
        canMove
        menuData={operationOptions(changeDisabled)}
        isSearching={isSearching}
        changeDisabled={changeDisabled}
        onCheckChange={this.onCheckChange}
        onClickIcon={this.onClickIcon}
        isDetailCategory={isDetailCategory}
        pageScope={pageScope}
      />
    );
  };

  render() {
    const { masterLibStore, changeDisabled = false, renderRightCom } = this.props;
    const {
      hasInit,
      treeData = [],
      showOpeBtns,
      onBatchDelete,
      onFilterShowElementsByName,
      currentTreeNodeName = [],
      inputSearch,
      showTemps = [],
      originData = [],
      onEditTempMsg,
      getBatchDeleteIds,
      onTempSortEnd,
      isSearching,
      isDetailCategory,
      createCat,
      tempcategories,
      useInfoDlgVisible,
    } = masterLibStore;
    const { editDiaData } = this.state;

    const canOperate = showOpeBtns();

    return (
      <div className={`${prefixCls}-tempLib-layout`}>
        <Layout
          weId={`${this.props.weId || ''}_lepl1m`}
          className={`${prefixCls}-tempLib-container`}
        >
          <Layout.Box
            weId={`${this.props.weId || ''}_p3ge5a`}
            className={`${prefixCls}-tempLib-header`}
            type="header"
          >
            <When weId={`${this.props.weId || ''}_bwqtdy`} condition={!renderRightCom}>
              <Header
                canOperate={canOperate}
                getBatchDeleteIds={getBatchDeleteIds}
                onBatchDelete={onBatchDelete}
                onSearch={onFilterShowElementsByName}
                breadCrumbItems={currentTreeNodeName}
                weId={`${this.props.weId || ''}_g3r6m7`}
              />
            </When>
          </Layout.Box>

          <Layout.Box
            weId={`${this.props.weId || ''}_aaedp8`}
            className={`${prefixCls}-tempLib-content`}
          >
            <If weId={`${this.props.weId || ''}_1bfqaf`} condition={hasInit}>
              <Then weId={`${this.props.weId || ''}_tockt3`}>
                <If weId={`${this.props.weId || ''}_gnzmc7`} condition={showTemps.length > 0}>
                  <Then weId={`${this.props.weId || ''}_qbsbbx`}>
                    <List
                      weId={`${this.props.weId || ''}_p2lh9g`}
                      direction="row"
                      data={toJS(showTemps)}
                      boxSelection
                      sortable={!isSearching && isDetailCategory && !changeDisabled}
                      sortableOptions={{
                        onEnd(evt: any) {
                          onTempSortEnd([evt.oldIndex, evt.newIndex]);
                        },
                      }}
                      customRenderContent={this.customRenderCard}
                    />
                  </Then>
                  <Else weId={`${this.props.weId || ''}_qp08f2`}>
                    <CorsComponent
                      app="@weapp/ebdcoms"
                      compName="Empty"
                      weId={`${this.props.weId || ''}_h6cagf`}
                      type={inputSearch ? 'search' : 'noData'}
                    />
                  </Else>
                </If>
              </Then>
              <Else weId={`${this.props.weId || ''}_hxjkc7`}>
                <Spin
                  style={{ textAlign: 'center', paddingTop: '205px' }}
                  weId={`${this.props.weId || ''}_v6c4n1`}
                />
              </Else>
            </If>
          </Layout.Box>
          <Layout.Box
            weId={`${this.props.weId || ''}_eygn38`}
            className={`${prefixCls}-tempLib-leftMenu`}
            type="side"
            width={180}
            outer
          >
            <If weId={`${this.props.weId || ''}_iuzxr6`} condition={hasInit}>
              <Then weId={`${this.props.weId || ''}_nwjw7c`}>
                <LeftMenu
                  weId={`${this.props.weId || ''}_blfda1`}
                  store={masterLibStore}
                  value={toJS(treeData)}
                  changeDisabled={changeDisabled}
                />
              </Then>
              <Else weId={`${this.props.weId || ''}_3ph3qv`}>
                <Spin
                  weId={`${this.props.weId || ''}_9b3qf3`}
                  style={{ textAlign: 'center', paddingTop: '205px' }}
                />
              </Else>
            </If>
          </Layout.Box>
        </Layout>
        <EditTempDialog
          weId={`${this.props.weId || ''}_191ocj`}
          type="MASTER"
          title={getLabel('118369', '编辑母板')}
          visible={this.state.editDiaVisible}
          editData={this.state.editDiaData}
          originData={toJS(originData)}
          categoryData={[null, ...toJS(tempcategories)] as TreeNode[]}
          onCreateCat={createCat}
          onClose={this.onCloseEditDia}
          saveDisabled={changeDisabled}
          onOk={onEditTempMsg}
        />
        <UserInfoDlg
          weId={`${this.props.weId || ''}_rvti3r`}
          visible={useInfoDlgVisible}
          data={editDiaData}
        />
      </div>
    );
  }
}

export default MasterLibContent as unknown as ComponentType<{
  onCheckChange?: (rowData: ListData) => void;
}>;
