import { Provider } from 'mobx-react';
import React, { PureComponent } from 'react';
import '../temp-lib/index.less';
import Main from './Main';
import masterLibStore from './store';
import { MasterLibProps } from './types';

export default class MasterLib extends PureComponent<MasterLibProps> {
  render() {
    return (
      <Provider weId={`${this.props.weId || ''}_mwrbkj`} masterLibStore={masterLibStore}>
        <Main weId={`${this.props.weId || ''}_w22ict`} {...this.props} />
      </Provider>
    );
  }
}
