import {
  Button, Dialog, Icon, Input,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, ReactText } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import MasterLibContent from './Content';
import './index.less';
import { MasterLibProps, MasterStoreProps } from './types';

const { confirm } = Dialog;

@inject('masterLibStore')
@observer
class Main extends React.PureComponent<MasterLibProps & MasterStoreProps> {
  componentDidMount() {
    this.props.events?.on('update.master', this.onDataLoad);
    this.props.renderRightCom?.(this.renderRightCom());
  }

  componentWillUnmount() {
    this.props.events?.off('update.master', this.onDataLoad);
  }

  onDataLoad = () => {
    this.props.onDataLoad?.();
  };

  onCheckChange = () => {
    this.props.renderRightCom?.(this.renderRightCom());
  };

  onBatchDelete = () => {
    const { getBatchDeleteIds, onBatchDelete } = this.props.masterLibStore;
    const deleteObj = getBatchDeleteIds();
    let nameBox: string = '';
    const idBox: string[] = [];
    deleteObj.forEach((temp) => {
      nameBox += `「${temp.name}」`;
      idBox.push(temp.id);
    });
    confirm({
      mask: true,
      content: (
        <div className={`${prefixCls}-temp-delete-confirm-content`}>
          <span>{getLabel('98883', '确定要删除模板')}</span>
          <span style={{ color: 'var(--danger)' }}>{nameBox}</span>？
        </div>
      ),
      onOk: () => {
        onBatchDelete(idBox);
      },
    });
  };

  onComNameSearch = (value: ReactText) => {
    const { onFilterShowElementsByName } = this.props.masterLibStore;

    onFilterShowElementsByName(`${value}`);
  };

  renderRightCom = () => (
    <div className={`${prefixCls}-masterLib-header-right`}>
      <When
        weId={`${this.props.weId || ''}_jost4x`}
        condition={this.props.masterLibStore.showOpeBtns()}
      >
        <Button weId={`${this.props.weId || ''}_g0b4fk`} type="danger" onClick={this.onBatchDelete}>
          {getLabel('74407', '批量删除')}
        </Button>
      </When>
      <Input
        className="ui-searchAdvanced-input"
        weId={`${this.props.weId || ''}_9cs2aj`}
        style={{ width: 200 }}
        allowClear
        suffix={
          <Icon
            className="ui-searchAdvanced-input-icon"
            weId={`${this.props.weId || ''}_2px8ru`}
            name="Icon-search"
          />
        }
        placeholder={getLabel('118354', '请输入母版名称')}
        onChange={this.onComNameSearch}
      />
    </div>
  );

  render() {
    return (
      <MasterLibContent
        weId={`${this.props.weId || ''}_vicd58`}
        {...this.props}
        onCheckChange={this.onCheckChange}
      />
    );
  }
}

export default Main as unknown as ComponentType<MasterLibProps>;
