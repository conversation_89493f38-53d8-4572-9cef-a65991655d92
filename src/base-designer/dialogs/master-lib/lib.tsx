import React, { ComponentType, Suspense } from 'react';
import { MasterLibProps } from './types';

const LazyMasterLib = React.lazy(
  () => import(
    /* webpackChunkName: "de_master_lib" */
    './index'
  ),
) as ComponentType<MasterLibProps>;

const MasterLib: React.FC<MasterLibProps> = (props: MasterLibProps) => (
  <Suspense weId={`${props.weId || ''}_g49qpo`} fallback={<div />}>
    <LazyMasterLib weId={`${props.weId || ''}_lf8h1b`} {...props} />
  </Suspense>
);

export default MasterLib;
