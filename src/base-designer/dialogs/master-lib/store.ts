import { Dialog, ListData } from '@weapp/ui';
import {
  cloneDeep, getLabel, isArray, isEmpty, isEqual, isString,
} from '@weapp/utils';
import {
  action, computed, observable, runInAction, toJS,
} from 'mobx';
import React from 'react';
import { EventEmitter } from '../../../core/utils';
import {
  ClientType, IComTemplateData, LayoutType, PageModuleType,
} from '../../../types';
import ebdcoms from '../../../utils/ebdcoms';
import { CompTempDataType } from '../../panels/template/types';
import { utils } from '../temp-lib/Card';
import { EditTempData, TreeNode } from './types';

const { message } = Dialog;

export const isVaildTempConfig = (configStr: string) => {
  if (!configStr) return false;
  const config = utils.isJsonStr(configStr) ? JSON.parse(configStr) : configStr;

  if (isArray(config) && isEmpty(config.filter((comp: any) => !!comp))) {
    return false;
  }
  return true;
};

const DefaultTreeNode = () => [
  {
    id: '100',
    name: '',
    fullname: [''],
    children: [],
    index: ['100'],
  },
] as TreeNode[];

export class MasterLibStore {
  events?: EventEmitter = new EventEmitter();

  /**
   * 显示模板
   */
  @observable showTemps: IComTemplateData[] = [];

  /**
   * 树节点
   */
  @observable treeData: TreeNode[] = [];

  /**
   * 当前选中目录索引
   */
  @observable currentTreeNodeIndex: string[] = [];

  /**
   * 当前选中目录名称
   */
  @observable currentTreeNodeName: string[] = [];

  /**
   * 组件作用域
   */
  @observable pageScope: PageModuleType = 'EB_PAGE';

  /**
   * 接口请求过来的原始数据
   */
  @observable originData: CompTempDataType[] = [];

  /**
   * 名称搜索条件
   */
  @observable inputSearch: string = '';

  @observable cateSearch: string = '';

  @observable focusId: string = '';

  @observable clientType: ClientType = 'PC';

  @observable hasInit: boolean = false;

  @observable activeListItem: ListData[] = [];

  @observable useInfoDlgVisible: boolean = false;

  /** 单终端 */
  @observable isSingleClient = false;

  layoutType?: LayoutType = 'GRID';

  @action.bound
  init(
    pageScope: PageModuleType,
    clientType: ClientType,
    layoutType?: LayoutType,
    events?: EventEmitter,
    isSingleClient = false,
  ) {
    this.inputSearch = '';
    this.clientType = clientType;
    this.layoutType = layoutType;
    this.hasInit = false;
    this.events = events;
    this.isSingleClient = isSingleClient;

    this.getAllTemplates(pageScope).then(() => {
      runInAction(() => {
        this.events?.emit('update.master');
        this.pageScope = pageScope;
        this.treeData = this.getTreeNode();
        const defaultIndex = 0;
        const defaultTree = toJS(this.treeData[defaultIndex]);
        // 初始化时默认为第一个有值的大分类
        this.currentTreeNodeIndex = [...defaultTree.index, defaultTree.children[0].id];
        this.currentTreeNodeName = [defaultTree.children[0]?.name];

        this.setListItem([defaultTree.children[0]]);

        this.getShowTemplatesByTreeId();
        this.hasInit = true;
      });
    });
  }

  @action.bound
  getTreeNode() {
    const treeData: TreeNode[] = cloneDeep(DefaultTreeNode());
    const caretogyText = toJS(this.cateSearch);

    this.originData.forEach((data: CompTempDataType) => {
      const {
        id, name, nameLabelId, defaultName,
      } = data;
      const pData = treeData[0];

      if (isString(caretogyText)) {
        if (name.toLocaleLowerCase().includes(caretogyText.toLocaleLowerCase())) {
          pData.children.push({
            id,
            name,
            children: [],
            index: [pData.id, id],
            fullname: [name],
            nameLabelId,
            defaultName,
          });
        }
      } else {
        pData.children.push({
          id,
          name,
          children: [],
          index: [pData.id, id],
          fullname: [name],
          nameLabelId,
          defaultName,
        });
      }
    });

    return treeData;
  }

  @action.bound
  getAllTemplates(pageScope: PageModuleType = 'EB_PAGE') {
    const params = {
      module: pageScope,
      layoutType: this.layoutType,
      isAll: true,
    } as any;

    if ((pageScope as any) !== 'EB_PAGE' && this.isSingleClient) {
      params.clientType = this.clientType;
    }

    if (params.layoutType) {
      delete params.isAll;
    }

    return ebdcoms.asyncExcu('ajax', {
      url: '/api/bs/ebuilder/designer/master/listWithCategory',
      params,
      error: () => {
        runInAction(() => {
          this.hasInit = true;
        });
        return true;
      },
      success: (data: any) => {
        runInAction(() => {
          this.originData = data?.map((cate: CompTempDataType) => ({
            ...cate,
            templates: cate.templates
              ? cate.templates.filter(
                // eslint-disable-next-line max-len
                (temp: IComTemplateData) => isVaildTempConfig(temp?.configMobile) || isVaildTempConfig(temp?.configPC),
              )
              : [],
          }));
        });
      },
    });
  }

  @action.bound
  createCat = (cat: string, nameLabelId?: string) => ebdcoms.asyncExcu('ajax', {
    url: '/api/bs/ebuilder/designer/tmpl/category/create',
    method: 'post',
    params: {
      module: this.pageScope,
      name: cat,
      templateType: 'MASTER',
      nameLabelId,
    },
    success: (data: CompTempDataType) => {
      message({
        type: 'success',
        content: getLabel('54117', '保存成功'),
      });
      const {
        creator, deleteType, id, isSys, name, showOrder,
      } = data;
      runInAction(() => {
        this.originData.push({
          creator,
          deleteType,
          id,
          isSys,
          name,
          showOrder,
          templates: [],
        });
        this.treeData = this.getTreeNode();
      });
    },
  });

  @action.bound
  onTreeNodeClick = (index: string[], fullname: string[]) => {
    this.currentTreeNodeIndex = index;
    this.currentTreeNodeName = fullname;

    this.getShowTemplatesByTreeId();
  };

  filterInput = (ele: IComTemplateData) => {
    if (ele.name.toLowerCase().includes(this.inputSearch.toLowerCase())) return true;
    return false;
  };

  /**
   * 根据索引获取节点下所有数据
   */
  @action.bound
  getShowTemplatesByTreeId = () => {
    const index = toJS(this.currentTreeNodeIndex);

    if (index && index.length === 1) {
      const caretogyId = index[0];
      this.showTemps = this.getTemplatesByCaretogy(caretogyId.toString()).filter(this.filterInput);
      return;
    }

    const tempValues = toJS(
      this.originData.find((caretogy) => index.indexOf(caretogy?.id as any) !== -1),
    );

    this.showTemps = tempValues?.templates.filter(this.filterInput) || [];
  };

  getTemplatesByCaretogy = (caretogyId: string) => {
    const result: IComTemplateData[] = [];
    const defaultTreeNode = DefaultTreeNode();
    toJS(this.originData).forEach((caretogy) => {
      const customCaregotyId = defaultTreeNode[0].id;
      if ((caretogyId === customCaregotyId || caretogyId === '0') && caretogy.templates) {
        result.push(...caretogy.templates);
      }
    });
    return result;
  };

  @action.bound
  getBatchDeleteIds() {
    const batchObj: CompTempDataType[] = [];
    this.showTemps.forEach((template) => {
      if (template?.checked) {
        batchObj.push(template as any);
      }
    });
    return batchObj;
  }

  /**
   * 批量删除模板
   * @param idBox
   * @returns
   */
  @action.bound
  onBatchDelete(idBox: string[]) {
    const data = new FormData();
    idBox.forEach((id) => data.append('ids', id));

    data.append('module', this.pageScope);

    return ebdcoms.asyncExcu('ajax', {
      url: '/api/bs/ebuilder/designer/master/delete',
      method: 'POST',
      data,
      success: () => {
        message({
          type: 'success',
          content: getLabel('54119', '删除成功'),
        });

        runInAction(() => {
          this.originData = this.originData.map((_originData) => {
            const { templates = [] } = _originData;

            const _templates = templates.filter((template) => !idBox.includes(template.id));

            return { ..._originData, templates: _templates };
          });

          this.refreshData();
        });
      },
    });
  }

  @action.bound
  onFilterShowElementsByName(name: string) {
    this.inputSearch = name;
    this.getShowTemplatesByTreeId();
    let temps: IComTemplateData[] = [];

    temps = this.showTemps.filter(this.filterInput);

    this.showTemps = temps;
  }

  @action.bound
  onCheckChange(data: IComTemplateData, value: any) {
    let changed = false;
    const changedTemps = this.showTemps.map((ele: IComTemplateData) => {
      if (ele.id === data.id && ele.checked !== value) {
        changed = true;
        return {
          ...ele,
          checked: value,
        };
      }
      return ele;
    });
    if (changed) {
      this.showTemps = changedTemps;
    }
  }

  /**
   * 折叠标题
   */
  @action.bound
  onClickCollapseTitle(newIndex: string[], newName: string[]) {
    this.currentTreeNodeIndex = newIndex;
    this.currentTreeNodeName = newName;
    this.getShowTemplatesByTreeId();
  }

  @action.bound
  setListItem(activeRowKey: ListData[]) {
    this.activeListItem = activeRowKey;
  }

  @action.bound
  getCheckedEle() {
    const checkded: IComTemplateData[] = [];
    this.showTemps.map((ele: IComTemplateData) => {
      if (ele.checked) {
        checkded.push(ele);
      }
      return ele;
    });
    return checkded;
  }

  @action.bound
  showOpeBtns() {
    return this.getCheckedEle().length > 0;
  }

  @action.bound
  setFocusId(focusId: string) {
    this.focusId = focusId;
  }

  @action.bound
  onCategorySearch(text: React.ReactText) {
    this.cateSearch = text as string;
    this.treeData = this.getTreeNode();
  }

  /**
   * 编辑模板信息成功回调
   * @param
   */
  @action
  onEidtTempMsgSuccess = (newTempData: EditTempData) => {
    const {
      id, name, category, icon, nameLabelId = '',
    } = newTempData;
    let currentTemp: IComTemplateData;
    let currentTemps: IComTemplateData[];
    this.originData = this.originData.map((_category) => {
      if (_category.id === category) {
        let hasCurrentTemp = false;
        _category.templates = _category.templates.map((temp) => {
          if (temp.id === id) {
            hasCurrentTemp = true;
            return {
              ...temp,
              name,
              icon,
              nameLabelId,
            };
          }
          return temp;
        });
        if (!hasCurrentTemp) {
          if (currentTemp) {
            _category.templates.push({
              ...currentTemp,
              name,
              icon,
              nameLabelId,
            });
          } else {
            currentTemps = _category.templates;
            currentTemp = {
              name,
              icon,
              nameLabelId,
            } as IComTemplateData;
          }
        }
      } else {
        _category.templates = _category.templates.filter((temp) => {
          const isCurrentTemp = temp.id === id;
          if (isCurrentTemp) {
            if (!currentTemps) {
              currentTemp = temp;
            } else {
              currentTemps.push({
                ...temp,
                ...currentTemp,
              });
            }
          }
          return !isCurrentTemp;
        });
      }
      return _category;
    });
  };

  /**
   * 编辑模板信息
   * @param newTempData
   */
  @action.bound
  onEditTempMsg(newTempData: EditTempData) {
    const {
      id, name, category, icon, nameLabelId = '',
    } = newTempData;

    const data = new FormData();
    data.append('id', id);
    data.append('name', name);
    data.append('fileId', icon);
    data.append('category', category);
    data.append('module', this.pageScope);
    data.append('layoutType', this.layoutType || 'GRID');
    data.append('nameLabelId', nameLabelId);

    return ebdcoms.asyncExcu('ajax', {
      url: '/api/bs/ebuilder/designer/master/update',
      method: 'POST',
      data,
      success: () => {
        message({
          type: 'success',
          content: getLabel('54117', '保存成功'),
        });
        this.onEidtTempMsgSuccess(newTempData);
        this.refreshData();
      },
    });
  }

  @action.bound
  onEditCategoryMsg(id: string, name: string, nameLabelId = '') {
    const data = new FormData();

    data.append('id', id);
    data.append('name', name);
    data.append('templateType', 'MASTER');
    data.append('nameLabelId', nameLabelId);

    const treeData = cloneDeep(toJS(this.treeData));
    treeData[0].children.map((item: any) => {
      if (item.id === id) {
        item.name = name;
      }
      return item;
    });
    this.treeData = treeData;

    return ebdcoms.asyncExcu('ajax', {
      url: '/api/bs/ebuilder/designer/tmpl/category/update',
      method: 'POST',
      data,
      success: () => {
        message({
          type: 'success',
          content: getLabel('41892', '修改成功'),
        });

        runInAction(() => {
          this.updateCurrentNodeName();
          this.focusId = '';
          this.refreshData();
        });
      },
    });
  }

  /**
   * 模板排序
   * @param sortBox
   */
  @action.bound
  onTempSortEnd(sortBox: number[]) {
    const [oldIndex, newIndex] = sortBox;

    if (oldIndex === newIndex) return;

    const temLength = this.showTemps.length;
    const srcId = this.showTemps[oldIndex]?.id;
    // 拖拽到最后位置时传 最后一个组件id
    // eslint-disable-next-line operator-linebreak
    const targetId =
      newIndex >= temLength ? this.showTemps[temLength]?.id : this.showTemps[newIndex]?.id;

    const data = new FormData();

    data.append('srcId', srcId);
    data.append('targetId', targetId);
    data.append('module', this.pageScope);

    return ebdcoms.asyncExcu('ajax', {
      url: '/api/bs/ebuilder/designer/master/sort',
      method: 'POST',
      data,
      success: () => {
        message({
          type: 'success',
          content: getLabel('54026', '操作成功'),
        });
        runInAction(() => {
          // 模版拖动排序
          const _currentTreeNodeIndex = toJS(this.currentTreeNodeIndex);

          if (isEmpty(_currentTreeNodeIndex)) return;

          const oldTempIndex = _currentTreeNodeIndex.pop();

          this.originData = this.originData.map((_originData: CompTempDataType) => {
            const { id, templates } = _originData;

            if (isEqual(oldTempIndex, id)) {
              const _templates = toJS(templates);
              const srcIndex = templates.findIndex((_data) => _data.id === srcId);
              const targetIndex = templates.findIndex((_data) => _data.id === targetId);

              if (srcIndex >= 0 && targetIndex >= 0) {
                const temp = _templates.splice(srcIndex, 1);

                _templates.splice(targetIndex, 0, ...temp);
                _originData.templates = _templates;
              }
            }

            return _originData;
          });

          this.refreshData();
        });
      },
    });
  }

  /**
   * 分类排序
   * @param sortBox
   * @returns
   */
  @action.bound
  onCategorySortEnd(sortBox: number[]) {
    const [oldIndex, newIndex] = sortBox;

    if (oldIndex === newIndex) return;

    const customerCategories = this.treeData[0].children;

    const srcId = customerCategories[oldIndex]?.id;
    const targetId = customerCategories[newIndex]?.id;

    const data = new FormData();

    data.append('srcId', srcId);
    data.append('targetId', targetId);
    data.append('templateType', 'MASTER');

    return ebdcoms.asyncExcu('ajax', {
      url: '/api/bs/ebuilder/designer/tmpl/category/sort',
      method: 'POST',
      data,
      success: () => {
        message({
          type: 'success',
          content: getLabel('54026', '操作成功'),
        });
        runInAction(() => {
          // 分类拖动排序
          const srcIndex = this.originData.findIndex((_data) => _data.id === srcId);
          const targetIndex = this.originData.findIndex((_data) => _data.id === targetId);

          if (srcIndex >= 0 && targetIndex >= 0) {
            const originData = toJS(this.originData);
            const temp = originData.splice(srcIndex, 1);

            originData.splice(targetIndex, 0, ...temp);
            this.originData = originData;
          }

          this.refreshData();
        });
      },
    });
  }

  /**
   * 删除分类
   * @param id
   */
  @action.bound
  onDeleteCategory(id: string) {
    return ebdcoms.asyncExcu('ajax', {
      url: '/api/bs/ebuilder/designer/tmpl/category/delete',
      method: 'POST',
      params: { id, templateType: 'MASTER' },
      success: () => {
        message({
          type: 'success',
          content: getLabel('54119', '删除成功'),
        });

        runInAction(() => {
          if (this.activeListItem[0].id === id) {
            // 默认的第一条数据
            const firstItem = this.treeData[0].children[0];
            this.setListItem([firstItem]);
            // 手动更新当前选中的分类
            this.currentTreeNodeIndex = firstItem?.index;
            this.currentTreeNodeName = firstItem?.fullname;
          }

          this.originData = this.originData.filter((data) => data.id !== id);

          this.refreshData();
        });
      },
    });
  }

  @action.bound
  refreshData() {
    this.events?.emit('update.master');

    runInAction(() => {
      this.treeData = this.getTreeNode();
      this.getShowTemplatesByTreeId();
    });
  }

  @action.bound
  reset() {
    this.showTemps = [];
    this.activeListItem = [];
    this.currentTreeNodeIndex = [];
    this.currentTreeNodeName = [];
    this.cateSearch = '';
    this.isSingleClient = false;
  }

  @action.bound
  updateCurrentNodeName() {
    const _currentTreeNodeIndex = toJS(this.currentTreeNodeIndex);

    if (isEmpty(_currentTreeNodeIndex)) return;

    const oldTempIndex = _currentTreeNodeIndex.pop();

    this.originData.forEach((data: CompTempDataType) => {
      const { id, name } = data;

      if (isEqual(oldTempIndex, id)) {
        this.currentTreeNodeName = [name];
      }
    });
  }

  @computed get isSearching() {
    return !isEmpty(this.inputSearch);
  }

  @computed get isDetailCategory() {
    return this.currentTreeNodeIndex.length > 1;
  }

  @computed get tempcategories() {
    const tempcates: TreeNode[] = cloneDeep(DefaultTreeNode());

    this.originData.forEach((data: CompTempDataType) => {
      const { id, name } = data;
      const pData = tempcates[0];

      pData.children.push({
        id,
        name,
        children: [],
        index: [pData.id, id],
        fullname: [pData.name, name],
      });
    });
    return tempcates;
  }

  @action
  setUserInfoDlgVisible = (visible: boolean) => {
    this.useInfoDlgVisible = visible;
  };
}

const masterLibStore = new MasterLibStore();

export default masterLibStore;
