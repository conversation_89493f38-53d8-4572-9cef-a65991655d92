@import (reference) '../../../style/prefix.less';

.@{prefix}-masterlib-left-list {
  &.ui-list {
    .ui-list-item {
      &.isActive,
      &:hover {
        background-color: #f5f5f5;
      }

      .ui-list-content {
        display: block;
        min-height: 30px;

        & > div {
          padding-left: 10px;
        }
      }
    }
  }

  .ui-icon {
    transform: rotate(0) !important;
  }

  &-search {
    width: 100%;
    display: flex;
    align-items: center;

    & > div {
      margin-right: 9px;
    }

    &-icon {
      cursor: pointer;

      &:hover {
        & > svg {
          color: var(--primary);
        }
      }

      & > svg {
        width: 19px;
        height: 19px;
      }
    }
  }
}

.@{prefix}-user-info-content {
  & > div:first-of-type {
    width: 150px;
    margin-bottom: 15px;
  }
}

.@{prefix}-masterLib-header-right {
  display: inline-flex;
  justify-content: space-between;
  line-height: 30px;

  .ui-btn {
    margin-right: 14px;
  }
}

.@{prefix}-master-lib-left-icon-null {
  margin-right: 4px;
}

body[page-rtl='true'] {
  .@{prefix}-temp-delete-confirm-content {
    direction: ltr;
  }
}
