import {
  Button, Dialog, Input, ListData, Table,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { inject, observer } from 'mobx-react';
import React, { ComponentType } from 'react';
import { dlgIconName, prefixCls } from '../../../constants';
import ebdcoms from '../../../utils/ebdcoms';
import './index.less';
import { MasterLibStore } from './store';

interface UserInfoDlgProps extends React.Attributes {
  visible: boolean;
  data: ListData | null;
}

interface UserInfoDlgStates {
  loading: boolean;
  datas: any[];
  origalDatas: any[];
  filterVal: string;
  current: number;
  pageSize: number;
}

const columns = [
  {
    title: getLabel('53866', '名称'),
    dataIndex: 'name',
  },
  {
    title: getLabel('118366', '所属应用'),
    dataIndex: 'belong',
  },
  {
    title: getLabel('118367', '客户端'),
    dataIndex: 'terminal',
  },
];

@inject('masterLibStore')
@observer
class UserInfoDlg extends React.PureComponent<
  UserInfoDlgProps & {
    masterLibStore: MasterLibStore;
  },
  UserInfoDlgStates
> {
  constructor(
    props: UserInfoDlgProps & {
      masterLibStore: MasterLibStore;
    },
  ) {
    super(props);

    this.state = {
      loading: false,
      origalDatas: [],
      datas: [],
      filterVal: '',
      current: 1,
      pageSize: 10,
    };
  }

  componentDidUpdate(
    preProps: UserInfoDlgProps & {
      masterLibStore: MasterLibStore;
    },
  ) {
    const { visible } = this.props;

    if (preProps.visible !== visible && visible) {
      this.init();
    }
  }

  init = () => {
    this.setState({
      loading: true,
      current: 1,
      pageSize: 10,
      filterVal: '',
    });

    this.getData();
  };

  getData = () => {
    const { data } = this.props;

    ebdcoms.asyncExcu('ajax', {
      url: '/api/bs/ebuilder/designer/master/useInfo',
      params: { id: data!.id },
      success: (_data: any[]) => {
        this.setState({ origalDatas: _data, datas: _data, loading: false });
      },
    });
  };

  onClose = () => {
    const { setUserInfoDlgVisible } = this.props.masterLibStore;

    setUserInfoDlgVisible(false);
  };

  onPressEnter = (value: React.ReactText) => {
    const { origalDatas } = this.state;
    const datas = origalDatas.filter((data) => data.name.includes(value as string));

    this.setState({ datas, filterVal: value as string, current: 1 });
  };

  onFilterValChange = (value: React.ReactText) => {
    this.setState({ filterVal: value as string });
  };

  onChange = (value: number, pageSize: number) => {
    this.setState({ current: value, pageSize });
  };

  getPageInfo = () => {
    const { current, pageSize, datas } = this.state;

    return {
      value: current,
      pageSize,
      total: datas.length,
      showSizeChanger: true,
      showTotal: false,
      showJumper: false,
      pageSizeOptions: [10, 20, 50, 100],
      onChange: this.onChange,
    };
  };

  render() {
    const { visible, data } = this.props;
    const { loading, datas, filterVal } = this.state;
    const footer = [
      <Button
        weId={`${this.props.weId || ''}_ti6kr6`}
        key="submit"
        type="primary"
        onClick={this.onClose}
      >
        {getLabel('40565', '确定')}
      </Button>,
    ];

    return (
      <Dialog
        weId={`${this.props.weId || ''}_kx0h0w`}
        visible={visible}
        onClose={this.onClose}
        title={`${getLabel('118360', '使用情况')}-${data?.name}`}
        width={700}
        height={382}
        closable
        destroyOnClose
        draggable
        mask
        maskClosable
        resize
        icon={dlgIconName}
        footer={footer}
      >
        <div className={`${prefixCls}-user-info-content`}>
          <Input
            weId={`${this.props.weId || ''}_ac74cq`}
            value={filterVal}
            allowClear
            onChange={this.onFilterValChange}
            onClear={this.onFilterValChange}
            onPressEnter={this.onPressEnter}
            placeholder={getLabel('54065', '请输入名称')}
          />
          <Table
            weId={`${this.props.weId || ''}_09o86w`}
            columns={columns}
            data={datas}
            pageInfo={this.getPageInfo()}
            isShowHead
            loading={loading}
            scroll={{ y: 122 }}
          />
        </div>
      </Dialog>
    );
  }
}

export default UserInfoDlg as unknown as ComponentType<UserInfoDlgProps>;
