import {
  CorsComponent,
  Dialog,
  Icon,
  Input,
  ITreeData,
  List,
  ListData,
  <PERSON>over,
  Scroller,
} from '@weapp/ui';
import { classnames, getLabel, isEmpty } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { PureComponent, SyntheticEvent } from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { prefixCls } from '../../../constants';
import './index.less';
import { LeftMenuProps, TreeNode } from './types';

const { confirm } = Dialog;

interface UnionListProps {
  data: TreeNode[];
  emptyContent: React.ReactNode;
  onSelect: (selected: ITreeData) => void;
  searchText: string;
  searchPlaceholder: string;
  selectedKeys: string[];
  focusId: string;
  activeListItem: ListData[];
  setListItem: (activeRowKey: ListData[]) => void;
  currentTreeNodeIndex: string[];
  /** 无权保存、修改配置 */
  changeDisabled?: boolean;
  createCat: (cat: string, nameLabelId?: string) => Promise<any>;
  refreshData: () => void;
  setFocusId: (focusId: string) => void;
  onCategorySortEnd: (sortBox: number[]) => void;
  onCategorySearch: (text: React.ReactText) => void;
  onEditCategoryMsg: (id: string, cateName: string, nameLabelId?: string) => void;
  onDeleteCategory: (id: string) => void;
  onClickCollapseTitle: (newIndex: string[], newName: string[]) => void;
}

interface UnionListStates {
  addCategoryDlgVisible: boolean;
  curOperateCategory: ListData | null;
}

class UnionList extends PureComponent<UnionListProps, UnionListStates> {
  state = { addCategoryDlgVisible: false, curOperateCategory: null };

  setExpand = (activeRowKey: ListData[]) => {
    this.props.setListItem(activeRowKey);
  };

  onSelect = (selected: ITreeData) => {
    const { onSelect } = this.props;
    this.setExpand([selected]);

    onSelect(selected);
  };

  setCaretogyName = (id: string, cateName: string, nameLabelId?: string) => {
    this.props.onEditCategoryMsg(id, cateName, nameLabelId);
  };

  setEdit = (category: ListData) => (e: SyntheticEvent) => {
    e.stopPropagation();

    this.setState({
      addCategoryDlgVisible: true,
      curOperateCategory: category,
    });
  };

  sureEdit = (e: SyntheticEvent) => {
    e.stopPropagation();
  };

  onDelete = (data: ListData) => (e: SyntheticEvent) => {
    e.stopPropagation();

    confirm({
      mask: true,
      content: <div>{getLabel('144209', '删除分类会级联删除分类下的母版，确认要删除吗？')}</div>,
      onOk: () => {
        this.props.onDeleteCategory(data.id!);
      },
    });
  };

  onTitleClick = (tree: TreeNode) => () => {
    this.props.setListItem([]);
    this.props.onClickCollapseTitle(tree.index, tree.fullname);
  };

  onAddCate = (id: string, cat: string, nameLabelId?: string) => {
    const { createCat, refreshData } = this.props;

    if (!id) {
      createCat(cat, nameLabelId).then(() => {
        refreshData();
      });
    } else {
      this.props.onEditCategoryMsg(id, cat, nameLabelId);
    }

    this.onAddCategoryDlgClose();
  };

  onAddCategoryDlgClose = () => {
    this.setState({ addCategoryDlgVisible: false, curOperateCategory: null });
  };

  onAddCategoryDlgShow =
    (addDisabled: boolean = false) => () => {
      if (addDisabled) return;

      this.setState({ addCategoryDlgVisible: true });
    };

  customRenderItem =
    (isSystem: boolean, changeDisabled: boolean = false) => (item: ListData) => {
      const isDefault = item?.id === '-1';
      const isForbidden = isSystem || isDefault;
      return (
        <div
          className={classnames(`${prefixCls}-tempLib-unionlist-item`, {
            system: isForbidden,
            dragable: !isForbidden,
          })}
          key={item.id}
        >
          {isForbidden ? (
            <span className={`icon-null move ${prefixCls}-master-lib-left-icon-null`} />
          ) : (
            <Icon
              weId={`${this.props.weId || ''}_cgd7f6`}
              name="Icon-move"
              className="my-handle move"
            />
          )}
          <span className={`${prefixCls}-tempLib-unionlist-item-name`}>
            <div className={`${prefixCls}-assets-input-text name`}>
              <span title={item.name}>{item.name}</span>
            </div>
          </span>
          <When weId={`${this.props.weId || ''}_9tf2kt`} condition={!isForbidden}>
            <span
              className={`${prefixCls}-tempLib-unionlist-item-operation`}
              style={{ display: changeDisabled ? 'flex' : 'block' }}
            >
              <If
                weId={`${this.props.weId || ''}_hxxjdn`}
                condition={item?.id === this.props.focusId}
              >
                <Then weId={`${this.props.weId || ''}_qyw1ei`}>
                  <CorsComponent
                    app="@weapp/ebdcoms"
                    compName="IconFont"
                    weId={`${this.props.weId || ''}_jlor2x`}
                    type="lujing"
                    onClick={this.sureEdit}
                  />
                </Then>
                <Else weId={`${this.props.weId || ''}_bz7v6q`}>
                  <CorsComponent
                    weId={`${this.props.weId || ''}_8c2vui`}
                    app="@weapp/components"
                    compName="Disabled"
                    condition={changeDisabled}
                  >
                    <Popover
                      weId={`${this.props.weId || ''}_reunad`}
                      popoverType="tooltip"
                      placement="top"
                      popup={getLabel('54009', '编辑')}
                    >
                      <span>
                        <Icon
                          weId={`${this.props.weId || ''}_8qr8yb`}
                          name="Icon-edit"
                          className="edit"
                          onClick={this.setEdit(item)}
                        />
                      </span>
                    </Popover>
                  </CorsComponent>
                  <CorsComponent
                    weId={`${this.props.weId || ''}_8c2vui`}
                    app="@weapp/components"
                    compName="Disabled"
                    condition={changeDisabled}
                  >
                    <CorsComponent
                      app="@weapp/ebdcoms"
                      compName="IconFont"
                      weId={`${this.props.weId || ''}_lgestm`}
                      type="recycle"
                      className="delete"
                      onClick={this.onDelete(item)}
                      title={getLabel('53951', '删除')}
                      placement="top"
                    />
                  </CorsComponent>
                </Else>
              </If>
            </span>
          </When>
        </div>
      );
    };

  onMove = (evt: any) => {
    // 默认分类不参加排序
    if (evt?.related && !!evt.related.querySelector('.system')) {
      return false;
    }
  };

  renderList(sortDisabled: boolean = false) {
    const {
      data, emptyContent, activeListItem: activeRowKey, changeDisabled = false,
    } = this.props;
    const [customTree] = data;

    const hasCusCat = !isEmpty(customTree.children);

    if (!hasCusCat) {
      return emptyContent;
    }

    return (
      <Scroller className="list" direction="v" weId={`${this.props.weId || ''}_8ppoqb`}>
        <List
          weId={`${this.props.weId || ''}_t9rk5p`}
          className={`${prefixCls}-masterlib-left-list`}
          sortable={!sortDisabled}
          sortableOptions={{
            handle: '.my-handle',
            onEnd: (evt: any) => {
              this.props.onCategorySortEnd([evt.oldIndex, evt.newIndex]);
            },
            onMove: this.onMove,
          }}
          direction="column"
          data={customTree.children as ListData[]}
          noBorder
          customRenderContent={this.customRenderItem(false, changeDisabled)}
          onRowClick={this.onSelect}
          activeRows={activeRowKey}
          isStopMouseDown
        />
      </Scroller>
    );
  }

  renderSearch(addDisabled: boolean = false) {
    const { onCategorySearch, searchPlaceholder, searchText } = this.props;

    return (
      <div className={`${prefixCls}-masterlib-left-list-search`}>
        <Input
          weId={`${this.props.weId || ''}_skyunc`}
          allowClear
          defaultValue={searchText}
          suffix={<Icon weId={`${this.props.weId || ''}_wfp5jw`} name="Icon-search" />}
          placeholder={searchPlaceholder}
          onChange={onCategorySearch}
        />
        <CorsComponent
          weId={`${this.props.weId || ''}_8c2vui`}
          app="@weapp/components"
          compName="Disabled"
          condition={addDisabled}
        >
          <CorsComponent
            app="@weapp/ebdcoms"
            compName="IconFont"
            className={`${prefixCls}-masterlib-left-list-search-icon`}
            weId={`${this.props.weId || ''}_ld8er8`}
            name="Icon-add-to03"
            title={getLabel('101591', '新建分类')}
            onClick={this.onAddCategoryDlgShow(addDisabled)}
          />
        </CorsComponent>
      </div>
    );
  }

  render() {
    const { changeDisabled = false } = this.props;
    const { addCategoryDlgVisible, curOperateCategory } = this.state;
    const placeholder = !curOperateCategory
      ? getLabel('118359', '请输入母版分类名称')
      : getLabel('143239', '编辑母版分类名称');

    return (
      <div className={`${prefixCls}-tempLib-unionlist`}>
        <div className={`${prefixCls}-tempLib-unionlist-search`}>
          {this.renderSearch(changeDisabled)}
        </div>
        <div className={`${prefixCls}-tempLib-unionlist-list`}>
          {this.renderList(changeDisabled)}
        </div>
        <CorsComponent
          weId={`${this.props.weId || ''}_rcofvl`}
          app="@weapp/designer"
          compName="CompTempAddCategoryDlg"
          placeholder={placeholder}
          visible={addCategoryDlgVisible}
          onAdd={this.onAddCate}
          onClose={this.onAddCategoryDlgClose}
          categoryData={curOperateCategory}
        />
      </div>
    );
  }
}

@observer
class LeftMenu extends PureComponent<LeftMenuProps> {
  onSelect = (selected: ITreeData) => {
    const { onTreeNodeClick: onFilter } = this.props.store;
    const { index, fullname } = selected;

    onFilter(index, fullname);
  };

  render() {
    const { store, value, changeDisabled = false } = this.props;
    const {
      currentTreeNodeIndex,
      activeListItem,
      cateSearch,
      focusId,
      refreshData,
      setFocusId,
      setListItem,
      createCat,
      onCategorySearch,
      onCategorySortEnd,
      onEditCategoryMsg,
      onDeleteCategory,
      onClickCollapseTitle,
    } = store;

    const selectKeys = [currentTreeNodeIndex[currentTreeNodeIndex.length - 1]];

    const emptyCont = (
      <CorsComponent
        app="@weapp/ebdcoms"
        compName="Empty"
        weId={`${this.props.weId || ''}_mwkv48`}
        type={value.length ? 'search' : 'noData'}
      />
    );
    return (
      <UnionList
        weId={`${this.props.weId || ''}_j5h22g`}
        data={value}
        emptyContent={emptyCont}
        selectedKeys={selectKeys}
        onSelect={this.onSelect}
        searchText={cateSearch}
        searchPlaceholder={getLabel('55126', '请输入内容')}
        focusId={focusId}
        activeListItem={activeListItem}
        changeDisabled={changeDisabled}
        setListItem={setListItem}
        currentTreeNodeIndex={currentTreeNodeIndex}
        setFocusId={setFocusId}
        createCat={createCat}
        refreshData={refreshData}
        onCategorySortEnd={onCategorySortEnd}
        onCategorySearch={onCategorySearch}
        onEditCategoryMsg={onEditCategoryMsg}
        onDeleteCategory={onDeleteCategory}
        onClickCollapseTitle={onClickCollapseTitle}
      />
    );
  }
}

export default LeftMenu;
