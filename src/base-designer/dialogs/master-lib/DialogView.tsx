import { Dialog } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React from 'react';
import { When } from 'react-if';
import { dlgIconName, prefixCls } from '../../../constants';
import '../temp-lib/index.less';
import MasterLib from './index';
import { MasterDlgProps } from './types';

const DialogView: React.FC<MasterDlgProps> = (props) => {
  const {
    visible,
    onClose,
    pageScope,
    pageInfo,
    clientType = 'PC',
    events,
    isSingleClient = false,
    changeDisabled = false,
  } = props;

  return (
    <When weId={`${props.weId || ''}_92cdey`} condition={visible}>
      <Dialog
        weId={`${props.weId || ''}_ms3q8h`}
        visible={visible}
        title={getLabel('118338', '母版管理')}
        onClose={onClose}
        closable
        destroyOnClose
        draggable
        mask
        resize
        width={960}
        height={610}
        bodyStyle={{ background: '#f9f9f9' }}
        className={`${prefixCls}-eleLib-dialogView temp-dialog`}
        icon={dlgIconName}
      >
        <MasterLib
          weId={`${props.weId || ''}_vp32tm`}
          pageScope={pageScope}
          pageInfo={pageInfo}
          clientType={clientType}
          events={events}
          changeDisabled={changeDisabled}
          isSingleClient={isSingleClient}
        />
      </Dialog>
    </When>
  );
};
export default DialogView;
