import { ListData } from '@weapp/ui';
import React, { ReactNode } from 'react';
import { ClientType, Page, PageModuleType } from '../../../core';
import { EventEmitter } from '../../../core/utils';
import { CompTempDataType } from '../../panels/template/types';
import { MasterLibStore } from './store';

export interface MasterLibProps extends React.Attributes {
  pageScope: PageModuleType;
  clientType: ClientType;
  pageInfo?: Page;
  events?: EventEmitter;
  renderRightCom?: (RightCom: ReactNode) => void;
  onCheckChange?: (rowData: ListData) => void;
  onDataLoad?: () => void;
  /** 终端仅单端 */
  isSingleClient?: boolean;
  /** 无权保存、修改配置 */
  changeDisabled?: boolean;
}

export interface MasterDlgProps extends MasterLibProps {
  visible: boolean;
  weId: string;
  pageInfo?: Page;
  onClose: () => void;
  /** 终端仅单端 */
  isSingleClient?: boolean;
  /** 无权保存、修改配置 */
  changeDisabled?: boolean;
}

export interface LeftMenuProps extends React.Attributes {
  store: MasterLibStore;
  value: TreeNode[];
  /** 无权保存、修改配置 */
  changeDisabled?: boolean;
}

export interface MasterStoreProps extends MasterLibProps {
  masterLibStore: MasterLibStore;
}

export interface TreeNode extends React.Attributes {
  id: string;
  name: string;
  children: TreeNode[];
  index: string[];
  fullname: string[];
  isSystem?: boolean;
  nameLabelId?: string;
  defaultName?: string;
}

export interface HeaderProps {
  canOperate: boolean;
  onBatchDelete: (idBox: string[]) => Promise<any>;
  onSearch: (name: string) => void;
  breadCrumbItems: string[];
  weId: string;
  isRenderSearchInput?: boolean;
  getBatchDeleteIds: () => CompTempDataType[];
}

export interface CardProps extends React.Attributes {
  rowData: ListData;
  rowID: number;
  weId: string;
  canMove: boolean;
  isSearching: boolean;
  isDetailCategory: boolean;
  onCheckChange: (data: CompTempDataType, value: any) => void;
  onClickIcon: (type: string, data: ListData) => void;
}

export interface EditTempData {
  id: string;
  icon: string;
  category: string;
  name: string;
  nameLabelId?: string;
}

export enum Operation {
  Modify = 'modify',
  UserInfo = 'userInfo',
  Delete = 'delete',
}

export default {};
