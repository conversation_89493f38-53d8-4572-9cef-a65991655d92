import {
  CorsComponent, Dialog, Layout, List, ListData, Spin,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { prefixCls } from '../../../constants';
import { LayoutType } from '../../../core';
import { CompTempDataType } from '../../panels/template/types';
import Card from './Card';
import EditTempDialog from './components/edit/editTempDialog';
import Footer from './Footer';
import Header from './Header';
import ImportTemplate from './ImportTemplate';
import LeftMenu from './LeftMenu';
import { Operation, TempStoreProps } from './types';

const { confirm } = Dialog;

interface TempStoreState {
  editDiaVisible: boolean;
  editDiaData: ListData | null;
}

@inject('tempLibStore')
@observer
class TempLibContent extends PureComponent<TempStoreProps, TempStoreState> {
  constructor(props: TempStoreProps) {
    super(props);
    this.state = {
      editDiaVisible: false,
      editDiaData: null,
    };
  }

  componentDidMount() {
    const {
      tempLibStore, pageScope, pageInfo, clientType, events,
    } = this.props;
    const { init } = tempLibStore;
    const layoutType = pageInfo?.layoutType;

    init(pageScope, clientType, layoutType as LayoutType, events);
  }

  componentWillUnmount() {
    this.props.tempLibStore.reset();
  }

  onCheckChange = (rowData: ListData, value: any) => {
    const { onCheckChange: _onCheckChange, tempLibStore } = this.props;
    const { onCheckChange } = tempLibStore;

    onCheckChange(rowData as CompTempDataType, value);

    if (_onCheckChange) {
      _onCheckChange(rowData);
    }
  };

  onClickIcon = (type: string, data: ListData) => {
    switch (type) {
      case Operation.Delete:
        this.onDeleteTemp(data);
        break;
      case Operation.Modify:
        this.setState({
          editDiaVisible: true,
          editDiaData: data,
        });
        break;
      case Operation.Export: {
        const { setExportSpinVisible, exportComTpl } = this.props.tempLibStore;

        if (data) {
          confirm({
            mask: true,
            content: (
              <div>
                {getLabel('114249', '确定要导出模板')}
                <span className={`${prefixCls}-delapp-confirm`}>「{data.name}」</span>？
              </div>
            ),
            onOk() {
              setExportSpinVisible(true);
              exportComTpl(data.id!);
            },
          });
        }

        break;
      }
      default:
        break;
    }
  };

  onDeleteTemp = (data: ListData) => {
    const { onBatchDelete } = this.props.tempLibStore;
    confirm({
      mask: true,
      content: (
        <div className={`${prefixCls}-temp-delete-confirm-content`}>
          <span>{getLabel('98883', '确定要删除模板')}</span>
          <span style={{ color: 'var(--danger)' }}>「{data.name}」</span>？
        </div>
      ),
      onOk: () => {
        onBatchDelete([data.id!]);
      },
    });
  };

  onCloseEditDia = () => {
    this.setState({ editDiaVisible: false });
  };

  customRenderCard = (rowData: ListData | ListData[], rowID: number) => {
    const { changeDisabled = false, tempLibStore } = this.props;
    const {
      isSystemCaretory, isSearching, isDetailCategory, pageScope,
    } = tempLibStore;
    return (
      <Card
        weId={`${this.props.weId || ''}_z4uiva`}
        rowData={rowData}
        rowID={rowID}
        canMove={isSystemCaretory}
        isSearching={isSearching}
        changeDisabled={changeDisabled}
        onCheckChange={this.onCheckChange}
        onClickIcon={this.onClickIcon}
        isDetailCategory={isDetailCategory}
        pageScope={pageScope}
      />
    );
  };

  render() {
    const { tempLibStore, changeDisabled = false, renderRightCom } = this.props;
    const {
      hasInit,
      treeData = [],
      isSystemCaretory,
      showOpeBtns,
      onBatchDelete,
      onFilterShowElementsByName,
      currentTreeNodeName = [],
      onClickBreadCrumb,
      inputSearch,
      showTemps = [],
      originData = [],
      onEditTempMsg,
      getBatchDeleteIds,
      onTempSortEnd,
      isSearching,
      isDetailCategory,
      createCat,
      tempcategories,
      exportSpinVisible,
      setUploadVisible,
      uploadVisible,
    } = tempLibStore;

    const canOperate = showOpeBtns();
    const exportStyleDiaplay = exportSpinVisible ? 'block' : 'none';
    return (
      <div className={`${prefixCls}-tempLib-layout`}>
        <Layout
          weId={`${this.props.weId || ''}_lepl1m`}
          className={`${prefixCls}-tempLib-container`}
        >
          <Layout.Box
            weId={`${this.props.weId || ''}_p3ge5a`}
            className={`${prefixCls}-tempLib-header`}
            type="header"
          >
            <When weId={`${this.props.weId || ''}_c34bre`} condition={!renderRightCom}>
              <Header
                canOperate={canOperate}
                getBatchDeleteIds={getBatchDeleteIds}
                onBatchDelete={onBatchDelete}
                onSearch={onFilterShowElementsByName}
                breadCrumbItems={currentTreeNodeName}
                onClickBreadCrumb={onClickBreadCrumb}
                weId={`${this.props.weId || ''}_g3r6m7`}
                addDisabled={changeDisabled}
                onUploadClick={setUploadVisible}
                canUpload={isSystemCaretory}
              />
            </When>
          </Layout.Box>

          <Layout.Box
            weId={`${this.props.weId || ''}_aaedp8`}
            className={`${prefixCls}-tempLib-content`}
          >
            <If weId={`${this.props.weId || ''}_1bfqaf`} condition={hasInit}>
              <Then weId={`${this.props.weId || ''}_tockt3`}>
                <If weId={`${this.props.weId || ''}_gnzmc7`} condition={showTemps.length > 0}>
                  <Then weId={`${this.props.weId || ''}_qbsbbx`}>
                    <List
                      weId={`${this.props.weId || ''}_p2lh9g`}
                      direction="row"
                      data={toJS(showTemps)}
                      boxSelection
                      sortable={
                        isSystemCaretory && !isSearching && isDetailCategory && !changeDisabled
                      }
                      sortableOptions={{
                        onEnd(evt: any) {
                          onTempSortEnd([evt.oldIndex, evt.newIndex]);
                        },
                      }}
                      customRenderContent={this.customRenderCard}
                    />
                    <Footer weId={`${this.props.weId || ''}_fzy6h3`} tempLibStore={tempLibStore} />
                  </Then>
                  <Else weId={`${this.props.weId || ''}_qp08f2`}>
                    <CorsComponent
                      weId={`${this.props.weId || ''}_v1eqhx`}
                      app="@weapp/ebdcoms"
                      compName="Empty"
                      type={inputSearch ? 'search' : 'noData'}
                    />
                  </Else>
                </If>
              </Then>
              <Else weId={`${this.props.weId || ''}_hxjkc7`}>
                <Spin
                  style={{ textAlign: 'center', paddingTop: '205px' }}
                  weId={`${this.props.weId || ''}_v6c4n1`}
                />
              </Else>
            </If>
          </Layout.Box>
          <Layout.Box
            weId={`${this.props.weId || ''}_eygn38`}
            className={`${prefixCls}-tempLib-leftMenu`}
            type="side"
            width={180}
            outer
          >
            <If weId={`${this.props.weId || ''}_iuzxr6`} condition={hasInit}>
              <Then weId={`${this.props.weId || ''}_nwjw7c`}>
                <LeftMenu
                  weId={`${this.props.weId || ''}_blfda1`}
                  store={tempLibStore}
                  value={toJS(treeData)}
                  changeDisabled={changeDisabled}
                />
              </Then>
              <Else weId={`${this.props.weId || ''}_3ph3qv`}>
                <Spin
                  weId={`${this.props.weId || ''}_9b3qf3`}
                  style={{ textAlign: 'center', paddingTop: '205px' }}
                />
              </Else>
            </If>
          </Layout.Box>
        </Layout>
        <EditTempDialog
          weId={`${this.props.weId || ''}_191ocj`}
          title={getLabel('98878', '编辑模板')}
          visible={this.state.editDiaVisible}
          editData={this.state.editDiaData}
          originData={toJS(originData)}
          categoryData={toJS(tempcategories)}
          saveDisabled={changeDisabled}
          onCreateCat={createCat}
          onClose={this.onCloseEditDia}
          onOk={onEditTempMsg}
        />
        <Spin
          weId={`${this.props.weId || ''}_dqeudh`}
          className={`${prefixCls}-exporting`}
          size="small"
          spinning={exportSpinVisible}
          style={{ display: exportStyleDiaplay }}
          text={getLabel('114261', '模板导出中')}
        />
        <When weId={`${this.props.weId || ''}_now5mf`} condition={uploadVisible}>
          <ImportTemplate weId={`${this.props.weId || ''}_w53jjc`} {...this.props} />
        </When>
      </div>
    );
  }
}

export default TempLibContent as unknown as ComponentType<{
  onCheckChange?: (rowData: ListData) => void;
}>;
