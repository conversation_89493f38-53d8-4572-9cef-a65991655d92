import {
  CorsComponent,
  Dialog,
  Icon,
  Input,
  ITreeData,
  List,
  ListData,
  Popover,
  Scroller,
} from '@weapp/ui';
import {
  classnames, getLabel, isEmpty, isEqual,
} from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { PureComponent, SyntheticEvent } from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { prefixCls } from '../../../constants';
import Collapse from './components/Collapse';
import { LeftMenuProps, SourceType, TreeNode } from './types';

const { confirm } = Dialog;

interface UnionListProps {
  data: TreeNode[];
  emptyContent: React.ReactNode;
  onSelect: (selected: ITreeData) => void;
  searchText: string;
  searchPlaceholder: string;
  selectedKeys: string[];
  focusId: string;
  activeListItem: ListData[];
  setListItem: (activeRowKey: ListData[]) => void;
  currentTreeNodeIndex: string[];
  /** 无权保存、修改配置 */
  changeDisabled?: boolean;
  onCreateCat: (cat: string, nameLabelId?: string) => void;
  setFocusId: (focusId: string) => void;
  onCategorySortEnd: (sortBox: number[]) => void;
  onCategorySearch: (text: React.ReactText) => void;
  onEditCategoryMsg: (id: string, cateName: string, nameLabelId?: string) => void;
  onDeleteCategory: (id: string) => void;
  onClickCollapseTitle: (newIndex: string[], newName: string[]) => void;
}

interface UnionListStates {
  addCategoryDlgVisible: boolean;
  curOperateCategory: ListData | null;
}

class UnionList extends PureComponent<UnionListProps, UnionListStates> {
  state = {
    addCategoryDlgVisible: false,
    curOperateCategory: null,
  };

  setExpand = (activeRowKey: ListData[]) => {
    this.props.setListItem(activeRowKey);
  };

  onSelect = (selected: ITreeData) => {
    const { onSelect } = this.props;
    this.setExpand([selected]);

    onSelect(selected);
  };

  setCaretogyName = (id: string, cateName: string, nameLabelId?: string) => {
    this.props.onEditCategoryMsg(id, cateName, nameLabelId);
  };

  setEdit =
    (category: ListData, editDisabled: boolean = false) => (e: SyntheticEvent) => {
      if (editDisabled) return;

      e.stopPropagation();

      this.setState({
        addCategoryDlgVisible: true,
        curOperateCategory: category,
      });
    };

  sureEdit = (e: SyntheticEvent) => {
    e.stopPropagation();
  };

  onDelete =
    (data: ListData, deleteDisabled: boolean = false) => (e: SyntheticEvent) => {
      if (deleteDisabled) return;

      e.stopPropagation();

      confirm({
        mask: true,
        content: <div>{getLabel('99185', '删除分类会级联删除分类下的模板，确认要删除吗')}？</div>,
        onOk: () => {
          this.props.onDeleteCategory(data.id!);
        },
      });
    };

  onAddCate = (id: string, cat: string, nameLabelId?: string) => {
    const { onCreateCat } = this.props;

    if (!id) {
      onCreateCat(cat, nameLabelId);
    } else {
      this.props.onEditCategoryMsg(id, cat, nameLabelId);
    }

    this.onAddCategoryDlgClose();
  };

  onAddCategoryDlgClose = () => {
    this.setState({ addCategoryDlgVisible: false, curOperateCategory: null });
  };

  onAddCategoryDlgShow = (addDisabled: boolean) => () => {
    if (addDisabled) return;

    this.setState({ addCategoryDlgVisible: true });
  };

  onTitleClick = (tree: TreeNode) => () => {
    this.props.setListItem([]);
    this.props.onClickCollapseTitle(tree.index, tree.fullname);
  };

  customRenderItem = (isSystem: boolean) => (item: ListData) => {
    const isDefault = item?.id === '-1';
    const isForbidden = isSystem || isDefault;
    const { changeDisabled = false } = this.props;
    return (
      <div
        className={classnames(`${prefixCls}-tempLib-unionlist-item`, {
          system: isForbidden,
          dragable: !isForbidden,
        })}
        key={item.id}
      >
        {isForbidden ? (
          <span className="icon-null move" />
        ) : (
          <Icon
            weId={`${this.props.weId || ''}_cgd7f6`}
            name="Icon-move"
            className="my-handle move"
          />
        )}
        <span className={`${prefixCls}-tempLib-unionlist-item-name`}>
          <div className={`${prefixCls}-assets-input-text name`}>
            <span title={item.name}>{item.name}</span>
          </div>
        </span>
        <When weId={`${this.props.weId || ''}_9tf2kt`} condition={!isForbidden}>
          <span
            className={`${prefixCls}-tempLib-unionlist-item-operation`}
            style={{ display: changeDisabled ? 'flex' : 'block' }}
          >
            <If
              weId={`${this.props.weId || ''}_hxxjdn`}
              condition={item?.id === this.props.focusId}
            >
              <Then weId={`${this.props.weId || ''}_qyw1ei`}>
                <CorsComponent
                  weId={`${this.props.weId || ''}_v1eqhx`}
                  app="@weapp/ebdcoms"
                  compName="IconFont"
                  type="lujing"
                  onClick={this.sureEdit}
                />
              </Then>
              <Else weId={`${this.props.weId || ''}_bz7v6q`}>
                <CorsComponent
                  weId={`${this.props.weId || ''}_8c2vui`}
                  app="@weapp/components"
                  compName="Disabled"
                  condition={changeDisabled}
                >
                  <Popover
                    weId={`${this.props.weId || ''}_reunad`}
                    popoverType="tooltip"
                    placement="top"
                    popup={getLabel('54009', '编辑')}
                  >
                    <span>
                      <Icon
                        weId={`${this.props.weId || ''}_8qr8yb`}
                        name="Icon-edit"
                        className="edit"
                        onClick={this.setEdit(item, changeDisabled)}
                      />
                    </span>
                  </Popover>
                </CorsComponent>
                <CorsComponent
                  weId={`${this.props.weId || ''}_8c2vui`}
                  app="@weapp/components"
                  compName="Disabled"
                  condition={changeDisabled}
                >
                  <CorsComponent
                    weId={`${this.props.weId || ''}_v1eqhx`}
                    app="@weapp/ebdcoms"
                    compName="IconFont"
                    type="recycle"
                    className="delete"
                    onClick={this.onDelete(item, changeDisabled)}
                    title={getLabel('53951', '删除')}
                    placement="top"
                  />
                </CorsComponent>
              </Else>
            </If>
          </span>
        </When>
      </div>
    );
  };

  onMove = (evt: any) => {
    // 默认分类不参加排序
    if (evt?.related && !!evt.related.querySelector('.system')) {
      return false;
    }
  };

  renderList(sortDisabled: boolean) {
    const {
      data, emptyContent, currentTreeNodeIndex, activeListItem: activeRowKey,
    } = this.props;
    const [systemTree, customTree] = data;

    const hasSysCat = !isEmpty(systemTree.children);
    const hasCusCat = !isEmpty(customTree.children);

    if (!hasSysCat && !hasCusCat) {
      return emptyContent;
    }

    const _currentTreeNodeIndex = toJS(currentTreeNodeIndex);

    return (
      <Scroller className="list" direction="v" weId={`${this.props.weId || ''}_8ppoqb`}>
        {hasSysCat ? (
          <Collapse
            weId={`${this.props.weId || ''}_hrt76c`}
            isSelect={isEqual(_currentTreeNodeIndex, systemTree.index)}
            title={systemTree.name}
            onTitleClick={this.onTitleClick(systemTree)}
          >
            <List
              weId={`${this.props.weId || ''}_t9rk5p`}
              direction="column"
              sortableOptions={{ handle: '.my-handle' }}
              data={systemTree.children as ListData[]}
              noBorder
              customRenderContent={this.customRenderItem(true)}
              onRowClick={this.onSelect}
              activeRows={activeRowKey}
              isStopMouseDown
            />
          </Collapse>
        ) : null}
        {hasCusCat ? (
          <Collapse
            weId={`${this.props.weId || ''}_hrt76c`}
            isSelect={isEqual(_currentTreeNodeIndex, customTree.index)}
            title={customTree.name}
            onTitleClick={this.onTitleClick(customTree)}
          >
            <List
              weId={`${this.props.weId || ''}_t9rk5p`}
              sortable={!sortDisabled}
              sortableOptions={{
                handle: '.my-handle',
                onEnd: (evt: any) => {
                  this.props.onCategorySortEnd([evt.oldIndex, evt.newIndex]);
                },
                onMove: this.onMove,
              }}
              direction="column"
              data={customTree.children as ListData[]}
              noBorder
              customRenderContent={this.customRenderItem(false)}
              onRowClick={this.onSelect}
              activeRows={activeRowKey}
              isStopMouseDown
            />
          </Collapse>
        ) : null}
      </Scroller>
    );
  }

  renderSearch(addDisabled: boolean) {
    const { onCategorySearch, searchPlaceholder, searchText } = this.props;
    return (
      <div className={`${prefixCls}-tempLib-unionlist-search-wrap`}>
        <Input
          weId={`${this.props.weId || ''}_skyunc`}
          style={{ width: '100%' }}
          allowClear
          defaultValue={searchText}
          suffix={<Icon weId={`${this.props.weId || ''}_wfp5jw`} name="Icon-search" />}
          placeholder={searchPlaceholder}
          onChange={onCategorySearch}
        />
        <CorsComponent
          weId={`${this.props.weId || ''}_8c2vui`}
          app="@weapp/components"
          compName="Disabled"
          condition={addDisabled}
        >
          <CorsComponent
            weId={`${this.props.weId || ''}_v1eqhx`}
            app="@weapp/ebdcoms"
            compName="IconFont"
            className={`${prefixCls}-tempLib-unionlist-search-wrap-icon`}
            name="Icon-add-to03"
            title={getLabel('101591', '新建分类')}
            onClick={this.onAddCategoryDlgShow(addDisabled)}
          />
        </CorsComponent>
      </div>
    );
  }

  render() {
    const { changeDisabled = false } = this.props;
    const { addCategoryDlgVisible, curOperateCategory } = this.state;
    return (
      <div className={`${prefixCls}-tempLib-unionlist`}>
        <div className={`${prefixCls}-tempLib-unionlist-search`}>
          {this.renderSearch(changeDisabled)}
        </div>
        <div className={`${prefixCls}-tempLib-unionlist-list`}>
          {this.renderList(changeDisabled)}
        </div>
        <CorsComponent
          weId={`${this.props.weId || ''}_rcofvl`}
          app="@weapp/designer"
          compName="CompTempAddCategoryDlg"
          visible={addCategoryDlgVisible}
          onAdd={this.onAddCate}
          onClose={this.onAddCategoryDlgClose}
          tableLabelfield="ebdd_tmpl_category.name"
          categoryData={curOperateCategory}
        />
      </div>
    );
  }
}

@observer
class LeftMenu extends PureComponent<LeftMenuProps> {
  onSelect = (selected: ITreeData) => {
    const { onTreeNodeClick: onFilter } = this.props.store;
    const { index, fullname } = selected;

    onFilter(index, fullname);
  };

  onCreateCat = (cat: string, nameLabelId?: string) => {
    const {
      params, createCat, getTempCategories, refreshData,
    } = this.props.store;

    createCat(cat, nameLabelId).then(() => {
      getTempCategories(params);
      refreshData({
        source: SourceType.Create,
      });
    });
  };

  render() {
    const { store, value, changeDisabled = false } = this.props;
    const {
      currentTreeNodeIndex,
      activeListItem,
      cateSearch,
      focusId,
      setFocusId,
      setListItem,
      onCategorySearch,
      onCategorySortEnd,
      onEditCategoryMsg,
      onDeleteCategory,
      onClickCollapseTitle,
    } = store;

    const selectKeys = [currentTreeNodeIndex[currentTreeNodeIndex.length - 1]];
    const emptyCont = (
      <CorsComponent
        weId={`${this.props.weId || ''}_v1eqhx`}
        app="@weapp/ebdcoms"
        compName="Empty"
        type={value.length ? 'search' : 'noData'}
      />
    );
    return (
      <UnionList
        weId={`${this.props.weId || ''}_j5h22g`}
        data={value}
        emptyContent={emptyCont}
        selectedKeys={selectKeys}
        onSelect={this.onSelect}
        onCreateCat={this.onCreateCat}
        searchText={cateSearch}
        searchPlaceholder={getLabel('55126', '请输入内容')}
        focusId={focusId}
        activeListItem={activeListItem}
        setListItem={setListItem}
        changeDisabled={changeDisabled}
        currentTreeNodeIndex={currentTreeNodeIndex}
        setFocusId={setFocusId}
        onCategorySortEnd={onCategorySortEnd}
        onCategorySearch={onCategorySearch}
        onEditCategoryMsg={onEditCategoryMsg}
        onDeleteCategory={onDeleteCategory}
        onClickCollapseTitle={onClickCollapseTitle}
      />
    );
  }
}

export default LeftMenu;
