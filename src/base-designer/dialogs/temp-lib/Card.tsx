import {
  Checkbox, CorsComponent, Icon, Menu,
} from '@weapp/ui';
import { classnames, getLabel, isObject } from '@weapp/utils';
import React, { PureComponent } from 'react';
import { Else, If, Then } from 'react-if';
import { prefixCls } from '../../../constants';
import ebdcoms from '../../../utils/ebdcoms';
import { CompTempDataType } from '../../panels/template/types';
import { CardProps, Operation } from './types';

export const isVaildImg = (data: any) => !!data?.icon;

const operationOptions = (disabled?: boolean) => [
  {
    id: Operation.Modify,
    content: getLabel('54009', '编辑'),
    icon: 'Icon-Invoice---batch-edit-o',
  },
  {
    id: Operation.Export,
    content: getLabel('55843', '导出'),
    icon: 'Icon-download01',
    disabled,
  },
  {
    id: Operation.Delete,
    content: getLabel('53951', '删除'),
    icon: 'Icon-delete',
    disabled,
  },
];

export const utils = {
  isJsonStr: (str: string) => {
    try {
      const obj = JSON.parse(str);
      if (typeof obj === 'object' && obj) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  },
};

export default class Card extends PureComponent<CardProps> {
  state = {
    onMouseEnter: false,
  };

  onMouseEnter = () => {
    this.setState({
      onMouseEnter: true,
    });
  };

  onMouseLeave = () => {
    this.setState({
      onMouseEnter: false,
    });
  };

  onCheckChange = (value: any, option: any, e: any) => {
    e?.nativeEvent?.preventDefault();
    e?.nativeEvent?.stopPropagation();

    const { rowData, onCheckChange } = this.props;
    onCheckChange(rowData as CompTempDataType, value);
  };

  renderImg = () => {
    const {
      rowData, canMove: move, isSearching, isDetailCategory,
    } = this.props;
    const data = rowData as any;

    // 图标为空时 使用缩略图
    const iconPath = data?.icon || data?.thumbnail || '';
    let icon = ebdcoms.excu('getImgUrl', iconPath);
    if (utils.isJsonStr(iconPath)) {
      icon = JSON.parse(iconPath);
    }
    const isImg = !isObject(icon);
    return (
      <div
        className={classnames(`${prefixCls}-tempLib-contentCard-image`, {
          move: move && !isSearching && isDetailCategory,
        })}
      >
        <If weId={`${this.props.weId || ''}_jprpj1`} condition={isImg}>
          <Then weId={`${this.props.weId || ''}_59w05b`}>
            {isVaildImg(data) ? <img src={icon} alt="" /> : null}
          </Then>
          <Else weId={`${this.props.weId || ''}_eiw6du`}>
            <CorsComponent
              weId="nawnjy"
              app="@weapp/ebdcoms"
              compName="AssetsItem"
              path=""
              {...icon}
              size="auto"
            />
          </Else>
        </If>
      </div>
    );
  };

  customRenderItem = (_this: any, itemNode: React.ReactNode) => (
    <CorsComponent
      weId={`${this.props.weId || ''}_8c2vui`}
      app="@weapp/components"
      compName="Disabled"
      condition={this.props.changeDisabled}
      hideMask
    >
      {itemNode}
    </CorsComponent>
  );

  onMenuChange = (val: string) => {
    const { rowData, onClickIcon } = this.props;
    onClickIcon(val, rowData);
  };

  render() {
    const {
      rowData, rowID, canMove, menuData, pageScope, changeDisabled = false,
    } = this.props;
    const { onMouseEnter } = this.state;
    const obj = rowData as any;
    const _module = rowData?.belongModule || rowData?.module;
    const isHide = (!obj.checked && !onMouseEnter) || pageScope !== _module;

    return (
      <div
        className={`${prefixCls}-tempLib-content-card-container`}
        onMouseEnter={this.onMouseEnter}
        onMouseLeave={this.onMouseLeave}
      >
        <div key={rowID} className={`${prefixCls}-tempLib-content-card`}>
          <div className={`${prefixCls}-tempLib-contentCard-header`}>
            {canMove ? (
              <>
                <CorsComponent
                  weId={`${this.props.weId || ''}_8c2vui`}
                  app="@weapp/components"
                  compName="Disabled"
                  condition={changeDisabled}
                  hideMask
                  style={{ display: 'inline-flex' }}
                >
                  <Checkbox
                    key={`${obj?.id}_${obj?.checked}`}
                    weId={`${this.props.weId || ''}_t771i0`}
                    onChange={this.onCheckChange}
                    hideCheck={isHide}
                    value={obj.checked}
                  />
                </CorsComponent>
                <Menu
                  weId={`${this.props.weId || ''}_c40vug`}
                  data={menuData || operationOptions(changeDisabled)}
                  type="select"
                  value=""
                  childTriggerProps={{
                    popupClassName: `${prefixCls}-card-btn-trigger`,
                  }}
                  customRenderItem={this.customRenderItem}
                  customSelectContent={
                    <span className={classnames(`${prefixCls}-card-more`, { show: !isHide })}>
                      <Icon weId={`${this.props.weId || ''}_3eyiyt`} name="Icon-more" />
                    </span>
                  }
                  onChange={this.onMenuChange}
                />
              </>
            ) : null}
          </div>
          {this.renderImg()}
        </div>
        <div className={`${prefixCls}-tempLib-content-footer`} title={obj.name}>
          {obj.name}
        </div>
      </div>
    );
  }
}
