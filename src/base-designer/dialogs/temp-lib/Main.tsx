import {
  Button, CorsComponent, Dialog, Icon, Input,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent, ReactText } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import TempLibContent from './Content';
import './index.less';
import { TempLibProps, TempStoreProps } from './types';

const { confirm } = Dialog;

@inject('tempLibStore')
@observer
class Main extends PureComponent<TempLibProps & TempStoreProps> {
  componentDidMount() {
    const {
      events, changeDisabled = false, onDataLoad, renderRightCom,
    } = this.props;
    events?.on('update.comptemp', () => {
      onDataLoad?.();
    });
    renderRightCom?.(this.renderRightCom(this.props.tempLibStore.isSystemCaretory, changeDisabled));
  }

  componentWillUnmount() {
    this.props.events?.off('update.comptemp');
  }

  onCheckChange = () => {
    const { changeDisabled = false, renderRightCom } = this.props;

    renderRightCom?.(this.renderRightCom(this.props.tempLibStore.isSystemCaretory, changeDisabled));
  };

  onBatchDelete = () => {
    const { getBatchDeleteIds, onBatchDelete } = this.props.tempLibStore;
    const deleteObj = getBatchDeleteIds();
    let nameBox: string = '';
    const idBox: string[] = [];
    deleteObj.forEach((temp) => {
      nameBox += `「${temp.name}」`;
      idBox.push(temp.id);
    });
    confirm({
      mask: true,
      content: (
        <div className={`${prefixCls}-temp-delete-confirm-content`}>
          <span>{getLabel('98883', '确定要删除模板')}</span>
          <span style={{ color: 'var(--danger)' }}>{nameBox}</span>？
        </div>
      ),
      onOk: () => {
        onBatchDelete(idBox);
      },
    });
  };

  onComNameSearch = (value: ReactText) => {
    const { onFilterShowElementsByName } = this.props.tempLibStore;

    onFilterShowElementsByName(`${value}`);
  };

  onUploadBtnClick = (addDisabled: boolean) => () => {
    if (addDisabled) return;

    const { setUploadVisible } = this.props.tempLibStore;

    setUploadVisible(true);
  };

  renderRightCom = (isSystemCaretory: boolean, addDisabled: boolean) => (
    <>
      <div className={`${prefixCls}-tempLib-right-com`}>
        <When weId={`${this.props.weId || ''}_6o0a4r`} condition={isSystemCaretory}>
          <CorsComponent
            weId={`${this.props.weId || ''}_jcewtc`}
            app="@weapp/components"
            compName="Disabled"
            condition={addDisabled}
            style={{ display: 'inline-block', marginLeft: '14px' }}
          >
            <Button
              weId={`${this.props.weId || ''}_6qivsp`}
              type="primary"
              onClick={this.onUploadBtnClick(addDisabled)}
            >
              <Icon weId={`${this.props.weId || ''}_36p92v`} name="Icon-add-to01" />
              <span>{getLabel('114424', '导入模板')}</span>
            </Button>
          </CorsComponent>
        </When>
        <When
          weId={`${this.props.weId || ''}_jost4x`}
          condition={this.props.tempLibStore.showOpeBtns()}
        >
          <Button
            weId={`${this.props.weId || ''}_g0b4fk`}
            type="danger"
            onClick={this.onBatchDelete}
          >
            {getLabel('74407', '批量删除')}
          </Button>
        </When>
        <Input
          className="ui-searchAdvanced-input"
          weId={`${this.props.weId || ''}_9cs2aj`}
          style={{ width: 200 }}
          allowClear
          suffix={
            <Icon
              className="ui-searchAdvanced-input-icon"
              weId={`${this.props.weId || ''}_2px8ru`}
              name="Icon-search"
            />
          }
          placeholder={getLabel('99296', '请输入模板名称')}
          onChange={this.onComNameSearch}
        />
      </div>
    </>
  );

  render() {
    const { changeDisabled = false, tempLibStore } = this.props;
    const { isSystemCaretory } = tempLibStore;
    this.props.renderRightCom?.(this.renderRightCom(isSystemCaretory, changeDisabled));
    return (
      <TempLibContent
        weId={`${this.props.weId || ''}_vicd58`}
        {...this.props}
        onCheckChange={this.onCheckChange}
      />
    );
  }
}

export default Main as unknown as ComponentType<TempLibProps>;
