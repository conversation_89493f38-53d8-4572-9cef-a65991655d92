import { Icon, Spin } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { prefixCls } from '../../../constants';
import { TempLibStore } from './store';

type FooterProps = {
  tempLibStore: TempLibStore;
};
class Footer extends PureComponent<FooterProps> {
  onClickMore = () => {
    const { params, getTemplates, addPageNo } = this.props.tempLibStore;

    addPageNo();
    getTemplates(params);
  };

  renderFooter = () => {
    const { loading, hasMore } = this.props.tempLibStore;

    if (loading) {
      return (
        <div className={`${prefixCls}-tempLib-footer-loading`}>
          <Spin
            weId={`${this.props.weId || ''}_xpeqvg`}
            text={`${getLabel('115120', '正在加载模板')},${getLabel('21380', '请稍候')}...`}
          />
        </div>
      );
    }

    if (!hasMore) {
      return (
        <div className={`${prefixCls}-tempLib-footer-nomore`}>
          {getLabel('115119', '已加载所有模板')}
        </div>
      );
    }

    return (
      <div className={`${prefixCls}-tempLib-footer-more`} onClick={this.onClickMore}>
        {getLabel('60173', '加载更多')}
        <Icon weId={`${this.props.weId || ''}_k5r6i1`} name="Icon-Down-arrow01" />
      </div>
    );
  };

  render() {
    return <div className={`${prefixCls}-tempLib-footer`}>{this.renderFooter()}</div>;
  }
}

export default observer(Footer);
