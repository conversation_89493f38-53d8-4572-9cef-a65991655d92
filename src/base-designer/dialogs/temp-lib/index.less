@import (reference) '../../../style/prefix.less';

.@{prefix}-eleLib-dialogView.temp-dialog .ui-dialog-content .ui-dialog-body {
  height: 100%;
  padding: 0px;
}

.@{prefix}-tempLib-layout {
  height: 100%;
}

.@{prefix}-tempLib-footer {
  width: 100%;

  &-more,
  &-nomore,
  &-loading {
    color: #999999;
    font-size: var(--font-size-12);
    margin-top: 10px;
    margin-bottom: 10px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  &-nomore {
    cursor: default;
  }

  &-more:hover {
    color: #5d9cec;
  }
}

.@{prefix}-tempLib-header {
  padding: 16px 16px 10px;

  .@{prefix}-tempLib-header-content {
    height: 30px;
    display: flex;
    justify-content: space-between;
  }

  .@{prefix}-tempLib-header-left {
    .ui-bread-crumb {
      width: 300px;
      height: 30px;
      line-height: 30px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }

  .@{prefix}-tempLib-header-right {
    display: flex;
    justify-content: space-between;
    line-height: 30px;

    .ui-btn {
      margin-right: 14px;
      display: flex;

      .ui-icon {
        color: var(--base-white);

        & > svg {
          color: var(--base-white) !important;
        }
      }
    }
  }
}

.@{prefix}-tempLib-right-com {
  display: inline-flex;
  justify-content: space-between;
  line-height: 30px;

  .ui-btn {
    margin-right: 14px;
  }
}

.@{prefix}-exporting {
  position: absolute;
  height: 30px !important;
  width: 120px !important;
  top: 10px;
  left: calc(50% - 30px);
  border-radius: 6px;
  background-color: var(--base-white);
  padding: 8px 16px 8px 8px !important;
  box-shadow: var(--dialog-box-shadow);
  font-size: var(--font-size-sm);

  .ui-spin-text {
    position: absolute;
    color: #333;
    top: 8px;
    left: 30px;
    width: 85px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.@{prefix}-tempLib-leftMenu {
  height: 100%;
  min-width: 180px;
  border-right: 1px solid var(--border-color);
  background-color: #fff;
  overflow-y: auto;

  .@{prefix}-tempLib-unionlist {
    position: relative;
    height: 100%;
    transform: translateY(-1px);
    display: flex;
    flex-direction: column;

    &-search {
      padding: 8px 10px;
      flex-basis: 46px;

      &-wrap {
        width: 100%;
        display: flex;
        align-items: center;

        & > div {
          margin-right: 9px;
        }

        &-icon {
          cursor: pointer;

          &:hover {
            & > svg {
              color: var(--primary);
            }
          }

          & > svg {
            width: 19px;
            height: 19px;
          }
        }
      }
    }

    &-list {
      flex: 1;
      height: calc(100% - 46px);
      position: relative;

      .list {
        height: 100%;
      }
    }

    &-item {
      width: 100%;
      height: 30px;
      line-height: 30px;
      display: flex;
      align-items: center;
      padding: 0 10px 0 20px;
      cursor: pointer;

      .move {
        flex-basis: 10px;
      }

      &-name {
        flex-grow: 1;
        margin-left: 6px;
        overflow: hidden;

        .name {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      &-operation {
        flex-basis: 35px;
        text-align: center;
        visibility: hidden;

        .delete {
          margin: 0 3px;
        }
      }

      .my-handle {
        cursor: move;
        visibility: hidden;
      }

      .icon-null {
        width: 14px;
        height: 14px;
        display: inline-block;
      }

      &:hover .my-handle,
      &:hover .@{prefix}-tempLib-unionlist-item-operation {
        visibility: visible;
      }
    }

    .ui-icon {
      color: var(--secondary-fc);
    }

    .ui-collapse {
      .ui-collapse-panel--inactive {
        border-bottom: none;
      }

      .ui-collapse-panel__title {
        color: var(--main-fc);
        font-weight: normal;
        padding: 5px 10px;

        .ui-icon {
          margin-right: 2px;
        }
      }

      .ui-collapse-panel__content-box {
        .ui-list {
          .ui-list-item {
            &.isActive,
            &:hover {
              background-color: #f5f5f5;
            }

            .ui-list-content {
              display: block;
              min-height: 30px;
            }
          }
        }

        .ui-icon {
          transform: rotate(0) !important;
        }
      }
    }
  }
}

.@{prefix}-tempLib-content {
  height: 100%;
  margin-left: 16px;
  overflow: auto;

  .@{prefix}-tempLib-content-card-container {
    .@{prefix}-tempLib-layoutContent-emptyTip {
      height: 200px;
      line-height: 200px;
    }

    .@{prefix}-tempLib-content-card {
      padding: 0 6px;
      margin: 0 8px 0 0;
      background-color: #fff;
      border: 1px solid var(--border-color);
      width: 100px;
      height: 100px;
      overflow: hidden;
      border-radius: 2px;

      .@{prefix}-tempLib-contentCard-header {
        height: 20px;
        line-height: 20px;
        display: flex;
        justify-content: space-between;

        svg {
          width: 15px;
          height: 20px;
          color: var(--secondary-fc);
        }

        .ui-icon-wrapper svg:hover {
          cursor: pointer;
        }

        .ui-checkbox-left {
          margin-top: 1px;
        }

        .@{prefix}-card-more {
          visibility: hidden;

          &.show {
            visibility: visible;
          }
        }
      }

      .@{prefix}-tempLib-contentCard-image {
        width: 100%;
        height: 60px;
        text-align: center;

        &.move {
          cursor: move;
        }

        img {
          background-color: white;
          pointer-events: none;
          width: 100% !important;
          height: 100% !important;
          object-fit: contain;
        }

        .ebcoms-assets-item {
          background-color: white;
          pointer-events: none;
          width: 100% !important;
          height: 100% !important;
        }
      }
    }

    .@{prefix}-tempLib-content-card:hover {
      box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.12);
    }

    .@{prefix}-tempLib-content-footer {
      padding: 0 4px;
      margin: 5px 0 8px;
      width: 100px;
      font-size: 12px;
      color: #666;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.@{prefix}-tempLib-container {
  height: 100%;
}

.@{prefix}-tempLib-tree {
  .ui-tree-empty {
    height: 100%;

    & > span:first-child {
      display: none;
    }
  }

  .ui-empty-image svg {
    height: 100%;
    margin-top: -60px;
  }
}

body[page-rtl='true'] {
  .@{prefix}-temp-delete-confirm-content {
    direction: ltr;
  }

  .@{prefix}-tempLib-content
    .@{prefix}-tempLib-content-card-container
    .@{prefix}-tempLib-content-footer.ui-rtl {
    text-align: right;
  }
}
