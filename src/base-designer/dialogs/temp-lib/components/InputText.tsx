import { Icon, Input } from '@weapp/ui';
import { classnames as classNames } from '@weapp/utils';
import React, {
  SyntheticEvent, useCallback, useEffect, useState,
} from 'react';
import { prefixCls } from '../../../../constants';
import './index.less';
import setTimeoutOnce from '../../../../utils/setTimeoutOnce';

interface InputTextProps extends React.Attributes {
  text: string;
  defaultFocus?: boolean;
  readOnly: boolean;
  onOk: (value: string) => void;
  className?: string;
}

export default function InputText(props: InputTextProps) {
  const {
    readOnly, defaultFocus, onOk, text, className,
  } = props;
  const [focus, setFocus] = useState(Boolean(defaultFocus));
  const [hover, setHover] = useState(false);
  const cls = classNames(`${prefixCls}-assets-input-text`, className);

  useEffect(() => {
    setFocus(Boolean(defaultFocus));
  }, [defaultFocus]);

  const onBlur = useCallback((value) => {
    onOk(value as string);

    // 左侧树重绘，不让字有一个从上次改动到这次的过程
    setTimeoutOnce(() => {
      setFocus(false);
    }, 200);
  }, []);

  const onMouseEnter = useCallback(() => {
    setHover(true);
  }, []);

  const onMouseLeave = useCallback(() => {
    setHover(false);
  }, []);

  const onPressEnter = useCallback((value, e: SyntheticEvent) => {
    e.stopPropagation();
    onOk(value as string);

    setTimeoutOnce(() => {
      setFocus(false);
    }, 200);
  }, []);

  const onClick = useCallback((e: SyntheticEvent) => {
    setFocus(true);
    setHover(false);
    e.stopPropagation();
  }, []);

  if (focus) {
    return (
      <div className={className}>
        <Input
          weId={`${props.weId || ''}_cgzrsf`}
          autoFocus
          defaultValue={text as any}
          className={`${prefixCls}-assets-inp`}
          onBlur={onBlur}
          onPressEnter={onPressEnter}
        />
      </div>
    );
  }

  return (
    <div onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave} className={cls}>
      <span title={text}>{text}</span>
      {!readOnly && hover && (
        <span>
          <Icon weId={`${props.weId || ''}_7qn7ql`} name="Icon-edit" onClick={onClick} />
        </span>
      )}
    </div>
  );
}
