@import (reference) '../../../../style/prefix.less';

.@{prefix}-assets-input-text {
  & > span {
    user-select: none;
  }
  & > span:last-of-type {
    cursor: pointer;
  }
}

.@{prefix}-collapse {
  & .active .ui-collapse-panel__title {
    background-color: #f5f5f5;
  }

  .ui-collapse-panel__title {
    border: none;
    padding: 5px 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-transform: capitalize;
    display: block;
    width: 100%;
    font-size: var(--font-size-sm);
    font-weight: bold;
    background-color: inherit;
    transition: opacity ease-in-out 0.225s;
    &:hover {
      opacity: 0.85;
      background-color: #f5f5f5;
    }
  }
  .ui-collapse-panel__content-box {
    padding: 0;
    background-color: inherit;
    font-size: var(--font-size-sm);
  }
  .ui-collapse-panel__content {
    padding: 0;
  }

  .ui-collapse-panel__title .ui-icon {
    margin-left: -3px;
    transition: transform 0.2s ease-in-out;
  }

  .ui-collapse-panel--active {
    .ui-collapse-collapse-panel__content-box {
      border-bottom-color: transparent;
    }
    .ui-collapse-panel__title .ui-icon {
      transform: rotate(0);
    }
  }

  .ui-collapse-panel--inactive {
    .ui-collapse-panel__title .ui-icon {
      transform: rotate(-90deg);
    }
    border-bottom: 1px solid #ebedf0;
  }
}

body[page-rtl='true'] {
  .@{prefix}-collapse-title > span.ui-rtl {
    transform: none;
  }

  .@{prefix}-collapse-title > span.ui-icon {
    margin-right: 0px;
    margin-left: 2px;
  }
}
