@import (reference) '../../../../../style/prefix.less';

.@{prefix}-tempLib-dialogView {
  overflow: hidden;

  .ui-dialog-body {
    height: 100%;
    padding: 0;
  }
}

.@{prefix}-temp-form {
  padding: 16px;
}

.@{prefix}-comp-temp-add-cat {
  &-dropdown {
    width: 200px;
    max-height: 313px;
    display: flex;
    flex-direction: column;

    .ui-input-group {
      width: 90%;
      margin: 0 10px;

      div {
        color: var(--primary);
      }
    }

    .add-box {
      flex-basis: 8%;
      display: flex;
      padding: 3px 0 0 12px;

      .ui-icon {
        margin-right: 3px;
        margin-top: -2px;
        vertical-align: middle;
      }
    }

    .list {
      flex: 1;
      padding: 0;
      margin: 0 0 3px 0;
      position: relative;

      .ui-scroller__wrap {
        max-height: 270px;
      }

      li {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        display: block;
      }
    }
  }
}

body[page-rtl='true'] {
  .@{prefix}-comp-temp-add-cat-dropdown .list li.ui-rtl {
    text-align: right;
  }
}
