import {
  <PERSON><PERSON>, Cors<PERSON><PERSON>po<PERSON>, Dialog, FormDatas, FormStore, ListData,
} from '@weapp/ui';
import { cloneDeep, getLabel, isEmpty } from '@weapp/utils';
import { PureComponent } from 'react';
import { prefixCls } from '../../../../../constants';
import { CompTempDataType } from '../../../../panels/template/types';
import { EditTempData, TreeNode } from '../../types';
import './index.less';
import TempForm from './TemplateForm';

export type TempType = 'MASTER' | 'COMP';
interface EditTempDialogProps {
  type?: TempType;
  title: string;
  visible: boolean;
  editData: ListData | null;
  originData: CompTempDataType[];
  categoryData: TreeNode[];
  /** 无权保存、修改配置 */
  saveDisabled?: boolean;
  onCreateCat: (cateName: string, nameLabelId?: string) => any;
  onClose: () => void;
  onOk: (tempData: EditTempData) => void;
}

export default class EditTempDialog extends PureComponent<EditTempDialogProps> {
  store = new FormStore();

  getCurEditData = () => {
    const { editData, originData } = this.props;
    const result = cloneDeep(editData);
    let category = editData?.category || '-1';
    const { name = '', defaultName = '' } = editData || {};

    if (result) {
      result.fileId = editData?.icon || editData?.thumbnail || '';
      result.name = defaultName || name;
    }
    originData.forEach((cat: CompTempDataType) => cat.templates.forEach((temp) => {
      if (temp.id === editData?.id) {
        category = cat.id;
      }
    }));

    return {
      ...result,
      category,
    } as FormDatas;
  };

  onOk = () => {
    const { editData, onClose } = this.props;
    const formData = cloneDeep(this.store.getFormDatas());

    this.store.validate().then((res: any) => {
      const { errors } = res;

      const { name, nameLabelId } = formData;
      const isStringValue = typeof name === 'string';
      const nameVal = isStringValue ? { nameAlias: name } : name;
      const { nameAlias, nameAliasLabelId = nameLabelId } = nameVal;

      if (isEmpty(errors)) {
        const newTempMsg = {
          id: editData?.id,
          category: formData.category,
          icon: formData.fileId,
          name: nameAlias,
          nameLabelId: nameAliasLabelId,
        } as EditTempData;
        this.props.onOk(newTempMsg);
        onClose();
      }
    });
  };

  render() {
    const {
      visible,
      editData,
      categoryData,
      title,
      type,
      saveDisabled = false,
      onClose,
      onCreateCat,
    } = this.props;

    if (!editData || !categoryData) return null;

    const [, customerData] = categoryData;

    const footer = [
      <CorsComponent
        weId={`${this.props.weId || ''}_8c2vui`}
        app="@weapp/components"
        compName="Disabled"
        key="submit"
        condition={saveDisabled}
        hideMask
        style={{ display: 'inline-flex', marginRight: 'calc(var(--hd)* 10)' }}
      >
        <Button weId="w30m24@submit" key="submit" type="primary" onClick={this.onOk}>
          {getLabel('97216', '保存')}
        </Button>
      </CorsComponent>,
      <Button weId="l5w8u3@close" key="close" type="default" onClick={onClose}>
        {getLabel('53937', '取消')}
      </Button>,
    ];

    return (
      <Dialog
        weId={`${this.props.weId || ''}_x29p38`}
        visible={visible}
        title={title}
        onClose={onClose}
        closable
        destroyOnClose
        draggable
        mask
        resize
        width={600}
        bodyStyle={{ background: '#f9f9f9' }}
        className={`${prefixCls}-tempLib-dialogView`}
        icon="Icon-e-builder-o"
        footer={footer}
      >
        <TempForm
          weId={`${this.props.weId || ''}_ufzw8y`}
          key={type}
          type={type}
          disabled={saveDisabled}
          data={this.getCurEditData()}
          store={this.store}
          categoryData={customerData.children}
          onCreateCat={onCreateCat}
        />
      </Dialog>
    );
  }
}
