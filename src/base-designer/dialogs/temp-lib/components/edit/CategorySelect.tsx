import {
  <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>po<PERSON>, <PERSON><PERSON>, Input, <PERSON><PERSON><PERSON>, Trigger,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { Component, SyntheticEvent } from 'react';
import { prefixCls } from '../../../../../constants';
import AddCateDlg from '../../../com-temp/AddCategoryDlg';
import { CatOpts } from '../../../com-temp/CategorySelect';
import { TreeNode } from '../../types';
import { TempType } from './editTempDialog';
import './index.less';

interface CategorySelectProps {
  type?: TempType;
  caretogyData: TreeNode[];
  value: string;
  /** 禁止修改 */
  disabled?: boolean;
  onChange?: (changeVal: any) => void;
  onCreateCat: (cateName: string, nameLabelId?: string) => any;
}

interface CategorySelectState {
  visible: boolean;
  trigVisible: boolean;
  data: TreeNode[];
}

const transform = (category: TreeNode[]) => category
  .filter((item) => !item.isSystem)
  .map((item) => ({
    id: item.id,
    content: item.name,
  }));

class CategorySelect extends Component<CategorySelectProps, CategorySelectState> {
  state = {
    visible: false,
    trigVisible: false,
    data: this.props.caretogyData,
  };

  onChange = (value: CatOpts) => () => {
    const { onChange } = this.props;
    const { id } = value;

    if (id) {
      this.setState({ trigVisible: false });
    }

    onChange?.(id);
  };

  onAddCate = (id: string, cateName: string, nameLabelId?: string) => {
    const { onChange, onCreateCat } = this.props;
    const { data } = this.state;

    if (!cateName) return;

    onCreateCat(cateName, nameLabelId).then((res: any) => {
      onChange?.(res?.id);
      this.setState({ visible: false, data: [...data, res] });
    });
  };

  openAddDlg = (e: SyntheticEvent) => {
    e.stopPropagation();
    this.setState({
      visible: true,
      trigVisible: false,
    });
  };

  setTrigVisible = (trigVisible: boolean) => {
    this.setState({ trigVisible });
  };

  setVisible = (visible: boolean) => () => {
    this.setState({ visible });
  };

  togglePopup = () => {
    const { trigVisible } = this.state;
    this.setState({
      trigVisible: !trigVisible,
    });
  };

  render() {
    const { value, type, disabled = false } = this.props;
    const { visible, trigVisible, data } = this.state;

    const selectData = transform(data);
    const catName = selectData.find((item) => item.id === value)?.content || '';

    return (
      <div className={`${prefixCls}-comp-temp-add-cat`}>
        <Trigger
          weId={`${this.props.weId || ''}_dm5w0h`}
          action={['click']}
          popupVisible={trigVisible}
          onPopupVisibleChange={this.setTrigVisible}
          popup={
            <div className={`${prefixCls}-comp-temp-add-cat-dropdown ui-select-dropdown`}>
              <Scroller className="list" direction="v" weId={`${this.props.weId || ''}_8ppoqb`}>
                {selectData.map((cat) => (
                  <li
                    className="ui-select-option"
                    key={cat.id}
                    title={cat.content}
                    onClick={this.onChange(cat)}
                  >
                    {cat.content}
                  </li>
                ))}
              </Scroller>
              <div className="add-box">
                <CorsComponent
                  weId={`${this.props.weId || ''}_8c2vui`}
                  app="@weapp/components"
                  compName="Disabled"
                  condition={disabled}
                  hideMask
                >
                  <Button
                    weId={`${this.props.weId || ''}_1qrgli`}
                    type="link"
                    onClick={this.openAddDlg}
                  >
                    <Icon weId={`${this.props.weId || ''}_ijigkt`} name="Icon-add-to01" />
                    {getLabel('54007', '新建')}
                  </Button>
                </CorsComponent>
              </div>
            </div>
          }
        >
          <Input
            weId={`${this.props.weId || ''}_l1d4s1`}
            value={catName}
            className={`${prefixCls}-comp-temp-add-cat-input`}
            style={{ width: 200 }}
            placeholder={getLabel('40502', '请选择')}
            // eslint-disable-next-line react/jsx-no-bind
            onBeforeFocusCheck={() => false}
            suffix={
              <Icon
                weId={`${this.props.weId || ''}_4erzd4`}
                name="Icon-Down-arrow01"
                onClick={this.togglePopup}
              />
            }
          />
        </Trigger>
        <AddCateDlg
          weId={`${this.props.weId || ''}_rcofvl`}
          visible={visible}
          placeholder={
            type === 'MASTER'
              ? getLabel('118359', '请输入母版分类名称')
              : getLabel('101594', '请输入模板分类名称')
          }
          onAdd={this.onAddCate}
          onClose={this.setVisible(false)}
        />
      </div>
    );
  }
}

export default CategorySelect;
