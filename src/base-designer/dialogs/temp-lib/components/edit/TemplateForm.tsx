import {
  CorsComponent,
  Form,
  FormDatas,
  FormItemProps,
  FormRenderProps,
  FormSwitchProps,
} from '@weapp/ui';
import { cloneDeep, getLabel } from '@weapp/utils';
import React from 'react';
import { prefixCls } from '../../../../../constants';
import AssetsSelect from '../../../com-temp/AssetsSelect';
import { TreeNode } from '../../types';
import CategorySelect from './CategorySelect';
import { TempType } from './editTempDialog';
import './index.less';

interface TempFormProps extends Omit<FormRenderProps, 'type'> {
  type?: TempType;
  data: FormDatas;
  /** 禁止修改 */
  disabled?: boolean;
  categoryData: TreeNode[];
  onCreateCat: (cateName: string, nameLabelId?: string) => any;
}

const assetsSelect = (props: any) => (
  <AssetsSelect weId={`${props.weId || ''}_iuid3x`} {...props.props} />
);

const categorySelect = (_props: FormSwitchProps, props: TempFormProps) => (
  <CategorySelect
    weId={`${props.weId || ''}_v1dhhx`}
    {..._props.props}
    type={props.type}
    disabled={props.disabled}
    caretogyData={props.categoryData}
    onCreateCat={props.onCreateCat}
  />
);

const appInitLayout = () => [
  [
    {
      id: 'name',
      label: getLabel('53866', '名称'),
      items: ['name'],
      labelSpan: 6,
      hide: false,
      className: `${prefixCls}-comp-temp-edit-name`,
    },
  ],
  [
    {
      id: 'fileId',
      label: getLabel('56345', '图标'),
      items: ['fileId'],
      labelSpan: 6,
      hide: false,
      className: `${prefixCls}-comp-temp-edit-fileId`,
    },
  ],
  [
    {
      id: 'category',
      label: getLabel('99292', '分类'),
      items: ['category'],
      labelSpan: 6,
      hide: false,
      className: `${prefixCls}-comp-temp-edit-category`,
    },
  ],
];

class TempForm extends React.Component<TempFormProps> {
  componentDidMount() {
    const { store, data } = this.props;
    const items = this.appItems();
    const layout = cloneDeep(appInitLayout());

    store.initForm({
      data,
      items,
      layout,
      groups: [],
    });
  }

  appItems = (): FormItemProps => {
    const { type } = this.props;

    return {
      name: {
        itemType: 'CUSTOM',
        placeholder:
          type === 'MASTER'
            ? getLabel('118354', '请输入母版名称')
            : getLabel('99296', '请输入模板名称'),
        required: true,
        autoFocus: true,
        errorType: 'popover',
      },
      fileId: {
        itemType: 'CUSTOM',
        required: true,
        errorType: 'popover',
      },
      category: {
        itemType: 'CUSTOM',
        required: true,
        errorType: 'popover',
      },
    };
  };

  onChange = (value?: FormDatas) => {
    this.props.store.updateDatas(value);
    this.forceUpdate();
  };

  nameLocaleSet = (props: any) => {
    const { type, store } = this.props;
    const formData = store.getFormDatas();
    const { nameLabelId } = formData;
    const tablefield = type === 'MASTER' ? 'ebdd_master_component.master_name' : 'ebdd_component_tmpl.name';

    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_jqboip`}
        app="@weapp/ebdcoms"
        compName="LocaleAnother"
        module="ebuilder_designer"
        tablefield={tablefield}
        targetId={nameLabelId}
        style={{ minWidth: '100%' }}
        {...props.props}
      />
    );
  };

  customRenderFormSwitch = (key: string, props: FormSwitchProps) => {
    switch (key) {
      case 'name':
        return this.nameLocaleSet(props);
      case 'category':
        return categorySelect(props, this.props);
      default:
        return assetsSelect(props);
    }
  };

  render() {
    const { store } = this.props;

    return (
      <div className={`${prefixCls}-temp-form`}>
        <Form
          weId={`${this.props.weId || ''}_mz3o3v`}
          store={store}
          onChange={this.onChange}
          customRenderFormSwitch={this.customRenderFormSwitch}
        />
      </div>
    );
  }
}

export default TempForm;
