import { Collapse as UICollapse, Icon } from '@weapp/ui';
import { classnames } from '@weapp/utils';
import React, { PropsWithChildren, PureComponent, SyntheticEvent } from 'react';
import { prefixCls } from '../../../../constants';
import './index.less';
import { CollapseProps } from './type';

interface CollapseStates {
  activeKey: string | string[];
}

const dftKey = 'dftKey';

export default class Collapse extends PureComponent<
  PropsWithChildren<CollapseProps>,
  CollapseStates
> {
  state = {
    activeKey: dftKey,
  };

  onToggle = (e: SyntheticEvent) => {
    e.stopPropagation();
    const { activeKey } = this.state;

    this.setState({
      activeKey: activeKey.includes(dftKey) ? [] : [dftKey],
    });
  };

  render() {
    const { activeKey } = this.state;
    const {
      isSelect = false, title, children, className, onTitleClick,
    } = this.props;
    const panelTitle = (
      <div className={`${prefixCls}-collapse-title`} onClick={onTitleClick}>
        <Icon
          weId={`${this.props.weId || ''}_2d5lvo`}
          name="Icon-Down-arrow04"
          onClick={this.onToggle}
        />
        <span>{title}</span>
      </div>
    );

    return (
      <UICollapse
        weId={`${this.props.weId || ''}_4b3cg9`}
        bordered={false}
        activeKey={activeKey}
        className={className || `${prefixCls}-collapse`}
      >
        <UICollapse.Panel
          showArrow={false}
          weId={`${this.props.weId || ''}_finc66`}
          title={panelTitle}
          key={dftKey}
          className={classnames({ active: isSelect })}
        >
          {children}
        </UICollapse.Panel>
      </UICollapse>
    );
  }
}
