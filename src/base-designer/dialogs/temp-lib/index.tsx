import { Provider } from 'mobx-react';
import React, { PureComponent } from 'react';
import './index.less';
import Main from './Main';
import tempLibStore from './store';
import { TempLibProps } from './types';

export default class TempLib extends PureComponent<TempLibProps> {
  render() {
    return (
      <Provider weId={`${this.props.weId || ''}_mwrbkj`} tempLibStore={tempLibStore}>
        <Main weId={`${this.props.weId || ''}_w22ict`} {...this.props} />
      </Provider>
    );
  }
}
