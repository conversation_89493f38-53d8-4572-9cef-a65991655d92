import {
  B<PERSON><PERSON><PERSON>b, Button, CorsComponent, Dialog, Icon, Input,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { ReactText, useCallback } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import { HeaderProps } from './types';

const { confirm } = Dialog;

const Header: React.FC<HeaderProps> = (props) => {
  const {
    onClickBreadCrumb, onUploadClick, canUpload, addDisabled = false,
  } = props;

  const onBatchDelete = useCallback(() => {
    const deleteObj = props.getBatchDeleteIds();
    let nameBox: string = '';
    const idBox: string[] = [];
    deleteObj.forEach((temp) => {
      nameBox += `「${temp.name}」`;
      idBox.push(temp.id);
    });
    confirm({
      mask: true,
      content: (
        <div className={`${prefixCls}-temp-delete-confirm-content`}>
          <span>{getLabel('98883', '确定要删除模板')}</span>
          <span style={{ color: 'var(--danger)' }}>{nameBox}</span>？
        </div>
      ),
      onOk: () => {
        props.onBatchDelete(idBox);
      },
    });
  }, []);

  const onUploadBtnClick = useCallback(() => {
    onUploadClick?.(true);
  }, []);

  const onComNameSearch = useCallback((value: ReactText) => {
    const { onSearch } = props;
    onSearch(`${value}`);
  }, []);

  const onClick = useCallback(
    (index: number) => () => {
      onClickBreadCrumb(index);
    },
    [],
  );

  function getBreadCrumbItems(breadCrumbItems: string[]) {
    return breadCrumbItems.map((t: string, index: number) => (
      <span key={t} onClick={onClick(index)}>
        {t}
      </span>
    ));
  }

  const data = getBreadCrumbItems(props.breadCrumbItems);

  return (
    <div className={`${prefixCls}-tempLib-header-content`}>
      <div className={`${prefixCls}-tempLib-header-left`}>
        <BreadCrumb weId={`${props.weId || ''}_jrzunv`} data={data} />
      </div>
      <div className={`${prefixCls}-tempLib-header-right`}>
        <When weId={`${props.weId || ''}_qmi7s1`} condition={canUpload}>
          <CorsComponent
            weId={`${props.weId || ''}_yiytvu`}
            app="@weapp/components"
            compName="Disabled"
            condition={addDisabled}
            style={{ display: 'inline-block', marginLeft: '14px' }}
          >
            <Button weId={`${props.weId || ''}_010y7d`} type="primary" onClick={onUploadBtnClick}>
              <Icon weId={`${props.weId || ''}_ozhl5j`} name="Icon-add-to01" />
              <span>{getLabel('114424', '导入模板')}</span>
            </Button>
          </CorsComponent>
        </When>
        <When weId={`${props.weId || ''}_jost4x`} condition={props.canOperate}>
          <Button weId={`${props.weId || ''}_g0b4fk`} type="danger" onClick={onBatchDelete}>
            {getLabel('74407', '批量删除')}
          </Button>
        </When>
        <Input
          className="ui-searchAdvanced-input"
          weId={`${props.weId || ''}_9cs2aj`}
          style={{ width: 200 }}
          allowClear
          suffix={
            <Icon
              className="ui-searchAdvanced-input-icon"
              weId={`${props.weId || ''}_2px8ru`}
              name="Icon-search"
            />
          }
          placeholder={getLabel('99296', '请输入模板名称')}
          onChange={onComNameSearch}
        />
      </div>
    </div>
  );
};

export default Header;
