import {
  Button, Dialog, Spin, Upload,
} from '@weapp/ui';
import { getLabel, isEmpty, isString } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import { ComponentType, PureComponent } from 'react';
import { prefixCls } from '../../../../constants';
import ebdcoms from '../../../../utils/ebdcoms';
import { TempStoreProps } from '../types';
import './index.less';

const { message, confirm } = Dialog;

interface ImportTemplateStates {
  uploadTitle: string;
  uploadProgress: number;
}

@inject('tempLibStore')
@observer
class ImportTemplate extends PureComponent<TempStoreProps, ImportTemplateStates> {
  instance: any;

  constructor(props: TempStoreProps) {
    super(props);
    this.state = {
      uploadTitle: getLabel('85521', '上传中'),
      uploadProgress: 0,
    };
  }

  onClose = () => {
    const { setUploadVisible, setUploadingVisible } = this.props.tempLibStore;

    setUploadingVisible(false);
    setUploadVisible(false);
  };

  onClearn = () => {
    const { setUploadingVisible } = this.props.tempLibStore;

    setUploadingVisible(false);

    this.setState({
      uploadProgress: 0,
      uploadTitle: getLabel('85521', '上传中'),
    });
  };

  setUploadProgress = (totalProcess: number) => {
    this.setState({
      uploadProgress: totalProcess,
    });
  };

  setUploadTitle = (title?: string) => {
    this.setState({
      uploadTitle: title || getLabel('85524', '正在导入中'),
    });
  };

  importPageTemplate = (option: any) => {
    const {
      tempLibStore: { activeListItem, pageScope },
    } = this.props;
    const formData = new FormData();
    formData.append('file', option.file);

    const importId = ebdcoms.excu('UUID');
    const importProgress = this.getProgress(importId);

    let category = isEmpty(toJS(activeListItem)) ? '-1' : activeListItem[0].id;

    if (!isEmpty(toJS(activeListItem)[0]?.children)) {
      category = toJS(activeListItem)?.[0]?.children?.[0]?.id || '-1';
    }

    ebdcoms
      .asyncExcu('ajax', {
        url: '/api/ebuilder/coms/packing/comptmpl/import',
        method: 'post',
        data: formData,
        params: {
          importId,
          moduleType: pageScope,
          type: 'comptmpl',
          category: category === '200' ? '-1' : category,
        },
      })
      .then((res: any) => {
        if (!res) return;

        const { style, status, name } = res;
        if (status === 0) {
          clearInterval(importProgress);
          this.onClearn();
          return message({
            type: 'error',
            content: getLabel('85420', '文件错误'),
            delay: 1000,
          });
        }

        if (status === 2) {
          clearInterval(importProgress);
          this.checkImportStatus(importId, style, name);
        } else {
          this.setUploadTitle();
        }
      })
      .catch(() => {
        clearInterval(importProgress);
        this.onClearn();
      });
  };

  coverStyle = (sty: string, isCover: boolean, importId: string) => () => {
    const { pageScope } = this.props.tempLibStore;

    const data = new FormData();

    // data.append('style', sty);
    data.append('isCover', isCover as any);
    data.append('importId', importId);
    data.append('moduleType', pageScope);
    data.append('type', 'comptmpl');

    if (this.instance) {
      this.instance.onClose();
    }

    if (!isEmpty(sty)) {
      this.setUploadTitle();
      this.getProgress(importId);
      ebdcoms
        .asyncExcu('ajax', {
          url: '/api/ebuilder/coms/packing/comptmpl/cover',
          method: 'POST',
          data,
          encrys: ['style'], // style属性加密
        })
        .catch(() => {
          message({
            type: 'error',
            content: isCover ? getLabel('92033', '覆盖失败！') : getLabel('92034', '新增失败！'),
            delay: 1000,
          });
        });
    }
  };

  getProgress = (importId: string) => {
    const { tempLibStore } = this.props;

    let isComplete = false;
    // 请求进度
    const importProgress = setInterval(() => {
      ebdcoms
        .asyncExcu('ajax', {
          url: '/api/ebuilder/coms/packing/comptmpl/importProgress',
          method: 'get',
          params: {
            importId,
            type: 'comptmpl',
          },
          error: () => {
            clearInterval(importProgress);
            this.onClearn();
          },
        })
        .then((r: any) => {
          if (isString(r)) {
            let totalProcess = 0;
            totalProcess = this.roundNumber(parseFloat((r as any) || 0));
            this.setUploadProgress(totalProcess);
          }

          if (r === '100') {
            if (!isComplete) {
              this.importSuccess();
              message({
                type: 'success',
                content: getLabel('85523', '导入成功'),
                delay: 1000,
              });
            }
            isComplete = true;
            this.onClose();
            clearInterval(importProgress);
          }

          const { uploadingVisible } = tempLibStore;
          if (!uploadingVisible) {
            this.onClearn();
          }
        })
        .catch(() => {
          clearInterval(importProgress);
          this.onClearn();
        });
    }, 200);

    return importProgress;
  };

  getInstance = (val: any) => {
    this.instance = val;
  };

  importSuccess = () => {
    const { tempLibStore } = this.props;
    const {
      pageScope, layoutType, clientType, events, activeListItem,
    } = tempLibStore;
    const category = isEmpty(toJS(activeListItem)) ? undefined : toJS(activeListItem)[0];
    this.onClose();
    tempLibStore.init(pageScope, clientType, layoutType, events, category);
    events?.emit('update.comptemp');
  };

  onCloseConfirm = () => {
    if (this.instance) {
      this.instance.onClose();
    }
    this.onClearn();
  };

  checkImportStatus = (importId: string, style: any, name: string) => {
    confirm({
      mask: true,
      content: (
        <span>
          {getLabel('115206', '系统已存在相同id的模板')}
          <span style={{ color: '#D9001B' }}>[{name}]，</span>
          {getLabel('115208', '请确认是新增还是覆盖此模板？')}
        </span>
      ),
      getInstance: this.getInstance,
      footer: (
        <span>
          <Button
            weId={`${this.props.weId || ''}_clw8ad`}
            type="primary"
            onClick={this.coverStyle(JSON.stringify(style), false, importId)}
          >
            {getLabel('56575', '新增')}
          </Button>
          <Button
            weId={`${this.props.weId || ''}_7hqbdo`}
            type="default"
            style={{ marginRight: '10px' }}
            onClick={this.coverStyle(JSON.stringify(style), true, importId)}
          >
            {getLabel('56306', '覆盖')}
          </Button>
          <Button
            weId={`${this.props.weId || ''}_523emu`}
            type="default"
            onClick={this.onCloseConfirm}
          >
            {getLabel('53937', '取消')}
          </Button>
        </span>
      ),
    });
  };

  roundNumber = (value: any) => Math.round(value * 100) / 100;

  setUploading = () => {
    const { setUploadingVisible } = this.props.tempLibStore;

    setUploadingVisible(true);
  };

  render() {
    const { uploadVisible, uploadingVisible } = this.props.tempLibStore;
    const { uploadTitle, uploadProgress } = this.state;

    return (
      <Dialog
        weId={`${this.props.weId || ''}_we4fcc`}
        visible={uploadVisible}
        title={getLabel('114424', '导入模板')}
        className={`${prefixCls}-import-template-dialog`}
        width={500}
        onClose={this.onClose}
        destroyOnClose
        draggable
        closable
        mask
        icon={ebdcoms.get().dlgIconName}
      >
        <div className={`${prefixCls}-import-wrap`}>
          <Spin
            weId={`${this.props.weId || ''}_k68l0s`}
            spinning={uploadingVisible}
            text={`${uploadTitle}:${uploadProgress}%`}
          >
            <Upload
              weId={`${this.props.weId || ''}_1ikiue`}
              uploadTitle={
                (
                  <div
                    className={`${prefixCls}-title`}
                    title={getLabel('115209', '点击或拖拽上传模板')}
                  >
                    <img
                      src={ebdcoms.excu('getImgPath', '/background/upload.png', 'ebdcoms')}
                      alt=""
                    />
                    <div className={`${prefixCls}-text`}>
                      <div className={`${prefixCls}-info-text`}>
                        {getLabel('115209', '点击或拖拽上传模板')}
                      </div>
                      <div className={`${prefixCls}-warning-text`}>
                        {getLabel('85520', '导入的格式为zip文件,请注意格式')}
                      </div>
                    </div>
                  </div>
                ) as any
              }
              limitType="zip"
              multiple={false}
              buttonType="image"
              style={{ width: '100%', height: '180px' }}
              uploadParams={{
                module: 'ebuilder',
              }}
              className={`${prefixCls}-uploaded`}
              customRequest={this.importPageTemplate}
              showListUploaded={false}
              showListUploading={false}
              onUploading={this.setUploading}
            />
          </Spin>
        </div>
      </Dialog>
    );
  }
}

export default ImportTemplate as unknown as ComponentType<{}>;
