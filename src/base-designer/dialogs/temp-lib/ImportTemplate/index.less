@import (reference) '../../../../style/prefix.less';

.@{prefix}-import-template-dialog {
  .@{prefix}-import-wrap {
    height: 300px;
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 5px;

    .@{prefix}-uploaded {
      height: 100% !important;

      .ui-upload-select-image {
        height: 100% !important;
        width: 100%;
        margin-left: 0;
        outline: none;

        .ui-upload-select-box {
          .ui-icon-xs {
            display: none;
          }

          .ui-upload-select-image-title {
            margin: 0;
            display: inline-block;
            width: 100%;
            height: 100%;
            overflow: visible;
          }
        }
      }
    }

    .@{prefix}-title {
      height: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;

      & > img {
        height: 237px;
        width: 237px;
      }

      .@{prefix}-text {
        position: relative;
        margin-top: 5px;
        bottom: 20px;

        .@{prefix}-info-text,
        .@{prefix}-warning-text {
          color: #666 !important;
          font-size: calc(var(--hd) * 12) !important;
        }

        .@{prefix}-info-text {
          height: 24px;
          font-weight: 700;
        }
      }
    }
  }
}
