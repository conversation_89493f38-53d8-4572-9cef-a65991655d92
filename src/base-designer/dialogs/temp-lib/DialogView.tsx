import { Dialog } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../../constants';
import TempLib from './index';
import './index.less';
import { TempDiaProps } from './types';

const DialogView: React.FC<TempDiaProps> = (props) => {
  const {
    visible,
    onClose,
    pageScope,
    pageInfo,
    clientType = 'PC',
    events,
    changeDisabled = false,
  } = props;

  return (
    <When weId={`${props.weId || ''}_8632px`} condition={visible}>
      <Dialog
        weId={`${props.weId || ''}_ms3q8h`}
        visible={visible}
        title={getLabel('98777', '模板管理')}
        onClose={onClose}
        closable
        destroyOnClose
        draggable
        mask
        resize
        width={960}
        height={610}
        bodyStyle={{ background: '#f9f9f9' }}
        className={`${prefixCls}-eleLib-dialogView temp-dialog`}
        icon="Icon-e-builder-o"
      >
        <TempLib
          weId={`${props.weId || ''}_vp32tm`}
          pageScope={pageScope}
          pageInfo={pageInfo}
          clientType={clientType}
          changeDisabled={changeDisabled}
          events={events}
        />
      </Dialog>
    </When>
  );
};
export default DialogView;
