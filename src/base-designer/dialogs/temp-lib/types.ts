import { ListData, MenuItemData } from '@weapp/ui';
import React, { ReactNode } from 'react';
import { ClientType, Page, PageModuleType } from '../../../core';
import { EventEmitter } from '../../../core/utils';
import { CompTempDataType } from '../../panels/template/types';
import { TempLibStore } from './store';

export interface CompTempCategoryType {
  /**
   *  模板分类列表
   */
  id: string;
  pid?: string;
  name: string;
  showOrder?: number;
  comps?: CompTempDataType[];
  childs?: CompTempCategoryType[];
}

export interface TempLibProps extends React.Attributes {
  pageScope: PageModuleType;
  clientType: ClientType;
  pageInfo?: Page;
  events?: EventEmitter;
  renderRightCom?: (RightCom: ReactNode) => void;
  onCheckChange?: (rowData: ListData) => void;
  onDataLoad?: () => void;
  /** 无权保存、修改配置 */
  changeDisabled?: boolean;
}

export interface TempDiaProps extends TempLibProps {
  visible: boolean;
  onClose: () => void;
}

export interface LeftMenuProps extends React.Attributes {
  store: TempLibStore;
  value: TreeNode[];
  /** 无权保存、修改配置 */
  changeDisabled?: boolean;
}

export interface TempStoreProps extends TempLibProps {
  tempLibStore: TempLibStore;
}

export interface TreeNode extends React.Attributes {
  id: string;
  name: string;
  children: TreeNode[];
  index: string[];
  fullname: string[];
  isSystem?: boolean;
  nameLabelId?: string;
  defaultName?: string;
}

export interface HeaderProps {
  canOperate: boolean;
  onBatchDelete: (idBox: string[]) => Promise<any>;
  onSearch: (name: string) => void;
  breadCrumbItems: string[];
  onClickBreadCrumb: (index: number) => void;
  isRenderSearchInput?: boolean;
  weId: string;
  getBatchDeleteIds: () => CompTempDataType[];
  onUploadClick?: (v: boolean) => void;
  /** 无权保存、修改配置 */
  addDisabled?: boolean;
  canUpload?: boolean;
}

export interface CardProps extends React.Attributes {
  rowData: ListData;
  rowID: number;
  weId: string;
  canMove: boolean;
  isSearching: boolean;
  isDetailCategory: boolean;
  pageScope: PageModuleType;
  menuData?: MenuItemData[];
  /** 无权保存、修改配置 */
  changeDisabled?: boolean;
  onCheckChange: (data: CompTempDataType, value: any) => void;
  onClickIcon: (type: string, data: ListData) => void;
}

export interface EditTempData {
  id: string;
  icon: string;
  category: string;
  name: string;
  nameLabelId?: string;
}

export enum Operation {
  Modify = 'modify',
  Delete = 'delete',
  Export = 'export',
}

export interface RefreshParams {
  /** 调用来源 */
  source?: SourceType;
}

/**
 * 调用来源枚举
 */
export enum SourceType {
  /** 删除操作完成后 */
  Delete = 'delete',
  /** 新增分类完成后 */
  Create = 'create',
}

export default {};
