import { Dialog, ListData } from '@weapp/ui';
import {
  cloneDeep, getLabel, isEmpty, isEqual, isString, RequestResult,
} from '@weapp/utils';
import {
  action, computed, observable, runInAction, toJS,
} from 'mobx';
import React from 'react';
import {
  ClientType, IComTemplateData, LayoutType, PageModuleType,
} from '../../../core';
import { EventEmitter } from '../../../core/utils';
import ajax from '../../../utils/ajax';
import ebdcoms from '../../../utils/ebdcoms';
import { CompTempDataType, Params } from '../../panels/template/types';
import { ComTempCategory, ComTempData } from '../com-temp/types';
import {
  EditTempData, RefreshParams, SourceType, TreeNode,
} from './types';

const { message } = Dialog;

const DefaultTreeNode = () => [
  {
    id: '1',
    name: get<PERSON>abel('55626', '系统'),
    fullname: [getLabel('55626', '系统')],
    children: [],
    index: ['1'],
  },
  {
    id: '100',
    name: getLabel('54002', '自定义'),
    fullname: [getLabel('54002', '自定义')],
    children: [],
    index: ['100'],
  },
] as TreeNode[];

export class TempLibStore {
  events?: EventEmitter = new EventEmitter();

  /**
   *  模板分类列表
   */
  @observable tempCategories: ComTempCategory[] = [];

  @observable loading: boolean = false;

  @observable hasMore: boolean = true;

  /** 页数 */
  @observable pageNo: number = 1;

  /** 获取模板列表参数 */
  @observable params: Params = { module: 'EB_PAGE', layoutType: 'FLOW', pageSize: 50 };

  /**
   * 显示模板
   */
  @observable showTemps: IComTemplateData[] = [];

  /**
   * 树节点
   */
  @observable treeData: TreeNode[] = [];

  /**
   * 当前选中目录索引
   */
  @observable currentTreeNodeIndex: string[] = [];

  /**
   * 当前选中目录名称
   */
  @observable currentTreeNodeName: string[] = [];

  /**
   * 组件作用域
   */
  @observable pageScope: PageModuleType = 'EB_PAGE';

  /**
   * 接口请求过来的原始数据
   */
  @observable originData: CompTempDataType[] = [];

  /**
   * 名称搜索条件
   */
  @observable inputSearch: string = '';

  @observable cateSearch: string = '';

  @observable focusId: string = '';

  @observable clientType: ClientType = 'PC';

  @observable hasInit: boolean = false;

  @observable activeListItem: ListData[] = [];

  layoutType?: LayoutType = 'FLOW';

  @action.bound
  init(
    pageScope: PageModuleType,
    clientType: ClientType,
    layoutType?: LayoutType,
    events?: EventEmitter,
    activeCategory?: any,
  ) {
    this.inputSearch = '';
    this.clientType = clientType;
    this.layoutType = layoutType;
    this.hasInit = false;
    this.events = events;

    const params = {
      module: pageScope,
      layoutType,
      pageSize: 50,
      clientType,
    };

    this.params = params;

    const categoriesParams = cloneDeep(this.params);
    delete categoriesParams.layoutType;

    this.getTempCategories(categoriesParams).then(() => {
      runInAction(() => {
        this.pageScope = pageScope;
        this.treeData = this.getTreeNode();

        if (activeCategory) {
          this.currentTreeNodeIndex = activeCategory.index;
          this.currentTreeNodeName = activeCategory.fullname;
          this.getShowTemplatesByTreeId();
        } else {
          const defaultIndex = isEmpty(toJS(this.treeData)[0].children) ? 1 : 0;
          const defaultTree = toJS(this.treeData[defaultIndex]);
          // 初始化时默认为第一个有值的大分类
          this.currentTreeNodeIndex = defaultTree.index;
          this.currentTreeNodeName = defaultTree.fullname;
          this.getShowTemplatesByTreeId({ isSys: 1, ...this.params });
        }
      });
    });
  }

  @action.bound
  getTreeNode() {
    const treeData: TreeNode[] = cloneDeep(DefaultTreeNode());
    const caretogyText = toJS(this.cateSearch);
    this.tempCategories.forEach((data) => {
      const {
        id, name, isSys, nameLabelId, defaultName,
      } = data;
      const pData = isSys ? treeData[0] : treeData[1];

      if (isString(caretogyText)) {
        if (name.toLocaleLowerCase().includes(caretogyText.toLocaleLowerCase())) {
          pData.children.push({
            id,
            name,
            children: [],
            index: [pData.id, id],
            fullname: [pData.name, name],
            isSystem: !!isSys,
            nameLabelId,
            defaultName,
          });
        }
      } else {
        pData.children.push({
          id,
          name,
          children: [],
          index: [pData.id, id],
          fullname: [pData.name, name],
          isSystem: !!isSys,
          nameLabelId,
          defaultName,
        });
      }
    });
    return treeData;
  }

  @action
  getTempCategories = (params: Params) => ebdcoms.asyncExcu('ajax', {
    url: '/api/ebuilder/coms/tmpl/category/list',
    params,
    success: (data: ComTempCategory[]) => {
      runInAction(() => {
        this.tempCategories = data;
        this.originData = data.map((item: any) => {
          item.templates = [];

          return item;
        });
      });
    },
  });

  @action.bound
  getTemplates = (params: Params, needUpdate: boolean = true) => {
    this.loading = true;

    if (params.isSys !== undefined && params.isSys !== 0) {
      delete params.category;
    }

    const _params = {
      pageNo: this.pageNo,
      isAll: true,
      ...params,
    };

    return ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/component/tmpl/list',
      params: _params,
      success: (res: ComTempData) => {
        runInAction(() => {
          if (_params.pageNo === 1) {
            this.showTemps = res.templates;
          } else {
            this.showTemps = [...this.showTemps, ...res.templates];
          }
          if (needUpdate) {
            this.events?.emit('update.comptemp');
          }
          this.hasMore = res.templates.length >= params.pageSize;
          this.loading = false;
          this.hasInit = true;
        });
      },
      error: () => {
        runInAction(() => {
          this.hasInit = true;
        });
        return true;
      },
    });
  };

  @action.bound
  createCat = (cat: string, nameLabelId?: string) => ebdcoms.asyncExcu('ajax', {
    url: '/api/ebuilder/coms/tmpl/category/create',
    method: 'post',
    params: {
      module: this.pageScope,
      name: cat,
      nameLabelId,
    },
    success: () => {
      message({
        type: 'success',
        content: getLabel('54117', '保存成功'),
      });
      this.getTempCategories(this.params).then(() => {
        this.refreshData({
          source: SourceType.Create,
        });
      });
    },
  });

  @action.bound
  onTreeNodeClick = (index: string[], fullname: string[]) => {
    if (!isEqual(index, toJS(this.currentTreeNodeIndex))) {
      this.currentTreeNodeIndex = index;
      this.currentTreeNodeName = fullname;
      this.hasMore = true;
      this.pageNo = 1;

      const cb = () => {};
      this.getShowTemplatesByTreeId(this.params, cb, false);
    }
  };

  /**
   * 根据索引获取节点下所有数据
   */
  @action.bound
  getShowTemplatesByTreeId = (params?: Params, cb?: () => void, needUpdate?: boolean) => {
    const index = toJS(this.currentTreeNodeIndex);

    this.params.category = index[1] as any;

    const _params = params || this.params;

    this.getTemplates(_params, needUpdate).then(() => {
      cb?.();
    });
  };

  @action.bound
  getBatchDeleteIds() {
    const batchObj: any[] = [];
    this.showTemps.forEach((template) => {
      if (template?.checked) {
        batchObj.push(template);
      }
    });
    return batchObj;
  }

  /**
   * 批量删除模板
   * @param idBox
   * @returns
   */
  @action.bound
  onBatchDelete(idBox: string[]) {
    const data = new FormData();
    idBox.forEach((id) => data.append('ids', id));

    return ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/component/tmpl/delete',
      method: 'POST',
      data,
      success: () => {
        message({
          type: 'success',
          content: getLabel('54119', '删除成功'),
        });
        const params = this.setParams();

        this.getTempCategories(this.params).then(() => {
          runInAction(() => {
            this.treeData = this.getTreeNode();
            const isSys = isEqual(toJS(this.currentTreeNodeIndex), DefaultTreeNode()[0].index)
              ? 1
              : 0;

            this.getShowTemplatesByTreeId({ ...params, isSys });
          });
        });
      },
    });
  }

  @action.bound
  onFilterShowElementsByName(name: string) {
    const params = cloneDeep(this.params);
    const isSys = isEqual(toJS(this.currentTreeNodeIndex), DefaultTreeNode()[0].index) ? 1 : 0;

    if (!params.category) {
      params.isSys = isSys;
    }

    params.name = name;
    this.inputSearch = name;
    this.params.name = name;
    this.getShowTemplatesByTreeId(params);
    let temps = [];

    temps = this.showTemps.filter((ele) => {
      if (ele.name.toLowerCase().includes(name.toLowerCase())) return true;
      return false;
    });

    this.showTemps = temps;
  }

  @action.bound
  onCheckChange(data: CompTempDataType, value: any) {
    let changed = false;
    const changedTemps = this.showTemps.map((ele) => {
      if (ele.id === data.id && ele.checked !== value) {
        changed = true;
        return {
          ...ele,
          checked: value,
        };
      }
      return ele;
    });
    if (changed) {
      this.showTemps = changedTemps;
    }
  }

  /**
   * 点击面包屑
   * @param index 面包屑下标
   */
  @action.bound
  onClickBreadCrumb(index: number) {
    const newIndex = this.currentTreeNodeIndex.slice(0, index + 1);
    const newName = this.currentTreeNodeName.slice(0, index + 1);
    this.currentTreeNodeIndex = newIndex;
    this.currentTreeNodeName = newName;
    if (newIndex && newIndex.length === 1) {
      this.setListItem([]);
    }

    this.getShowTemplatesByTreeId();
  }

  /**
   * 折叠标题
   */
  @action.bound
  onClickCollapseTitle(newIndex: string[], newName: string[]) {
    const isSys = isEqual(newIndex, DefaultTreeNode()[0].index) ? 1 : 0;
    const params = { isSys, pageNo: 1, ...this.params };

    if (!isEqual(toJS(this.currentTreeNodeIndex), newIndex)) {
      this.currentTreeNodeIndex = newIndex;
      this.currentTreeNodeName = newName;
      this.getShowTemplatesByTreeId(params);
    }
  }

  @action.bound
  setListItem(activeRowKey: ListData[]) {
    this.activeListItem = activeRowKey;
  }

  @action.bound
  addPageNo() {
    this.pageNo += 1;
  }

  @action.bound
  getCheckedEle() {
    const checkded: any[] = [];
    this.showTemps.map((ele) => {
      if (ele.checked) {
        checkded.push(ele);
      }
      return ele;
    });
    return checkded;
  }

  @action.bound
  showOpeBtns() {
    return this.getCheckedEle().length > 0;
  }

  @action.bound
  setFocusId(focusId: string) {
    this.focusId = focusId;
  }

  @action.bound
  onCategorySearch(text: React.ReactText) {
    this.cateSearch = text as string;
    this.treeData = this.getTreeNode();
  }

  /**
   * 编辑模板信息
   * @param newTempData
   */
  @action.bound
  onEditTempMsg(newTempData: EditTempData) {
    const {
      id, name, icon, category, nameLabelId = '',
    } = newTempData;

    const data = new FormData();
    data.append('id', id);
    data.append('name', name);
    data.append('fileId', icon);
    data.append('category', category);
    data.append('nameLabelId', nameLabelId);

    return ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/component/tmpl/update',
      method: 'POST',
      data,
      success: () => {
        message({
          type: 'success',
          content: getLabel('54117', '保存成功'),
        });
        const params = this.setParams();
        const isSys = isEqual(toJS(this.currentTreeNodeIndex), DefaultTreeNode()[0].index) ? 1 : 0;

        this.getShowTemplatesByTreeId({ ...params, isSys });
      },
    });
  }

  @action.bound
  onEditCategoryMsg(id: string, name: string, nameLabelId = '') {
    const data = new FormData();
    data.append('id', id);
    data.append('name', name);
    data.append('nameLabelId', nameLabelId);

    const treeData = cloneDeep(toJS(this.treeData));
    treeData[1].children.map((item) => {
      if (item.id === id) {
        item.name = name;
      }
      return item;
    });
    this.treeData = treeData;

    return ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/tmpl/category/update',
      method: 'POST',
      data,
      success: () => {
        message({
          type: 'success',
          content: getLabel('41892', '修改成功'),
        });
        this.getTempCategories(this.params).then(() => {
          runInAction(() => {
            this.treeData = this.getTreeNode();
            this.getShowTemplatesByTreeId();
            this.focusId = '';
            this.updateCurrentNodeName();
          });
        });
      },
    });
  }

  /**
   * 模板排序
   * @param sortBox
   */
  @action.bound
  onTempSortEnd(sortBox: number[]) {
    const [oldIndex, newIndex] = sortBox;

    if (oldIndex === newIndex) return;

    const srcId = this.showTemps[oldIndex]?.id;
    const targetId = this.showTemps[newIndex]?.id;

    const data = new FormData();
    data.append('srcId', srcId);
    data.append('targetId', targetId);

    return ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/component/tmpl/sort',
      method: 'POST',
      data,
      success: () => {
        message({
          type: 'success',
          content: getLabel('54026', '操作成功'),
        });
        const params = this.setParams();

        this.getShowTemplatesByTreeId(params);
      },
    });
  }

  /**
   * 分类排序
   * @param sortBox
   * @returns
   */
  @action.bound
  onCategorySortEnd(sortBox: number[]) {
    const [oldIndex, newIndex] = sortBox;

    if (oldIndex === newIndex) return;

    const customerCategories = this.treeData[1].children;

    const srcId = customerCategories[oldIndex]?.id;
    const targetId = customerCategories[newIndex]?.id;

    const data = new FormData();
    data.append('srcId', srcId);
    data.append('targetId', targetId);
    return ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/tmpl/category/sort',
      method: 'POST',
      data,
      success: () => {
        message({
          type: 'success',
          content: getLabel('54026', '操作成功'),
        });
        const params = this.setParams();

        this.getShowTemplatesByTreeId(params);
      },
    });
  }

  /**
   * 删除分类
   * @param id
   */
  @action.bound
  onDeleteCategory(id: string) {
    return ebdcoms.asyncExcu('ajax', {
      url: '/api/ebuilder/coms/tmpl/category/delete',
      method: 'POST',
      params: { id },
      success: () => {
        message({
          type: 'success',
          content: getLabel('54119', '删除成功'),
        });
        this.getTempCategories(this.params).then(() => {
          this.refreshData({
            source: SourceType.Delete,
          });
        });
      },
    });
  }

  @action.bound
  refreshData(refreshParams?: RefreshParams) {
    const { source = '' } = refreshParams || {};
    const [primaryCate, secondaryCate] = this.currentTreeNodeIndex;
    const isSelectedPrimary = !secondaryCate && primaryCate;
    // 创建时的刷新逻辑，可能只选择了一级分类，展示模板时按第一级进行展示
    if (source === SourceType.Create && isSelectedPrimary) {
      const targetCate = this.treeData?.find((item) => item.index[0] === primaryCate);
      if (targetCate) {
        const isSys = targetCate?.id === DefaultTreeNode()[0].id ? 1 : 0;
        /**
         * 按点击一级分类时的效果调用 getTemplates
         * - then 方法中执行的回调
         */
        this.getShowTemplatesByTreeId({ isSys, pageNo: 1, ...this.params }, () => {
          runInAction(() => {
            this.treeData = this.getTreeNode();
          });
        });
        return;
      }
    }
    return this.getTemplates(this.params).then(() => {
      runInAction(() => {
        if (source === SourceType.Delete) {
          const defaultIndex = isEmpty(toJS(this.treeData)[0].children) ? 1 : 0;
          let defaultTree = toJS(this.treeData[defaultIndex]);
          if (!isEmpty(defaultTree.children)) {
            defaultTree = defaultTree.children.shift()!;
          }
          this.currentTreeNodeIndex = defaultTree?.index;
          this.currentTreeNodeName = defaultTree?.fullname;
          this.setListItem([defaultTree]);
        }
        this.treeData = this.getTreeNode();
        this.getShowTemplatesByTreeId();
      });
    });
  }

  @action.bound
  reset() {
    this.showTemps = [];
    this.activeListItem = [];
    this.currentTreeNodeIndex = [];
    this.currentTreeNodeName = [];
    this.cateSearch = '';
  }

  @action.bound
  setParams() {
    const pageSize = this.params.pageSize * this.pageNo;
    const params = cloneDeep(this.params);

    params.pageSize = pageSize;
    params.pageNo = 1;

    return params;
  }

  @action.bound
  updateCurrentNodeName() {
    const tempcates: TreeNode[] = cloneDeep(DefaultTreeNode());
    if (isEmpty(toJS(this.currentTreeNodeIndex))) return;
    const oldTempIndex = toJS(this.currentTreeNodeIndex).pop();

    toJS(this.tempCategories).forEach((data) => {
      const { id, name, isSys } = data;
      const pData = isSys ? tempcates[0] : tempcates[1];

      if (isEqual(oldTempIndex, id)) {
        this.currentTreeNodeName = [pData.name, name];
      }
    });
  }

  @computed get isSystemCaretory() {
    return this.currentTreeNodeIndex.includes(DefaultTreeNode()[1].id);
  }

  @computed get isSearching() {
    return !isEmpty(this.inputSearch);
  }

  @computed get isDetailCategory() {
    return this.currentTreeNodeIndex.length > 1;
  }

  @computed get tempcategories() {
    const tempcates: TreeNode[] = cloneDeep(DefaultTreeNode());
    this.tempCategories.forEach((data) => {
      const { id, name, isSys } = data;
      const pData = isSys ? tempcates[0] : tempcates[1];

      pData.children.push({
        id,
        name,
        children: [],
        index: [pData.id, id],
        fullname: [pData.name, name],
        isSystem: !!isSys,
      });
    });
    return tempcates;
  }

  /** **************     导入导出组件模板     **************** */

  @observable exportSpinVisible: boolean = false;

  @observable uploadVisible: boolean = false;

  @observable uploadingVisible: boolean = false;

  @action
  setExportSpinVisible = (visible: boolean) => {
    this.exportSpinVisible = visible;
  };

  getTemplateAttach = (templateId: string) => ajax({
    url: '/api/ebuilder/coms/packing/comptmpl/export',
    params: { styleId: templateId, type: 'comptmpl' },
    method: 'GET',
    resultRootData: true,
  });

  @action
  exportComTpl = (templateId: string) => {
    this.getTemplateAttach(templateId).then((res: RequestResult) => {
      const { code, data } = res;
      if (code === 200) {
        runInAction(() => {
          this.setExportSpinVisible(false);
        });

        message({
          type: 'success',
          content: getLabel('114302', '导出模板成功'),
        });
        window.open(data);
      } else {
        message({ type: 'error', content: getLabel('114305', '导出模板失败') });
      }
    });
  };

  @action
  setUploadVisible = (visible: boolean) => {
    this.uploadVisible = visible;
  };

  @action
  setUploadingVisible = (visible: boolean) => {
    this.uploadingVisible = visible;
  };
}

const eleLibStore = new TempLibStore();
export default eleLibStore;
