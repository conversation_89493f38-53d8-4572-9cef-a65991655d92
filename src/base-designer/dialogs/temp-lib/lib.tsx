import React, { ComponentType, Suspense } from 'react';
import { TempLibProps } from './types';

const LazyTempLib = React.lazy(
  () => import(
    /* webpackChunkName: "de_temp_lib" */
    './index'
  ),
) as ComponentType<TempLibProps>;

const TempLib: React.FC<TempLibProps> = (props: TempLibProps) => (
  <Suspense weId={`${props.weId || ''}_g49qpo`} fallback={<div />}>
    <LazyTempLib weId={`${props.weId || ''}_lf8h1b`} {...props} />
  </Suspense>
);

export default TempLib;
