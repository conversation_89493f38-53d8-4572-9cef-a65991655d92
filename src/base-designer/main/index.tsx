import { Spin } from '@weapp/ui';
import { classnames } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { prefixCls } from '../../constants';
import { DesignerContext, DesignerMainProvider } from '../Context';
import { initCompDesignStores } from '../utils';
import { MainProps } from './types';

interface DesignMainProps extends React.Attributes {
  main?: MainProps;
  [key: string]: any;
}

@observer
export default class Main extends PureComponent<DesignMainProps> {
  static contextType = DesignerContext;

  constructor(props: DesignMainProps) {
    super(props);
    const { store, layoutDatas, internal } = props;

    initCompDesignStores({
      store,
      layoutDatas,
      internal,
    });
  }

  componentDidUpdate(prevProps: DesignMainProps) {
    const { store, layoutDatas, internal } = this.props;
    /**
     * 确保双端页面中，两端都能拿到store
     * - 避免接口返回较慢，执行初始化store时，只初始化了一个端的情况
     */
    if (layoutDatas?.length !== prevProps?.layoutDatas?.length) {
      initCompDesignStores({
        store,
        layoutDatas,
        internal,
      });
    }
  }

  render() {
    const { children, main, layoutDatas } = this.props;
    const { clientType, selectPage } = this.context;
    const { loading } = this.context.main;
    const hasLayoutDatas = !!layoutDatas && layoutDatas.length > 0;
    const classNameStr = classnames(`${prefixCls}-workspace`, {
      m: clientType === 'MOBILE',
    });

    if (loading || !hasLayoutDatas) {
      return (
        <main className={classNameStr}>
          <Spin
            className={`${prefixCls}-main-loading`}
            weId={`${this.props.weId || ''}_s7pj3l`}
            spinning
          />
        </main>
      );
    }

    return (
      <DesignerMainProvider weId={`${this.props.weId || ''}_wr05p2`} value={main}>
        <main id={main?.id} className={classNameStr} onClick={selectPage}>
          {children}
        </main>
      </DesignerMainProvider>
    );
  }
}
