import { AnyObj, FormInitAllDatas, IconNames } from '@weapp/ui';
import { ReactNode } from 'react';
import { IComData } from '../../core';
import { PageInfo } from '../../grid-designer/main/types';
import CompDesignStore from '../core/CompDesignStore';

export type MainProps = {
  toolbar?: ToolBarProps;
  id?: string;
  getWrapperContainer?: () => HTMLElement;
  [key: string]: any;
};

export type ActionType = 'delete';

export type ExtraAction = {
  /** 动作action name */
  id: string;
  /** 图标popover的提示标题 */
  title?: string;
  /** 动作图标 */
  icon: IconNames | ReactNode;
  /** 图标样式 */
  className?: string;
  /** 是否隐藏 */
  hidden?: boolean;
  /** 扩展动作回调 */
  onAction?: Function;
  /** icon的样式 */
  style?: any;
  /** 组件管理弹窗配置项 */
  options?: ExtraActionOpts;
  /** 无权保存、修改配置 */
  disabled?: boolean;
  customHide?: (com: IComData, store: CompDesignStore) => boolean;
  customAction?: (act: ExtraAction) => HTMLElement;
};

export type ToolBarProps = {
  /** 支持自定义扩展的动作 */
  extraActions?: ExtraAction[];
  /** 支持页面toolbar自定义名称 */
  pageName?: string;
  /** 无权保存、修改配置 */
  disabled?: boolean;
  actionsFilter?: (value: AnyObj) => boolean;
};

export type ExcelProps = {
  defaultHeight?: number;
  defaultWidth?: number;
  maxRowCount?: number;
  maxColCount?: number;
  defaultRowCount?: number;
  defaultColCount?: number;
  defaultOffset?: string;
  showExcelRibbonTabs?: string;
  rightMenuItems?: any;
  cellCustomRender?: (com: any) => {};
  monitorConfigKeys?: string[];
};

export type ExtraActionOpts = {
  /** 页面所属模块 */
  pageScope?: string;
  /** 页面信息 */
  pageInfo?: PageInfo;
  /** 自定义配置新增组件的配置 */
  onBeforeInitForm?: (allDatas: FormInitAllDatas) => FormInitAllDatas;
  onBeforeSave?: (params: AnyObj) => AnyObj;
};
