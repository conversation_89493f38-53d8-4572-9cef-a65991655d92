import {
  classnames, getLabel, getLang, isEqual, pick,
} from '@weapp/utils';
import { toJS } from 'mobx';
import { Provider } from 'mobx-react';
import React, { ReactNode } from 'react';
import { When } from 'react-if';
import { ErrorBoundary } from '../common';
import {
  cNLang,
  EVENT_BEFORE_CREATE_COM,
  EVENT_BEFORE_DELETE_COM,
  EVENT_MOVEIN_COM,
  prefixCls,
} from '../constants';
import { ClientType } from '../types';
import { DesignerProvider, InternalDesignerContextType, InternalDesignerProvider } from './Context';
import Header from './header';
import Hotkeys from './hotkeys';
import './index.less';
import LeftSide from './left-side';
import Main from './main';
import RightSide from './right-side';
import { pickedPropNames } from './store/utils';
import { BaseDesignerProps } from './types';
import { cachePageLayoutChanged } from './utils/cachePageLayoutChanged';
import getLocalization from './utils/getLocalization';
import withWrapper from './withWrapper';

class Designer extends React.PureComponent<BaseDesignerProps> {
  internalCtxValue: InternalDesignerContextType = {};

  listenerMap: Record<string, any> = {};

  constructor(props: BaseDesignerProps) {
    super(props);
    const { initLayoutDatas, pluginCenter } = props.store;
    pluginCenter?.invoke('onBeforeMount');

    const pageId = props.layoutDatas?.[0].pageId;
    if (pageId && window.BroadcastChannel) {
      // 初始化广播
      props.store.channel = new BroadcastChannel(`designer_channel_${pageId}`);
    }

    initLayoutDatas(props.layoutDatas);
    this.initMain();
    this.initInternalCxtValue();
  }

  componentDidMount() {
    const {
      store,
      servicePath,
      onSave,
      onBeforeCreate,
      onDidMount,
      onBeforeDelete,
      canMoveIn,
      isRegisterBeforeUnload = true,
    } = this.props;

    this.listenerMap = {
      save: onSave,
      [EVENT_BEFORE_CREATE_COM]: onBeforeCreate,
      [EVENT_BEFORE_DELETE_COM]: onBeforeDelete,
      [EVENT_MOVEIN_COM]: canMoveIn,
    };

    store.pluginCenter?.invoke('onDidMount');
    onDidMount?.(store);
    // 初始化设计器事件
    store.on(this.listenerMap);
    store.setServicePath(servicePath);

    // 绑定即将离开当前页面的事件
    if (isRegisterBeforeUnload) {
      window.addEventListener('beforeunload', this.handleBeforeUnload);
    }

    // 绑定页面关闭事件
    window.addEventListener('unload', this.handleUnload);
  }

  getSnapshotBeforeUpdate() {
    this.initInternalCxtValue();
  }

  componentDidUpdate(preProps: BaseDesignerProps) {
    const { layoutDatas, store, main } = this.props;
    const { layoutDatas: preLayoutDatas, main: preMain } = preProps;

    // 在没有实现自动更新设计器layoutDatas时才会走对比逻辑
    if (!store.autoUpdateLayoutDatas) {
      // 优化减小toJS操作和数组循环，降低isEqual的比较范围
      let dataChanged = layoutDatas?.length !== preLayoutDatas?.length;
      let thumbnailChanged = layoutDatas?.length !== preLayoutDatas?.length;
      layoutDatas?.forEach((layoutData, index) => {
        const preLayoutData = preLayoutDatas?.[index];
        if (
          !dataChanged
          && !isEqual(
            toJS(pick(layoutData, pickedPropNames)),
            toJS(pick(preLayoutData, pickedPropNames)),
          )
        ) {
          dataChanged = true;
        }
        if (
          !thumbnailChanged
          && !isEqual(toJS(pick(layoutData, 'thumbnail')), toJS(pick(preLayoutData, 'thumbnail')))
        ) {
          thumbnailChanged = true;
        }
      });

      /**
       * 页面版本还原以及切换页面都会判断layoutDatas或者当前布局内容是否改变来重新生成新的nodes触发更新
       */
      if (dataChanged) {
        store.initLayoutDatas(layoutDatas);
      } else if (thumbnailChanged && layoutDatas) {
        // 页面保存后更新最新的缩略图id
        store.setLayoutDatas(layoutDatas);
      }
    }
    if (!isEqual(main, preMain)) {
      this.initMain();
    }
  }

  componentWillUnmount() {
    this.props.store.off(this.listenerMap);
    this.props.store.pluginCenter?.invoke('destory');

    if (this.props.isRegisterBeforeUnload) {
      window.removeEventListener('beforeunload', this.handleBeforeUnload);
    }

    if (this.props.ignoreLayoutChanged) {
      cachePageLayoutChanged.delete(this.props.store.page.id);
    }

    window.removeEventListener('unload', this.handleUnload);

    // 关闭广播
    this.props.store.channel?.close();
  }

  initMain() {
    const { store, main } = this.props;
    store.main.setMainProps(main);
  }

  initInternalCxtValue() {
    const {
      resolver,
      panels,
      store,
      isLayoutChanged = false,
      localization,
      splitLongTask,
      pageStyleClassName,
      renderDragComponent,
      renderDesign,
      renderConfig,
      renderContent,
    } = this.props;

    this.internalCtxValue = Object.assign(this.internalCtxValue, {
      splitLongTask,
      resolver,
      panels,
      page: store?.page,
      isLayoutChanged,
      pageStyleClassName,
      localization: getLocalization(localization),
      renderDragComponent,
      renderDesign,
      renderConfig,
      renderContent,
    });
  }

  handleUnload = () => {
    // 关闭当前页面时，发送广播数据
    this.props.store.channel?.postMessage({
      layoutDatas: JSON.stringify(this.props.store.prevLayoutDatas),
      isSaved: true,
    });
  };

  handleBeforeUnload = (e: BeforeUnloadEvent) => {
    const { store, isLayoutChanged } = this.props;

    if (store.isLayoutChanged || isLayoutChanged) {
      e.preventDefault();
      e.returnValue = getLabel('80834', '布局有改动，离开此页面可能会丢失此部分改动内容');
    }
  };

  onClientChange = (client: ClientType) => {
    const { store, header } = this.props;

    store.changeClient(client);
    store.pluginCenter?.invoke('onClientChange', { args: [client] });
    header?.onClientChange?.(client);

    store.initSourceCode();
  };

  withProvider(nodes: ReactNode) {
    const { store, withProvider = (v) => v } = this.props;

    return (
      <ErrorBoundary weId={`${this.props.weId || ''}_obs0j7`}>
        {/* 兼容grid layout的mobx inject写法 */}
        <Provider weId={`${this.props.weId || ''}_wfmo1b`} designStore={store}>
          <DesignerProvider weId={`${this.props.weId || ''}_30goch`} value={store}>
            <InternalDesignerProvider
              weId={`${this.props.weId || ''}_l245j0`}
              value={this.internalCtxValue}
            >
              {withProvider(nodes)}
            </InternalDesignerProvider>
          </DesignerProvider>
        </Provider>
      </ErrorBoundary>
    );
  }

  render() {
    const {
      ignoreLayoutChanged,
      ls,
      rs,
      header = {},
      store,
      main,
      children,
      hotkeys = true,
      className,
      resolver,
      layoutDatas,
    } = this.props;
    const { singleClient, clientType } = store;
    const hasLayoutDatas = !!layoutDatas && layoutDatas.length > 0;
    const RightSideComp = resolver?.RightSide || RightSide;
    const userLanguage = getLang();
    const isChinese = userLanguage === cNLang;

    return this.withProvider(
      <div className={classnames(prefixCls, className)}>
        <Header
          weId={`${this.props.weId || ''}_61f5vr`}
          {...header}
          showOperations={hasLayoutDatas}
          showClient={!singleClient}
          clientType={clientType}
          ignoreLayoutChanged={ignoreLayoutChanged}
          onClientChange={this.onClientChange}
        />
        <main
          className={classnames(`${prefixCls}-main`, {
            [`${prefixCls}-main-narrower`]: !isChinese,
          })}
        >
          <LeftSide weId={`${this.props.weId || ''}_4fsbag`} {...ls} />
          <Main
            weId={`${this.props.weId || ''}_7maxdi`}
            main={main}
            layoutDatas={layoutDatas}
            store={store}
            internal={this.internalCtxValue}
          >
            {children}
          </Main>
          <RightSideComp weId={`${this.props.weId || ''}_6uqx2z`} {...rs} />
        </main>
        <When weId={`${this.props.weId || ''}_nby3ij`} condition={hotkeys}>
          <Hotkeys weId={`${this.props.weId || ''}_8t1k0z`} />
        </When>
      </div>,
    );
  }
}

export default withWrapper(Designer);
