import { AnyObj } from '@weapp/ui';
import React, {
  ComponentType, CSSProperties, ReactElement, ReactNode,
} from 'react';
import { ExternalEcodeProps } from '../common/sourcecode/types';
import { MoveInSourceType } from '../constants';
import { PreSubcomponentType } from '../grid-designer/core/stores/base/types';
import {
  IComData, IComDescription, ILayoutData, LayoutType,
} from '../types';
import { HeaderProps } from './header/types';
import { LeftSideProps } from './left-side/types';
import { ExtraAction, MainProps } from './main/types';
import { DesignerPluginClass } from './plugin-center/types';
import { RightSideProps } from './right-side/types';
import BaseDesignerStore from './store';
import { ToolBarLocalization } from './toolbar';

export * from './left-side/types';
export * from './right-side/types';

export type RenderDragComponent = (data: IComDescription, comItem: ReactNode) => ReactNode;
export type RenderDragComList = (
  data: IComDescription[] | undefined,
  comItem: ReactNode,
) => ReactNode;

export type RenderDesign = (design: ReactElement) => ReactElement;

export type RenderConfig = (config: ReactElement) => ReactElement;

type CanMoveout = (draggingCom: IComData, parentCom: IComData) => boolean | { error: string };

type MoveInOptions = {
  sourceType: MoveInSourceType;
};
export interface Localization {
  /** toolbar自定义配置 */
  toolbar?: ToolBarLocalization;
}

export interface DroppableProps<T = IComData> extends React.Attributes {
  /** 容器的唯一标志 */
  id: string;
  style?: CSSProperties;
  className?: string;
  droppable?: boolean;
  /** 是否作为独立的组件，独立的组件可以被选中和有统一配置 */
  standalone?: boolean;
  /** 工具栏左侧显示的名称 */
  displayName?: string;
  /** 允许拖入的最大子组件数量 */
  limit?: number;
  /** 当容器内没有子组件时，显示的空状态文案 */
  empty?: ReactNode;
  /** 预置子组件 */
  preSubcomponents?: PreSubcomponentType[];
  /** 子组件是否允许被放入容器的回调 */
  canMoveIn?: (
    target: T | IComDescription,
    source: T,
    options?: MoveInOptions,
  ) => boolean | { error: string } | void;
  /** 子组件放入后的回调  */
  onDrop?: (target: T, source: T) => void;
  /** 创建容器时的回调，返回容器组件的信息 */
  onCreate?: (com: T) => void;
  /** 容器删除时的回调，返回容器组件的信息 */
  onDelete?: (com: T) => void;
  /** 容器子组件拖出到设计区的回调，返回拖出的组件的信息 */
  onMoveOut?: (com: T) => void;
  /** 是否允许展示拖拽指示线 */
  shouldShowInicator?: (target: T, source: T) => boolean;
  /** 是否允许子组件拖出 */
  canMoveout?: boolean | CanMoveout;
  /** 自定义子组件props */
  childrenProps?: Record<string, any>;
  /** 组件加载完成 */
  onMount?: (el: HTMLElement) => void;
  /** 过滤子组件工具栏
   * 说明：通过Droppable组件传递filterChildComAction，只可过滤当前Droppable下面一级的子组件，不会作用到更深层的子组件。目前主要用于导航面板内置选项卡的场景。
   * 网格布局中，是将Droopable组件的filterChildComAction传递给Toolbar组件进行过滤。
   * 流式布局中，是在Toolbar的filter方法中，通过全局的nodes找到父节点，获取其props上的filterChildComAction使用，进行过滤。
   */
  filterChildComAction?: (action: ExtraAction, com: T) => boolean;
}

export interface ComDesignProps extends React.Attributes {
  comId: string;
}

export type ResolverType = {
  Droppable?: ComponentType<DroppableProps>;
  ElementPanel?: ComponentType<any>;
  DraggableIcon?: ComponentType<any>;
  DatasetPanel?: ComponentType<any>;
  FlattenComList?: ComponentType<any>;
  PreTempPanel?: ComponentType<any>;
  RightSide?: ComponentType<any>;
  ComDesign?: ComponentType<any>;
} & ExternalResolverType;

/** 设计器外部传入自定义渲染的组件 */
export type ExternalResolverType = {
  SourceCode?: ComponentType<ExternalEcodeProps>;
};

export interface BaseDesignerProps<T = BaseDesignerStore> {
  store: T;
  /** 左侧栏属性 */
  ls?: LeftSideProps;
  /** 右侧栏属性 */
  rs?: RightSideProps;
  /** 设计器头部属性 */
  header?: HeaderProps;
  /** 设计器主体属性 */
  main?: MainProps;
  /** 设计器自定义的panel，可以在rs,ls中的menu.panel中使用 */
  panels?: Record<string, React.ComponentType<any>>;
  /** 设计器布局类型，传入该类型可以跳过layoutDatas的非空判断，直接进入设计器的渲染 */
  layoutType?: LayoutType;
  /** 设计器布局信息 */
  layoutDatas?: ILayoutData[] | null;
  /** 后端微服务路径 */
  servicePath?: string;
  /** 设计器外部组件的布局是否改变 */
  isLayoutChanged?: boolean;
  /** 是否绑定即将离开当前页面的事件 */
  isRegisterBeforeUnload?: boolean;
  /** 忽略预览前的校验 */
  ignoreLayoutChanged?: boolean;
  /** 设计器本地化配置 */
  localization?: Localization;
  /** 设计器布局类名 */
  className?: string;
  /** 页面样式PageStyle上面的className */
  pageStyleClassName?: string;
  /** 是否使用默认的hotkeys */
  hotkeys?: boolean;
  /** 需要在Designer Provder和设计器容器之间自定义一些Provider */
  withProvider?: (nodes: ReactNode) => ReactNode;
  /** 自定义支持左侧栏组件为可拖拽组件 */
  renderDragComponent?: RenderDragComponent;
  /** 自定义Design */
  renderDesign?: RenderDesign;
  /** 自定义Config */
  renderConfig?: RenderConfig;
  /** 自定义渲染设计区域 */
  renderContent?: (content: ReactNode) => ReactNode;
  /** 自定义渲染设计器 */
  renderDesigner?: (content: ReactNode) => ReactNode;
  /** 自定义一些通用的全局组件，并在特定的地方使用 */
  resolver?: ResolverType;
  /** 保存时的回调 */
  onSave?: (data: ILayoutData[], otherParams?: AnyObj) => Promise<any>;
  /** 组件创建前的回调，主要用于对组件数据做些处理 */
  onBeforeCreate?: (com: IComData) => Promise<any> | void;
  /** 组件删除前的回调 */
  onBeforeDelete?: (com: IComData) => Promise<boolean> | void;
  /** 组件是否允许被放入页面的回调 */
  canMoveIn?: (com: IComData) => boolean | { error: string } | void;
  /** 组件didmount回调 */
  onDidMount?: (store: T) => void;
  /** 设计器插件 */
  plugins?: DesignerPluginClass[];
  /** 插件初始化配置,key必须得是插件名，val是对象，插件内部自己通过插件名去获取配置 */
  pluginOptions?: Record<string, any>;
  /**
   * 渲染不进行长任务拆分，自行处理性能问题
   * - splitLongTask为false时直接加载，默认为true
   */
  splitLongTask?: boolean;
}

export default {};
