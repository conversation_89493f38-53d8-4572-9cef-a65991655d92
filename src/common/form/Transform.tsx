import {
  FormDatas, FormLayoutProps, FormLayoutType, utils,
} from '@weapp/ui';
import { GroupProps } from '@weapp/ui/lib/components/form/types';
import { classnames, flatten, isUndefined } from '@weapp/utils';
import { prefixCls } from '../../constants';
import { transformItem } from './Resolver';
import { FormInitAllDatasEx, FormItemPropsEx } from './types';

type TransformOpts = {
  title?: boolean;
  footer?: boolean;
  fixedArea?: boolean;
  buildInItems?: FormItemPropsEx;
};

export const BUILDIN_GROUPID = 'build-in-group';

// 值为reactElement的数据传递给form会报错
const isReactElement = (value: any) => value?.$$typeof?.toString?.() === 'Symbol(react.element)';

export default class Transform {
  options: TransformOpts = {};

  items: FormItemPropsEx;

  layout: FormLayoutType[];

  groups: GroupProps[];

  buildInItems: FormItemPropsEx = {};

  customLayout: (layout: FormLayoutType[]) => FormLayoutType[];

  customLayoutByItems: (layout: FormLayoutType[]) => FormLayoutType[];

  /**
   * 是否有分组，
   * 用于判断layout是否添加groupId
   */
  hasGroups: boolean;

  /**
   * 内置字段， 标题等
   */
  buildInKeys: string[] = [];

  /** 作为表单datas上面的属性的key集合 */
  valueItemKeys: string[] = [];

  constructor(datas: FormInitAllDatasEx, options?: TransformOpts) {
    const {
      items,
      layout = [],
      groups = [],
      customLayout = (v) => v,
      customLayoutByItems = (v) => v,
    } = datas;

    this.customLayout = customLayout;
    this.customLayoutByItems = customLayoutByItems;
    this.hasGroups = !!groups.length;
    this.options = { ...this.options, ...options };
    this.buildInItems = this.options.buildInItems || {};
    this.buildInKeys = Object.keys(this.buildInItems);
    this.items = this.getItems(items);
    this.layout = this.getLayout(layout);
    this.groups = this.getGroups(groups);
    this.valueItemKeys = this.getValueItemKeys();

    this.removeLabelForItems();
  }

  /** 根据实际的items配置来获取formDatas */
  getData(data: FormDatas) {
    return Object.keys(this.items).reduce((_data: Record<string, any>, key: string) => {
      const item = this.items[key];

      if (item.itemType === 'SWITCH') {
        data[key] = isUndefined(data[key]) ? Boolean(item.defaultValue) : Boolean(data[key]);
      }

      if (item.valueItems) {
        item.valueItems.forEach((itemKey) => {
          if (!isReactElement(data[itemKey])) _data[itemKey] = data[itemKey];
        });
      } else if (!isReactElement(data[key])) _data[key] = data[key];

      return _data;
    }, {});
  }

  getItems(_items: FormItemPropsEx): FormItemPropsEx {
    const items: any = {};

    // 深拷贝，防止污染组件config里的初始数据
    _items = utils.deepClone(_items);

    this.buildInKeys.forEach((key) => {
      items[key] = utils.deepClone(this.buildInItems[key]);
    });

    Object.keys(_items).forEach((key: string) => {
      transformItem(_items[key]);
      delete _items[key].customRender;
    });

    return {
      ...items,
      ..._items,
    };
  }

  getFormLayout = (layout: FormLayoutType[]) => layout.map((l) => l.map((item) => {
    const [itemKey] = item.items;

    return { ...this.getLayoutItem(itemKey, item), items: item.items };
  }));

  getLayout(_layout: FormLayoutType[]) {
    if (_layout.length) {
      const buildInLayout = this.getBuildInLayout(_layout);
      const formLayout = [...buildInLayout, ...this.getFormLayout(_layout)];
      const customLayout = this.customLayout(formLayout);
      return customLayout;
    }
    return this.getLayoutByItems();
  }

  getGroups(groups: GroupProps[]): GroupProps[] {
    let newGroups = groups;
    const groupIds = flatten(this.layout).map((item) => item.groupId);

    // 去掉冗余和脏数据的分组，如字段中不存在该分组的字段
    newGroups = groups.filter((group) => groupIds.includes(group.id));

    if (!this.hasGroups || !this.buildInKeys.length) {
      return newGroups;
    }

    return [
      {
        id: BUILDIN_GROUPID,
        title: '',
        visible: true,
        custom: false,
      },
      ...newGroups,
    ];
  }

  /**
   * 获取内置的layout，如title,titleEnabled等
   */
  getBuildInLayout(_layout: FormLayoutType[]) {
    const layout: FormLayoutType[] = [];
    // 判断是否有对内置字段自定义layout，如果有则不初始化
    const hasLayout = (key: string) => _layout.find(([l]) => l.id === key);

    this.buildInKeys.forEach((key) => {
      if (!hasLayout(key)) {
        layout.push([
          this.getLayoutItem(key, {
            groupId: BUILDIN_GROUPID,
          }),
        ]);
      }
    });

    return layout;
  }

  /**
   * 当没有传入layout时，通过items获取layout
   */
  getLayoutByItems() {
    return Object.keys(this.items).reduce((layout: FormLayoutType[], itemKey: string) => {
      if (this.buildInKeys.includes(itemKey)) {
        layout.push([
          this.getLayoutItem(itemKey, {
            groupId: BUILDIN_GROUPID,
          }),
        ]);
      } else {
        layout.push([this.getLayoutItem(itemKey, this.items[itemKey])]);
      }
      // 通过items获取的layout也需要支持自定义
      return this.customLayoutByItems(layout);
    }, [] as FormLayoutType[]);
  }

  getLayoutItem(itemKey: string, _layoutItem: Partial<FormLayoutProps> = {}) {
    const item = this.items[itemKey];
    const {
      label,
      labelSpan = item.labelSpan,
      groupId,
      className = '',
      align,
      hide = false,
      id,
      disabled, // 公共组件加了判断，layoutItem disabled会无法渲染
      ...restLayout
    } = _layoutItem;
    const layoutItem: FormLayoutProps = {
      /** 默认helpTip 提示位置为label 后面 */
      helpTipPostion: 'afterLabel',
      ...restLayout,
      labelSpan,
      id: id || itemKey,
      items: [itemKey],
      hide, // 公共组件加了判断，不加该字段，组件无法显示
      label: label || item?.label || '',
      className: classnames(className, {
        [`${prefixCls}-config-item-right`]: (align || item?.align) === 'right',
      }),
    };

    if (this.hasGroups) {
      const isBuildInItem = this.buildInItems[itemKey];

      layoutItem.groupId = isBuildInItem ? BUILDIN_GROUPID : groupId || BUILDIN_GROUPID;
    }

    if (typeof layoutItem.labelSpan === 'undefined') {
      if (item.itemType === 'SWITCH') {
        layoutItem.labelSpan = 20;
      } else if (itemKey === 'title') {
        layoutItem.labelSpan = 7;
      } else {
        // 默认上下结构
        layoutItem.labelSpan = 24;
      }
    }

    return layoutItem;
  }

  /**
   * 在layout获取完item中的属性后，将item中的label字段移除
   */
  removeLabelForItems() {
    Object.keys(this.items).forEach((itemKey) => {
      const item = this.items[itemKey];

      // 只需要在layout中设置label即可
      // item中设置label会导致问题，如switch多一个label
      delete item.label;
    });
  }

  getValueItemKeys() {
    const keys: string[] = [];

    Object.keys(this.items).forEach((key) => {
      const item = this.items[key];
      keys.push(key);
      if (item.valueItems) {
        keys.push(...item.valueItems);
      }
    });

    return keys;
  }
}
