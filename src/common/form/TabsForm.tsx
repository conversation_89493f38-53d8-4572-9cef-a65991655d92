import {
  FormLayoutType, FormStore, Menu, MenuItemData,
} from '@weapp/ui';
import { classnames, classUseMemo } from '@weapp/utils';
import { observer } from 'mobx-react';
import { createRef, PureComponent } from 'react';
import { prefixCls } from '../../constants';
import { scrollDomToClient } from '../../flow-designer/store/utils';
import setTimeoutOnce from '../../utils/setTimeoutOnce';
import { rightMenuScroll, subcomponentClick } from './constants';
import Form from './Form';
import { FormInitAllDatasEx, TabsFormProps } from './types';
import { defaultOnBeforeInitConfig, getGroupId } from './utils';

interface TabsFormState {
  activeKey: string;
}

type SubcomponentClickData = {
  tabId?: string;
  groupId?: string;
};

const { MenuContent } = Menu;

@observer
export default class TabsForm extends PureComponent<TabsFormProps, TabsFormState> {
  scrollRef = createRef<HTMLDivElement>();

  anchorRef = createRef<any>();

  form: FormStore = this.props.store || new FormStore();

  formRef = createRef<any>();

  constructor(props: TabsFormProps) {
    super(props);

    this.state = {
      activeKey: '',
    };
  }

  componentDidMount() {
    const { events, com, onMount } = this.props;

    const tabs = this.getTabDatas();

    if (tabs?.length) {
      this.handleTabChange(tabs[0].id);
    }

    events?.on(subcomponentClick, com?.id, this.handleSubComClick);
    onMount?.({
      resetForm: this.resetForm,
    });
  }

  componentWillUnmount() {
    const { events, com } = this.props;
    events?.off(subcomponentClick, com?.id);
  }

  resetForm = (initialDatas?: FormInitAllDatasEx) => {
    const { initialDatas: propsInitDatas, data: propsData } = this.props;
    const _initialDatas = initialDatas || propsInitDatas;
    const { getGroups, groups } = _initialDatas;
    const data = _initialDatas.data || propsData || {};
    const initData: FormInitAllDatasEx = {
      ..._initialDatas,
      groups: getGroups ? getGroups(data) : groups,
      customLayout: this.customLayout as any,
      customLayoutByItems: this.customLayout,
    };
    this.formRef.current?.resetForm?.(initData);
  };

  handleSubComClick = (data: SubcomponentClickData) => {
    const { tabId, groupId } = data;
    if (tabId) {
      this.handleTabChange(tabId);
    }
    if (groupId) {
      this.handleAnchorChange(groupId);
    }
  };

  /** tab切换事件 */
  handleTabChange = (activeKey: string) => {
    const { events, com } = this.props;
    this.setState({ activeKey }, () => {
      // 需要手动刷新form组件的layout显隐藏
      this.resetForm();
    });
    events?.emit(rightMenuScroll, com?.id, activeKey);
  };

  customLayout = (layout: FormLayoutType[]) => {
    const { initialDatas, data: propsData, tabs } = this.props;
    const { activeKey } = this.state;
    const {
      customLayout,
      customLayoutByItems,
      getGroups,
      groups,
      layout: initLayout,
    } = initialDatas;

    const data = initialDatas.data || propsData || {};
    // 全部类型
    const allConfigTabKey = tabs?.find((i) => i.isAllConfig)?.id;

    // 表单中存在group数据则不再去额外获取
    const _groups = getGroups ? getGroups(data) : groups;

    const customLayoutFunc = initLayout?.length ? customLayout : customLayoutByItems;

    let sourceLayout = customLayoutFunc ? customLayoutFunc(layout || []) : layout;

    sourceLayout = allConfigTabKey === activeKey
      ? sourceLayout
      : sourceLayout?.filter((layoutItem: any) => {
        const { groupId } = layoutItem[0];
        const group: any = _groups?.find((item) => item.id === groupId);

        return group?.tabId === activeKey;
      }) || [];

    const formActiveKeys = _groups?.map((item) => item.id) || [];

    const { setActiveKey } = this.form;
    setActiveKey(formActiveKeys);

    return sourceLayout;
  };

  getTabDatas = () => {
    const {
      tabs,
      data,
      initialDatas,
      getTabs,
      onBeforeInitConfig = defaultOnBeforeInitConfig,
    } = this.props;
    const initDatas = onBeforeInitConfig(initialDatas, data);
    const config = initDatas.data || data || {};

    if (tabs || getTabs) {
      return typeof getTabs === 'function' ? getTabs(config, tabs) : tabs;
    }
    const tabsData: MenuItemData[] = [];

    const { groups } = this.form;

    groups?.forEach((groupItem) => {
      const { id, title } = groupItem;
      if (!title) {
        return;
      }
      tabsData.push({
        id,
        content: title,
      });
    });

    return tabsData;
  };

  handleAnchorChange = (groupId: string) => {
    // 等tab滚动后再进行锚点滚动
    setTimeoutOnce(() => {
      const { groups = [] } = this.props.initialDatas;
      const group = groups.find((g) => g.id === groupId);

      if (!group) return;
      const { tabId, id } = group as any;
      const herfId = getGroupId(tabId ? `${tabId}-${id}` : id);
      const groupEl = document.getElementById(herfId);

      if (!this.scrollRef.current || !groupEl) return;
      scrollDomToClient(this.scrollRef.current, groupEl);
    }, 300);
  };

  render() {
    const { activeKey } = this.state;
    const {
      className, store, onBeforeInitConfig, initialDatas, ...resProps
    } = this.props;

    const tabDatas = this.getTabDatas();

    const formCls = classnames(`${prefixCls}-form-tab-content`, className);

    const initData: FormInitAllDatasEx = classUseMemo(
      'initData',
      this,
      () => ({
        ...initialDatas,
        customLayout: this.customLayout as any,
        customLayoutByItems: this.customLayout,
      }),
      [initialDatas],
    );

    return (
      <div className={`${prefixCls}-form-tab-container`}>
        <Menu
          weId={`${this.props.weId || ''}_tsh6t9`}
          value={activeKey}
          onChange={this.handleTabChange}
          className={`${prefixCls}-form-tab`}
          overflowType="scroll"
          type="tab"
          data={tabDatas}
        />
        <MenuContent
          bindKey={`${this.props.weId || ''}_epkym0`}
          weId={`${this.props.weId || ''}_gl2tyb`}
        >
          <div className={`${prefixCls}-form-tab-scroll`} ref={this.scrollRef}>
            <Form
              {...resProps}
              store={this.form}
              ref={this.formRef}
              weId={`${this.props.weId || ''}_k9gmxn`}
              initialDatas={initData}
              className={formCls}
            />
          </div>
        </MenuContent>
      </div>
    );
  }
}
