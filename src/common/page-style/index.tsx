import { LayoutType } from '../../grid-render/common/constants';
import { StyleConfigAttrs, TerminalType } from '../../grid-render/types';
import Loadable from '../loadable';

export interface BackgroundMaskProps {
  pageBgConfig?: StyleConfigAttrs;
  clientType?: TerminalType;
  layoutType?: LayoutType;
}

export const BackgroundMask = Loadable({
  name: 'BackgroundMask',
  middleware: false,
  loader: () => import(
    /* webpackChunkName: "de_pagestyle_bgmask" */
    './BackGroundMask'
  ),
});

export default {};
