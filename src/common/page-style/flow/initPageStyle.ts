import { cloneDeep } from '@weapp/utils';
import { TerminalType } from '../../../grid-render/types';
import ebdcoms from '../../../utils/ebdcoms';
import { formatImgUrl, getMarginForHeight } from '../utils';
import { StyleConfigAttrs } from './types';

const getStyles = (styleConfig: StyleConfigAttrs, clientType?: TerminalType) => {
  const {
    width,
    margin: marginCfg,
    padding: paddingCfg,
    height,
    background: backgroundCfg,
  } = styleConfig;

  const padding = paddingCfg?.space && paddingCfg?.selectedId !== 'default'
    ? ebdcoms.excu('parseMPSpace', paddingCfg?.space, 'padding')
    : {};
  const margin = marginCfg?.space
    ? ebdcoms.excu('parseMPSpace', marginCfg?.space, 'margin')
    : { margin: 'auto' };

  const background = formatImgUrl(cloneDeep(backgroundCfg));

  const marginNum = getMarginForHeight(margin);
  const _height = height || (marginNum === 'auto' ? '100%' : `calc(100% - ${marginNum})`);

  const cssProperties = {
    ...styleConfig,
    width: width || (marginCfg?.space && 'auto'),
    ...padding,
    ...margin,
    ...formatImgUrl(background),
  };

  /**
   * 移动端没有高度配置入口，但是配置了Margin的话，要设置一下高度
   */
  if (clientType === 'PC' || (clientType === 'MOBILE' && !height && marginNum)) {
    cssProperties.height = _height;
  }

  return ebdcoms.excu('parseAttrToCss', cssProperties);
};

/** 初始化页面样式并返回页面class */
export function initPageStyle(
  styleConfig?: StyleConfigAttrs,
  pageId?: string,
  clsName?: string,
  clientType?: TerminalType,
) {
  const time = Date.now();
  const pageCssAttrs = getStyles(styleConfig || {}, clientType);

  ebdcoms.excu(
    'createCssText',
    clsName,
    pageCssAttrs,
    false,
    undefined,
    `#ebpage_${pageId}`,
    time,
    clsName,
    'ebd_page',
  );
}

export default {};
