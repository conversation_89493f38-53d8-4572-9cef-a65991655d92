import { Popover } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import React, { useCallback } from 'react';
import { prefixCls } from '../../../../constants';

interface ShowProps extends React.Attributes {
  value: string;
  onChange: (value: string) => void;
}

const showItems = [
  { id: 'group', name: get<PERSON>abe<PERSON>('54220', '分组') },
  { id: 'list', name: getLabel('118851', '排列') },
];

const Show: React.FC<ShowProps> = (props) => {
  const { value = 'list', onChange } = props;

  const onClick = useCallback(
    (show: string) => () => {
      onChange(show);
    },
    [],
  );

  return (
    <div className={`${prefixCls}-field-property-show`}>
      {showItems.map((item) => (
        <Popover
          key={item.id}
          weId={`${props.weId || ''}_dr6r8j@${item.id}`}
          popoverType="tooltip"
          placement="top"
          popup={item.name}
        >
          <span className={classnames({ selected: value === item.id })} onClick={onClick(item.id)}>
            {item.name}
          </span>
        </Popover>
      ))}
    </div>
  );
};

export default Show;
