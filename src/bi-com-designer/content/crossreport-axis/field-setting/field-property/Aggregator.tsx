import { Select, SelectValueType } from '@weapp/ui';
import { cloneDeep } from '@weapp/utils';
import React, { useMemo } from 'react';
import { getAggregatorOptions } from '../constants';

interface AggregatorProps extends React.Attributes {
  value: string;
  field: any;
  onChange: (value: SelectValueType) => void;
}

const Aggregator: React.FC<AggregatorProps> = (props) => {
  const { value, field, onChange } = props;
  const options = useMemo(() => {
    const _aggregatorOptions = cloneDeep(getAggregatorOptions());

    if (field.type !== 'Number' && field.type !== 'NumberComponent') {
      _aggregatorOptions.splice(0, 4);
    }

    return _aggregatorOptions;
  }, [field.type]);

  return (
    <Select weId={`${props.weId || ''}_ob2xcr`} value={value} data={options} onChange={onChange} />
  );
};

export default Aggregator;
