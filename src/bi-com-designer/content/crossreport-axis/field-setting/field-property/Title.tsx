import { CorsComponent } from '@weapp/ui';
import { inject, observer } from 'mobx-react';
import React, { ComponentType } from 'react';
import { DesignerStore } from '../../../../store';

interface TitleProps extends React.Attributes {
  value: string;
  onChange: (val: any) => void;
}

const Title: React.FC<TitleProps & { designStore: DesignerStore }> = (props) => {
  const { id } = props.designStore.page;

  return (
    <CorsComponent
      app="@weapp/ebdcoms"
      compName="LocaleEx"
      weId={`${props.weId || ''}_bxz1hn`}
      {...props}
      targetId={id}
      style={{ maxWidth: 'none' }}
    />
  );
};

export default inject('designStore')(observer(Title)) as unknown as ComponentType<TitleProps>;
