import { CorsComponent, IconNames } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { useCallback } from 'react';
import { prefixCls } from '../../../../constants';
import './index.less';

export const getAlignData = (): any[] => [
  { id: 'left', tip: getLabel('55677', '左对齐'), name: 'Icon-Align-left02' },
  { id: 'center', tip: getLabel('95473', '居中对齐') },
  { id: 'right', tip: getLabel('55679', '右对齐'), name: 'Icon-Align-right02' },
].map((item, index) => ({
  ...item,
  icon: (
      <CorsComponent
        app="@weapp/ebdcoms"
        compName="IconFont"
        title={item.tip}
        weId={`adfrrc@${index}`}
        name={item.name as IconNames}
        type={`text-align-${item.id}`}
      />
  ),
}));

export const AlignData = getAlignData();

interface AlignComProps extends React.Attributes {
  value: string;
  onChange: (val: string) => void;
}

const AlignCom: React.FC<AlignComProps> = (props) => {
  const { value, onChange } = props;

  const onClick = useCallback((val: string) => {
    onChange(val);
  }, []);

  return (
    <CorsComponent
      weId={`${props.weId || ''}_lzgltp`}
      className={`${prefixCls}-field-property-align`}
      app="@weapp/ebdcoms"
      compName="StyleButtonGroup"
      data={getAlignData()}
      value={value}
      onClick={onClick}
    />
  );
};

export default AlignCom;
