import { Select } from '@weapp/ui';
import React, { useMemo } from 'react';
import { getDateGroupOptions } from '../../utils';
import { dateGroupOptions, DateType } from '../constants';

interface DateGroupProps extends React.Attributes {
  field: any;
  value: string;
  onChange: (val: any) => void;
}

const DateGroup: React.FC<DateGroupProps> = (props) => {
  const { field, value, onChange } = props;

  const data = useMemo(() => {
    let options = dateGroupOptions;

    // 日期字段在数据库存为时间戳格式，日期分组去掉月份、日分组选项
    if (field.dbFieldType === 'STRING') {
      options = dateGroupOptions.filter((d: any) => ![DateType.MONTH, DateType.DAY].includes(d.id));
    }

    options = getDateGroupOptions(options, field);

    return options;
  }, [field]);

  return (
    <Select weId={`${props.weId || ''}_6bzq62`} data={data} value={value} onChange={onChange} />
  );
};

export default DateGroup;
