import { CorsComponent, Spin } from '@weapp/ui';
import { observer } from 'mobx-react';
import React, { useEffect, useMemo, useState } from 'react';
import { useDesigner } from '../../../../base-designer/hooks';
import ebdcoms from '../../../../utils/ebdcoms';
import { prefixCls } from '../../../constants';
import { DesignerStore } from '../../../store';
import { replaceStr } from '../utils';
import { useFieldSetContext } from './Context';
import './index.less';

interface ShowTransProps extends React.Attributes {
  value: any;
  field: any;
  onChange: (trans: any) => void;
}

const selectTypes = [
  'Select',
  'ImageRadioBox',
  'ImageCheckBox',
  'CheckBox',
  'RadioBox',
  'SelectMultiple',
];

const cacheSyncGetSelectOptions: Record<string, Promise<any>> = {};

export enum RuleTypeType {
  highlight = 'highlight', // 突出显示
  valueFormat = 'valueFormat', // 显示格式
  option = 'option', // 选项
  dataBar = 'dataBar', // 数据条
  iconSet = 'iconSet', // 图标集
  iconTrans = 'iconTrans', // 图标转换
  gradation = 'gradation', // 色阶
  tableFormat = 'tableFormat', // 表格格式
  preSuffix = 'preSuffix', // 前后缀
  cusHtml = 'cusHtml', // 自定义html
}

// const typeOptions = [
//   RuleTypeType.highlight,
//   RuleTypeType.valueFormat,
//   RuleTypeType.option,
//   RuleTypeType.dataBar,
//   RuleTypeType.iconSet,
//   RuleTypeType.iconTrans,
//   RuleTypeType.gradation,
//   RuleTypeType.cusHtml,
// ];

const ShowTrans: React.FC<ShowTransProps> = (props) => {
  const { field, value, onChange } = props;
  const { dataset } = useDesigner() as DesignerStore;
  const { cusDataSet } = useFieldSetContext() as Record<string, any>;
  const [loading, setLoading] = useState(selectTypes.includes(field.type));
  const [transOption, setTransOption] = useState([]);

  const fieldType = useMemo(() => {
    const _fieldType = field.compType || field.type;
    return _fieldType === 'ProgressBar' ? 'NumberComponent' : replaceStr(_fieldType) || 'string';
  }, [field.type, field.compType]);

  useEffect(() => {
    if (loading) {
      if (!cacheSyncGetSelectOptions[field.uid]) {
        /**
         * 页面二维报表和BI二维报表都会加载此组件，但页面二维报表不会提前在 designerStore 中
         * 注入 dataset，所以在引用组件时从外部传入 dataset
         */
        const _dataset = cusDataSet ?? dataset;
        cacheSyncGetSelectOptions[field.uid] = ebdcoms.asyncExcu('ajax', {
          url: '/api/ebuilder/common/ds/options',
          method: 'get',
          ebBusinessId: field.uid,
          params: {
            fieldName: field.id,
            objId: field.objId,
            sourceType: _dataset.sourceType,
            groupId: _dataset.groupId,
          },
        });
      }

      cacheSyncGetSelectOptions[field.uid].then((result: any) => {
        const _transOption = result.map((item: any) => ({ id: item.id, content: item.name }));

        setTransOption(_transOption);
        setLoading(false);
      });
    }
  }, []);

  if (loading) {
    return (
      <Spin
        weId={`${props.weId || ''}_chf25w`}
        spinning
        className={`${prefixCls}-filed-showtrans-loading`}
      />
    );
  }

  return (
    <CorsComponent
      weId={`${props.weId || ''}_51yeij`}
      app="@weapp/edc"
      compName="ShowTrans"
      value={value}
      ruleSize={5}
      onChange={onChange}
      optionFormatTypes={transOption}
      // typeOptions 优先级高于 fieldType
      // typeOptions={typeOptions}
      // 通过 fieldType 显示转换组件可以判断支持哪些转换规则
      fieldType={fieldType}
    />
  );
};

export default observer(ShowTrans);
