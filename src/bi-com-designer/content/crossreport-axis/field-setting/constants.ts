import { getLabel } from '@weapp/utils';
import { Aggregator, OrderType as _OrderType } from '../../field-list/constants';

const {
  COUNT, SUM, AVG, MAX, MIN, DISTINCTCOUNT,
} = Aggregator;

const { DEFAULT, DESC, ASC } = _OrderType;

export enum DateType {
  YEAR = '1', // 年份
  YEARMOMTH = '2', // 年月
  MONTH = '3', // 月份
  YEARMONTHDAY = '4', // 年月日
  DAY = '6', // 日
  NONE = '5', // 不设置
}

export const getDateGroupOptions = () => [
  { id: DateType.YEAR, content: getLabel('56060', '年份') },
  { id: DateType.YEARMOMTH, content: getLabel('243459', '年月') },
  { id: DateType.YEARMONTHDAY, content: getLabel('119031', '年月日') },
  { id: DateType.NONE, content: getLabel('252703', '不设置') },
];

export const dateGroupOptions = getDateGroupOptions();

export enum AxisFieldSettingMenuType {
  ATTR = 'attr',
  TRANS = 'trans',
  FILTER = 'filter',
}

export const getAxisFieldSettingMenus = () => [
  { id: 'attr', content: getLabel('54502', '属性') },
  { id: 'trans', content: getLabel('109581', '显示转换') },
  { id: 'filter', content: getLabel('252704', '结果筛选') },
];

export const axisFieldSettingMenus = getAxisFieldSettingMenus();

export const getAggregatorOptions = () => [
  {
    id: SUM,
    content: getLabel('55438', '总和'),
  },
  {
    id: AVG,
    content: getLabel('55439', '平均值'),
  },
  {
    id: MAX,
    content: getLabel('55440', '最大值'),
  },
  {
    id: MIN,
    content: getLabel('55441', '最小值'),
  },
  {
    id: COUNT,
    content: getLabel('55442', '计数'),
  },
  {
    id: DISTINCTCOUNT,
    content: getLabel('99288', '计数(去重)'),
  },
];

export const aggregatorOptions = getAggregatorOptions();

export const getOrderTypeOptions = () => [
  { id: DEFAULT, content: getLabel('55466', '默认排序') },
  { id: DESC, content: getLabel('53956', '降序') },
  { id: ASC, content: getLabel('53955', '升序') },
];

export const orderTypeOptions = getOrderTypeOptions();

export const getCountOptions = () => [
  { id: 'sum', content: getLabel('56277', '求和') },
  { id: 'count', content: getLabel('55442', '计数') },
  { id: 'max', content: getLabel('55440', '最大值') },
  { id: 'min', content: getLabel('55441', '最小值') },
  { id: 'avg', content: getLabel('55439', '平均值') },
];

export const countOptions = getCountOptions();

export enum FieldFilterType {
  BEFORE = 'Before', // 前多少个
  LAST = 'Last', // 后多少个
  ODD = 'Odd', // 奇数
  EVEN = 'Even', // 偶数
  DESIGNATES = 'Designates', // 特定
}

export default {};
