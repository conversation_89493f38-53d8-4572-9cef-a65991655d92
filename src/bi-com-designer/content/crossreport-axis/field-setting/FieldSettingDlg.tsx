import { <PERSON><PERSON>, <PERSON><PERSON>, FormDatas } from '@weapp/ui';
import { getLabel, memoize } from '@weapp/utils';
import { toJS } from 'mobx';
import React, { PureComponent } from 'react';
import { dlgIconName } from '../../../../constants';
import { AxisType } from '../constants';
import { axisFieldSettingMenus, AxisFieldSettingMenuType } from './constants';
import FieldSettingContent from './Content';
import { FieldSetProvider } from './Context';
import { FieldSettingDlgProps } from './types';

interface FieldSettingDlgStates {
  menuType: AxisFieldSettingMenuType;
  field: any;
}

export const getAxisTypeName = (axisType: keyof typeof AxisType) => {
  let name = getLabel('252697', '行维度');

  switch (axisType) {
    case 'verticalDimensions':
      name = getLabel('252698', '列维度');
      break;
    case 'measures':
      name = getLabel('233086', '度量项');
      break;
    case 'conditionItems':
      name = getLabel('252681', '筛选项');
      break;
    default:
      name = getLabel('252697', '行维度');
      break;
  }

  return name;
};

export default class FieldSettingDlg extends PureComponent<
  FieldSettingDlgProps,
  FieldSettingDlgStates
> {
  state = { menuType: AxisFieldSettingMenuType.ATTR, field: toJS(this.props.field) };

  fieldSettingContentRef = React.createRef<{
    validate:() => { validate: boolean; message?: string; menuType: AxisFieldSettingMenuType };
  }>();

  getAxisFieldSettingMenus = memoize((axisType: keyof typeof AxisType) => (axisType === 'measures'
    ? axisFieldSettingMenus.filter((menu) => menu.id !== 'filter')
    : axisFieldSettingMenus));

  onMenuChange = (value: string) => {
    this.setState({ menuType: value as AxisFieldSettingMenuType });
  };

  onChange = (value?: FormDatas) => {
    const { field } = this.state;

    this.setState({ field: { ...field, ...value } });
  };

  onOk = () => {
    const { onOk } = this.props;
    const { validate, message, menuType } = this.fieldSettingContentRef.current?.validate?.() || {
      validate: true,
    };

    if (!validate) {
      Dialog.message({ type: 'error', content: message });

      this.onMenuChange(menuType!);
    } else {
      onOk(this.state.field);
    }
  };

  render() {
    const {
      visible, axisType, onClose, dialogTitle, cusDataSet,
    } = this.props;
    const { menuType, field } = this.state;
    const title = dialogTitle || `${getAxisTypeName(axisType)}${getLabel('79713', '字段设置')}`;
    const _axisFieldSettingMenus = this.getAxisFieldSettingMenus(axisType);

    return (
      <Dialog
        weId={`${this.props.weId || ''}_9hxz1y`}
        visible={visible}
        title={title}
        width={550}
        closable
        destroyOnClose
        isStopPropagation={false}
        icon={dlgIconName}
        menus={_axisFieldSettingMenus}
        menuValue={menuType}
        onMenuChange={this.onMenuChange}
        onClose={onClose}
        rightCol={8}
        leftCol={16}
        footer={[
          <Button
            weId={`${this.props.weId || ''}_ti6kr6`}
            key="submit"
            type="primary"
            onClick={this.onOk}
          >
            {getLabel('40565', '确定')}
          </Button>,
          <Button
            weId={`${this.props.weId || ''}_ti6kr6`}
            key="close"
            type="default"
            onClick={onClose}
          >
            {getLabel('53937', '取消')}
          </Button>,
        ]}
      >
        <FieldSetProvider
          weId={`${this.props.weId || ''}_smuy65`}
          value={{
            cusDataSet,
          }}
        >
          <FieldSettingContent
            weId={`${this.props.weId || ''}_7nixlb`}
            ref={this.fieldSettingContentRef}
            axisType={axisType}
            field={field}
            menuType={menuType}
            onChange={this.onChange}
          />
        </FieldSetProvider>
      </Dialog>
    );
  }
}
