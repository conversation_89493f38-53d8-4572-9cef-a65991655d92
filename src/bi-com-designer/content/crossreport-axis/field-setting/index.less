@import '../../../../style/prefix.less';

.@{bi-prefix}-fieldsetting-fieldfilter {
  height: 245px;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border: 1px solid var(--border-color);

  & > span {
    text-align: right;
    color: var(--primary);
    font-size: var(--font-size-12);
    margin: 10px 12px 0px 12px;
  }

  & > div {
    flex: 1;
    overflow: auto;
  }

  &-item {
    width: 100%;
    padding: 10px;

    .ui-select {
      width: 100%;
    }

    &-icon {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-self: center;
    }

    &-value {
      display: flex;
      align-items: center;

      & > span {
        margin-left: 4px;
        color: var(--secondary-fc);
        cursor: pointer;
      }
    }

    &-require {
      color: rgb(255, 77, 79);
    }
  }
}

.@{bi-prefix}-filed-showtrans-loading {
  height: 245px;
  display: flex;
  align-items: center;
  justify-content: center;
}
