import {
  Button, CorsComponent, Icon, Input, Layout, Select,
} from '@weapp/ui';
import { getLabel, isNumber, isString } from '@weapp/utils';
import React, { ReactText, useCallback, useState } from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { UUID } from '../../../../utils';
import { prefixCls } from '../../../constants';
import { FieldFilterType } from './constants';
import './index.less';

const { Row, Col } = Layout;
const { InputNumber } = Input;

export type FieldFilterVal = {
  id: string;
  currentField: string;
  value: ReactText;
};

interface FieldFilterProps extends React.Attributes {
  value: FieldFilterVal[];
  onChange: (value: FieldFilterVal[]) => void;
}

interface FieldFilterItemProps extends React.Attributes {
  value: FieldFilterVal;
  onChange: (value: FieldFilterVal) => void;
  onDelete: () => void;
}

const fieldFilterOpts = [
  { id: FieldFilterType.BEFORE, content: getLabel('252690', '前多少个') },
  { id: FieldFilterType.LAST, content: getLabel('252691', '后多少个') },
  { id: FieldFilterType.EVEN, content: getLabel('252692', '偶数') },
  { id: FieldFilterType.ODD, content: getLabel('252693', '奇数') },
  { id: FieldFilterType.DESIGNATES, content: getLabel('252694', '特定') },
];

const FieldFilterItem: React.FC<FieldFilterItemProps> = (props) => {
  const { value, onChange, onDelete } = props;
  const [fieldFilterVal, setFieldFilterVal] = useState<FieldFilterVal>({ ...value });

  const onFieldFilterChange = (key: string) => (_value: any) => {
    const _fieldFilterVal = { ...fieldFilterVal, [key]: _value };

    if (
      key === 'currentField'
      && (_value === FieldFilterType.BEFORE || _value === FieldFilterType.LAST)
    ) {
      _fieldFilterVal.value = isNumber(_fieldFilterVal.value) ? Number(_fieldFilterVal.value) : '';
    }

    setFieldFilterVal(_fieldFilterVal);
    onChange(_fieldFilterVal);
  };

  return (
    <Row
      weId={`${props.weId || ''}_j75rqp`}
      className={`${prefixCls}-fieldsetting-fieldfilter-item`}
      gutter={10}
    >
      <Col weId={`${props.weId || ''}_pau16v`} span={8}>
        <Select
          weId={`${props.weId || ''}_yhwwwh`}
          data={fieldFilterOpts}
          value={fieldFilterVal.currentField}
          onChange={onFieldFilterChange('currentField')}
        />
      </Col>
      <When
        weId={`${props.weId || ''}_506hrz`}
        condition={
          fieldFilterVal.currentField !== FieldFilterType.ODD
          && fieldFilterVal.currentField !== FieldFilterType.EVEN
        }
      >
        <Col
          weId={`${props.weId || ''}_dca1uu`}
          span={11}
          className={`${prefixCls}-fieldsetting-fieldfilter-item-value`}
        >
          <If
            weId={`${props.weId || ''}_dcs6ut`}
            condition={fieldFilterVal.currentField === FieldFilterType.DESIGNATES}
          >
            <Then weId={`${props.weId || ''}_gyj419`}>
              <Input
                weId={`${props.weId || ''}_gdxnzm`}
                value={fieldFilterVal.value}
                onChange={onFieldFilterChange('value')}
              />
              <When
                weId={`${props.weId || ''}_jnco67`}
                condition={isString(fieldFilterVal.value) && !fieldFilterVal.value}
              >
                <Icon
                  weId={`${props.weId || ''}_pb86ny`}
                  name="Icon-Required"
                  size="xxs"
                  className={`${prefixCls}-fieldsetting-fieldfilter-item-require`}
                />
              </When>

              <CorsComponent
                weId={`${props.weId || ''}_kwk44s`}
                app="@weapp/ebdcoms"
                compName="IconFont"
                name="Icon-help02-o"
                title={getLabel('252695', '特定筛选格式为：1,3-10,12，从1开始计数。')}
              />
            </Then>
            <Else weId={`${props.weId || ''}_rz5iqr`}>
              <InputNumber
                weId={`${props.weId || ''}_v06luc`}
                step={1}
                min={1}
                defaultValue=""
                value={fieldFilterVal.value || ''}
                onChange={onFieldFilterChange('value')}
              />

              <When
                weId={`${props.weId || ''}_jnco67`}
                condition={isString(fieldFilterVal.value) && !fieldFilterVal.value}
              >
                <Icon
                  weId={`${props.weId || ''}_pb86ny`}
                  name="Icon-Required"
                  size="xxs"
                  className={`${prefixCls}-fieldsetting-fieldfilter-item-require`}
                />
              </When>
            </Else>
          </If>
        </Col>
      </When>

      <Col
        weId={`${props.weId || ''}_c3j3fb`}
        span={2}
        className={`${prefixCls}-fieldsetting-fieldfilter-item-icon`}
      >
        <CorsComponent
          weId={`${props.weId || ''}_en4d6m`}
          app="@weapp/ebdcoms"
          compName="IconFont"
          name="Icon-delete04"
          title={getLabel('53951', '删除')}
          placement="top"
          onClick={onDelete}
        />
      </Col>
    </Row>
  );
};

const FieldFilter: React.FC<FieldFilterProps> = (props: FieldFilterProps) => {
  // eslint-disable-next-line max-len
  const { value = [], onChange } = props;

  const onFieldFilterItemChange = (index: number) => (v: FieldFilterVal) => {
    value.splice(index, 1, v);

    onChange(value);
  };

  const onFieldFilterItemDelete = (index: number) => () => {
    value.splice(index, 1);

    onChange(value);
  };

  const onFieldFilterItemAdd = useCallback(() => {
    const defFieldFilterVal = { id: UUID(), currentField: FieldFilterType.BEFORE, value: '' };

    value.push(defFieldFilterVal);

    onChange(value);
  }, [value]);

  return (
    <div className={`${prefixCls}-fieldsetting-fieldfilter`}>
      <span>
        <Button weId={`${props.weId || ''}_6xn73p`} type="link" onClick={onFieldFilterItemAdd}>
          + {getLabel('56776', '添加条件')}
        </Button>
      </span>
      <div>
        <If weId={`${props.weId || ''}_d8u5f4`} condition={!value.length}>
          <Then weId={`${props.weId || ''}_e70wg1`}>
            <CorsComponent
              weId={`${props.weId || ''}_42uj97`}
              app="@weapp/ebdcoms"
              compName="Empty"
              type="noData"
              description={getLabel('252696', '暂无结果筛选')}
            />
          </Then>
          <Else weId={`${props.weId || ''}_idhunn`}>
            {value.map((v: FieldFilterVal, index: number) => (
              <FieldFilterItem
                weId={`${props.weId || ''}_z27u5q@${v.id}`}
                key={v.id}
                value={v}
                onChange={onFieldFilterItemChange(index)}
                onDelete={onFieldFilterItemDelete(index)}
              />
            ))}
          </Else>
        </If>
      </div>
    </div>
  );
};

export default FieldFilter;
