import {
  Form, FormDatas, FormStore, FormSwitchProps,
} from '@weapp/ui';
import { classnames, getLabel, isString } from '@weapp/utils';
import React, {
  useCallback, useEffect, useImperativeHandle, useMemo,
} from 'react';
import { Case, Default, Switch } from 'react-if';
import { FieldType, prefixCls } from '../../../constants';
import { AxisType } from '../constants';
import { AxisFieldSettingMenuType, FieldFilterType, getOrderTypeOptions } from './constants';
import {
  Aggregator, Align, DateGroup, Show, Title,
} from './field-property';
import FieldFilter, { FieldFilterVal } from './FieldFilter';
import ShowTrans from './ShowTrans';

interface FieldSettingContentProps extends React.Attributes {
  axisType: keyof typeof AxisType;
  menuType: AxisFieldSettingMenuType;
  field: any;
  onChange: (value?: FormDatas) => void;
}

const dimensionItems = {
  showName: {
    itemType: 'CUSTOM',
    label: getLabel('56091', '显示名称'),
    Com: Title,
  },
  show: {
    itemType: 'CUSTOM',
    label: getLabel('83248', '显示方式'),
    Com: Show,
  },
  dateGroup: {
    itemType: 'CUSTOM',
    label: getLabel('70478', '日期分组'),
    Com: DateGroup,
  },
  orderType: {
    itemType: 'SELECT',
    label: getLabel('181621', '排序方式'),
    data: getOrderTypeOptions(),
  },
  align: {
    id: 'align',
    itemType: 'CUSTOM',
    label: getLabel('102295', '对齐方式'),
    Com: Align,
  },
  width: {
    itemType: 'INPUTNUMBER',
    suffix: 'px',
    label: getLabel('119288', '自定义宽度'),
  },
};

const measureItems = {
  showName: {
    itemType: 'CUSTOM',
    label: getLabel('56091', '显示名称'),
    Com: Title,
  },
  aggregator: {
    itemType: 'CUSTOM',
    label: getLabel('149194', '汇总方式'),
    Com: Aggregator,
  },
  orderType: {
    itemType: 'SELECT',
    label: getLabel('181621', '排序方式'),
    data: getOrderTypeOptions(),
  },
  align: {
    id: 'align',
    itemType: 'CUSTOM',
    label: getLabel('102295', '对齐方式'),
    Com: Align,
  },
  width: {
    itemType: 'INPUTNUMBER',
    suffix: 'px',
    label: getLabel('119288', '自定义宽度'),
  },
};

const FieldSettingContent = React.forwardRef((props: FieldSettingContentProps, ref: any) => {
  const {
    field, menuType, axisType, onChange,
  } = props;

  const form = useMemo(() => new FormStore(), []);

  const getFieldSettingFormInitDatas: any = () => {
    const items = axisType === 'measures' ? measureItems : dimensionItems;

    const itemKeys = Object.keys(items);

    // 年月日不需要再设置 日期分组
    if (
      (['verticalDimensions', 'rowDimensions'].includes(axisType)
        && field.type !== FieldType.FDate
        && field.type !== FieldType.CDate)
      || ((field.type === FieldType.CDate || field.type === FieldType.FDate) && field.dateParentId)
    ) {
      itemKeys.splice(2, 1);
    }

    const layout = itemKeys.map((itemKey: string) => [
      {
        id: itemKey,
        items: [itemKey],
        label: (items as any)[itemKey].label,
        labelSpan: 6,
        hide: false,
      },
    ]);

    return { items, layout, groups: [] };
  };

  const initFormDatas = useMemo(() => getFieldSettingFormInitDatas(), []);

  useEffect(() => {
    form.initForm({ ...initFormDatas, data: field });
  }, []);

  const onShowTransChange = useCallback((value: any) => {
    onChange({ trans: value });
  }, []);

  const onFieldFilterChange = useCallback((value: any) => {
    onChange({ filter: value });
  }, []);

  const customRenderFormSwitch = useCallback(
    (_id: string, _props: FormSwitchProps) => {
      const comProps = (_props as any).props;
      const CustomCom = initFormDatas.items[_id].Com;

      return <CustomCom weId={`${props.weId || ''}_4q8u8a`} {...comProps} field={field} />;
    },
    [initFormDatas],
  );

  // 字段属性校验
  const validate = useCallback(() => {
    const { filter = [] } = field;

    const fieldFiltersValidate = () => {
      const hasNoVerbItem = filter.find(
        (v: FieldFilterVal) => v.currentField !== FieldFilterType.EVEN
          && v.currentField !== FieldFilterType.ODD
          && isString(v.value)
          && !v.value,
      );

      return {
        validate: !hasNoVerbItem,
        message: getLabel('252687', '筛选结果必填项未填写(内容均为必填项)'),
      };
    };

    const { validate: _fieldFiltersValidate, message } = fieldFiltersValidate();

    if (!_fieldFiltersValidate) {
      return { validate: false, message, menuType: AxisFieldSettingMenuType.FILTER };
    }

    return { validate: true };
  }, [field]);

  useImperativeHandle(ref, () => ({
    validate,
  }));

  return (
    <div>
      <Switch weId={`${props.weId || ''}_gmduho`}>
        <Case
          weId={`${props.weId || ''}_90p3oq`}
          condition={menuType === AxisFieldSettingMenuType.TRANS}
        >
          <ShowTrans
            weId={`${props.weId || ''}_51yeij`}
            value={field.trans}
            field={field}
            onChange={onShowTransChange}
          />
        </Case>
        <Case
          weId={`${props.weId || ''}_62e6dg`}
          condition={menuType === AxisFieldSettingMenuType.FILTER}
        >
          <FieldFilter
            weId={`${props.weId || ''}_9ckugc`}
            value={field.filter}
            onChange={onFieldFilterChange}
          />
        </Case>
        <Default weId={`${props.weId || ''}_9n4hw5`}>
          <Form
            weId={`${props.weId || ''}_zmfva1`}
            store={form}
            customRenderFormSwitch={customRenderFormSwitch}
            onChange={onChange}
            className={classnames(`${prefixCls}-fieldsetting-atrr-form`, {
              hide: menuType === AxisFieldSettingMenuType.ATTR,
            })}
          />
        </Default>
      </Switch>
    </div>
  );
});

export default FieldSettingContent;
