import { CorsComponent, Icon } from '@weapp/ui';
import React, { PureComponent } from 'react';
import { Else, If, Then } from 'react-if';
import { prefixCls } from '../../constants';
import { getLocaleValue } from '../field-list/utils';
import { AxisType } from './constants';
import { FieldSettingDlg } from './field-setting';
import { FilterFieldSettingDlg } from './filter-field-setting';

interface FieldItemProps extends React.Attributes {
  index: number;
  field: any;
  type: keyof typeof AxisType;
  onChange?: (field: any) => void;
  onRemove?: () => void;
}

interface FieldItemStates {
  fieldSettingVisible: boolean;
  filterFieldSettingVisible: boolean;
}

export default class FieldItem extends PureComponent<FieldItemProps, FieldItemStates> {
  constructor(props: FieldItemProps) {
    super(props);

    this.state = { fieldSettingVisible: false, filterFieldSettingVisible: false };
  }

  onToggleSettingDlgVisible = () => {
    const { type } = this.props;
    const visibleKey = type === 'conditionItems' ? 'filterFieldSettingVisible' : 'fieldSettingVisible';
    const visible = this.state[visibleKey];

    this.setState({ [visibleKey]: !visible } as unknown as FieldItemStates);
  };

  onFieldSettingChange = (field: any) => {
    const { type, onChange } = this.props;

    onChange?.(field);
    if (type !== AxisType.measures) {
      this.onToggleSettingDlgVisible();
    }
  };

  render() {
    const { field, type, onRemove } = this.props;
    const { fieldSettingVisible, filterFieldSettingVisible } = this.state;
    const { text, showName } = field;

    return (
      <div className={`${prefixCls}-crossreoprt-axis-fielditem`}>
        <span title={getLocaleValue(showName) || text}>{getLocaleValue(showName) || text}</span>
        <If weId={`${this.props.weId || ''}_gjl3re`} condition={type === AxisType.measures}>
          <Then weId={`${this.props.weId || ''}_3nbr5q`}>
            <CorsComponent
              weId={`${this.props.weId || ''}_kv4x3d`}
              app="@weapp/analyze"
              compName="CrossReportConfigCom"
              className={`${prefixCls}-crossreoprt-axis-fielditem-measure`}
              type={type}
              field={field}
              iconName="Icon-Basic-settings02"
              onOk={this.onFieldSettingChange}
            />
          </Then>
          <Else weId={`${this.props.weId || ''}_heocg5`}>
            <Icon
              weId={`${this.props.weId || ''}_snkmat`}
              name="Icon-Basic-settings02"
              className={`${prefixCls}-crossreoprt-axis-fielditem-setting`}
              size="s"
              onClick={this.onToggleSettingDlgVisible}
            />
          </Else>
        </If>
        <Icon
          weId={`${this.props.weId || ''}_f048ak`}
          name="Icon-solid"
          className={`${prefixCls}-crossreoprt-axis-fielditem-delete`}
          size="s"
          onClick={onRemove}
        />
        {fieldSettingVisible ? (
          <FieldSettingDlg
            weId={`${this.props.weId || ''}_qmofvv`}
            field={field}
            axisType={type}
            visible={fieldSettingVisible}
            onClose={this.onToggleSettingDlgVisible}
            onOk={this.onFieldSettingChange}
          />
        ) : null}
        {filterFieldSettingVisible ? (
          <FilterFieldSettingDlg
            weId={`${this.props.weId || ''}_ypkurc`}
            field={field}
            visible={filterFieldSettingVisible}
            onClose={this.onToggleSettingDlgVisible}
            onOk={this.onFieldSettingChange}
          />
        ) : null}
      </div>
    );
  }
}
