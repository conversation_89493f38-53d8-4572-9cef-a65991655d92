import { FieldType } from '../../../constants';
import { DatasetFields } from '../../../left-side/FieldList';
import { AxisType } from '../constants';
import { getFieldData } from '../utils';

// eslint-disable-next-line max-len
export const getFieldId = (field: any) => ((field.type === FieldType.FDate || field.type === FieldType.CDate) && field.dateParentId
  ? `${field.id}-${field.dateGroup}`
  : field.id);

export const getDFields = (datasetFields: DatasetFields[]) => {
  const allFields: any[] = [];

  datasetFields.forEach((datasetField) => {
    const { fields: _fields } = datasetField;

    const childFields = _fields.filter(
      // eslint-disable-next-line max-len
      (field) => !((field.type === FieldType.FDate || field.type === FieldType.CDate) && field.dateParentId),
    );

    allFields.push(...childFields);
  });

  return allFields;
};

export const getDFieldIds = (datasetFields: DatasetFields[], needDateGroup: boolean) => {
  const fieldIds: string[] = [];

  datasetFields.forEach((datasetField) => {
    let { fields } = datasetField;

    if (!needDateGroup) {
      fields = fields.filter(
        // eslint-disable-next-line max-len
        (field) => (field.type !== FieldType.FDate && field.type !== FieldType.CDate) || !field.dateParentId,
      );
    }

    fields.forEach((field) => {
      const id = getFieldId(field);

      fieldIds.push(id);
    });
  });

  return fieldIds;
};

export const getSelectedFields = ({
  axisType,
  selectedFieldIds,
  datasetFields,
  needDateGroup,
}: {
  axisType: keyof typeof AxisType;
  selectedFieldIds: string[];
  datasetFields: any[];
  needDateGroup: boolean;
}) => {
  const _selectedFields: any[] = [];

  // eslint-disable-next-line max-len
  const getSelectedField = (field: any) => getFieldData(axisType, field);

  datasetFields.forEach((datasetField) => {
    const fieldId = getFieldId(datasetField);

    if (selectedFieldIds.includes(fieldId)) {
      _selectedFields.push(getSelectedField(datasetField));
    }

    if (datasetField.children && needDateGroup) {
      datasetField.children.forEach((_field: any) => {
        const _fieldId = getFieldId(_field);

        if (selectedFieldIds.includes(_fieldId)) {
          _selectedFields.push(getSelectedField(_field));
        }
      });
    }
  });

  return _selectedFields;
};

export default {};
