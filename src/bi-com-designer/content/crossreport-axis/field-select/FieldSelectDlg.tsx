import { Button, Dialog } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import React, { PureComponent } from 'react';
import { dlgIconName } from '../../../../constants';
import { getAxisTypeName } from '../field-setting/FieldSettingDlg';
import Content from './Content';
import './index.less';
import { FieldSelectDlgProps } from './types';
import { getSelectedFields } from './utils';

export default class FieldSelectDlg extends PureComponent<FieldSelectDlgProps> {
  contentRef = React.createRef<any>();

  onOk = () => {
    const {
      axisType, needDateGroup, onClose, onAddFields,
    } = this.props;
    const { dFields, selectedFieldIds } = this.contentRef.current.state;

    if (selectedFieldIds.length) {
      const _selectedFields = getSelectedFields({
        selectedFieldIds,
        datasetFields: dFields,
        axisType,
        needDateGroup: !!needDateGroup,
      });

      onAddFields(_selectedFields);
    }

    onClose();
  };

  render() {
    const {
      axisType, visible, datasetFields, needDateGroup, filterField, onClose,
    } = this.props;
    const title = `${getAxisTypeName(axisType)}${getLabel('79709', '字段配置')}`;

    return (
      <Dialog
        weId={`${this.props.weId || ''}_1fnb7i`}
        title={title}
        width={610}
        visible={visible}
        icon={dlgIconName}
        closable
        onClose={onClose}
        footer={[
          <Button
            weId={`${this.props.weId || ''}_0d4kb2`}
            type="primary"
            onClick={this.onOk}
            key="sure"
          >
            {getLabel('55437', '确认')}
          </Button>,
          <Button weId={`${this.props.weId || ''}_vj2in6`} onClick={onClose} key="cancel">
            {getLabel('53937', '取消')}
          </Button>,
        ]}
      >
        <Content
          weId={`${this.props.weId || ''}_bv09rd`}
          ref={this.contentRef}
          filterField={filterField}
          datasetFields={datasetFields}
          needDateGroup={!!needDateGroup}
        />
      </Dialog>
    );
  }
}
