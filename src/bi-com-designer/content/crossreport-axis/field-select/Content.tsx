import {
  Checkbox, CheckboxValueType, CorsComponent, Icon, Layout,
} from '@weapp/ui';
import { classnames, cloneDeep, getLabel } from '@weapp/utils';
import React, { useCallback, useState } from 'react';
import { When } from 'react-if';
import setTimeoutOnce from '../../../../utils/setTimeoutOnce';
import { prefixCls } from '../../../constants';
import { DatasetFields } from '../../../left-side/FieldList';
import { getIconInfo } from '../../../utils';
import { getDFieldIds, getDFields } from './utils';

const { Row, Col } = Layout;

interface ContentProps extends React.Attributes {
  ref: any;
  needDateGroup: boolean;
  datasetFields: DatasetFields[];
  filterField?: (field: any) => boolean;
}

interface FieldItemProps extends React.Attributes {
  isChildren?: boolean;
  field: any;
  needDateGroup: boolean;
  selectedFieldIds: string[];
  setScrollVisible: () => void;
  onFieldCheck: (fieldId: string, checked: CheckboxValueType) => void;
}

interface ContentStates {
  selectedFieldIds: string[];
  dFields: DatasetFields[];
  allFieldIds: string[];
  hasScroll: boolean;
}

const FieldItem: React.FC<FieldItemProps> = (props) => {
  const {
    field, selectedFieldIds, isChildren, needDateGroup, onFieldCheck, setScrollVisible,
  } = props;
  const [expended, setExpended] = useState(needDateGroup);
  const toggleExpended = useCallback(() => {
    setExpended(!expended);

    setTimeoutOnce(() => {
      setScrollVisible();
    }, 100);
  }, [expended]);

  const fieldId = isChildren ? `${field.id}-${field.dateGroup}` : field.id;

  const onCheckChange = useCallback(
    (value: CheckboxValueType) => {
      onFieldCheck(fieldId, value);
    },
    [fieldId],
  );

  return (
    <>
      <div
        className={classnames(`${prefixCls}-fieldselect-content-item`, {
          parent: field.children?.length && needDateGroup,
          children: isChildren,
          hasnogroup: !needDateGroup,
        })}
      >
        <When
          weId={`${props.weId || ''}_kgmttm`}
          condition={field.children?.length && needDateGroup}
        >
          <Icon
            weId={`${props.weId || ''}_i6nu31`}
            name={expended ? 'Icon-Down-arrow04' : 'Icon-Right-arrow04'}
            onClick={toggleExpended}
          />
        </When>
        <Row weId={`${props.weId || ''}_yhzbth`}>
          <Col
            weId={`${props.weId || ''}_m0jm9m`}
            span={20}
            className={`${prefixCls}-fieldselect-content-item-name`}
          >
            <When weId={`${props.weId || ''}_ul5zgb`} condition={!isChildren}>
              <CorsComponent
                app="@weapp/ebdcoms"
                compName="IconFont"
                weId={`${props.weId || ''}_77wo1k`}
                type={`${getIconInfo(field).name}`}
                style={{ color: `${getIconInfo(field).color}` }}
                size={getIconInfo(field).name === 'date' ? 'xs' : 's'}
                className={`${prefixCls}-fieldselect-content-item-icon`}
              />
            </When>
            <span className={`${prefixCls}-fieldselect-content-item-name-text`} title={field.text}>
              {field.text}
            </span>
          </Col>
          <Col weId={`${props.weId || ''}_wxb73t`} span={4}>
            <Checkbox
              weId={`${props.weId || ''}_jeh5dr`}
              value={selectedFieldIds.includes(fieldId)}
              onChange={onCheckChange}
            />
          </Col>
        </Row>
      </div>
      <When weId={`${props.weId || ''}_9tnad5`} condition={field.children && expended}>
        <div>
          {field.children?.map?.((childField: any) => (
            <FieldItem
              weId={`${props.weId || ''}_5ci5rs@${childField.id}`}
              key={`${childField.id}_${childField.dateGroup}`}
              field={childField}
              selectedFieldIds={selectedFieldIds}
              isChildren
              needDateGroup={needDateGroup}
              onFieldCheck={onFieldCheck}
              setScrollVisible={setScrollVisible}
            />
          ))}
        </div>
      </When>
    </>
  );
};

export default class Content extends React.PureComponent<ContentProps, ContentStates> {
  constructor(props: ContentProps) {
    super(props);

    const { datasetFields, needDateGroup, filterField = () => true } = props;
    const dFields = getDFields(datasetFields).filter(filterField);
    const allFieldIds = getDFieldIds(datasetFields, needDateGroup);

    this.state = {
      dFields,
      selectedFieldIds: [],
      allFieldIds,
      hasScroll: false,
    };
  }

  componentDidMount() {
    setTimeoutOnce(() => {
      this.setScrollVisible();
    }, 100);
    this.setScrollVisible();
  }

  setScrollVisible = () => {
    const fieldsContentDom = document.querySelector(`.${prefixCls}-fieldselect-content-fields`)!;

    const hasScroll = fieldsContentDom?.scrollHeight > fieldsContentDom?.clientHeight;

    this.setState({ hasScroll });
  };

  onFieldCheck = (fieldId: string, checked: CheckboxValueType) => {
    const { selectedFieldIds: _selectedFieldIds } = this.state;
    const selectedFieldIds = cloneDeep(_selectedFieldIds);

    if (checked) {
      selectedFieldIds.push(fieldId);
    } else {
      const selectedFieldInd = selectedFieldIds.findIndex((id) => id === fieldId);

      if (selectedFieldInd >= 0) {
        selectedFieldIds.splice(selectedFieldInd, 1);
      }
    }

    this.setState({ selectedFieldIds });
  };

  onAllFieldCheck = (checked: CheckboxValueType) => {
    const { allFieldIds } = this.state;

    if (checked) {
      this.setState({ selectedFieldIds: allFieldIds });
    } else {
      this.setState({ selectedFieldIds: [] });
    }
  };

  render() {
    const {
      dFields, selectedFieldIds, allFieldIds, hasScroll,
    } = this.state;
    const { needDateGroup } = this.props;

    return (
      <div className={`${prefixCls}-fieldselect-content`}>
        <Row
          weId={`${this.props.weId || ''}_3ijbpe`}
          className={`${prefixCls}-fieldselect-content-header`}
        >
          <Col weId={`${this.props.weId || ''}_sa1ufn`} span={20}>
            {getLabel('109595', '字段名称')}
          </Col>
          <Col weId={`${this.props.weId || ''}_aplfcw`} span={4}>
            <Checkbox
              weId={`${this.props.weId || ''}_vsmqtk`}
              value={allFieldIds.length === selectedFieldIds.length}
              onChange={this.onAllFieldCheck}
            />
            {getLabel('54039', '显示')}
          </Col>
        </Row>
        <div className={`${prefixCls}-fieldselect-content-fields-wrapper`}>
          <div
            className={classnames(`${prefixCls}-fieldselect-content-fields`, {
              [`${prefixCls}-fieldselect-content-fields-scroll`]: hasScroll,
            })}
          >
            {dFields.map((dField) => (
              <FieldItem
                weId={`${this.props.weId || ''}_a6pz0t@${dField.id}`}
                key={dField.id}
                field={dField}
                needDateGroup={needDateGroup}
                selectedFieldIds={selectedFieldIds}
                onFieldCheck={this.onFieldCheck}
                setScrollVisible={this.setScrollVisible}
              />
            ))}
          </div>
        </div>
      </div>
    );
  }
}
