import React from 'react';
import { DatasetFields } from '../../../left-side/FieldList';
import { AxisType } from '../constants';

export interface FieldSelectDlgProps extends React.Attributes {
  visible: boolean;
  axisType: keyof typeof AxisType;
  datasetFields: DatasetFields[];
  needDateGroup?: boolean;
  filterField?: (field: any) => boolean;
  onAddFields: (fields: any[]) => void;
  onClose: () => void;
}

export default {};
