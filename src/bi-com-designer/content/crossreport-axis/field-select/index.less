@import '../../../../style/prefix.less';

.@{bi-prefix}-fieldselect-content {
  max-height: 316px;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  background: var(--base-white);
  font-size: var(--font-size-12);
  border: 1px solid var(--border-color);

  &-header {
    line-height: 34px;
    border-bottom: 1px solid var(--border-color);

    & > div {
      display: flex;
      align-items: center;

      &:first-of-type {
        font-weight: bold;
        padding-left: 20px;
      }

      &:last-of-type {
        padding-left: 5px;

        & > label {
          margin-right: 6px;
        }
      }
    }
  }

  &-fields {
    overflow: auto;
    flex: 1;

    &-scroll {
      .@{bi-prefix}-fieldselect-content-item {
        & > div > div:last-of-type {
          padding-left: 7px !important;
        }
      }
    }
  }

  &-item {
    line-height: 30px;
    display: flex;
    align-items: center;
    padding-left: 28px;

    &.parent {
      margin-left: -20px;
    }

    &.children {
      padding-left: 49px;

      & > div > div:last-of-type {
        margin-left: -3px;
      }
    }

    &.hasnogroup {
      padding-left: 20px;

      & > div > div:last-of-type {
        padding-left: 2px;
      }
    }

    & > span {
      margin-right: 5px;
      cursor: pointer;
    }

    &-name {
      display: flex;

      &-text {
        display: block;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    &:hover {
      background-color: var(--hover-bc);
    }

    &-icon {
      padding-right: 8px;
      margin-bottom: 1px;
    }

    & > div {
      flex: 1;
      overflow: hidden;

      & > div {
        display: flex;
        align-items: center;

        &:first-of-type {
          flex: 1;
        }
      }
    }
  }
}

.@{bi-prefix}-fieldselect-content-fields-wrapper {
  flex: 1;
  display: flex;
  overflow: hidden;
  flex-direction: column;
}
