import { ComponentType } from 'react';
import Loadable from '../../../../common/loadable';
import { FilterFieldSettingDlgProps } from './types';

export const FilterFieldSettingDlg = Loadable({
  name: 'FilterFieldSettingDlg',
  loader: () => import(
    /* webpackChunkName: "de_bi_com_designer" */
    './FilterFieldSettingDlg'
  ),
}) as ComponentType<FilterFieldSettingDlgProps>;

export default {};
