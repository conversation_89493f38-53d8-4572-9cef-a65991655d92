@import '../../../../style/prefix.less';

.@{bi-prefix}-ccni-field-content {
  background-color: var(--base-white);
  border: 1px solid var(--border-color);

  &-localposition {
    border-bottom: 1px solid var(--border-color);

    .ui-select {
      width: 100%;
    }

    .ui-select-input {
      max-width: none;
    }
  }

  &-dft-loading {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .weapp-ebdf-field-setting-filterGroupSet {
    width: 100%;

    & > div {
      border-bottom: 1px solid var(--border-color);
      padding: var(--form-item-padding-module);

      &:last-of-type {
        border-bottom: none;
      }

      & > .ui-formItem-label-col {
        flex: 7 !important;
      }

      & > .ui-formItem-wrapper-col {
        flex: 17 !important;

        .ui-switch {
          width: 100%;
          display: flex;
          justify-content: flex-end;

          & > button {
            width: auto;
          }
        }
      }
    }

    .filterSet-dragTitle {
      border-bottom: 0px;
      font-size: var(--font-size-12);
    }
  }
}
