import { <PERSON><PERSON>, <PERSON>alog, FormDatas } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import React, { PureComponent } from 'react';
import { dlgIconName } from '../../../../constants';
import { prefixCls } from '../../../constants';
import FilterFieldSettingContent from './Content';
import { FilterFieldSetProvider } from './Context';
import './index.less';
import { FilterFieldSettingDlgProps } from './types';

interface FilterFieldSettingDlgStates {
  field: any;
}

export default class FilterFieldSettingDlg extends PureComponent<
  FilterFieldSettingDlgProps,
  FilterFieldSettingDlgStates
> {
  constructor(props: FilterFieldSettingDlgProps) {
    super(props);

    const { field } = props;

    this.state = { field: toJS(field) };
  }

  fieldFieldRef: any = null;

  onFieldFilterRef = (fieldFieldRef: any) => {
    this.fieldFieldRef = fieldFieldRef;

    fieldFieldRef.getCommentContainer = () => document.body;
  };

  onFieldDefValChange = (defFieldFilterData: any) => {
    const { field } = this.state;
    const _field = { ...field, listFilterDefaultSets: defFieldFilterData };

    this.setState({ field: _field });
  };

  onFieldChange = (changes?: FormDatas) => {
    const { field } = this.state;
    const _field = { ...field, ...changes };

    this.setState({ field: _field });
  };

  onOk = () => {
    const { onOk } = this.props;
    const { field } = this.state;

    const success = () => {
      const { datas } = this.fieldFieldRef.state;
      const { fieldInfo, configNameJson, ...listFilterDefaultSets } = datas;

      onOk({
        ...field,
        showName: configNameJson ? JSON.parse(configNameJson) : '',
        configNameJson,
        listFilterDefaultSets: { ...listFilterDefaultSets, configNameJson },
      });
    };

    if (this.fieldFieldRef) {
      if (this.fieldFieldRef.editableRef.current) {
        this.fieldFieldRef.onValidate(success);
      } else {
        success();
      }
    } else {
      onOk(field);
    }
  };

  render() {
    const { visible, onClose, cusDataSet } = this.props;
    const { field } = this.state;

    return (
      <Dialog
        weId={`${this.props.weId || ''}_3mnp9e`}
        title={`${field.text}${getLabel('252709', '字段查询条件设置')}`}
        visible={visible}
        onClose={onClose}
        width={600}
        closable
        mask
        destroyOnClose
        icon={dlgIconName}
        wrapClassName={`${prefixCls}-ccni-modal`}
        footer={[
          <Button
            weId={`${this.props.weId || ''}_m6tknp`}
            type="primary"
            onClick={this.onOk}
            key="sure"
          >
            {getLabel('55437', '确认')}
          </Button>,
          <Button weId={`${this.props.weId || ''}_mgk9hi`} onClick={onClose} key="cancel">
            {getLabel('53937', '取消')}
          </Button>,
        ]}
      >
        <FilterFieldSetProvider
          weId={`${this.props.weId || ''}_orz3v4`}
          value={{
            cusDataSet,
          }}
        >
          <FilterFieldSettingContent
            weId={`${this.props.weId || ''}_3gzyn2`}
            field={field}
            onFieldFilterRef={this.onFieldFilterRef}
            onFieldChange={this.onFieldChange}
          />
        </FilterFieldSetProvider>
      </Dialog>
    );
  }
}
