import {
  Cors<PERSON>omponent, FormDatas, FormItem, Spin,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { inject, observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { prefixCls } from '../../../constants';
import { DesignerStore } from '../../../store';
import { GET_URL, MODULE, SAVE_URL } from '../../constants';
import { FilterFieldSetContext } from './Context';
import getFieldOpts from './getFieldOpts';

interface FilterFieldSettingContentProps extends React.Attributes {
  field: any;
  index: number;
  onFieldFilterRef: (fieldFilterRef: any) => void;
  onFieldChange: (changes?: FormDatas) => void;
}

interface FilterFieldSettingContentStates {
  loading: boolean;
  field: any;
}

/** 单选框、复选框、下拉 */
export const SelectType = ['CheckBox', 'Select', 'RadioBox'];

@inject('designStore')
@observer
class Content extends PureComponent<
  FilterFieldSettingContentProps & { designStore: DesignerStore },
  FilterFieldSettingContentStates
> {
  state = { loading: SelectType.includes(this.props.field.compType), field: this.props.field };

  static contextType = FilterFieldSetContext;

  async componentDidMount() {
    const { designStore } = this.props;
    const { dataset } = designStore;
    const field = { ...this.props.field };
    /**
     * 页面二维报表和BI二维报表都会加载此组件，但页面二维报表不会提前在 designerStore 中
     * 注入 dataset，所以在引用组件时从外部传入 dataset
     */
    const _dataset = this.context?.cusDataSet ?? dataset;

    // 下拉类型的字段通过接口动态获取其下拉项数据
    if (SelectType.includes(field.compType) && field.compType !== 'Cascader') {
      // 避免因为接口报错导致不再继续执行下面的代码
      try {
        field.options = await getFieldOpts(_dataset, field);
      } catch (error) {
        field.options = [];
      }
    }

    // 级联获取options
    if (field.compType === 'Cascader') {
      field.options = field.config?.options || [];
    }

    this.setState({ loading: false, field });
  }

  render() {
    const { designStore, onFieldFilterRef, onFieldChange } = this.props;
    const { page, comServicePath, layoutStore } = designStore;
    const { loading, field } = this.state;

    return (
      <div className={`${prefixCls}-ccni-field-content`}>
        <When
          weId={`${this.props.weId || ''}_422jjp`}
          condition={layoutStore.selectedCom?.config?.filterShowType === 'filter'}
        >
          <FormItem
            weId={`${this.props.weId || ''}_tsh38j`}
            label={getLabel('54603', '显示位置')}
            labelSpan={7}
            com={['isQuick']}
            item={{
              isQuick: {
                itemType: 'SELECT',
                data: [
                  { id: '0', content: getLabel('55658', '平铺') },
                  { id: '1', content: getLabel('54290', '更多') },
                ],
                defaultValue: field.isQuick,
              },
            }}
            className={`${prefixCls}-ccni-field-content-localposition`}
            onChange={onFieldChange}
          />
        </When>
        <If weId={`${this.props.weId || ''}_k0s6xn`} condition={loading}>
          <Then weId={`${this.props.weId || ''}_2raxay`}>
            <Spin
              weId={`${this.props.weId || ''}_gcuqxd`}
              className={`${prefixCls}-ccni-field-content-dft-loading`}
            />
          </Then>
          <Else weId={`${this.props.weId || ''}_ied6z8`}>
            <CorsComponent
              weId={`${this.props.weId || ''}_vxp8m6`}
              app="@weapp/ebdform"
              compName="FieldFilterItemSet"
              type="ebDesigner"
              dfDatas={{
                ...field.listFilterDefaultSets,
                componentKey: field.componentKey,
                fieldInfo: field,
              }}
              localeProps={{
                getUrl: GET_URL,
                saveUrl: SAVE_URL,
                rowKey: 'labelid',
                params: {
                  module: MODULE,
                  targetId: page.id,
                },
              }}
              configParams={{ RIU: true, needDetail: false }}
              needNewDateOpts
              needCommonFilterGroups
              ebBusinessId={page.id}
              needRename
              needDateMenu
              needTile
              needGroupEmployeeFilter
              needSearchRelationEnable
              needStyleSetting
              comServicePath={comServicePath}
              enableSqlEncry
              needManualFilter
              needGroupCountNumSet
              needDateTimeSwitch
              onRef={onFieldFilterRef}
            />
          </Else>
        </If>
      </div>
    );
  }
}

export default Content as any;
