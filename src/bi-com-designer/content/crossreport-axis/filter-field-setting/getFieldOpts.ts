import { isEmpty, memoize } from '@weapp/utils';
import ebdcoms from '../../../../utils/ebdcoms';

/** 格式化级联选项 */
const formatCascaderOptions = (opts: any[], pid: string = '') => {
  const childs: any[] = [];
  const datas = opts
    .filter((item) => {
      if (item.pid !== pid) {
        childs.push(item);
        return false;
      }
      return true;
    })
    .map((item) => {
      const opt: any = {
        value: item.id,
        label: item.name,
        children: formatCascaderOptions(childs, item.id),
      };
      return opt;
    });
  return datas;
};

/**
 * 获取字段的选项
 * @param dataset 数据源
 * @param field 字段
 * @param comType 组件类型
 * @returns
 */
const getFieldOpts: any = memoize(
  async (dataset: any, field: any) => {
    if (isEmpty(dataset) || isEmpty(field) || dataset?.type === 'BIZ' || dataset?.type === 'mock') {
      return Promise.resolve([]);
    }

    let { name } = field;
    const { type: sourceType, groupId, servicePath = 'ebuilder/common' } = dataset;
    let opts = [];

    if (sourceType === 'ETEAMS' && field.optionType) {
      name = field.optionType;
    }

    const res = await ebdcoms.excu('ajax', {
      url: `/api/${servicePath}/ds/options`,
      params: {
        fieldName: name || field?.fieldName,
        objId: field.objId,
        sourceType,
        groupId,
      },
    });

    if (field.compType === 'Cascader') {
      opts = formatCascaderOptions(res);
    } else {
      opts = res.map((el: any) => ({ ...el, value: el.id }));
    }

    return Promise.resolve(opts);
  },
  (dataset: any, field: any) => `${dataset?.id}_${dataset?.type}_${field?.name}_${field?.objId}_${field?.shortKey}`,
);

export default getFieldOpts;
