import { CorsComponent, Dialog, Icon } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import { When } from 'react-if';
import { ReactSortable, SortableEvent } from 'react-sortablejs';
import { noop } from '../../../constants';
import { FieldType, moveFieldDistance, prefixCls } from '../../constants';
import { DesignerStore } from '../../store';
import { group } from '../design/CrossReportWrapper';
import { AxisType, notUseFields } from './constants';
import { FieldSelectDlg } from './field-select';
import { getFieldId } from './field-select/utils';
import FieldItem from './FieldItem';
import { getFieldData } from './utils';

interface AxisProps extends React.Attributes {
  className?: string;
  type: keyof typeof AxisType;
  fields: any[];
  onChange: (fields: any[]) => void;
}

interface AxisStates {
  isSpread: boolean; // 是否展开
  showSpreadIcon: boolean; // 是否展示展开icon
  fieldSelectDlgVisible: boolean;
}

const spreadHeight = 32; // 收缩的高度

const typeContent = {
  conditionItems: getLabel('252681', '筛选项'),
  rowDimensions: getLabel('252697', '行维度'),
  verticalDimensions: getLabel('252698', '列维度'),
  measures: getLabel('233086', '度量项'),
};

export const addFieldValidate = (field: any, fields: any, type: keyof typeof AxisType) => {
  // 判断字段是否重复拖入
  // eslint-disable-next-line max-len
  const hasFieldUsed = () => fields.find((_field: any) => getFieldId(_field) === getFieldId(field));

  if (['conditionItems'].includes(type) && hasFieldUsed()) {
    return {
      validate: false,
      msg: `${getLabel('233083', '已存在')}${field.text}，${getLabel('233084', '不可重复')}！`,
    };
  }

  // 日期分组字段不允许拖入筛选和度量项中
  if (
    (field.type === FieldType.FDate || field.type === FieldType.CDate)
    && field.dateParentId
    && ['conditionItems', 'measures'].includes(type)
  ) {
    let content = `${getLabel('252680', '日期分组字段不允许拖入')}${typeContent[type]}`;
    content += getLabel('55673', '中');

    return { validate: false, msg: content };
  }

  // 计算字段只能作为度量项
  if (field.expressId && type !== 'measures') {
    let content = `${getLabel('252683', '计算字段不允许拖入')}${typeContent[type]}`;
    content += getLabel('55673', '中');

    return { validate: false, msg: content };
  }

  if (
    (notUseFields.includes(field.type) || field.usage === 'display')
    && type === 'conditionItems'
  ) {
    const content = getLabel('260372', '该字段不允许筛选');

    return { validate: false, msg: content };
  }

  return { validate: true };
};

@inject('designStore')
@observer
class Axis extends PureComponent<AxisProps & { designStore: DesignerStore }, AxisStates> {
  constructor(props: AxisProps & { designStore: DesignerStore }) {
    super(props);

    this.state = { isSpread: false, showSpreadIcon: false, fieldSelectDlgVisible: false };
  }

  sortableRef = React.createRef<any>();

  componentDidMount() {
    const { type, fields, designStore } = this.props;
    const { events, layoutStore } = designStore;

    if (fields.length) {
      this.setShowSpreadIcon();
    }

    events.on(`propChange_${type}`, `${layoutStore.selectedCom!.id}`, this.setShowSpreadIcon);
  }

  componentWillUnmount() {
    const { type, designStore } = this.props;
    const { events, layoutStore } = designStore;

    events.off(`propChange_${type}`, `${layoutStore.selectedCom!.id}`, this.setShowSpreadIcon);
  }

  setShowSpreadIcon = () => {
    setTimeout(() => {
      const { showSpreadIcon } = this.state;
      const sortableDom = this.sortableRef.current?.ref?.current;

      if (sortableDom) {
        const _showSpreadIcon = sortableDom.scrollHeight > spreadHeight;

        if (showSpreadIcon !== _showSpreadIcon) {
          this.setState({ showSpreadIcon: _showSpreadIcon, isSpread: false });
        }
      }
    }, 100);
  };

  onAdd = (e: SortableEvent) => {
    const { fields, type, onChange } = this.props;
    const { newIndex } = e;
    const field = JSON.parse(e.clone.getAttribute('data-field')) as any;
    const { validate, msg } = addFieldValidate(field, fields, type);

    if (!validate) {
      Dialog.message({
        type: 'error',
        content: msg,
      });

      return;
    }

    const _field = getFieldData(type, field);
    const _fields = toJS(fields);

    _fields.splice(newIndex, 0, _field);

    onChange(_fields);
  };

  onEnd = (e: SortableEvent) => {
    const {
      newIndex,
      oldIndex,
      originalEvent: { pageX, pageY },
    } = e;

    const {
      x, y, height, width,
    } = this.sortableRef.current.ref.current!.getBoundingClientRect();

    /** 判断字段是否拖拽出列表删除 */
    if (
      pageY > y + height + moveFieldDistance
      || pageY + moveFieldDistance < y
      || pageX > x + width + moveFieldDistance
      || pageX + moveFieldDistance < x
    ) {
      this.onRemove(oldIndex)();
    } else if (newIndex !== oldIndex) {
      this.onSort(oldIndex, newIndex);
    }
  };

  onChange = (index: number) => (field: any) => {
    const { fields, onChange } = this.props;
    const _fields = toJS(fields);

    _fields.splice(index, 1, field);

    onChange(_fields);
  };

  onRemove = (index: number) => () => {
    const { fields, onChange } = this.props;
    const _fields = toJS(fields);

    _fields.splice(index, 1);

    onChange(_fields);
  };

  onSort = (oldIndex: number, newIndex: number) => {
    const { fields, onChange } = this.props;
    const _fields = toJS(fields);

    const field = _fields.splice(oldIndex, 1);

    _fields.splice(newIndex, 0, field[0]);

    onChange(_fields);
  };

  onSpreadToggle = () => {
    const { isSpread } = this.state;

    this.setState({ isSpread: !isSpread });
  };

  onAddFields = (addFields: any[]) => {
    const { fields, onChange } = this.props;
    const _fields = toJS(fields);

    _fields.push(...addFields);
    onChange(_fields);
  };

  filterField = (field: any) => {
    const { fields, type } = this.props;

    if (type === 'conditionItems') {
      return (
        !notUseFields.includes(field.type)
        && field.usage !== 'display'
        && !fields.find((f) => f.id === field.id)
      );
    }

    return true;
  };

  onFieldSelectDlgVisibleToggle = () => {
    const { fieldSelectDlgVisible } = this.state;

    this.setState({ fieldSelectDlgVisible: !fieldSelectDlgVisible });
  };

  render() {
    const {
      designStore, className, type, fields,
    } = this.props;
    const { isSpread, showSpreadIcon, fieldSelectDlgVisible } = this.state;
    const dropIconName = isSpread ? 'Icon-collapse-menu' : 'Icon-drop-down-menu';
    const datasetFields = type === 'measures'
      ? [...designStore.datasetFields, ...designStore.calculateFields]
      : designStore.datasetFields;

    return (
      <div className={classnames(className, `${prefixCls}-crossreoprt-axis`)}>
        <ReactSortable
          ref={this.sortableRef}
          weId={`${this.props.weId || ''}_556rbe`}
          animation={100}
          list={fields}
          setList={noop}
          group={group}
          onAdd={this.onAdd}
          onEnd={this.onEnd}
          className={classnames(`${prefixCls}-crossreoprt-axis-sortable`, { spread: isSpread })}
        >
          {fields.map((field: any, index: number) => (
            <FieldItem
              weId={`${this.props.weId || ''}_4h3s8w`}
              index={index}
              key={field.uid}
              field={field}
              type={type}
              onChange={this.onChange(index)}
              onRemove={this.onRemove(index)}
            />
          ))}
        </ReactSortable>
        <div className={classnames(`${prefixCls}-crossreoprt-axis-icons`, { spread: isSpread })}>
          <When weId={`${this.props.weId || ''}_07o7mo`} condition={fields.length}>
            <>
              <CorsComponent
                app="@weapp/ebdcoms"
                compName="IconFont"
                weId={`${this.props.weId || ''}_pcgcpi`}
                name="Icon-add-to"
                title={getLabel('56100', '添加字段')}
                onClick={this.onFieldSelectDlgVisibleToggle}
              />
              <Icon
                weId={`${this.props.weId || ''}_lvr2km`}
                name={dropIconName}
                onClick={this.onSpreadToggle}
                className={classnames({
                  [`${prefixCls}-crossreoprt-axis-icons-spread-hide`]: !showSpreadIcon,
                })}
              />
            </>
          </When>
        </div>
        {fieldSelectDlgVisible ? (
          <FieldSelectDlg
            weId={`${this.props.weId || ''}_pe1a0j`}
            visible={fieldSelectDlgVisible}
            axisType={type}
            needDateGroup={['rowDimensions', 'verticalDimensions'].includes(type)}
            filterField={this.filterField}
            datasetFields={datasetFields}
            onAddFields={this.onAddFields}
            onClose={this.onFieldSelectDlgVisibleToggle}
          />
        ) : null}
      </div>
    );
  }
}

export default Axis as unknown as ComponentType<AxisProps>;
