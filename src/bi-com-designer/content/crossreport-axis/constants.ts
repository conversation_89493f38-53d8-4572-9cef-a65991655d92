export enum AxisType {
  verticalDimensions = 'verticalDimensions', // 纵向维度
  rowDimensions = 'rowDimensions', // 行维度
  measures = 'measures', // 度量
  conditionItems = 'conditionItems', // 筛选项
}

export enum AxisOptType {
  SETTING = 'setting',
  DELETE = 'delete',
}

/** 屏蔽不支持的字段 */
export const notUseFields = [
  'ComboSelect',
  'MatrixComponent',
  'FileComponent',
  'ImageComponent',
  'TreeSelect',
  'radioboximg',
  'checkboximg',
  'ImageCheckBox',
  'ImageRadioBox',
  // 级联字段
  'Cascader',
];

export default {};
