import { UUID } from '../../../utils';
import { FieldType } from '../../constants';
import { Aggregator, OrderType } from '../field-list/constants';
import { DateType } from './field-setting/constants';

// 后端formatType 逻辑
// if ('yyyy'.equalsIgnoreCase(format)) {
//   return '1';
// } else if ('yyyy-MM'.equalsIgnoreCase(format)) {
//   return '2';
// } else if ('yyyy-MM-dd'.equalsIgnoreCase(format)) {
//   return '4';
// } else if ('yyyy-MM-dd HH:mm'.equalsIgnoreCase(format)) {
//   return '5';
// } else if ('yyyy-MM-dd HH:mm:ss'.equalsIgnoreCase(format)) {
//   return '6';
// } else if ('HH:mm'.equalsIgnoreCase(format)) {
//   return '7';
// } else if ('HH:mm:ss'.equalsIgnoreCase(format)) {
//   return '8';
// }
// return '3';

const { COUNT, SUM } = Aggregator;

export const getFieldData = (type: string, field: any) => {
  const text = [FieldType.CDate, FieldType.FDate].includes(field.type) && field.dateParentId
    ? `${field.pFieldText}（${field.text}）`
    : field.text;

  if (type === 'measures') {
    field = {
      ...field,
      aggregator: COUNT,
      showName: text,
      orderType: OrderType.DEFAULT,
      align: 'left',
      width: 100,
      axisType: type,
      feature: {
        numFormat: 'auto',
        digit: 2,
        unit: 'none',
        thousandth: true,
      },
    };

    if (field.type === FieldType.FNumber || field.type === FieldType.CNumber) {
      field = { ...field, aggregator: SUM };
    }
  }

  if (type === 'rowDimensions' || type === 'verticalDimensions') {
    field = {
      ...field,
      axisType: type,
      showName: text,
      show: 'group',
      dateGroup: field.dateGroup || DateType.NONE,
      orderType: OrderType.DEFAULT,
      align: 'left',
      width: 100,
    };

    if (field.type === FieldType.FDate || field.type === FieldType.CDate) {
      field = { ...field, dateGroup: field.dateGroup || DateType.NONE };
    }
  }

  if (type === 'conditionItems') {
    field = {
      ...field,
      isQuick: '0',
      fieldId: field.id,
      fieldName: `field_${field.id}`,
      fieldShowName: text,
      fieldType: field.type,
      filterId: UUID(),
      showName: text,
      listFilterDefaultSets: {
        defaultType: '0',
        endValue: '',
        listFilterSets: [],
        optionEnable: '0',
        startValue: '',
        startValueSpan: '',
        configNameJson: JSON.stringify(text),
      },
    };
  }

  return { ...field, text, uid: UUID(16) };
};

export const getDateGroupOptions = (
  dateGroupOptions: {
    id: DateType;
    content: string;
  }[],
  field: any,
) => {
  if (field.formatType === '1') {
    // eslint-disable-next-line max-len
    return dateGroupOptions.filter((dateGroupOption) => [DateType.YEAR, DateType.NONE].includes(dateGroupOption.id));
  }

  if (field.formatType === '2') {
    // eslint-disable-next-line max-len
    return dateGroupOptions.filter((dateGroupOption) => [DateType.YEAR, DateType.NONE].includes(dateGroupOption.id));
  }

  if (field.formatType === '7' || field.formatType === '8') {
    // eslint-disable-next-line max-len
    return dateGroupOptions.filter((dateGroupOption) => [DateType.DAY, DateType.NONE].includes(dateGroupOption.id));
  }

  return dateGroupOptions;
};

export const replaceStr = (str?: string) => {
  if (!str) return str;
  const reg = /\b(\w)|\s(\w)/g;
  return str.replace(reg, (m) => m.toUpperCase());
};

export default {};
