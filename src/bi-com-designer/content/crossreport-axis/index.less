@import '../../../style/prefix.less';

.@{bi-prefix}-crossreport-opts {
  background-color: #fff;
  font-size: var(--font-size-12);

  &.dragging {
    .@{bi-prefix}-crossreoprt-axis {
      outline: 1px solid #68a5ff;
    }
  }

  .@{bi-prefix}-crossreport-dimensions {
    display: flex;
    padding: 12px 14px;

    &-row,
    &-col {
      flex: 1;
    }

    &-change {
      width: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: var(--de-icon-color);
    }
  }

  .@{bi-prefix}-crossreport-dimensions-row,
  .@{bi-prefix}-crossreport-dimensions-col,
  .@{bi-prefix}-crossreport-measures,
  .@{bi-prefix}-crossreport-filters {
    min-height: 34px;
    display: flex;
    align-items: center;

    & > span {
      width: 70px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .@{bi-prefix}-crossreport-measures,
  .@{bi-prefix}-crossreport-filters {
    padding: 0px 14px 12px;
  }

  .@{bi-prefix}-crossreoprt-axis {
    flex: 1;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    background-color: var(--base-white);
    display: flex;

    &-sortable {
      height: 32px;
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      overflow: hidden;

      &.spread {
        max-height: 300px;
        height: fit-content;
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    &-icons {
      width: 50px;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      &-spread-hide {
        display: none !important;
      }

      &.spread {
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-end;

        & > span {
          padding-right: 5px;
        }
      }

      & > span {
        cursor: pointer;
        height: 25px;
        width: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary);
      }

      & > span:last-of-type {
        color: var(--regular-fc);
      }
    }
  }
}

.@{bi-prefix}-crossreoprt-axis-fielditem {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
  position: relative;

  & > span:first-of-type {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &-setting,
  .@{comsPrefix}-cross-report-newconfig-menu-icon {
    color: #666;
    cursor: pointer;
    padding-left: 4px;
  }

  .@{comsPrefix}-cross-report-newconfig-menu-icon {
    & > .ui-icon-xs {
      height: 16px;
      width: 16px;
    }
  }

  &-delete {
    position: absolute;
    color: #999;
    display: none;
    right: -8px;
    top: -6px;
    background: #fff;
    border-radius: 50%;
  }

  &:hover {
    .@{bi-prefix}-crossreoprt-axis-fielditem-delete {
      display: block;
    }
  }
}

.@{bi-prefix}-crossreoprt-axis-fielditem-measure.@{comsPrefix}-cross-report-newconfig-menu {
  .ui-menu-list-item > span {
    width: 90px;
  }
}

.@{bi-prefix}-crossreoprt-axis {
  .@{bi-prefix}-leftside-content-field-list-item.sortable-chosen,
  .@{bi-prefix}-crossreoprt-axis-fielditem {
    display: inline-block;
    font-size: var(--font-size-12);
    margin: 4px 0px 4px 10px;
    padding: 0 8px 0 10px;
    cursor: pointer;
    align-items: center;
    line-height: 24px;
    height: 24px;
    min-width: 88px;
    max-width: 200px;
    background-color: rgba(195, 219, 255, 0.3);
    color: #5d9cec;
    border: 1px solid rgba(195, 219, 255, 1);
    border-radius: 3px;
  }

  .@{bi-prefix}-leftside-content-field-list-item-opts {
    display: none !important;
  }

  .@{bi-prefix}-leftside-content-field-list-item.sortable-chosen {
    & > .ui-icon {
      display: none !important;
    }

    & > span:last-of-type {
      height: 100%;
      display: flex;
      align-items: center;
    }
  }

  &.@{bi-prefix}-crossreport-axis-verticalDimensions {
    .@{bi-prefix}-leftside-content-field-list-item.sortable-chosen,
    .@{bi-prefix}-crossreoprt-axis-fielditem {
      background-color: rgba(133, 239, 209, 0.3);
      border: 1px solid rgba(133, 239, 209, 1);
      color: #40d8ad;
    }
  }

  &.@{bi-prefix}-crossreport-axis-measures {
    .@{bi-prefix}-leftside-content-field-list-item.sortable-chosen,
    .@{bi-prefix}-crossreoprt-axis-fielditem {
      background-color: rgba(243, 146, 94, 0.3);
      border: 1px solid rgba(243, 146, 94, 1);
      color: #fd9c66;
    }
  }

  &.@{bi-prefix}-crossreport-axis-filters {
    .@{bi-prefix}-leftside-content-field-list-item.sortable-chosen,
    .@{bi-prefix}-crossreoprt-axis-fielditem {
      background-color: rgba(243, 146, 94, 0.3);
      border: 1px solid rgba(243, 146, 94, 1);
      color: #fd9c66;
    }
  }
}
