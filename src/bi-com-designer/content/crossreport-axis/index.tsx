import { CorsComponent } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import { When } from 'react-if';
import { BI_FIELD_DRAG, prefixCls } from '../../constants';
import { DesignerStore } from '../../store';
import Axis from './Axis';
import './index.less';

interface CrossReportAxisStates {
  stauts: string;
}

@inject('designStore')
@observer
class CrossReportAxis extends PureComponent<{ designStore: DesignerStore }, CrossReportAxisStates> {
  state = { stauts: 'draggend' };

  componentDidMount() {
    const { events } = this.props.designStore;

    events.on(BI_FIELD_DRAG, this.onDragStatusChange);
  }

  componentWillUnmount() {
    const { events } = this.props.designStore;

    events.off(BI_FIELD_DRAG, this.onDragStatusChange);
  }

  onDragStatusChange = (stauts: string) => {
    this.setState({ stauts });
  };

  onChange = (type: string) => (fields: any) => {
    const { layoutStore, events } = this.props.designStore;

    layoutStore.updateComConfig({ [type]: fields });

    // 主动触发视图层更新
    if (type !== 'conditionItems') {
      events.emit('propChange_columnsChange', layoutStore.selectedCom!.id);
    }

    events.emit(`propChange_${type}`, layoutStore.selectedCom!.id, fields);
  };

  onDimensionsExchange = () => {
    // eslint-disable-next-line max-len
    const { rowDimensions, verticalDimensions } = this.props.designStore.layoutStore.selectedCom!.config!;

    const _rowDimensions = verticalDimensions.map((dimension: any) => ({
      ...dimension,
      axisType: 'rowDimensions',
    }));
    const _verticalDimensions = rowDimensions.map((dimension: any) => ({
      ...dimension,
      axisType: 'verticalDimensions',
    }));

    this.onChange('rowDimensions')(_rowDimensions);
    this.onChange('verticalDimensions')(_verticalDimensions);
  };

  render() {
    const { selectedCom } = this.props.designStore.layoutStore;
    const { stauts } = this.state;

    return (
      <div className={classnames(`${prefixCls}-crossreport-opts`, stauts)}>
        <div className={`${prefixCls}-crossreport-dimensions`}>
          <div className={`${prefixCls}-crossreport-dimensions-row`}>
            <span title={getLabel('252697', '行维度')}>{getLabel('252697', '行维度')}</span>
            <Axis
              weId={`${this.props.weId || ''}_v2bzrz`}
              type="rowDimensions"
              className={`${prefixCls}-crossreport-axis-rowDimensions`}
              fields={toJS(selectedCom?.config.rowDimensions)}
              onChange={this.onChange('rowDimensions')}
            />
          </div>
          <div className={`${prefixCls}-crossreport-dimensions-change`}>
            <CorsComponent
              weId={`${this.props.weId || ''}_93dd18`}
              app="@weapp/ebdcoms"
              compName="IconFont"
              title={getLabel('252710', '交换')}
              name="Icon-switch"
              size="md"
              placement="top"
              onClick={this.onDimensionsExchange}
            />
          </div>
          <div className={`${prefixCls}-crossreport-dimensions-col`}>
            <span title={getLabel('252698', '列维度')}>{getLabel('252698', '列维度')}</span>
            <Axis
              weId={`${this.props.weId || ''}_ncnsua`}
              type="verticalDimensions"
              className={`${prefixCls}-crossreport-axis-verticalDimensions`}
              fields={selectedCom?.config.verticalDimensions}
              onChange={this.onChange('verticalDimensions')}
            />
          </div>
        </div>
        <div className={`${prefixCls}-crossreport-measures`}>
          <span title={getLabel('233086', '度量项')}>{getLabel('233086', '度量项')}</span>
          <Axis
            weId={`${this.props.weId || ''}_i8xgnc`}
            type="measures"
            className={`${prefixCls}-crossreport-axis-measures`}
            fields={selectedCom?.config.measures}
            onChange={this.onChange('measures')}
          />
        </div>
        <When weId={`${this.props.weId || ''}_46j0tt`} condition={selectedCom?.config.showSearch}>
          <div className={`${prefixCls}-crossreport-filters`}>
            <span title={getLabel('252681', '筛选项')}>{getLabel('252681', '筛选项')}</span>
            <Axis
              weId={`${this.props.weId || ''}_q9zzpt`}
              type="conditionItems"
              className={`${prefixCls}-crossreport-axis-filters`}
              fields={selectedCom?.config.conditionItems}
              onChange={this.onChange('conditionItems')}
            />
          </div>
        </When>
      </div>
    );
  }
}

export default CrossReportAxis as unknown as ComponentType<{}>;
