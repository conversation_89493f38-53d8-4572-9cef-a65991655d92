import { getLabel, isArray } from '@weapp/utils';
import React from 'react';
import { IComData } from '../../../types';
import { comAxis, prefixCls } from '../../constants';

interface ChartWrapperProps {
  com: IComData;
}

export const verifyComConfig = (
  type: string,
  { mlen = 0, dlen = 0 }: { mlen?: number; dlen?: number },
  config: any,
) => {
  let verified = true;
  let text = '';

  const { match, dimensions, measures } = comAxis[type];

  verified = match(dlen, mlen, config);

  if ((measures.max && mlen > measures.max) || (dimensions.max && dlen > dimensions.max)) {
    text = getLabel('233088', '当前图表类型仅支持');

    if (isArray(dimensions.label)) {
      dimensions.label.forEach((label: string, index: number) => {
        text += `${label}${getLabel('55478', '维度')}，${measures.label[index]}`;
        text += getLabel('55479', '度量');

        if (dimensions.label.length > index + 1) {
          text += getLabel('231585', '或');
        }
      });
    } else {
      text += `${dimensions.label}${getLabel('55478', '维度')}，${measures.label}`;
      text += getLabel('55479', '度量');
    }
  }

  return { verified, text };
};

const ChartWrapper: React.FC<ChartWrapperProps> = (props) => {
  const { com, children } = props;
  const { verified, text } = verifyComConfig(
    com.config.type,
    {
      mlen: com.config.measures?.length,
      dlen: com.config.dimensions?.length,
    },
    com.config,
  );

  if (!verified && text) {
    return <div className={`${prefixCls}-chart-content-chartdesign-content-tip`}>{text}</div>;
  }

  return <div className={`${prefixCls}-chart-content-chartdesign-content`}>{children}</div>;
};

export default ChartWrapper;
