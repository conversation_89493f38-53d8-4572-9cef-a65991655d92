import React from 'react';
import { IComData } from '../../../types';
import { otherComTypes } from '../../constants';
import ChartWrapper from './ChartWrapper';
import CrossReportWrapper from './CrossReportWrapper';

interface WrapperProps {
  com: IComData;
}

const Wrapper: React.FC<WrapperProps> = (props) => {
  const { com } = props;

  if (com.type === otherComTypes.CrossReport) {
    return <CrossReportWrapper weId="_scyacx" {...props} />;
  }

  return <ChartWrapper weId="_bqxr1c" {...props} />;
};

export default Wrapper;
