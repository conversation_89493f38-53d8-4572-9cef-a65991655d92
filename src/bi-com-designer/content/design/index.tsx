import { classnames } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import { When } from 'react-if';
import { Design as ComDesign } from '../../../base-designer';
import { prefixCls } from '../../constants';
import { DesignerStore } from '../../store';
import Wrapper from './Wrapper';

interface DesignProps extends React.Attributes {
  designStore: DesignerStore;
  className?: string;
}
@inject('designStore')
@observer
class Design extends PureComponent<DesignProps> {
  render() {
    const { selectedCom, updateComConfig } = this.props.designStore.layoutStore;

    return (
      <div className={classnames(this.props.className, `${prefixCls}-design-content`)}>
        <When weId={`${this.props.weId || ''}_ygdy1q`} condition={!!selectedCom}>
          <Wrapper weId={`${this.props.weId || ''}_l9f9h1`} com={selectedCom!}>
            <ComDesign
              weId={`${this.props.weId || ''}_a41j92`}
              {...this.props}
              data={toJS(selectedCom)!}
              // 兼容老eb组件
              onChange={updateComConfig}
              onConfigChange={updateComConfig}
            />
          </Wrapper>
        </When>
      </div>
    );
  }
}

export default Design as unknown as ComponentType<{ className?: string }>;
