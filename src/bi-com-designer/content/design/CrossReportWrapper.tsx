import { Dialog, FormDatas } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, {
  ComponentType, PureComponent, useCallback, useEffect, useState,
} from 'react';
import { Else, If, Then } from 'react-if';
import { ReactSortable, SortableEvent } from 'react-sortablejs';
import { noop } from '../../../constants';
import { BI_FIELD_DRAG, prefixCls } from '../../constants';
import { DesignerStore } from '../../store';
import { addFieldValidate } from '../crossreport-axis/Axis';
import { AxisType } from '../crossreport-axis/constants';
import FieldItem from '../crossreport-axis/FieldItem';
import { getFieldData } from '../crossreport-axis/utils';

export const group = {
  name: 'bicomDesigenrfield',
  pull: true,
  put: ['fieldlist'],
};

interface EmptyPorps extends React.Attributes {
  selectedComId?: string;
  events: any;
  verticalDimensions: any[];
  rowDimensions: any[];
  measures: any[];
  onChange: (formDatas: FormDatas) => void;
}

interface ItemProps extends React.Attributes {
  type: keyof typeof AxisType;
  placeholder: string;
  fields: any[];
  onChange: (fields: any[]) => void;
}

const Item: React.FC<ItemProps> = (props) => {
  const {
    type, fields, placeholder, onChange,
  } = props;
  const [showPlaceholder, setShowPlaceholder] = useState<boolean>(true);
  const ref = React.createRef<HTMLDivElement>();

  const callback = useCallback((mutationsList) => {
    if (mutationsList[0]?.type === 'childList') {
      if (mutationsList[0]?.removedNodes?.length) {
        setShowPlaceholder(true);
      } else if (mutationsList[0]?.addedNodes?.length) {
        setShowPlaceholder(false);
      }
    }
  }, []);

  useEffect(() => {
    const mutationObserver = new MutationObserver(callback);

    mutationObserver.observe(ref.current!, { childList: true, subtree: true });

    return () => {
      mutationObserver.disconnect();
    };
  }, []);

  const onAdd = useCallback(
    (e: SortableEvent) => {
      const { newIndex } = e;
      const field = JSON.parse(e.clone.getAttribute('data-field')) as any;
      const _field = getFieldData(type, field);
      const _fields = toJS(fields);
      const { validate, msg } = addFieldValidate(field, fields, type);

      if (!validate) {
        Dialog.message({
          type: 'error',
          content: msg,
        });

        return;
      }

      _fields.splice(newIndex, 0, _field);

      onChange(_fields);
    },
    [fields],
  );

  const onEnd = useCallback(
    (e: SortableEvent) => {
      const { newIndex, oldIndex } = e;

      const _fields = toJS(fields);
      const field = _fields.splice(oldIndex, 1);

      _fields.splice(newIndex, 0, field[0]);

      onChange(_fields);
    },
    [fields],
  );

  const onRemove = useCallback(
    (index: number) => () => {
      const _fields = toJS(fields);

      _fields.splice(index, 1);
      onChange(_fields);
    },
    [fields],
  );

  return (
    <div
      ref={ref}
      className={`${prefixCls}-crossreport-axis-${type} ${prefixCls}-crossreoprt-axis`}
    >
      <ReactSortable
        weId={`${props.weId || ''}_mzkj82`}
        list={fields}
        setList={noop}
        animation={100}
        group={group}
        className={`${prefixCls}-crossreport-content-design-item-sortable`}
        onAdd={onAdd}
        onEnd={onEnd}
      >
        {fields.map((field: any, index: number) => (
          <FieldItem
            weId="_4h3s8w"
            index={index}
            key={field.uid}
            field={field}
            type={type}
            onRemove={onRemove(index)}
          />
        ))}
      </ReactSortable>
      <div
        className={classnames(`${prefixCls}-crossreport-content-design-item-empty`, {
          showplaceholder: showPlaceholder && !fields.length,
        })}
      >
        {placeholder}
      </div>
    </div>
  );
};

const Empty: React.FC<EmptyPorps> = (props) => {
  const {
    verticalDimensions, rowDimensions, measures, events, selectedComId, onChange,
  } = props;

  const onFieldChange = useCallback(
    (type: string) => (fields: any) => {
      onChange({ [type]: fields });

      // 主动触发视图层更新
      if (type !== 'conditionItems') {
        events.emit('propChange_columnsChange', selectedComId);
      }

      events.emit(`propChange_${type}`, selectedComId, fields);
    },
    [],
  );

  return (
    <div className={`${prefixCls}-crossreport-content-design-content-empty`}>
      <div>
        <div />
        <Item
          weId={`${props.weId || ''}_cljmrx`}
          type="verticalDimensions"
          fields={verticalDimensions}
          placeholder={getLabel('252711', '拖动生成列维度项')}
          onChange={onFieldChange('verticalDimensions')}
        />
      </div>
      <div>
        <Item
          weId={`${props.weId || ''}_cljmrx`}
          type="rowDimensions"
          fields={rowDimensions}
          placeholder={getLabel('252712', '拖动生成行维度项')}
          onChange={onFieldChange('rowDimensions')}
        />
        <Item
          weId={`${props.weId || ''}_cljmrx`}
          type="measures"
          fields={measures}
          placeholder={getLabel('233086', '度量项')}
          onChange={onFieldChange('measures')}
        />
      </div>
    </div>
  );
};

@inject('designStore')
@observer
class CrossReportWrapper extends PureComponent<{ designStore: DesignerStore }, { stauts: string }> {
  state = { stauts: 'draggend' };

  componentDidMount() {
    const { events } = this.props.designStore;

    events.on(BI_FIELD_DRAG, this.onDragStatusChange);
  }

  componentWillUnmount() {
    const { events } = this.props.designStore;

    events.off(BI_FIELD_DRAG, this.onDragStatusChange);
  }

  onDragStatusChange = (stauts: string) => {
    this.setState({ stauts });
  };

  render() {
    const { layoutStore, events } = this.props.designStore;
    const { selectedCom, updateComConfig } = layoutStore;
    const { verticalDimensions = [], rowDimensions = [], measures = [] } = selectedCom!.config;
    const { stauts } = this.state;

    return (
      <div className={classnames(`${prefixCls}-crossreport-content-design-content`, stauts)}>
        <If
          weId={`${this.props.weId || ''}_4htnt0`}
          condition={![...verticalDimensions, ...rowDimensions].length || !measures.length}
        >
          <Then weId={`${this.props.weId || ''}_yiaqz9`}>
            <Empty
              weId={`${this.props.weId || ''}_qrqy7d`}
              selectedComId={selectedCom?.id}
              events={events}
              verticalDimensions={verticalDimensions}
              rowDimensions={rowDimensions}
              measures={measures}
              onChange={updateComConfig}
            />
          </Then>
          <Else weId={`${this.props.weId || ''}_0bu5k6`}>{this.props.children}</Else>
        </If>
      </div>
    );
  }
}

export default CrossReportWrapper as unknown as ComponentType<{}>;
