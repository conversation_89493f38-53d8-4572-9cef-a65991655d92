import { Dialog } from '@weapp/ui';
import { classnames, getLabel, isUndefined } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import { UUID } from '../../../utils';
import {
  charts, comAxis, FieldType, prefixCls,
} from '../../constants';
import { DesignerStore } from '../../store';
import { DateRangeMode, SystemValue } from '../constants';
import { verifyComConfig } from '../design/ChartWrapper';
import FieldList from '../field-list';
import {
  BarType, DateGroupType, OrderType, supportCalcDate,
} from '../field-list/constants';
import { isSupportYearGroupDate } from '../field-list/utils';
import { getDimensionField } from './XAxis';

interface DimensionsProps extends React.Attributes {
  designStore: DesignerStore;
}

// 单选的项
const singleItems = ['showMissingDate'];

const genUUID = () => {
  const _uuid = (): string => {
    const uuid = `f${UUID(6)}`;
    return uuid;
  };
  return _uuid();
};

// 作为默认measure
export const getDefDimensions = (barType?: BarType) => ({
  axisId: UUID(),
  shortKey: genUUID(),
  // 同比柱状图维度项默认升序排序
  orderType: barType && barType === BarType.YOY ? OrderType.ASC : OrderType.DEFAULT,
});

// 是否为同比柱状图
// eslint-disable-next-line max-len
const isYoyBarType = (type: string, barType: string) => type === charts.Bar && barType === BarType.YOY;

@inject('designStore')
@observer
class Dimensions extends PureComponent<DimensionsProps> {
  onChange = (changes: Record<string, any>) => {
    const { updateComConfig } = this.props.designStore.layoutStore;

    updateComConfig(changes);
  };

  onFieldChange = (index: number, _changes: Record<string, any>) => {
    const { selectedCom } = this.props.designStore.layoutStore;
    const {
      config: {
        type, barType, dimensionType, yoyDimensions, dimensions,
      },
    } = selectedCom!;
    const _dimensions = toJS(dimensions);
    const dimension = _dimensions[index];

    const changes: Record<string, any> = {};

    Object.keys(_changes).forEach((key) => {
      const val = _changes[key];

      if (singleItems.includes(key)) {
        // 针对单选的项，选中后添加对应的key，取消选中后删除key
        if (!dimension[key]) {
          dimension[key] = val;
        } else {
          delete dimension[key];
        }
      } else {
        if (dimension.yearGroup && key === 'dateGroupType' && !isSupportYearGroupDate(val)) {
          dimension.yearGroup = false;
        }
        dimension[key] = val;
      }

      dimension[key] = val;
    });

    const { dateGroupType } = dimension;

    // 指标项不支持除了年、月、日以外的日期分组
    if (supportCalcDate.includes(dateGroupType)) {
      // 日历热力图：更新日期分组后，修改日期选项默认值
      if (type === charts.HeatMap) {
        const dateRange = {
          mode: DateRangeMode.System,
          value: SystemValue.CurYear,
        };

        changes.dateRange = dateRange;
      } else if (isYoyBarType(type, barType) && dimensionType !== 'YoYDimension') {
        changes.yoyDimensions = yoyDimensions;
      }
    }

    _dimensions.splice(index, 1, dimension);

    changes.dimensions = _dimensions;

    this.onChange(changes);
  };

  onFieldAdd = (index: number, field: any) => {
    const { sourceType, layoutStore } = this.props.designStore;
    // 校验字段是否能拖入维度项中
    if (sourceType === 'targetcard' && field._targetCardFieldType !== 'dimension') {
      Dialog.message({
        type: 'info',
        content: getLabel('269807', '请拖入维度字段'),
      });

      return;
    }

    const { type, config } = layoutStore.selectedCom!;
    const _dimensions = toJS(config.dimensions);

    const changes: Record<string, any> = {};

    const dimension = getDimensionField(field);

    const isDateField = field.type === FieldType.FDate || field.type === FieldType.CDate;

    if (config.type === charts.Pie && dimension.type === FieldType.FDepartment) {
      dimension.statChildren = false;
    }

    // 如果是同比柱状图：
    // 1. 日期分类只显示：按季度和按月份（默认选中按月份）
    // 2. 按年统计默认为是，且隐藏按年统计选项
    if (isYoyBarType(type, config.barType)) {
      if (config.dimensionType === 'Dimension' && isDateField && dimension.formatType !== '1') {
        dimension.dateGroupType = DateGroupType.SINGLEMONTH;
      }
      // 如果是同比，日期分类为 按年，日期分类隐藏
      if (config.dimensionType === 'YoYDimension') {
        dimension.dateGroupType = DateGroupType.YEAR;
      }
      if (isDateField) {
        dimension.yearGroup = true;
      }
    }

    _dimensions.splice(index, 0, dimension);

    changes.dimensions = _dimensions;

    // 只做提示不做阻止拖拽的行为
    if (
      !isUndefined(comAxis[config.type].dimensions.max)
      && (comAxis[config.type].dimensions.max as number) < _dimensions.length
    ) {
      const { text } = verifyComConfig(config.type, { dlen: _dimensions.length }, config);

      if (text) {
        Dialog.message({ type: 'info', content: text });
      }
    }

    this.onChange(changes);
  };

  onFieldDelete = (index: number) => {
    const { config } = this.props.designStore.layoutStore.selectedCom!;
    const _dimensions = toJS(config.dimensions);

    _dimensions.splice(index, 1);

    this.onChange({ dimensions: _dimensions });
  };

  onFieldSort = (oldIndex: number, newIndex: number) => {
    const { config } = this.props.designStore.layoutStore.selectedCom!;
    const _dimensions = toJS(config.dimensions);

    const field = _dimensions.splice(oldIndex, 1);

    _dimensions.splice(newIndex, 0, field[0]);

    this.onChange({ dimensions: _dimensions });
  };

  render() {
    const { config, id } = this.props.designStore.layoutStore.selectedCom!;
    const dimensions = toJS(config.dimensions);
    let maxLen = comAxis[config.type].dimensions.max;

    maxLen = config.type === charts.Scatter ? 1 : maxLen;

    return (
      <FieldList
        weId={`${this.props.weId || ''}_4cwkmd`}
        className={classnames(
          `${prefixCls}-chart-content-axis-${config.type}`,
          `${prefixCls}-chart-content-dimensions`,
        )}
        type="dimensions"
        maxLen={maxLen}
        fields={dimensions}
        comId={id}
        comType={config.type}
        onFieldDelete={this.onFieldDelete}
        onFieldSort={this.onFieldSort}
        onFieldChange={this.onFieldChange}
        onFieldAdd={this.onFieldAdd}
      />
    );
  }
}

export default Dimensions as unknown as ComponentType<{}>;
