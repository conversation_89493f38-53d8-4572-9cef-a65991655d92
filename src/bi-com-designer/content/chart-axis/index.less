@import '../../../style/prefix.less';

.@{bi-prefix}-chart-content {
  &-field-opt {
    padding: 0 5px;
    border-bottom: 1px solid var(--de-box-lineColor);

    & > div:last-of-type {
      border-bottom: 0px;
    }

    &.dragging {
      background: #ecf6ff;

      .@{bi-prefix}-content-axis {
        &-fields-placeholder {
          color: #999;

          &.enter {
            color: #ecf6ff;
          }
        }

        .@{bi-prefix}-charts-config-x-select {
          .ui-select-input {
            background: #ecf6ff;
          }
        }
      }
    }
  }

  &-axis-Scatter {
    & > span {
      width: 120px;
    }

    .@{bi-prefix}-chart-content-axis {
      &-fields-placeholder {
        padding-left: 150px !important;
      }
    }
  }

  &-dimensions,
  &-measures {
    min-height: 45px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    font-size: var(--font-size-12);
    position: relative;
    color: var(--m-readonly-text);

    &-fields {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      & > div.sortable-chosen {
        position: relative;
        z-index: 1;
        display: flex;
        align-items: center;
        padding: 0px !important;

        & > .ui-icon {
          display: none !important;
        }
      }

      & > div {
        margin-left: 12px;
        padding: 5px 0;
      }

      &-item-content {
        flex: 1;
        display: flex;

        & > span {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        &-showtext {
          max-width: 200px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          display: inline-block !important;
        }
      }

      &-item-icon {
        margin-left: 8px;
        display: block !important;
        color: #b9babb;
      }
    }

    .@{bi-prefix}-chart-content-dimensions-fields {
      & > div.sortable-chosen > span,
      &-item {
        padding: 0 10px;
        cursor: pointer;
        display: flex;
        align-items: center;
        line-height: 23px;
        min-width: 88px;
        background-color: rgba(195, 219, 255, 0.3);
        color: #5d9cec;
        border: 1px solid rgba(195, 219, 255, 1);
        border-radius: 3px;
      }
    }

    .@{bi-prefix}-chart-content-measures-fields {
      & > div.sortable-chosen > span,
      &-item {
        padding: 0 10px;
        display: inline-block;
        cursor: pointer;
        display: flex;
        align-items: center;
        line-height: 23px;
        min-width: 88px;
        background-color: rgba(133, 239, 209, 0.3);
        color: #40d8ad;
        border: 1px solid rgba(133, 239, 209, 1);
        border-radius: 3px;
      }
    }
  }

  .@{bi-prefix}-chart-xaxis-title {
    .ui-select-input {
      border: none;
      box-shadow: none;
    }
  }
}
