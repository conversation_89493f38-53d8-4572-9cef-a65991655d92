import { Dialog, Select } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, {
  ComponentType, FC, useCallback, useMemo, useState,
} from 'react';
import { FieldType, prefixCls } from '../../constants';
import { DesignerStore } from '../../store';
import { hasMeasuresData } from '../../utils';
import FieldList from '../field-list';
import { Aggregator, DataShowWay, DateGroupType } from '../field-list/constants';
import { isSupportGroupField, isSupportYearGroupDate } from '../field-list/utils';
import { getDefDimensions } from './Dimensions';
import { getDefMeasure } from './Measures';

export type XAxisValue = any;

export interface XAxisProps extends React.Attributes {
  designStore: DesignerStore;
}

export type ScatterType = 'measures' | 'dimensions';

const { COUNT, SUM } = Aggregator;

// 指标默认值
const meaField = {
  ...getDefMeasure(),
};

export const getDimensionField = (field: any) => {
  field = { ...field, ...getDefDimensions() };

  const isDateField = field.type === FieldType.FDate || field.type === FieldType.CDate;

  if (isDateField) {
    // 年
    if (field.formatType === '1') {
      field.dateGroupType = DateGroupType.YEAR;
    } else if (field.formatType === '2') {
      // 年月
      field.dateGroupType = DateGroupType.MONTH;
    } else if (!['7', '8'].includes(field.formatType)) {
      field.dateGroupType = DateGroupType.DAY;
    } else {
      delete field.dateGroupType;
    }
  } else {
    // 其他字段类型不需要这日期字段的属性
    delete field.formatType;
    delete field.dateGroupType;
  }

  delete field.statChildren;

  if (field.type === FieldType.FSelect || field.type === FieldType.CSelect) {
    field.dataShowWay = DataShowWay.BYRESULT;
  } else {
    // 其他字段类型不需要这下拉框字段的属性
    delete field.dataShowWay;
  }
  if (field.yearGroup && !isSupportYearGroupDate(field.dateGroupType)) {
    field.yearGroup = false;
  }

  return field;
};

const XAxis: FC<XAxisProps> = (props) => {
  const { selectedCom, updateComConfig } = props.designStore.layoutStore;
  const { config, id } = selectedCom!;
  const { yAxis = meaField, xAxis } = config;
  const [typeSetting, setType] = useState<ScatterType>('measures');

  if (xAxis?.type !== undefined && xAxis.type !== typeSetting) {
    setType(xAxis.type);
  }

  const typeSettingData = useMemo(
    () => [
      {
        id: 'measures',
        content: getLabel('55486', '指标'),
        title: getLabel('55486', '指标'),
      },
      {
        id: 'dimensions',
        content: getLabel('55478', '维度'),
        title: getLabel('55478', '维度'),
      },
    ],
    [],
  );

  const yAxisData = useMemo(() => {
    if (hasMeasuresData(yAxis)) {
      return yAxis;
    }

    return null;
  }, [yAxis]);

  const fields = useMemo(
    () => (xAxis?.data && hasMeasuresData(xAxis.data) ? [toJS(xAxis?.data)] : []),
    [xAxis],
  );

  const onFieldDelete = useCallback(() => {
    const xAxisValue = {
      type: typeSetting,
      data: {},
    };

    // 同步修改measures属性，便于数据处理
    const cloneMeasures = yAxisData ? [yAxisData] : [];
    updateComConfig({ measures: cloneMeasures, xAxis: xAxisValue });
  }, [typeSetting, yAxisData]);

  const onFieldSort = useCallback(() => {}, []);

  const onFieldChange = useCallback(
    (index: number, changes: Record<string, any>) => {
      const xAxisValue = {
        type: typeSetting,
        data: { ...xAxis.data, ...changes },
      };

      // 同步修改measures属性，便于数据处理
      const cloneMeasures = yAxisData ? [xAxisValue.data, yAxisData] : [xAxisValue.data];
      updateComConfig({ measures: cloneMeasures, xAxis: xAxisValue });
    },
    [typeSetting, yAxisData],
  );

  const handleSelectChange = useCallback(
    (selectValue: any) => {
      const xAxisValue = {
        type: selectValue,
        data: {},
      };

      setType(selectValue);

      // 同步修改measures属性，便于数据处理
      const cloneMeasures = yAxisData ? [yAxisData] : [];
      updateComConfig({ measures: cloneMeasures, xAxis: xAxisValue });
    },
    [yAxisData],
  );

  const onFieldAdd = useCallback(
    (index: number, field: any) => {
      if (index >= 1) {
        Dialog.message({
          type: 'info',
          content: '无法设置多个X轴字段',
        });
      } else if (typeSetting === 'measures') {
        field = { ...field, ...getDefMeasure() };

        if (field.type === FieldType.FNumber || field.type === FieldType.CNumber) {
          field.aggregator = SUM;
        } else {
          field.aggregator = COUNT;
        }
        if (!isSupportGroupField(field)) {
          field.group = false;
        }
      } else {
        field = getDimensionField(field);
      }

      field.isDimension = typeSetting === 'dimensions';

      const xAxisValue = {
        type: typeSetting,
        data: field,
      };

      // 同步修改measures属性，便于数据处理
      const cloneMeasures = yAxisData ? [field, yAxisData] : [field];
      updateComConfig({ measures: cloneMeasures, xAxis: xAxisValue });
    },
    [typeSetting, yAxisData],
  );

  const customRenderTitle = useCallback(
    () => (
      <span className={`${prefixCls}-chart-xaxis-title`}>
        {getLabel('69011', 'X轴')} (
        <Select
          weId={`${props.weId || ''}_ljj0d1`}
          data={typeSettingData}
          value={typeSetting}
          onChange={handleSelectChange}
          className={`${prefixCls}-charts-config-x-select`}
        />
        )
      </span>
    ),
    [typeSetting],
  );

  return (
    <FieldList
      weId={`${props.weId || ''}_4cwkmd`}
      className={classnames(
        `${prefixCls}-chart-content-axis-${config.type}`,
        `${prefixCls}-chart-content-${typeSetting}`,
      )}
      type={typeSetting}
      comId={id}
      fields={fields}
      maxLen={1}
      comType={config.type}
      onFieldDelete={onFieldDelete}
      onFieldSort={onFieldSort}
      onFieldChange={onFieldChange}
      onFieldAdd={onFieldAdd}
      customRenderTitle={customRenderTitle}
    />
  );
};

const _XAxis = inject('designStore')(observer(XAxis));

export default _XAxis as unknown as ComponentType<{}>;
