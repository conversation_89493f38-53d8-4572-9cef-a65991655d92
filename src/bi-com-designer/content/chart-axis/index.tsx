import React, { PureComponent } from 'react';
import { When } from 'react-if';
import {
  hasNoDimensionsChartTypes,
  hasNoMeasuresChartTypes,
  hasXAxisChartTypes,
} from '../../constants';
import Dimensions from './Dimensions';
import './index.less';
import Measures from './Measures';
import XAxis from './XAxis';
import YAxis from './YAxis';

export default class ChartAxis extends PureComponent<{ type: string }> {
  render() {
    const { type } = this.props;

    return (
      <>
        <When
          weId={`${this.props.weId || ''}_py25t4`}
          condition={!hasNoDimensionsChartTypes.includes(type)}
        >
          <Dimensions weId={`${this.props.weId || ''}_ja488r`} />
        </When>

        <When
          weId={`${this.props.weId || ''}_6eiggm`}
          condition={!hasNoMeasuresChartTypes.includes(type)}
        >
          <Measures weId={`${this.props.weId || ''}_nw7s5v`} />
        </When>

        <When
          weId={`${this.props.weId || ''}_9skpu4`}
          condition={hasXAxisChartTypes.includes(type)}
        >
          <XAxis weId={`${this.props.weId || ''}_ntguf9`} />
        </When>

        <When
          weId={`${this.props.weId || ''}_9skpu4`}
          condition={hasXAxisChartTypes.includes(type)}
        >
          <YAxis weId={`${this.props.weId || ''}_ntguf9`} />
        </When>
      </>
    );
  }
}
