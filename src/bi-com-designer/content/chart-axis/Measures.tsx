import { Dialog } from '@weapp/ui';
import { classnames, getLabel, isUndefined } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import { UUID } from '../../../utils';
import { comAxis, prefixCls } from '../../constants';
import { DesignerStore } from '../../store';
import { verifyComConfig } from '../design/ChartWrapper';
import FieldList from '../field-list';
import {
  Aggregator,
  ComposeChart,
  COMPOSECHART,
  FieldType,
  FormatType,
  OrderType,
} from '../field-list/constants';
import { isSupportGroupField } from '../field-list/utils';

const { message } = Dialog;

interface MeasuresProps extends React.Attributes {
  designStore: DesignerStore;
}

const { COUNT, SUM } = Aggregator;

// 作为默认measure
export const getDefMeasure = () => ({
  axisId: UUID(),
  orderType: OrderType.DEFAULT,
  shortKey: `f${UUID(6)}`,
  aggregator: COUNT,
  composeChart: ComposeChart.NONE,
  format: FormatType.NONEFORMAT,
});

@inject('designStore')
@observer
class Measures extends PureComponent<MeasuresProps> {
  onChange = (changes: Record<string, any>) => {
    const { updateComConfig } = this.props.designStore.layoutStore;

    updateComConfig(changes);
  };

  onFieldChange = (index: number, changes: Record<string, any>) => {
    const { config } = this.props.designStore.layoutStore.selectedCom!;
    const _measures = toJS(config.measures);

    let measure = _measures[index];

    measure = { ...measure, ...changes };

    _measures.splice(index, 1, measure);

    if (Object.keys(changes).includes(COMPOSECHART)) {
      const composeChartM = _measures.find(
        (_measure: any) => !_measure.composeChart || _measure.composeChart === ComposeChart.NONE,
      );

      if (!composeChartM) {
        message({
          type: 'info',
          content: getLabel('116339', '无法设置！组合图表至少保证有一个指标为当前图表类型！'),
        });
        return;
      }
    }

    this.onChange({ measures: _measures });
  };

  onFieldAdd = (index: number, field: any) => {
    const { sourceType, layoutStore } = this.props.designStore;

    // 校验字段是否能拖入度量项中
    if (sourceType === 'targetcard' && field._targetCardFieldType !== 'measure') {
      Dialog.message({
        type: 'info',
        content: getLabel('269808', '请拖入度量字段'),
      });

      return;
    }

    const { config } = layoutStore.selectedCom!;
    const _measures = toJS(config.measures);

    field = { ...field, ...getDefMeasure() };

    if (field.type === FieldType.FNumber || field.type === FieldType.CNumber) {
      field.aggregator = SUM;
    } else {
      field.aggregator = COUNT;
    }
    if (!isSupportGroupField(field)) {
      field.group = false;
    }

    _measures.splice(index, 0, field);

    if (
      !isUndefined(comAxis[config.type].measures.max)
      && (comAxis[config.type].measures.max as number) < _measures.length
    ) {
      const { text } = verifyComConfig(config.type, { mlen: _measures.length }, config);

      if (text) {
        Dialog.message({ type: 'info', content: text });
      }
    }

    let changes: Record<string, any> = { measures: _measures };

    if (config.choseLabels === undefined) {
      const choseLabels = _measures.map((measure: any) => measure.id);

      changes = { ...changes, choseLabels };
    } else {
      const choseLabels = toJS(config.choseLabels || []);

      choseLabels.push(field.id);
      changes = { ...changes, choseLabels };
    }

    this.onChange(changes);
  };

  onFieldDelete = (index: number) => {
    const { config } = this.props.designStore.layoutStore.selectedCom!;
    const _measures = toJS(config.measures);

    const notComposeM = _measures.find(
      (item: any) => !item.composeChart || item.composeChart === ComposeChart.NONE,
    );

    if (_measures.length && !notComposeM) {
      message({
        content: getLabel('116576', '无法删除！组合图表至少保证有一个指标为当前图表类型！'),
      });

      return;
    }

    const measure = _measures.splice(index, 1);
    let changes: Record<string, any> = { measures: _measures };

    if (config.choseLabels === undefined) {
      const choseLabels = _measures.map((_measure: any) => _measure.id);

      changes = { ...changes, choseLabels };
    } else {
      const choseLabels = toJS(config.choseLabels || []);
      const valIndex = choseLabels.indexOf(measure.id);

      if (valIndex > -1) {
        choseLabels.splice(valIndex, 1);
      }

      changes = { ...changes, choseLabels };
    }

    this.onChange(changes);
  };

  onFieldSort = (oldIndex: number, newIndex: number) => {
    const { config } = this.props.designStore.layoutStore.selectedCom!;
    const _measures = toJS(config.measures);

    const field = _measures.splice(oldIndex, 1);

    _measures.splice(newIndex, 0, field[0]);

    this.onChange({ measures: _measures });
  };

  render() {
    const { config, id } = this.props.designStore.layoutStore.selectedCom!;
    const measures = toJS(config.measures);
    const maxLen = comAxis[config.type].measures.max;

    return (
      <FieldList
        weId={`${this.props.weId || ''}_4cwkmd`}
        type="measures"
        className={classnames(
          `${prefixCls}-chart-content-axis-${config.type}`,
          `${prefixCls}-chart-content-measures`,
        )}
        comId={id}
        fields={measures}
        maxLen={maxLen}
        comType={config.type}
        onFieldDelete={this.onFieldDelete}
        onFieldSort={this.onFieldSort}
        onFieldChange={this.onFieldChange}
        onFieldAdd={this.onFieldAdd}
      />
    );
  }
}

export default Measures as unknown as ComponentType<{}>;
