import { Dialog } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, {
  ComponentType, FC, useCallback, useMemo,
} from 'react';
import { FieldType, prefixCls } from '../../constants';
import { DesignerStore } from '../../store';
import { hasMeasuresData } from '../../utils';
import FieldList from '../field-list';
import { Aggregator } from '../field-list/constants';
import { isSupportGroupField } from '../field-list/utils';
import { getDefMeasure } from './Measures';

export type YAxisValue = any;

export interface YAxisProps extends React.Attributes {
  designStore: DesignerStore;
}

const { COUNT, SUM } = Aggregator;

// 指标默认值
const meaField = {
  ...getDefMeasure(),
};

const YAxis: FC<YAxisProps> = (props) => {
  const { selectedCom, updateComConfig } = props.designStore.layoutStore;
  const { config, id } = selectedCom!;

  const {
    xAxis = {
      data: meaField,
    },
    yAxis,
  } = config!;

  const xAxisData = useMemo(() => {
    if (hasMeasuresData(xAxis?.data)) {
      return xAxis?.data;
    }

    return null;
  }, [xAxis?.data]);

  const fields = useMemo(() => (yAxis && hasMeasuresData(yAxis) ? [toJS(yAxis)] : []), [yAxis]);

  const onFieldDelete = useCallback(() => {
    const cloneMeasures = xAxisData ? [xAxisData] : [];

    updateComConfig?.({ measures: cloneMeasures, yAxis: {} });
  }, [xAxisData]);

  const onFieldSort = useCallback(() => {}, []);

  const onFieldChange = useCallback(
    (index: number, changes: Record<string, any>) => {
      const yAxisValue = { ...yAxis, ...changes };
      const cloneMeasures = xAxisData ? [xAxisData, yAxisValue] : [yAxisValue];

      updateComConfig?.({ measures: cloneMeasures, yAxis: yAxisValue });
    },
    [xAxisData],
  );

  const onFieldAdd = useCallback(
    (index: number, field: any) => {
      if (index >= 1) {
        Dialog.message({
          type: 'info',
          content: '无法设置多个Y轴字段',
        });
      } else {
        field = { ...field, ...getDefMeasure() };

        if (field.type === FieldType.FNumber || field.type === FieldType.CNumber) {
          field.aggregator = SUM;
        } else {
          field.aggregator = COUNT;
        }
        if (!isSupportGroupField(field)) {
          field.group = false;
        }

        const cloneMeasures = xAxisData ? [xAxisData, field] : [field];

        updateComConfig?.({ measures: cloneMeasures, yAxis: field });
      }
    },
    [xAxisData],
  );

  const customRenderTitle = useCallback(() => getLabel('69012', 'Y轴') as any, []);

  return (
    <FieldList
      weId={`${props.weId || ''}_4cwkmd`}
      className={classnames(
        `${prefixCls}-chart-content-axis-${config.type}`,
        `${prefixCls}-chart-content-measures`,
      )}
      type="measures"
      comId={id}
      fields={fields}
      maxLen={1}
      comType={config.type}
      onFieldDelete={onFieldDelete}
      onFieldSort={onFieldSort}
      onFieldChange={onFieldChange}
      onFieldAdd={onFieldAdd}
      customRenderTitle={customRenderTitle}
    />
  );
};

const _YAxis = inject('designStore')(observer(YAxis));

export default _YAxis as unknown as ComponentType<{}>;
