import { DataSet } from '@weapp/ebdcoms';
import {
  CorsComponent, Dialog, Icon, Menu, MenuItemData,
} from '@weapp/ui';
import {
  classnames, cloneDeep, getLabel, includes, isArray, isEmpty,
} from '@weapp/utils';
import { toJS } from 'mobx';
import React, { PureComponent, SyntheticEvent, useCallback } from 'react';
import { Else, If, Then } from 'react-if';
import { FieldListType } from '.';
import {
  openConditionSet,
  transFormConditionValue,
} from '../../../base-designer/panels/dataset/utils';
import { UUID } from '../../../utils';
import {
  charts,
  prefixCls,
  supportAddCalculatedData,
  supportComposeCharts,
  supportShowMissTimeData,
} from '../../constants';
import { SourceType } from '../../types';
import AliasDialog from './AliasDialog';
import {
  Aggregator,
  AxisOptType,
  BarType,
  CalculationFieldSource,
  COMPOSECHART,
  ComposeChart,
  CountType,
  DataShowWay,
  DateGroupType,
  defaultUnit,
  FieldType,
  FORMAT,
  FormatType,
  GROUP,
  OrderType,
  supportCalcDate,
  supportCalcDateWithBase,
  UNIT,
  UnitTypeData,
} from './constants';
import {
  getLocaleValue,
  isETSqlDatasource,
  isGrouped,
  isSupportGroup,
  isSupportGroupField,
  isSupportYearGroup,
} from './utils';

const { confirm } = Dialog;
interface FieldMenuProps {
  chartType?: string;
  type: FieldListType;
  axis: any;
  config?: any;
  dataset: DataSet;
  dimensions?: any[];
  fields?: any[];
  index?: number;
  comId: string;
  pageId?: string;
  sourceType: SourceType;
  onChange: (changes: Record<string, any>) => void;
  onRemove: () => void;
}

interface FieldMenuStates {
  aliasVisible: boolean;
  calculationVisible: boolean;
  customUnitVisible: boolean;
  filterDlgVisible: boolean;
  calculationField: any;
  unit: null | any;
}
interface ItemProps extends React.Attributes {
  attr: string;
  text: string;
  type: string;
  selected: boolean;
  showDot?: boolean;
  onClick?: (value: any) => void;
}

interface UnitItemProps extends ItemProps {
  onUnitSelect: (key: string) => void;
  showCustomUniDlg: (key: string) => void;
  onUnitDelete: (key: string, text: string) => void;
}

interface ParentItemProps extends React.Attributes {
  attr?: string;
  text: string;
  showDot: boolean;
  className?: string;
  onClick?: (attr: string) => void;
}

interface MeaSureMenuChild {
  key: Aggregator | FormatType | CountType | ComposeChart | OrderType | boolean;
  type: string;
  text: string;
}

interface MeaSureMenuData {
  key: Aggregator | OrderType | string;
  type: string;
  text: string;
  groupId: string;
  children?: MeaSureMenuChild[];
}

const dataShowWay = 'dataShowWay';
export const showMissingDate = 'showMissingDate';
const { DEFAULT, DESC, ASC } = OrderType;
export const {
  ALIAS,
  DATEGROUP,
  ADDCALCULATED,
  YEARGROUP,
  STATCHILDREN,
  DELETE,
  SUMMARY,
  SORTORDER,
} = AxisOptType;
const {
  SUM, AVG, MAX, MIN, COUNT, DISTINCTCOUNT, AGGDEFAULT, FILTER,
} = Aggregator;
const {
  THOUSANDS, PERCENTAGEFORD, PERCENTAGEFORM, PERCENTAGE, NONEFORMAT,
} = FormatType;
const {
  YEAR,
  MONTH,
  DAY,
  SINGLESEASON,
  SINGLEMONTH,
  SINGLEWEEKS,
  SINGLEWEEK,
  MONTHDAY,
  SINGLEDAY,
} = DateGroupType;
const { BYALL, BYRESULT } = DataShowWay;

const Item = ({
  attr, text, selected, showDot, type, onClick,
}: ItemProps) => {
  const onMenuClick = useCallback(() => {
    if (onClick) {
      onClick(attr);
    }
  }, []);

  return (
    <div
      className={classnames(`${prefixCls}-field-menu-item`, {
        selected: selected && (type !== FORMAT || attr !== AGGDEFAULT) && attr !== 'nothing',
        dot: showDot,
      })}
      onClick={onMenuClick}
    >
      {text}
    </div>
  );
};

const UnitItem = ({
  attr,
  text,
  weId,
  selected,
  onUnitDelete,
  showCustomUniDlg,
  onUnitSelect,
}: UnitItemProps) => {
  const onDelete = useCallback((e: SyntheticEvent) => {
    e.stopPropagation();

    onUnitDelete(attr, text);
  }, []);

  const onShow = useCallback((e: SyntheticEvent) => {
    e.stopPropagation();

    showCustomUniDlg(attr);
  }, []);

  const onSelect = useCallback(() => {
    onUnitSelect(attr);
  }, []);

  return (
    <div
      className={classnames(`${prefixCls}-field-menu-item`, `${prefixCls}-field-menu-item-unit`, {
        selected,
      })}
      onClick={onSelect}
    >
      <span>{text}</span>
      <span>
        <Icon weId={`${weId || ''}_9kkfk4`} name="Icon-edit-o" onClick={onShow} className="edit" />
        <Icon
          weId={`${weId || ''}_tk1g1u`}
          name="Icon-delete02"
          onClick={onDelete}
          className="delete"
        />
      </span>
    </div>
  );
};

const ParentItem = ({
  attr, text, showDot, className, onClick,
}: ParentItemProps) => {
  const onSelect = useCallback(() => {
    onClick?.(attr || '');
  }, []);

  return (
    <div
      className={classnames(className, { [`${prefixCls}-field-menu-p-dot`]: showDot })}
      onClick={onSelect}
    >
      {text}
    </div>
  );
};

export const getDateGroup = (
  field: any,
  chartType?: string,
  barType?: BarType,
  noYearGroup?: boolean,
) => {
  const { formatType } = field;
  const returnList = [];
  const data: any = {
    key: DATEGROUP,
    type: DATEGROUP,
    text: getLabel('70478', '日期分组'),
    groupId: 'dategroup',
    children: [],
  };

  // 年
  if (formatType === '1') {
    data.children = [
      {
        key: YEAR,
        type: 'dateGroupType',
        text: getLabel('70481', '按年'),
      },
    ];
  } else if (formatType === '2') {
    // 年月
    data.children = [
      {
        key: MONTH,
        type: 'dateGroupType',
        text: getLabel('98730', '按年月'),
      },
      {
        key: YEAR,
        type: 'dateGroupType',
        text: getLabel('70481', '按年'),
      },
      {
        key: SINGLESEASON,
        type: 'dateGroupType',
        text: getLabel('98738', '按季度'),
      },
      {
        key: SINGLEMONTH,
        type: 'dateGroupType',
        text: getLabel('98739', '按月份'),
      },
    ];
  } else if (!['7', '8'].includes(formatType)) {
    data.children = [
      {
        key: DAY,
        type: 'dateGroupType',
        text: getLabel('98729', '按年月日'),
      },
      {
        key: MONTH,
        type: 'dateGroupType',
        text: getLabel('98730', '按年月'),
      },
      {
        key: YEAR,
        type: 'dateGroupType',
        text: getLabel('70481', '按年'),
      },
      {
        key: SINGLESEASON,
        type: 'dateGroupType',
        text: getLabel('98738', '按季度'),
      },
      {
        key: SINGLEMONTH,
        type: 'dateGroupType',
        text: getLabel('98739', '按月份'),
      },
      {
        key: SINGLEWEEKS,
        type: 'dateGroupType',
        text: getLabel('98740', '按周数'),
      },
      {
        key: SINGLEWEEK,
        type: 'dateGroupType',
        text: getLabel('98741', '按星期'),
      },
      {
        key: MONTHDAY,
        type: 'dateGroupType',
        text: getLabel('98742', '按月日'),
      },
      {
        key: SINGLEDAY,
        type: 'dateGroupType',
        text: getLabel('70479', '按日'),
      },
    ];
  }

  // 如果是同比柱状图：
  // 1. 日期分类只显示：按季度和按月份（默认选中按月份）
  // 2. 按年统计默认为是，且隐藏按年统计选项
  if (barType && barType === BarType.YOY) {
    if (formatType !== '1') {
      data.children = [
        {
          key: SINGLESEASON,
          type: 'dateGroupType',
          text: getLabel('98738', '按季度'),
        },
        {
          key: SINGLEMONTH,
          type: 'dateGroupType',
          text: getLabel('98739', '按月份'),
        },
      ];

      returnList.push(data);
    }
  } else if (chartType === charts.HeatMap) {
    // 日历热力图仅支持 年月日 年月 年
    data.children = [
      {
        key: DAY,
        type: 'dateGroupType',
        text: getLabel('98729', '按年月日'),
      },
      {
        key: MONTH,
        type: 'dateGroupType',
        text: getLabel('98730', '按年月'),
      },
      {
        key: YEAR,
        type: 'dateGroupType',
        text: getLabel('70481', '按年'),
      },
    ];

    returnList.push(data);
  } else {
    if (data.children.length) {
      returnList.push(data);
    }

    if (isSupportYearGroup(field.dateGroupType, chartType) && !noYearGroup) {
      const yearGroup: any = {
        key: YEARGROUP,
        type: YEARGROUP,
        text: getLabel('114913', '按年统计'),
        groupId: 'dategroup',
        children: [
          {
            key: false,
            type: YEARGROUP,
            text: getLabel('56273', '否'),
          },
          {
            key: true,
            type: YEARGROUP,
            text: getLabel('62694', '是'),
          },
        ],
      };
      returnList.push(yearGroup);
    }
  }
  return returnList;
};

const statChildren = {
  key: STATCHILDREN,
  type: STATCHILDREN,
  text: getLabel('115067', '统计下级'),
  children: [
    {
      key: false,
      type: STATCHILDREN,
      text: getLabel('56273', '否'),
    },
    {
      key: true,
      type: STATCHILDREN,
      text: getLabel('62694', '是'),
    },
  ],
};

const dataShowWayData = {
  key: dataShowWay,
  type: dataShowWay,
  text: getLabel('55463', '数据项显示'),
  children: [
    {
      key: BYRESULT,
      type: dataShowWay,
      text: getLabel('55464', '按查询结果'),
    },
    {
      key: BYALL,
      type: dataShowWay,
      text: getLabel('55465', '显示全部'),
    },
  ],
};

const composeChartData = {
  key: COMPOSECHART,
  type: COMPOSECHART,
  text: getLabel('115404', '组合图表'),
  children: [
    { key: ComposeChart.NONE, type: COMPOSECHART, text: getLabel('56273', '否') },
    { key: ComposeChart.LINE, type: COMPOSECHART, text: getLabel('115399', '折线图组合') },
    { key: ComposeChart.AREA, type: COMPOSECHART, text: getLabel('115400', '面积图组合') },
  ],
  groupId: 'compose',
};

const addCalculatedData = {
  key: ADDCALCULATED,
  type: ADDCALCULATED,
  text: getLabel('100446', '转换成计算'),
  children: [
    { key: CountType.NONE, type: ADDCALCULATED, text: getLabel('55443', '无') },
    { key: CountType.YOY, type: ADDCALCULATED, text: getLabel('56083', '同比') },
    { key: CountType.MOM, type: ADDCALCULATED, text: getLabel('56084', '环比') },
  ],
  groupId: 'calcu',
};

const dimenSionMenuData: any[] = [
  {
    key: DEFAULT,
    type: 'orderType',
    text: getLabel('55466', '默认排序'),
    groupId: '',
  },
  {
    key: DESC,
    type: 'orderType',
    text: getLabel('53956', '降序'),
    groupId: '',
  },
  {
    key: ASC,
    type: 'orderType',
    text: getLabel('53955', '升序'),
    groupId: '',
  },
  {
    key: ALIAS,
    type: ALIAS,
    text: getLabel('55436', '设置别名'),
    groupId: '',
  },
  {
    key: DELETE,
    type: DELETE,
    text: getLabel('53951', '删除'),
    groupId: '',
  },
];

const showMissingDateData = {
  key: showMissingDate,
  type: showMissingDate,
  text: getLabel('98749', '显示缺失时间'),
  groupId: showMissingDate,
};

const meaSureMenuData: MeaSureMenuData[] = [
  {
    key: SUMMARY,
    type: 'aggregator',
    text: getLabel('149194', '汇总方式'),
    groupId: '',
    children: [
      {
        key: SUM,
        type: 'aggregator',
        text: getLabel('55438', '总和'),
      },
      {
        key: AVG,
        type: 'aggregator',
        text: getLabel('55439', '平均值'),
      },
      {
        key: MAX,
        type: 'aggregator',
        text: getLabel('55440', '最大值'),
      },
      {
        key: MIN,
        type: 'aggregator',
        text: getLabel('55441', '最小值'),
      },
      {
        key: COUNT,
        type: 'aggregator',
        text: getLabel('55442', '计数'),
      },
      {
        key: DISTINCTCOUNT,
        type: 'aggregator',
        text: getLabel('99288', '计数(去重)'),
      },
    ],
  },
  {
    key: SORTORDER,
    type: 'orderType',
    text: getLabel('181621', '排序方式'),
    groupId: '',
    children: [
      {
        key: DEFAULT,
        type: 'orderType',
        text: getLabel('55466', '默认排序'),
      },
      {
        key: DESC,
        type: 'orderType',
        text: getLabel('53956', '降序'),
      },
      {
        key: ASC,
        type: 'orderType',
        text: getLabel('53955', '升序'),
      },
    ],
  },
  {
    key: FORMAT,
    type: FORMAT,
    text: getLabel('55459', '显示格式'),
    groupId: '',
    children: [
      {
        key: AGGDEFAULT,
        type: FORMAT,
        text: getLabel('55443', '无'),
      },
      {
        key: THOUSANDS,
        type: FORMAT,
        text: getLabel('55467', '千分位'),
      },
      {
        key: PERCENTAGEFORM,
        type: FORMAT,
        text: getLabel('74197', '百分比(同一度量)'),
      },
      {
        key: PERCENTAGEFORD,
        type: FORMAT,
        text: getLabel('74198', '百分比(同一维度)'),
      },
    ],
  },
  {
    key: UNIT,
    type: UNIT,
    text: getLabel('55468', '显示单位'),
    groupId: '',
  },
  {
    key: GROUP,
    type: GROUP,
    text: getLabel('54220', '分组'),
    groupId: '',
    children: [
      {
        key: false,
        type: GROUP,
        text: getLabel('56273', '否'),
      },
      {
        key: true,
        type: GROUP,
        text: getLabel('62694', '是'),
      },
    ],
  },
  {
    key: FILTER,
    type: FILTER,
    text: getLabel('53953', '筛选条件'),
    groupId: 'filter',
  },
  {
    key: ALIAS,
    type: ALIAS,
    text: getLabel('55436', '设置别名'),
    groupId: '',
  },
  {
    key: DELETE,
    type: DELETE,
    text: getLabel('53951', '删除'),
    groupId: '',
  },
];

const formatPercentage = {
  key: PERCENTAGE,
  type: FORMAT,
  text: getLabel('55497', '百分比'),
};

export default class FieldMenu extends PureComponent<FieldMenuProps, FieldMenuStates> {
  constructor(props: FieldMenuProps) {
    super(props);

    const { calculationField = [] } = props.config;

    this.state = {
      aliasVisible: false,
      calculationVisible: false,
      customUnitVisible: false,
      unit: null,
      calculationField: calculationField.length ? calculationField[0] : {},
      filterDlgVisible: false,
    };
  }

  hideAliasDlg = () => {
    this.setState({ aliasVisible: false });
  };

  showAliasDlg = () => {
    this.setState({ aliasVisible: true });
  };

  hideCalculationDlg = () => {
    this.setState({ calculationVisible: false });
  };

  showFilterDlg = () => {
    this.setState({ filterDlgVisible: true });
  };

  hideFilterDlg = () => {
    this.setState({ filterDlgVisible: false });
  };

  onFieldFilterChange = (filter: any, other: any = {}) => {
    const { onChange } = this.props;
    const { filterIsNull } = other;

    this.hideFilterDlg();
    onChange({ filters: filter, filterIsNull: !!filterIsNull });
  };

  hideCustomUnitDlg = () => {
    this.setState({ customUnitVisible: false });
  };

  showCustomUniDlg = (key?: string) => {
    const { axis } = this.props;

    const custom = axis?.unit?.custom || [];

    let unit = null;

    if (axis?.unit?.isNew) {
      unit = axis?.unit;
    } else if (!key && custom?.length) {
      [unit] = custom;
      if (unit) {
        unit = {
          ...unit,
          customName: unit.customName || {
            nameAlias: unit.text,
          },
          unitType: 'custom',
          FormatType: 'divide',
          formatValue: 1,
        };
      }
    }

    if (key) {
      unit = custom.find((c: any) => c.id === key);
    }

    this.setState({ customUnitVisible: true, unit });
  };

  // 是否展示添加计算
  showAddCalculatedData = () => {
    const {
      dimensions, chartType, config, dataset,
    } = this.props;
    const dFieldDateGroupType = dimensions?.length && dimensions[0].dateGroupType;
    const dimensionsFieldType = dimensions?.length && dimensions[0].type;

    // sql数据集增加Text的支持
    const isSupportText = dataset?.groupId === 'sql' && dimensionsFieldType === FieldType.CText;

    // 维度项是否为日期类型
    // eslint-disable-next-line max-len
    const isDateFieldType = dimensionsFieldType === FieldType.FDate
      || dimensionsFieldType === FieldType.CDate
      || isSupportText;

    // 是否为柱状图或者折线图
    const isSupportChartType = supportAddCalculatedData.includes(chartType || '');
    // 柱状图为普通柱状图时
    const isDefaultBar = isSupportChartType && chartType === 'Bar' ? config?.barType === BarType.Default : true;
    // 日期分组为年月日、年月、年、按季度、按月份时
    const isSupportDataGroupType = supportCalcDate.includes(dFieldDateGroupType) || isSupportText;

    return isDateFieldType && isSupportChartType && isDefaultBar && isSupportDataGroupType;
  };

  onFieldShowNameChange = (showName: string) => {
    const { onChange } = this.props;

    onChange({ showName });
    this.hideAliasDlg();
  };

  onCustomUnitChange = (newUnit: any) => {
    const { axis, onChange } = this.props;
    const { id, isNew } = newUnit;

    // 新版单位
    if (isNew) {
      onChange({ unit: newUnit });
    } else {
      const custom = axis?.unit?.custom || [];

      const _custom = toJS(custom);
      const newUnitIndex = custom.findIndex((item: any) => item.id === newUnit.id);

      if (newUnitIndex !== -1) {
        _custom[newUnitIndex] = newUnit;
      } else {
        _custom.push(newUnit);
      }

      onChange({
        unit: {
          checked: id,
          custom: _custom,
        },
      });
    }

    this.hideCustomUnitDlg();
  };

  onUnitSelect = (key: string) => {
    const { axis, onChange } = this.props;
    const { unit } = axis;

    onChange({
      unit: {
        ...unit,
        checked: key,
      },
    });
  };

  onUnitDelete = (key: string, text: string) => {
    const { axis, onChange } = this.props;
    let custom = axis?.unit?.custom || [];
    let checked = axis?.unit?.checked;

    confirm({
      mask: true,
      content: (
        <span>
          {getLabel('55469', '确定删除单位')}{' '}
          <span className={`${prefixCls}-charts-field-unit-text`}>{text}</span>
        </span>
      ),
      onOk: () => {
        custom = custom.filter((unit: any) => unit.id !== key);

        if (checked === key) {
          checked = defaultUnit()[0].key;
        }

        onChange({ unit: { checked, custom } });
      },
    });
  };

  onMenuChange = (type: string) => (value: any) => {
    const { axis, onChange, onRemove } = this.props;

    if (type === showMissingDate && axis.showMissingDate) {
      value = '';
    }

    if (type === ALIAS) {
      this.showAliasDlg();
    } else if (type === UNIT) {
      this.showCustomUniDlg();
    } else if (type === FILTER) {
      this.showFilterDlg();
    } else if (type === ADDCALCULATED) {
      if (value === CountType.NONE) {
        onChange({ calculationFields: [] });
      } else {
        const { calculationFields = [] } = axis;
        let newCalculationField = {
          countType: value,
          baseDate: '',
          baseDateType: '',
          ratioType: '',
          timeField: {},
          id: UUID(),
        };

        // 有值则编辑
        if (!isEmpty(calculationFields)) {
          const [calculationField] = calculationFields;

          if (calculationField.countType === value) {
            newCalculationField = calculationField;
          }
        }

        this.setState({
          calculationField: newCalculationField,
          calculationVisible: true,
        });
      }
    } else if (type === DELETE) {
      onRemove();
    } else {
      onChange({ [type]: value });
    }
  };

  onSure = (value: any) => {
    const { onChange } = this.props;

    onChange({ calculationFields: [value] });

    this.hideCalculationDlg();
  };

  // eslint-disable-next-line max-len
  hasActiveKey = (activeKey: string, childs: any[]) => !!childs?.find((child) => child?.key === activeKey);

  render() {
    const {
      type: fieldType,
      axis,
      dataset,
      dimensions,
      chartType,
      fields,
      config,
      index,
      comId,
      pageId,
      sourceType,
    } = this.props;

    const {
      aliasVisible,
      calculationVisible,
      customUnitVisible,
      unit,
      calculationField,
      filterDlgVisible,
    } = this.state;
    let custom = axis?.unit?.custom || [];
    const dFieldDateGroupType = dimensions?.length && dimensions[0].dateGroupType;

    const supportBaseDate = supportCalcDateWithBase.includes(dFieldDateGroupType);

    custom = custom.map((c: any) => ({ ...c, key: c.id }));

    let dimenSionMenuOpt = cloneDeep(dimenSionMenuData);

    // 日期字段增加 日期分组
    if (axis.type === FieldType.CDate || axis.type === FieldType.FDate) {
      // sql数据集不支持日期分组、同比不支持日期分组（默认按年分组）
      if (config?.dataset?.groupId !== 'sql') {
        // eslint-disable-next-line max-len
        dimenSionMenuOpt.splice(
          3,
          0,
          ...getDateGroup(
            axis,
            chartType,
            config?.barType,
            fieldType === 'dimensions' && index !== 0,
          ),
        );
      }

      if (includes(supportShowMissTimeData, chartType)) {
        dimenSionMenuOpt.splice(3, 0, showMissingDateData);
      }
    }
    // 选择项字段 增加数据项显示
    if (axis.type === FieldType.CSelect || axis.type === FieldType.FSelect) {
      dimenSionMenuOpt.splice(3, 0, dataShowWayData);
    }

    // 饼状图添加统计下级字段
    if (
      chartType === 'Pie'
      && (axis.type === FieldType.FDepartment || axis.type === FieldType.FEmployee)
    ) {
      dimenSionMenuOpt.splice(3, 0, statChildren);
    }

    if (isETSqlDatasource(dataset as DataSet)) {
      dimenSionMenuOpt = dimenSionMenuOpt.filter((item) => item.groupId !== 'order');
    }

    const getDimenSionData = () => dimenSionMenuOpt.map((data) => {
      const {
        key, type, text, groupId, children = [],
      } = data;

      return {
        id: key,
        content: children.length ? (
            <ParentItem
              weId={`${this.props.weId || ''}_3rsx0a@${key}`}
              text={text}
              showDot={
                (type === DATEGROUP && axis.dateGroupType)
                || type === dataShowWay
                || (type === YEARGROUP && isGrouped(axis[YEARGROUP]))
                || (type === STATCHILDREN && axis?.statChildren)
              }
            />
        ) : (
            <Item
              weId={`${this.props.weId || ''}_plue1z@${key}`}
              key={key}
              attr={key}
              text={text}
              type={type}
              selected={axis[type] === key}
              showDot={type === ALIAS && getLocaleValue(axis.showName)}
              onClick={this.onMenuChange(type)}
            />
        ),
        groupId,
        children: children.map((child: any) => {
          const { key: childKey, type: childType, text: childText } = child;
          // 兼容历史数据 数据项显示，默认按照查询结果
          const selected = axis[childType] === childKey
              || (childType === dataShowWay && childKey === BYRESULT && !axis[childType]);

          return {
            id: childKey,
            content: (
                <Item
                  weId={`${this.props.weId || ''}_46lh4z@${childKey}`}
                  key={childKey}
                  attr={childKey}
                  text={childText}
                  type={childType}
                  selected={selected}
                  onClick={this.onMenuChange(childType)}
                />
            ),
          };
        }),
      };
    });

    let meaSureMenuOpt = cloneDeep(meaSureMenuData);

    // 指标显示 添加计算
    if (this.showAddCalculatedData()) {
      meaSureMenuOpt.splice(-3, 0, addCalculatedData);
    }

    // 柱状图、堆积柱图添加组合图表选项
    if (supportComposeCharts.includes(chartType || '')) {
      // 同比柱状图不显示组合图表
      if (!(chartType === 'Bar' && config?.barType === BarType.YOY)) {
        meaSureMenuOpt.splice(-3, 0, composeChartData);
      }
    }

    if (isETSqlDatasource(dataset as DataSet)) {
      meaSureMenuOpt = meaSureMenuOpt.filter(
        (item) => item.groupId !== 'order' && item.groupId !== 'aggr',
      );
    }
    // 柱状图、堆积柱图、条形图、堆积条图  才有指标【分组】
    // 只支持单选和下拉
    if (!(chartType && isSupportGroup(chartType)) || !isSupportGroupField(axis)) {
      meaSureMenuOpt = meaSureMenuOpt.filter((item) => item.key !== GROUP);
    }

    if (sourceType === 'targetcard') {
      meaSureMenuOpt = meaSureMenuOpt.filter((item) => item.key !== FILTER && item.key !== UNIT);
    }

    const getMeaSureData = () => meaSureMenuOpt.map((data) => {
      const {
        key, type, text, groupId, children = [],
      } = data;

      let _text = text;
      let attr = key;
      let disabled = false;

      const childs = cloneDeep(children);

      if (key === SUMMARY && axis.type !== 'Number' && axis.type !== 'NumberComponent') {
        childs.splice(0, 4);
      }

      if (type === UNIT && custom.length) {
        childs.splice(3, 0, ...custom);
      }

      // 柱状图格式添加 百分比(同一度量)、百分比(同一维度)
      if (type === FORMAT && chartType !== charts.Bar) {
        childs.splice(2);
      }

      if (type === FORMAT && chartType === charts.Bubble) {
        childs.push(formatPercentage as any);
      }

      let hasChangedUnit = false;

      if (type === 'unit' && axis.unit) {
        if (axis.unit.isNew) {
          hasChangedUnit = axis.unit.unitType !== UnitTypeData.NONE;
        } else {
          hasChangedUnit = axis.unit.checked !== 'nothing';
        }
      }

      if (key === SUMMARY) {
        const childText = childs.find((child) => child.key === axis.aggregator)?.text;

        _text += childText ? ` （${childText}）` : '';
        attr = axis.aggregator;
      }

      if (key === SORTORDER) {
        const childText = childs.find((child) => child.key === axis.orderType)?.text;

        _text += childText ? ` （${childText}）` : '';
        attr = axis.orderType;
      }

      // 指标数据图表不允许选择汇总方式
      if (['aggregator'].includes(type) && sourceType === 'targetcard') {
        disabled = true;
      }

      const menuData = {
        id: key,
        content: childs.length ? (
            <ParentItem
              weId={`${this.props.weId || ''}_6ya31h@${key}`}
              attr={attr}
              text={_text}
              showDot={
                (type === 'format'
                  && axis.format
                  && axis.format !== AGGDEFAULT
                  && axis.format !== NONEFORMAT
                  && this.hasActiveKey(axis[type], childs))
                || hasChangedUnit
                || (type === ADDCALCULATED
                  && axis.calculationFields
                  && axis.calculationFields.length)
                || (type === COMPOSECHART
                  && axis.composeChart
                  && axis.composeChart !== ComposeChart.NONE)
                || (type === GROUP && isGrouped(axis.group))
              }
              className={classnames({
                [`${prefixCls}-charts-config-field-menu-unit`]: hasChangedUnit,
              })}
              onClick={this.onMenuChange(type)}
            />
        ) : (
            <Item
              weId={`${this.props.weId || ''}_3qcqxw@${key}`}
              key={key}
              attr={key}
              text={text}
              type={type}
              showDot={
                (type === ALIAS && getLocaleValue(axis.showName))
                || (type === FILTER && axis.filters && Object.keys(axis.filters).length)
                || hasChangedUnit
              }
              selected={axis[type] === key}
              onClick={this.onMenuChange(type)}
            />
        ),
        groupId,
        children: childs.map((child: any) => {
          const { key: childKey, type: childType, text: childText } = child;

          if (childType === UNIT && !defaultUnit().find((defUnit) => defUnit.key === childKey)) {
            return {
              id: childKey,
              content: (
                  <UnitItem
                    weId={`${this.props.weId || ''}_37qs5h@${childKey}`}
                    key={childKey}
                    attr={childKey}
                    text={childText}
                    type={childType}
                    selected={axis[childType] && axis[childType].checked === childKey}
                    onUnitSelect={this.onUnitSelect}
                    showCustomUniDlg={this.showCustomUniDlg}
                    onUnitDelete={this.onUnitDelete}
                  />
              ),
            };
          }

          let selected = axis[childType] === childKey;

          // 单位值的value是checked
          if (childType === UNIT) {
            selected = axis[childType]?.checked === childKey;
          } else if (
            childType === ADDCALCULATED
              && axis.calculationFields
              && axis.calculationFields.length
          ) {
            const [calculationFieldItem] = axis.calculationFields;
            selected = calculationFieldItem.countType === childKey;
          }
          return {
            id: childKey,
            content: (
                <Item
                  weId={`${this.props.weId || ''}_lsl62n@${childKey}`}
                  key={childKey}
                  attr={childKey}
                  text={childText}
                  type={childType}
                  selected={selected}
                  onClick={this.onMenuChange(type)}
                />
            ),
          };
        }),
        disabled,
      };

      if (!children.length) {
        delete (menuData as any).children;
      }

      return menuData;
    });

    const data: any = fieldType === 'measures' ? getMeaSureData() : getDimenSionData();

    let { filters } = axis;
    const { filterIsNull = true } = axis;
    // eslint-disable-next-line max-len
    const isOpenConditionSet = isEmpty(dataset?.type) || openConditionSet(dataset?.type, dataset?.groupId);
    filters = transFormConditionValue(isOpenConditionSet, filters);

    return (
      <>
        <Menu
          weId={`${this.props.weId || ''}_9qtgu0`}
          value=""
          data={data as MenuItemData[]}
          type="select"
          popupPlacement="bottomRight"
          childPopupPlacement="rightBottom"
          action={['click']}
          overlayClassName={`${prefixCls}-charts-config-field-menu`}
          childTriggerProps={{
            overlayClassName: `${prefixCls}-charts-config-field-menu-overlay`,
            popupClassName: `${prefixCls}-charts-config-field-menu-popup`,
          }}
          customSelectContent={this.props.children as React.ReactElement}
        />

        <AliasDialog
          weId={`${this.props.weId || ''}_0jal9r`}
          value={axis?.showName}
          visible={aliasVisible}
          onCancel={this.hideAliasDlg}
          onOk={this.onFieldShowNameChange}
        />

        {/* 新建同比、环比 */}
        <CorsComponent
          weId={`${this.props.weId || ''}_8982ms`}
          app="@weapp/ebdcoms"
          compName="CalculationField"
          visible={calculationVisible}
          onCancel={this.hideCalculationDlg}
          onOk={this.onSure}
          source={CalculationFieldSource.Charts}
          data={calculationField}
          fields={fields || []}
          supportBaseDate={supportBaseDate}
          pageId={pageId}
        />

        <CorsComponent
          weId={`${this.props.weId || ''}_gtucez`}
          app="@weapp/ebdchartcoms"
          compName="NewCustomUnitView"
          visible={customUnitVisible}
          unit={unit}
          pageId={pageId}
          onCancel={this.hideCustomUnitDlg}
          onOk={this.onCustomUnitChange}
        />

        <If
          weId={`${this.props.weId || ''}_8tqj09`}
          condition={isOpenConditionSet && !isArray(toJS(filters))}
        >
          <Then weId={`${this.props.weId || ''}_gqql8s`}>
            <CorsComponent
              weId={`${this.props.weId || ''}_qy3d5v`}
              app="@weapp/components"
              compName="ConditionSetDlg"
              visible={filterDlgVisible}
              dataSet={dataset}
              value={filters}
              title={axis.text}
              onCancel={this.hideFilterDlg}
              onOk={this.onFieldFilterChange}
              filterIsNull={filterIsNull}
              showFilterIsNull
              enableSqlEncry
              comId={comId}
            />
          </Then>
          <Else weId={`${this.props.weId || ''}_17ahj4`}>
            <CorsComponent
              weId={`${this.props.weId || ''}_oyu175`}
              app="@weapp/ebdcoms"
              compName="FilterDlg"
              visible={filterDlgVisible}
              filters={filters}
              title={axis.text}
              dataset={dataset}
              onCancel={this.hideFilterDlg}
              onOk={this.onFieldFilterChange}
            />
          </Else>
        </If>
      </>
    );
  }
}
