@import '../../../style/prefix.less';

.@{bi-prefix}-content {
  &-axis {
    border-bottom: 1px solid #eee;

    & > span {
      width: 60px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      z-index: 2;
    }

    .@{bi-prefix}-axis-content-fields-placeholder {
      position: absolute;
      right: 0;
      top: 0;
      left: 0;
      bottom: 0;
      padding-left: 92px;
      line-height: 45px;
      color: var(--placeholder-fc);
    }
  }

  &-field-item-max {
    color: #f86b6a !important;
    border-color: rgba(255, 196, 196, 1) !important;
    background-color: rgba(255, 196, 196, 0.3) !important;
  }
}

.@{bi-prefix}-field-menu-item {
  height: 30px;
  line-height: 30px;
  cursor: pointer;

  &.selected {
    color: var(--primary);
  }

  &.dot {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      width: 8px;
      height: 8px;
      background: var(--danger);
      border-radius: 50%;
      right: 10px;
      top: 11px;
    }
  }
}

.@{bi-prefix}-field-menu-item-unit {
  display: flex;
  align-items: center;

  & > span:first-of-type {
    flex: 1 1 auto;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  & > span:last-of-type {
    .edit {
      color: var(--primary);
      padding: 2px;
    }

    .delete {
      color: var(--danger);
      padding: 2px;
      margin-left: 2px;
    }
  }
}

.@{bi-prefix}-field-menu-p-dot {
  position: relative;
  line-height: normal;

  &::after {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    background: var(--danger);
    border-radius: 50%;
    right: 1px;
    top: 5px;
  }
}

.@{bi-prefix}-custom-unit-dlg {
  .ui-dialog-body {
    background-color: var(--base-white);
    padding-bottom: 30px;
  }
}

.@{bi-prefix}-custom-unit {
  height: 425px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &-format-type {
    & > span {
      font-size: 12px;
    }

    .ui-input-wrap {
      width: 50px;
      min-width: 50px;
      height: 28px;
    }
  }

  &-example {
    padding: 15px;
    background-color: #f3f3f3;
    line-height: normal;
    border-radius: 4px;

    &-title {
      font-size: var(--font-size-14);
      margin-bottom: 5px;
    }

    &-cont {
      margin-bottom: -5px;

      & > div {
        margin-bottom: 5px;
        font-size: var(--font-size-12);
        color: var(--secondary-fc);
      }
    }
  }

  &-cont {
    flex: 1 1 auto;
    overflow-y: auto;

    & > li {
      display: flex;
      align-items: center;
      margin-top: 10px;

      .leftInput {
        display: flex;
        align-items: center;

        .number {
          width: 155px;
        }

        .baseUnit {
          max-width: 36px;
          // min-width: 36px; // 没有单位时width为0不显示
          margin: 0 5px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .inp {
        flex: 1 1 auto;
      }

      .opt,
      .rightNumber {
        margin: 0 5px;
      }

      .btn {
        & > span {
          cursor: pointer;
          padding: 7px 4px;

          &.add {
            color: var(--primary);
          }

          &.delete {
            color: var(--danger);
          }
        }
      }
    }
  }
}

.@{bi-prefix}-charts-field-unit-text {
  color: var(--danger);
}

.@{bi-prefix}-charts-config-field-menu.@{bi-prefix}-charts-config-field-menu {
  margin-top: 5px;
  background-color: transparent;

  .ui-menu-list-item {
    padding: 0;

    & > span {
      display: inline-block;
      width: 100%;

      & > div {
        width: 140px;
        padding-left: 15px;
      }
    }
  }

  .ui-menu-list-subitem-title {
    padding: 0;
    padding-right: 6px;

    .ui-menu-list-item-content > div {
      padding-left: 15px;
    }
  }

  &-unit {
    width: 128px !important;
  }

  .ui-menu-list-item-disabled {
    .ui-menu-list-item-righticon {
      display: none;
    }
  }
}

.@{bi-prefix}-charts-config-field-menu-popup {
  max-width: 200px;
  background-color: #fff !important;

  .ui-menu-list-item {
    padding: 0;

    & > span {
      display: inline-block;
      width: 100%;

      & > div {
        padding: 0 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
