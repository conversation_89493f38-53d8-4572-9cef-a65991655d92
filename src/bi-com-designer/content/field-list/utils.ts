import { DataSet } from '@weapp/ebdcoms';
import { charts } from '../../constants';
import { DateGroupType } from './constants';

export const isGrouped = (group: string) => !!group;

// 图表类型是否支持日期分组
export const isSupportYearGroupType = (chartType?: string) => [charts.Bar, charts.Line].includes(chartType || '');
// 图表类型是否只支持时间和日期
export const isOnlySupportDateAndTimeType = (chartType?: string) => [charts.HeatMap].includes(chartType || '');

// 日期分组知否支持按年分组
export const isSupportYearGroupDate = (dateGroupType: DateGroupType) => [
  DateGroupType.SINGLESEASON,
  DateGroupType.SINGLEMONTH,
  DateGroupType.SINGLEWEEKS,
  DateGroupType.MONTHDAY,
].includes(dateGroupType);

// 图表类型是否支持分组
export const isSupportGroup = (type: string) => ['Bar', 'BarStack', 'HBar', 'HBarStack'].includes(type);

// 字段类型是否支持分组
// eslint-disable-next-line max-len
export const isSupportGroupField = (axis: any) => ['RadioBox', 'Select'].includes(axis.compType);

export const isETSqlDatasource = (dataset?: DataSet) => dataset?.type === 'ETEAMS' && dataset?.groupId === 'sql';

// eslint-disable-next-line max-len
export const isSupportYearGroup = (dateGroupType: DateGroupType, chartType?: string) => isSupportYearGroupDate(dateGroupType) && isSupportYearGroupType(chartType);

const hasOwnProperty = (obj: object, propName: string) => {
  const result = Object.prototype.hasOwnProperty.call(obj, propName);

  return result;
};

export const getLocaleValue = (value?: any, nameAliasKey?: string): any => {
  if (typeof value === 'undefined' || value === null) {
    return '';
  }

  if (typeof value === 'string') {
    return value;
  }

  if (hasOwnProperty(value, 'nameAlias')) {
    return value.nameAlias || '';
  }

  if (nameAliasKey && hasOwnProperty(value, nameAliasKey)) {
    return getLocaleValue(value[nameAliasKey]);
  }

  return '';
};

export default {};
