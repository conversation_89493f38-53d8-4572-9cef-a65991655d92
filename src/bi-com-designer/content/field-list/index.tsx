import { Dialog } from '@weapp/ui';
import { classnames, getLabel, isUndefined } from '@weapp/utils';
import React, { PureComponent, ReactNode } from 'react';
import {
  Else, If, Then, When,
} from 'react-if';
import { ReactSortable, SortableEvent } from 'react-sortablejs';
import { noop } from '../../../constants';
import {
  charts, FieldType, moveFieldDistance, prefixCls,
} from '../../constants';
import FeidItem from './FieldItem';
import './index.less';
import PlaceHolder from './PlaceHolder';

export const group = {
  name: 'bicomDesigenrfield',
  pull: true,
  put: ['fieldlist'],
};

export type FieldListType = 'measures' | 'dimensions';

interface FieldListProps extends React.Attributes {
  comType: string;
  className?: string;
  type: FieldListType;
  fields: any[];
  maxLen?: number;
  comId: string;
  onFieldDelete: (index: number) => void;
  onFieldSort: (oldIndex: number, newIndex: number) => void;
  onFieldAdd: (index: number, field: any) => void;
  onFieldChange: (index: number, changes: Record<string, any>) => void;
  customRenderTitle?: () => ReactNode;
  customRenderItem?: (field: any, index: number) => ReactNode;
}
export default class FieldList extends PureComponent<FieldListProps> {
  ref = React.createRef<HTMLDivElement>();

  onAdd = (e: SortableEvent) => {
    const { newIndex } = e;

    const {
      type, fields, comType, onFieldAdd,
    } = this.props;
    const field = JSON.parse(e.clone.getAttribute('data-field')) as any;

    if (type === 'dimensions' && fields.find((measure: any) => measure.id === field.id)) {
      let content = `${getLabel('233083', '已存在')}${field.text}`;
      content += `，${getLabel('233084', '不可重复')}！`;

      Dialog.message({
        type: 'error',
        content,
      });
    } else if (
      type === 'dimensions'
      && comType === charts.HeatMap
      && field.type !== FieldType.CDate
      && field.type !== FieldType.FDate
    ) {
      Dialog.message({
        type: 'error',
        content: `${getLabel('242091', '日历热力图维度项只支持日期字段')}！`,
      });
    } else {
      onFieldAdd(newIndex, field);
    }
  };

  onEnd = (e: SortableEvent) => {
    const { onFieldSort, onFieldDelete } = this.props;
    const {
      newIndex,
      oldIndex,
      originalEvent: { pageX, pageY },
    } = e;

    const {
      x, y, height, width,
    } = this.ref.current!.getBoundingClientRect();

    /** 判断字段是否拖拽出列表删除 */
    if (
      pageY > y + height + moveFieldDistance
      || pageY + moveFieldDistance < y
      || pageX > x + width + moveFieldDistance
      || pageX + moveFieldDistance < x
    ) {
      onFieldDelete(oldIndex);
    } else if (newIndex !== oldIndex) {
      onFieldSort(oldIndex, newIndex);
    }
  };

  onRemove = (index: number) => () => {
    const { onFieldDelete } = this.props;

    onFieldDelete(index);
  };

  onChange = (index: number) => (changes: Record<string, any>) => {
    const { onFieldChange } = this.props;

    onFieldChange(index, changes);
  };

  render() {
    const {
      type, fields, maxLen, comId, className, customRenderTitle, customRenderItem,
    } = this.props;

    return (
      <div className={classnames(`${prefixCls}-content-axis`, className)} ref={this.ref}>
        <span>
          <If weId={`${this.props.weId || ''}_yrqins`} condition={!!customRenderTitle}>
            <Then weId={`${this.props.weId || ''}_afq4sm`}>{customRenderTitle?.()}</Then>
            <Else weId={`${this.props.weId || ''}_0gvp0t`}>
              {type === 'dimensions' ? getLabel('233085', '维度项') : getLabel('233086', '度量项')}
            </Else>
          </If>
        </span>
        <When weId={`${this.props.weId || ''}_6ibtlf`} condition={!fields.length}>
          <PlaceHolder weId={`${this.props.weId || ''}_yfx7sg`} />
        </When>
        <ReactSortable
          weId={`${this.props.weId || ''}_w7y5j6`}
          animation={100}
          list={fields}
          setList={noop}
          group={group}
          className={`${prefixCls}-chart-content-${type}-fields`}
          onAdd={this.onAdd}
          onEnd={this.onEnd}
        >
          {fields.map((field: any, index: number) => (
            <If
              weId={`${this.props.weId || ''}_xc63k5`}
              condition={!!customRenderItem}
              key={field.id}
            >
              <Then weId={`${this.props.weId || ''}_khq3uf`}>
                {customRenderItem?.(field, index)}
              </Then>
              <Else weId={`${this.props.weId || ''}_61pwbw`}>
                <FeidItem
                  weId={`${this.props.weId || ''}_4h3s8w`}
                  className={
                    !isUndefined(maxLen) && index + 1 > maxLen
                      ? `${prefixCls}-content-field-item-max`
                      : ''
                  }
                  index={index}
                  key={field.id}
                  comId={comId}
                  field={field}
                  type={type}
                  onChange={this.onChange(index)}
                  onRemove={this.onRemove(index)}
                />
              </Else>
            </If>
          ))}
        </ReactSortable>
      </div>
    );
  }
}
