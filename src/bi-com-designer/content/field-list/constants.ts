import { getLabel } from '@weapp/utils';

export enum OrderType {
  DEFAULT = 'DEFAULT',
  DESC = 'DESC', // 降序
  ASC = 'ASC', // 升序
}

// 度量（维度）配置
export enum AxisOptType {
  COUNT = 'COUNT', // 计数
  ALIAS = 'ALIAS', // 别名
  FORMAT = 'FORMAT', // 格式
  UNIT = 'UNIT', // 单位
  ADDCALCULATED = 'ADDCALCULATED', // 添加计算
  DATEGROUP = 'DATEGROUP', // 日期分组
  YEARGROUP = 'yearGroup', // 按年统计
  STATCHILDREN = 'statChildren', // 统计下级
  MORE = 'MORE', // 更多
  DELETE = 'DELETE', // 删除
  SUMMARY = 'SUMMARY', // 汇总方式
  SORTORDER = 'SORTORDER', // 排序方式
}

// 单位
export enum Aggregator {
  SUM = 'SUM',
  AVG = 'AVG',
  MAX = 'MAX',
  MIN = 'MIN',
  COUNT = 'COUNT',
  DISTINCTCOUNT = 'DISTINCTCOUNT', // 计数(去重)
  AGGDEFAULT = 'DEFAULT', // 默认
  AMOUNT = 'AMOUNT', // 金额
  EDIT = 'EIDT',
  FILTER = 'FILTER',
  TBORHB = 'TBORHB',
  ORIGINALVALUE = 'ORIGINALVALUE',
}

export const getAggText = () => ({
  SUM: getLabel('55438', '总和'),
  AVG: getLabel('55439', '平均值'),
  MAX: getLabel('55440', '最大值'),
  MIN: getLabel('55441', '最小值'),
  COUNT: getLabel('55442', '计数'),
  DISTINCTCOUNT: getLabel('99288', '计数(去重)'),
});

export const aggText = getAggText();

// 格式
export enum FormatType {
  NONEFORMAT = 'NONEFORMAT', // 无
  VALUE = 'VALUE', // 数值
  THOUSANDS = 'THOUSANDS', // 千分位
  PERCENTAGEFORD = 'PERCENTAGEFORD', // 百分比(同一度量)
  PERCENTAGEFORM = 'PERCENTAGEFORM', // 百分比(同一维度)
  PERCENTAGE = 'PERCENTAGE', // 百分比
}

export enum DateGroupType {
  DAY = 'day',
  MONTH = 'month',
  YEAR = 'year',
  SINGLESEASON = 'singleSeason',
  SINGLEMONTH = 'singleMonth',
  SINGLEWEEKS = 'singleWeeks',
  SINGLEWEEK = 'singleWeek',
  MONTHDAY = 'monthDay',
  SINGLEDAY = 'singleDay',
}

// 支持添加计算的日期类型
export const supportCalcDate = [
  DateGroupType.DAY,
  DateGroupType.MONTH,
  DateGroupType.YEAR,
  DateGroupType.SINGLESEASON,
  DateGroupType.SINGLEMONTH,
];

// 支持添加计算的日期类型中需要对比基准的类型
export const supportCalcDateWithBase = [DateGroupType.SINGLESEASON, DateGroupType.SINGLEMONTH];

// 单位
export const UNIT = 'unit';

/** 单位数值类型 */
export enum UnitTypeData {
  NONE = 'none',
  TENTHOUSAND = 'tenThousand',
  HUNDREDMILLION = 'hundredMillion',
  THOUSAND = 'thousand',
  MILLION = 'million',
  BILLION = 'billion',
  GRAM = 'gram',
  KILOGRAM = 'kilogram',
  TON = 'ton',
  CUSTOM = 'custom',
}

/** 单位转换类型 */
export enum FormatTypeData {
  MULTIPLY = 'Multiply',
  DIVIDE = 'divide',
}

// 组合图表
export const COMPOSECHART = 'composeChart';

export enum ComposeChart {
  NONE = 'none',
  LINE = 'line',
  AREA = 'area',
}

// 显示格式
export const FORMAT = 'format';

// 分组
export const GROUP = 'group';

export const defaultUnit = () => [
  {
    key: 'nothing',
    type: UNIT,
    text: getLabel('55443', '无'),
  },
  {
    key: 'default_million',
    type: UNIT,
    text: `${getLabel('55444', '中文')}: (${getLabel('55445', '万')}/${getLabel(
      '55446',
      '亿',
    )}) / ${getLabel('55447', '国际')}: (K/M/B)`,
  },
  {
    key: 'default_kg',
    type: UNIT,
    text: `${getLabel('55448', '克')}/${getLabel('55449', '千克')}/${getLabel('55450', '吨')}`,
  },
  {
    key: 'custom',
    type: UNIT,
    text: `${getLabel('55451', '自定义单位')}`,
  },
];

// 选择项字段支持 显示全部、按查询结果
export enum DataShowWay {
  BYALL = 'byAll',
  BYRESULT = 'byResult',
}

/** 单位数值类型 */
export const getUnitTypeData = () => [
  { id: UnitTypeData.NONE, content: getLabel('55443', '无') },
  { id: UnitTypeData.TENTHOUSAND, content: getLabel('55445', '万') },
  { id: UnitTypeData.HUNDREDMILLION, content: getLabel('55446', '亿') },
  { id: UnitTypeData.THOUSAND, content: 'K' },
  { id: UnitTypeData.MILLION, content: 'M' },
  { id: UnitTypeData.BILLION, content: 'B' },
  { id: UnitTypeData.GRAM, content: getLabel('55448', '克') },
  { id: UnitTypeData.KILOGRAM, content: getLabel('55449', '千克') },
  { id: UnitTypeData.TON, content: getLabel('55450', '吨') },
  { id: UnitTypeData.CUSTOM, content: getLabel('54002', '自定义') },
];

export const unitTypeData = getUnitTypeData();

/** 单位转换类型 */
export const formatTypeData = [
  { id: FormatTypeData.DIVIDE, content: '÷' },
  { id: FormatTypeData.MULTIPLY, content: '×' },
];

export enum TimeType {
  YEAR = 'year',
  MONTH = 'month',
  WEEK = 'week',
  DAY = 'day',
}

export enum BaseType {
  CURRENT = 'current',
  CUSTOM = 'custom',
}

export enum CalculationType {
  GrowthValue = 'growthValue',
  GrowthRate = 'growthRate',
}

const {
  YEAR, MONTH, WEEK, DAY,
} = TimeType;
const { CURRENT, CUSTOM } = BaseType;
const { GrowthValue, GrowthRate } = CalculationType;

export enum CalculationFieldSource {
  Charts = 'charts', // 柱状图、折线图
  Digitalpanel = 'digitalpanel', // 数字面板
}

export const dftPrefixScopeText = {
  increase: '',
  decrease: '',
  flat: '',
};

export const getRatioTypeOptions = () => [
  { id: YEAR, content: getLabel('55460', '年') },
  { id: MONTH, content: getLabel('55461', '月') },
  { id: WEEK, content: getLabel('56073', '周') },
  { id: DAY, content: getLabel('55462', '日') },
];

export const ratioTypeOptions = getRatioTypeOptions();

export const getBaseOptionsForYear = () => [
  { id: CURRENT, content: getLabel('55233', '当前年') },
  { id: CUSTOM, content: getLabel('56074', '指定年') },
];

export const getBaseOptionsForMonth = () => [
  { id: CURRENT, content: getLabel('55234', '当前月') },
  { id: CUSTOM, content: getLabel('56075', '指定月') },
];

export const getBaseOptionsForDay = () => [
  { id: CURRENT, content: getLabel('55235', '当前日') },
  { id: CUSTOM, content: getLabel('56076', '指定日') },
];

export const getCalculationTypeOptions = () => [
  { id: GrowthRate, content: getLabel('98325', '增长率') },
  { id: GrowthValue, content: getLabel('98324', '增长值') },
];

export const calculationTypeOptions = getCalculationTypeOptions();

export enum BarType {
  Default = 'default',
  YOY = 'yoy',
}

export enum FieldType {
  FSelect = 'Select',
  FRadioBox = 'RadioBox',
  FCheckBox = 'CheckBox',
  FEmployee = 'Employee', // 人员
  FDepartment = 'Department', // 部门
  FDate = 'Date',
  FNumber = 'Number',

  // crm字段标识 eteams字段
  CText = 'Text',
  CNumber = 'NumberComponent',
  CSelect = 'BusinessData',
  CDate = 'DateComponent',
}

export enum CountType {
  NONE = 'COUNT', // 无计算
  YOY = 'YOY', // 同比
  MOM = 'MOM', // 环比
}

export default {};
