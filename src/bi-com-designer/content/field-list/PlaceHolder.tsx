import { classnames, getLabel } from '@weapp/utils';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import { BI_FIELD_DRAG, prefixCls } from '../../constants';
import { DesignerStore } from '../../store';

@inject('designStore')
@observer
class PlaceHolder extends PureComponent<{ designStore: DesignerStore }, { enter: boolean }> {
  placeholderRef = React.createRef<HTMLDivElement>();

  state = { enter: false };

  componentDidMount() {
    const { events } = this.props.designStore;

    this.placeholderRef.current?.addEventListener('dragenter', this.dragEnter);

    events.on(BI_FIELD_DRAG, this.onFieldDrag);
  }

  componentWillUnmount() {
    const { events } = this.props.designStore;

    this.placeholderRef.current?.removeEventListener('dragenter', this.dragEnter);

    events.off(BI_FIELD_DRAG, this.onFieldDrag);
  }

  dragEnter = () => {
    this.setState({ enter: true });
  };

  onFieldDrag = (stauts: string) => {
    if (stauts === 'draggend') {
      this.setState({ enter: false });
    }
  };

  render() {
    const { enter } = this.state;

    return (
      <div
        className={classnames(`${prefixCls}-axis-content-fields-placeholder`, { enter })}
        ref={this.placeholderRef}
      >
        {getLabel('233087', '请拖拽字段到此处')}
      </div>
    );
  }
}

export default PlaceHolder as unknown as ComponentType<{}>;
