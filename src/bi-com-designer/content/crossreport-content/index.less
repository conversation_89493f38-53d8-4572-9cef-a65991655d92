@import '../../../style/prefix.less';

.@{bi-prefix}-crossreport-content {
  width: 100%;
  height: 100%;
  padding: 15px;
  display: flex;
  flex-direction: column;

  &-blank {
    height: 15px;
    background: transparent;
  }

  &-design {
    background: #fff;
    flex: 1;
    overflow: auto;

    &-content {
      width: 100%;
      height: 100%;

      &.dragging {
        .@{bi-prefix}-crossreoprt-axis {
          background-color: #ecf6ff;
        }
      }
    }
  }

  &-design-content-empty {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .@{bi-prefix}-crossreport-content-design-item {
      &-empty {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #333;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: none;
        font-size: var(--font-size-12);
        pointer-events: none;

        &.showplaceholder {
          display: flex;
        }
      }

      &-sortable {
        width: 100%;
        height: 100%;
        padding: 20px;
      }
    }

    & > div:first-of-type {
      flex: 1;
      border-bottom: 1px solid #ebebeb;
    }

    & > div:last-of-type {
      flex: 2;
    }

    & > div {
      display: flex;

      & > div:first-of-type {
        position: relative;
        flex: 1;
        border-right: 1px solid #ebebeb;
      }

      & > div:last-of-type {
        position: relative;
        flex: 2;
      }
    }

    .@{bi-prefix}-crossreoprt-axis-fielditem {
      display: inline-block !important;

      &-setting {
        display: none;
      }
    }
  }
}
