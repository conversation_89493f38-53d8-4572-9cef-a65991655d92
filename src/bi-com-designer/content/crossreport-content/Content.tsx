import React, { PureComponent } from 'react';
import { prefixCls } from '../../constants';
import CrossReportAxis from '../crossreport-axis';
import Design from '../design';
import './index.less';

export default class Content extends PureComponent {
  render() {
    return (
      <div className={`${prefixCls}-crossreport-content`}>
        <CrossReportAxis weId={`${this.props.weId || ''}_1jtqlz`} />
        <div className={`${prefixCls}-crossreport-content-blank`} />
        <Design
          weId={`${this.props.weId || ''}_kvonub`}
          className={`${prefixCls}-crossreport-content-design`}
        />
      </div>
    );
  }
}
