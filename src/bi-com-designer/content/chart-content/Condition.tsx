import { getLabel, isArray } from '@weapp/utils';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../constants';
import { DesignerStore } from '../../store';

@inject('designStore')
@observer
class Condition extends PureComponent<{ designStore: DesignerStore }> {
  render() {
    const { selectedCom } = this.props.designStore.layoutStore;

    return (
      <div className={`${prefixCls}-chart-content-condition`}>
        <When
          weId={`${this.props.weId || ''}_464agv`}
          condition={
            selectedCom?.config?.filter
            && isArray(toJS(selectedCom?.config?.filter.datas))
            && selectedCom?.config?.filter.datas.length
          }
        >
          <span className={`${prefixCls}-chart-content-search-condition-tip`}>
            {getLabel('86045', '过滤条件')}
          </span>
        </When>
      </div>
    );
  }
}

export default Condition as unknown as ComponentType<{}>;
