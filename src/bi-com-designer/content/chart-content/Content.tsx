import { classnames } from '@weapp/utils';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import { When } from 'react-if';
import { BI_FIELD_DRAG, charts, prefixCls } from '../../constants';
import { DesignerStore } from '../../store';
import ChartAxis from '../chart-axis';
import Design from '../design';
import Condition from './Condition';
import SearchCondition from './SearchCondition';
import './index.less';

@inject('designStore')
@observer
class Content extends PureComponent<{ designStore: DesignerStore }> {
  state = { stauts: 'draggend' };

  componentDidMount() {
    const { events } = this.props.designStore;

    events.on(BI_FIELD_DRAG, this.onDragStautsChange);
  }

  componentWillUnmount() {
    const { events } = this.props.designStore;

    events.off(BI_FIELD_DRAG, this.onDragStautsChange);
  }

  onDragStautsChange = (stauts: string) => {
    this.setState({ stauts });
  };

  render() {
    const { stauts } = this.state;
    const { selectedCom } = this.props.designStore.layoutStore;
    const { type } = selectedCom!.config!;

    return (
      <div className={`${prefixCls}-chart-content`}>
        <div className={classnames(`${prefixCls}-chart-content-field-opt`, stauts)}>
          <When weId={`${this.props.weId || ''}_5cth1a`} condition={charts[type]}>
            <ChartAxis weId={`${this.props.weId || ''}_6xs7n9`} type={type} />
          </When>
        </div>
        <div className={`${prefixCls}-chart-content-show`}>
          <div className={`${prefixCls}-chart-content-filter`}>
            <Condition weId={`${this.props.weId || ''}_ox4owx`} />
            <SearchCondition weId={`${this.props.weId || ''}_mh7mp7`} />
          </div>
          <Design
            weId={`${this.props.weId || ''}_0ztbzd`}
            className={`${prefixCls}-chart-content-chartdesign`}
          />
        </div>
      </div>
    );
  }
}

export default Content as unknown as ComponentType<{}>;
