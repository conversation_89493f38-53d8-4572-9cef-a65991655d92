import { getLabel } from '@weapp/utils';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import { When } from 'react-if';
import { prefixCls } from '../../constants';
import { DesignerStore } from '../../store';

@inject('designStore')
@observer
class SearchCondition extends PureComponent<{ designStore: DesignerStore }> {
  render() {
    const { selectedCom } = this.props.designStore.layoutStore;

    return (
      <div className={`${prefixCls}-chart-content-search-condition`}>
        <When
          weId={`${this.props.weId || ''}_464agv`}
          condition={
            selectedCom?.config?.searchConditions
            && selectedCom?.config?.searchConditions?.commonFilters?.length
          }
        >
          <span className={`${prefixCls}-chart-content-search-condition-tip`}>
            {getLabel('55818', '搜索条件')}
          </span>
        </When>
      </div>
    );
  }
}

export default SearchCondition as unknown as ComponentType<{}>;
