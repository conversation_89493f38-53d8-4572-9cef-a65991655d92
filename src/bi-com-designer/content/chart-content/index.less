@import '../../../style/prefix.less';

.@{bi-prefix}-chart-content {
  margin: 12px;
  width: calc(100% - 24px);
  height: calc(100% - 24px);
  display: flex;
  flex-direction: column;
  background-color: var(--base-white);

  &-show {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  &-filter {
    width: 220px;
    border-right: 1px solid var(--de-box-lineColor);
    display: flex;
    flex-direction: column;

    & > div {
      flex: 1;
    }

    & > div:first-of-type {
      border-bottom: 1px solid var(--de-box-lineColor);
    }
  }

  &-chartdesign {
    flex: 1;
    overflow: auto;

    &-content,
    &-content > div {
      height: 100% !important;
      border: 0px !important;
    }

    &-content-tip {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--font-size-14);
      color: var(--placeholder-fc);
    }

    .@{comsPrefix}-charts-custom-header {
      display: none !important;
    }

    .ebcoms-charts-main > .ebcoms-empty-text > .ui-empty-description {
      color: var(--placeholder-fc);
      opacity: 1;
    }
  }

  &-search-condition,
  &-condition {
    position: relative;
    overflow: hidden;

    & > div {
      height: 100%;

      & > div {
        height: 100%;
        overflow: auto;
        border: none !important;
        text-align: center;
        padding: 37px 0px 4px 0px !important;

        & > .ui-icon {
          display: none;
        }
      }
    }

    &-tip {
      position: absolute;
      top: 15px;
      left: 24px;
      font-size: var(--font-size-12);
      color: var(--m-readonly-text);
    }

    .ebcomponents-config-filter-empty,
    .ebcoms-csc-empty {
      color: var(--main-fc) !important;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }

  &-search-condition > div > div {
    & > .text {
      overflow: auto;
      height: 100%;
      padding: 0 23px;
      text-align: left;
      display: flex !important;
      flex-direction: column;
      white-space: nowrap;

      & > span {
        width: 100%;
        overflow: visible;
        overflow-x: clip;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #777 !important;
        margin: 0 !important;
        line-height: 20px;
      }
    }
  }

  &-condition > div > div {
    & > .text {
      padding: 0 23px;
      color: #777;
      line-height: 20px;
      text-align: left;
      height: 100%;
      overflow: auto;
    }
  }
}
