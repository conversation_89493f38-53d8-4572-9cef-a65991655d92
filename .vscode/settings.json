// Place your settings in this file to overwrite default and user settings.
{
  "files.exclude": {
    "**/node_modules": true,
    "**/build": true,
    "**/public": true,
    "**/__snapshots__": true,
    ".git": true,
    "**/.DS_Store": true,
    "tsconfig.json": true,
    "tsconfig.lib.json": true,
    "commitlint.config.js": true,
    ".eslintcache": true,
    ".eslintrc": true,
    "yarn.lock": true,
    ".gitignore": true,
    "**/reportWebVitals.ts": true,
    "**/setupTests.ts": true,
    ".prettierrc": true,
    "**/setupProxy.js": true,
    "yarn-error.log": true,
    "*.gitattributes": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/build": true
  },
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  // Turn off tsc task auto detection since we have the necessary tasks as npm scripts
  "typescript.tsc.autoDetect": "off",
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit"
  },
  "editor.formatOnSave": true,
  "[typescript]": {
    "editor.codeActionsOnSave": {
      "source.fixAll": "explicit",
      "source.organizeImports": "explicit"
    }
  },
  "[typescriptreact]": {
    "editor.codeActionsOnSave": {
      "source.fixAll": "explicit",
      "source.organizeImports": "explicit"
    },
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "json.schemaDownload.enable": false
}
