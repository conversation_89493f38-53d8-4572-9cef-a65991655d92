var OpenDialog=function(){var e=document.createElement("iframe"),t=(e.style.backgroundColor="transparent",e.allowTransparency="true",e.style.borderStyle="none",e.style.borderWidth="0px",e.style.overflow="hidden",e.frameBorder="0",mxClient.IS_VML&&(null==document.documentMode||document.documentMode<8)?20:0);e.setAttribute("width",(Editor.useLocalStorage?640:320)+t+"px"),e.setAttribute("height",(Editor.useLocalStorage?480:220)+t+"px"),e.setAttribute("src",OPEN_FORM),this.container=e},ColorDialog=function(t,e,l,n){this.editorUi=t;var c=document.createElement("input"),u=(c.style.marginBottom="10px",c.style.width="216px",mxClient.IS_IE&&(c.style.marginTop="10px",document.body.appendChild(c)),this.init=function(){mxClient.IS_TOUCH||c.focus()},new jscolor.color(c)),i=(u.pickerOnfocus=!1,u.showPicker(),document.createElement("div")),p=(jscolor.picker.box.style.position="relative",jscolor.picker.box.style.width="230px",jscolor.picker.box.style.height="100px",jscolor.picker.box.style.paddingBottom="10px",i.appendChild(jscolor.picker.box),document.createElement("center"));function m(){var e=o(0==ColorDialog.recentColors.length?["FFFFFF"]:ColorDialog.recentColors,11,"FFFFFF",!0);return e.style.marginBottom="8px",e}function o(e,t,l,n){t=null!=t?t:12;for(var i=document.createElement("table"),o=(i.style.borderCollapse="collapse",i.setAttribute("cellspacing","0"),i.style.marginBottom="20px",i.style.cellSpacing="0px",document.createElement("tbody")),a=(i.appendChild(o),e.length/t),r=0;r<a;r++){for(var d=document.createElement("tr"),s=0;s<t;s++)!function(e){var t=document.createElement("td");t.style.border="1px solid black",t.style.padding="0px",t.style.width="16px",t.style.height="16px","none"==(e=null==e?l:e)?t.style.background="url('"+Dialog.prototype.noColorImage+"')":t.style.backgroundColor="#"+e,d.appendChild(t),null!=e&&(t.style.cursor="pointer",mxEvent.addListener(t,"click",function(){"none"==e?(u.fromString("ffffff"),c.value="none"):u.fromString(e)}))}(e[r*t+s]);o.appendChild(d)}return n&&((n=document.createElement("td")).setAttribute("title",mxResources.get("reset")),n.style.border="1px solid black",n.style.padding="0px",n.style.width="16px",n.style.height="16px",n.style.backgroundImage="url('"+Dialog.prototype.closeImage+"')",n.style.backgroundPosition="center center",n.style.backgroundRepeat="no-repeat",n.style.cursor="pointer",d.appendChild(n),mxEvent.addListener(n,"click",function(){ColorDialog.resetRecentColors(),i.parentNode.replaceChild(m(),i)})),p.appendChild(i),i}i.appendChild(c),mxUtils.br(i),m();var a=o(this.presetColors),a=(a.style.marginBottom="8px",(a=o(this.defaultColors)).style.marginBottom="16px",i.appendChild(p),document.createElement("div")),r=(a.style.textAlign="right",a.style.whiteSpace="nowrap",mxUtils.button(mxResources.get("cancel"),function(){t.hideDialog(),null!=n&&n()})),d=(r.className="geBtn",t.editor.cancelFirst&&a.appendChild(r),null!=l?l:this.createApplyFunction()),l=mxUtils.button(mxResources.get("apply"),function(){var e=c.value;/(^#?[a-zA-Z0-9]*$)/.test(e)?("none"!=e&&"#"!=e.charAt(0)&&(e="#"+e),ColorDialog.addRecentColor("none"!=e?e.substring(1):e,12),d(e),t.hideDialog()):t.handleError({message:mxResources.get("invalidInput")})});l.className="geBtn gePrimaryBtn",a.appendChild(l),t.editor.cancelFirst||a.appendChild(r),null!=e&&("none"==e?(u.fromString("ffffff"),c.value="none"):u.fromString(e)),i.appendChild(a),this.picker=u,this.colorInput=c,mxEvent.addListener(i,"keydown",function(e){27==e.keyCode&&(t.hideDialog(),null!=n&&n(),mxEvent.consume(e))}),this.container=i},AboutDialog=(ColorDialog.prototype.presetColors=["E6D0DE","CDA2BE","B5739D","E1D5E7","C3ABD0","A680B8","D4E1F5","A9C4EB","7EA6E0","D5E8D4","9AC7BF","67AB9F","D5E8D4","B9E0A5","97D077","FFF2CC","FFE599","FFD966","FFF4C3","FFCE9F","FFB570","F8CECC","F19C99","EA6B66"],ColorDialog.prototype.defaultColors=["none","FFFFFF","E6E6E6","CCCCCC","B3B3B3","999999","808080","666666","4D4D4D","333333","1A1A1A","000000","FFCCCC","FFE6CC","FFFFCC","E6FFCC","CCFFCC","CCFFE6","CCFFFF","CCE5FF","CCCCFF","E5CCFF","FFCCFF","FFCCE6","FF9999","FFCC99","FFFF99","CCFF99","99FF99","99FFCC","99FFFF","99CCFF","9999FF","CC99FF","FF99FF","FF99CC","FF6666","FFB366","FFFF66","B3FF66","66FF66","66FFB3","66FFFF","66B2FF","6666FF","B266FF","FF66FF","FF66B3","FF3333","FF9933","FFFF33","99FF33","33FF33","33FF99","33FFFF","3399FF","3333FF","9933FF","FF33FF","FF3399","FF0000","FF8000","FFFF00","80FF00","00FF00","00FF80","00FFFF","007FFF","0000FF","7F00FF","FF00FF","FF0080","CC0000","CC6600","CCCC00","66CC00","00CC00","00CC66","00CCCC","0066CC","0000CC","6600CC","CC00CC","CC0066","990000","994C00","999900","4D9900","009900","00994D","009999","004C99","000099","4C0099","990099","99004D","660000","663300","666600","336600","006600","006633","006666","003366","000066","330066","660066","660033","330000","331A00","333300","1A3300","003300","00331A","003333","001933","000033","190033","330033","33001A"],ColorDialog.prototype.createApplyFunction=function(){return mxUtils.bind(this,function(e){var t=this.editorUi.editor.graph;t.getModel().beginUpdate();try{t.setCellStyles(this.currentColorKey,e),this.editorUi.fireEvent(new mxEventObject("styleChanged","keys",[this.currentColorKey],"values",[e],"cells",t.getSelectionCells()))}finally{t.getModel().endUpdate()}})},ColorDialog.recentColors=[],ColorDialog.addRecentColor=function(e,t){null!=e&&(mxUtils.remove(e,ColorDialog.recentColors),ColorDialog.recentColors.splice(0,0,e),ColorDialog.recentColors.length>=t&&ColorDialog.recentColors.pop())},ColorDialog.resetRecentColors=function(){ColorDialog.recentColors=[]},function(e){var t=document.createElement("div"),l=(t.setAttribute("align","center"),document.createElement("h3")),l=(mxUtils.write(l,mxResources.get("about")+" GraphEditor"),t.appendChild(l),document.createElement("img")),l=(l.style.border="0px",l.setAttribute("width","176"),l.setAttribute("width","151"),l.setAttribute("src",IMAGE_PATH+"/logo.png"),t.appendChild(l),mxUtils.br(t),mxUtils.write(t,"Powered by mxGraph "+mxClient.VERSION),mxUtils.br(t),document.createElement("a")),l=(l.setAttribute("href","http://www.jgraph.com/"),l.setAttribute("target","_blank"),mxUtils.write(l,"www.jgraph.com"),t.appendChild(l),mxUtils.br(t),mxUtils.br(t),mxUtils.button(mxResources.get("close"),function(){e.hideDialog()}));l.className="geBtn gePrimaryBtn",t.appendChild(l),this.container=t}),FilenameDialog=function(l,e,t,n,i,o,a,r,d,s,c,u){d=null==d||d;var p,m,g=document.createElement("table"),h=document.createElement("tbody"),x=(g.style.marginTop="8px",p=document.createElement("tr"),(m=document.createElement("td")).style.whiteSpace="nowrap",m.style.fontSize="10pt",m.style.width="120px",mxUtils.write(m,(i||mxResources.get("filename"))+":"),p.appendChild(m),document.createElement("input")),C=(x.setAttribute("value",e||""),x.style.marginLeft="4px",x.style.width=null!=u?u+"px":"180px",mxUtils.button(t,function(){null!=o&&!o(x.value)||(d&&l.hideDialog(),n(x.value))})),e=(C.className="geBtn gePrimaryBtn",this.init=function(){var e,t;null==i&&null!=a||(x.focus(),mxClient.IS_GC||mxClient.IS_FF||5<=document.documentMode||mxClient.IS_QUIRKS?x.select():document.execCommand("selectAll",!1,null),Graph.fileSupport&&null!=(e=g.parentNode)&&(l.editor.graph,t=null,mxEvent.addListener(e,"dragleave",function(e){null!=t&&(t.style.backgroundColor="",t=null),e.stopPropagation(),e.preventDefault()}),mxEvent.addListener(e,"dragover",mxUtils.bind(this,function(e){null==t&&(!mxClient.IS_IE||10<document.documentMode)&&((t=x).style.backgroundColor="#ebf2f9"),e.stopPropagation(),e.preventDefault()})),mxEvent.addListener(e,"drop",mxUtils.bind(this,function(e){null!=t&&(t.style.backgroundColor="",t=null),0<=mxUtils.indexOf(e.dataTransfer.types,"text/uri-list")&&(x.value=decodeURIComponent(e.dataTransfer.getData("text/uri-list")),C.click()),e.stopPropagation(),e.preventDefault()}))))},(m=document.createElement("td")).style.whiteSpace="nowrap",m.appendChild(x),p.appendChild(m),null==i&&null!=a||(h.appendChild(p),null!=c&&m.appendChild(FilenameDialog.createTypeHint(l,x,c))),null!=a&&(p=document.createElement("tr"),(m=document.createElement("td")).colSpan=2,m.appendChild(a),p.appendChild(m),h.appendChild(p)),p=document.createElement("tr"),(m=document.createElement("td")).colSpan=2,m.style.paddingTop="20px",m.style.whiteSpace="nowrap",m.setAttribute("align","right"),mxUtils.button(mxResources.get("cancel"),function(){l.hideDialog(),null!=s&&s()}));e.className="geBtn",l.editor.cancelFirst&&m.appendChild(e),null!=r&&((u=mxUtils.button(mxResources.get("help"),function(){l.editor.graph.openLink(r)})).className="geBtn",m.appendChild(u)),mxEvent.addListener(x,"keypress",function(e){13==e.keyCode&&C.click()}),m.appendChild(C),l.editor.cancelFirst||m.appendChild(e),p.appendChild(m),h.appendChild(p),g.appendChild(h),this.container=g},TextareaDialog=(FilenameDialog.filenameHelpLink=null,FilenameDialog.createTypeHint=function(l,t,n){function e(){i.setAttribute("src",Editor.helpImage),i.setAttribute("title",mxResources.get("help"));for(var e=0;e<n.length;e++)if(0<n[e].ext.length&&t.value.substring(t.value.length-n[e].ext.length-1)=="."+n[e].ext){i.setAttribute("src",mxClient.imageBasePath+"/warning.png"),i.setAttribute("title",mxResources.get(n[e].title));break}}var i=document.createElement("img");i.style.cssText="vertical-align:top;height:16px;width:16px;margin-left:4px;background-repeat:no-repeat;background-position:center bottom;cursor:pointer;",mxUtils.setOpacity(i,70);return mxEvent.addListener(t,"keyup",e),mxEvent.addListener(t,"change",e),mxEvent.addListener(i,"click",function(e){var t=i.getAttribute("title");i.getAttribute("src")==Editor.helpImage?l.editor.graph.openLink(FilenameDialog.filenameHelpLink):""!=t&&l.showError(null,t,mxResources.get("help"),function(){l.editor.graph.openLink(FilenameDialog.filenameHelpLink)},null,mxResources.get("ok"),null,null,null,340,90),mxEvent.consume(e)}),e(),i},function(e,t,l,n,i,o,a,r,d,s,c,u,p,m){a=null!=a?a:300,r=null!=r?r:120,s=null!=s&&s;var g=document.createElement("table"),h=document.createElement("tbody"),x=document.createElement("tr"),C=document.createElement("td"),b=(C.style.fontSize="10pt",C.style.width="100px",mxUtils.write(C,t),x.appendChild(C),h.appendChild(x),x=document.createElement("tr"),C=document.createElement("td"),document.createElement("textarea"));if(c&&b.setAttribute("wrap","off"),b.setAttribute("spellcheck","false"),b.setAttribute("autocorrect","off"),b.setAttribute("autocomplete","off"),b.setAttribute("autocapitalize","off"),mxUtils.write(b,l||""),b.style.resize="none",b.style.width=a+"px",b.style.height=r+"px",this.textarea=b,this.init=function(){b.focus(),b.scrollTop=0},C.appendChild(b),x.appendChild(C),h.appendChild(x),x=document.createElement("tr"),(C=document.createElement("td")).style.paddingTop="14px",C.style.whiteSpace="nowrap",C.setAttribute("align","right"),null!=p&&((t=mxUtils.button(mxResources.get("help"),function(){e.editor.graph.openLink(p)})).className="geBtn",C.appendChild(t)),null!=m)for(var y=0;y<m.length;y++)!function(e,t){e=mxUtils.button(e,function(e){t(e,b)});e.className="geBtn",C.appendChild(e)}(m[y][0],m[y][1]);c=mxUtils.button(o||mxResources.get("cancel"),function(){e.hideDialog(),null!=i&&i()});c.className="geBtn",e.editor.cancelFirst&&C.appendChild(c),null!=d&&d(C,b),null!=n&&((l=mxUtils.button(u||mxResources.get("apply"),function(){s||e.hideDialog(),n(b.value)})).className="geBtn gePrimaryBtn",C.appendChild(l)),e.editor.cancelFirst||C.appendChild(c),x.appendChild(C),h.appendChild(x),g.appendChild(h),this.container=g}),EditDiagramDialog=function(o){var e=document.createElement("div"),a=(e.style.textAlign="right",document.createElement("textarea"));a.setAttribute("wrap","off"),a.setAttribute("spellcheck","false"),a.setAttribute("autocorrect","off"),a.setAttribute("autocomplete","off"),a.setAttribute("autocapitalize","off"),a.style.overflow="auto",a.style.resize="none",a.style.width="600px",a.style.height="360px",a.style.marginBottom="16px",a.value=mxUtils.getPrettyXml(o.editor.getGraphXml()),e.appendChild(a),this.init=function(){a.focus()},Graph.fileSupport&&(a.addEventListener("dragover",function(e){e.stopPropagation(),e.preventDefault()},!1),a.addEventListener("drop",function(e){var t,l;e.stopPropagation(),e.preventDefault(),0<e.dataTransfer.files.length?(t=e.dataTransfer.files[0],(l=new FileReader).onload=function(e){a.value=e.target.result},l.readAsText(t)):a.value=o.extractGraphModelFromEvent(e)},!1));var t=mxUtils.button(mxResources.get("cancel"),function(){o.hideDialog()}),r=(t.className="geBtn",o.editor.cancelFirst&&e.appendChild(t),document.createElement("select")),l=(r.style.width="180px",r.className="geBtn",o.editor.graph.isEnabled()&&((l=document.createElement("option")).setAttribute("value","replace"),mxUtils.write(l,mxResources.get("replaceExistingDrawing")),r.appendChild(l)),document.createElement("option")),l=(l.setAttribute("value","new"),mxUtils.write(l,mxResources.get("openInNewWindow")),EditDiagramDialog.showNewWindowOption&&r.appendChild(l),o.editor.graph.isEnabled()&&((l=document.createElement("option")).setAttribute("value","import"),mxUtils.write(l,mxResources.get("addToExistingDrawing")),r.appendChild(l)),e.appendChild(r),mxUtils.button(mxResources.get("ok"),function(){var e=Graph.zapGremlins(mxUtils.trim(a.value)),t=null;if("new"==r.value)o.hideDialog(),o.editor.editAsNew(e);else if("replace"==r.value){o.editor.graph.model.beginUpdate();try{o.editor.setGraphXml(mxUtils.parseXml(e).documentElement),o.hideDialog()}catch(e){t=e}finally{o.editor.graph.model.endUpdate()}}else if("import"==r.value){o.editor.graph.model.beginUpdate();try{var l=mxUtils.parseXml(e),n=new mxGraphModel,i=(new mxCodec(l).decode(l.documentElement,n),n.getChildren(n.getChildAt(n.getRoot(),0)));o.editor.graph.setSelectionCells(o.editor.graph.importCells(i)),o.hideDialog()}catch(e){t=e}finally{o.editor.graph.model.endUpdate()}}null!=t&&mxUtils.alert(t.message)}));l.className="geBtn gePrimaryBtn",e.appendChild(l),o.editor.cancelFirst||e.appendChild(t),this.container=e},ExportDialog=(EditDiagramDialog.showNewWindowOption=!0,function(a){var e,t,r=a.editor.graph,l=r.getGraphBounds(),n=r.view.scale,i=Math.ceil(l.width/n),o=Math.ceil(l.height/n),l=document.createElement("table"),n=document.createElement("tbody"),d=(l.setAttribute("cellpadding",mxClient.IS_SF?"0":"2"),e=document.createElement("tr"),(t=document.createElement("td")).style.fontSize="10pt",t.style.width="100px",mxUtils.write(t,mxResources.get("filename")+":"),e.appendChild(t),document.createElement("input")),s=(d.setAttribute("value",a.editor.getOrCreateFilename()),d.style.width="180px",(t=document.createElement("td")).appendChild(d),e.appendChild(t),n.appendChild(e),e=document.createElement("tr"),(t=document.createElement("td")).style.fontSize="10pt",mxUtils.write(t,mxResources.get("format")+":"),e.appendChild(t),document.createElement("select")),c=(s.style.width="180px",document.createElement("option")),c=(c.setAttribute("value","png"),mxUtils.write(c,mxResources.get("formatPng")),s.appendChild(c),document.createElement("option")),c=(ExportDialog.showGifOption&&(c.setAttribute("value","gif"),mxUtils.write(c,mxResources.get("formatGif")),s.appendChild(c)),document.createElement("option")),c=(c.setAttribute("value","jpg"),mxUtils.write(c,mxResources.get("formatJpg")),s.appendChild(c),document.createElement("option")),c=(c.setAttribute("value","pdf"),mxUtils.write(c,mxResources.get("formatPdf")),s.appendChild(c),document.createElement("option")),u=(c.setAttribute("value","svg"),mxUtils.write(c,mxResources.get("formatSvg")),s.appendChild(c),ExportDialog.showXmlOption&&((c=document.createElement("option")).setAttribute("value","xml"),mxUtils.write(c,mxResources.get("formatXml")),s.appendChild(c)),(t=document.createElement("td")).appendChild(s),e.appendChild(t),n.appendChild(e),e=document.createElement("tr"),(t=document.createElement("td")).style.fontSize="10pt",mxUtils.write(t,mxResources.get("zoom")+" (%):"),e.appendChild(t),document.createElement("input")),p=(u.setAttribute("type","number"),u.setAttribute("value","100"),u.style.width="180px",(t=document.createElement("td")).appendChild(u),e.appendChild(t),n.appendChild(e),e=document.createElement("tr"),(t=document.createElement("td")).style.fontSize="10pt",mxUtils.write(t,mxResources.get("width")+":"),e.appendChild(t),document.createElement("input")),m=(p.setAttribute("value",i),p.style.width="180px",(t=document.createElement("td")).appendChild(p),e.appendChild(t),n.appendChild(e),e=document.createElement("tr"),(t=document.createElement("td")).style.fontSize="10pt",mxUtils.write(t,mxResources.get("height")+":"),e.appendChild(t),document.createElement("input")),g=(m.setAttribute("value",o),m.style.width="180px",(t=document.createElement("td")).appendChild(m),e.appendChild(t),n.appendChild(e),e=document.createElement("tr"),(t=document.createElement("td")).style.fontSize="10pt",mxUtils.write(t,mxResources.get("dpi")+":"),e.appendChild(t),document.createElement("select")),c=(g.style.width="180px",document.createElement("option")),c=(c.setAttribute("value","100"),mxUtils.write(c,"100dpi"),g.appendChild(c),document.createElement("option")),c=(c.setAttribute("value","200"),mxUtils.write(c,"200dpi"),g.appendChild(c),document.createElement("option")),c=(c.setAttribute("value","300"),mxUtils.write(c,"300dpi"),g.appendChild(c),document.createElement("option")),c=(c.setAttribute("value","400"),mxUtils.write(c,"400dpi"),g.appendChild(c),document.createElement("option")),h=(c.setAttribute("value","custom"),mxUtils.write(c,mxResources.get("custom")),g.appendChild(c),document.createElement("input")),x=(h.style.width="180px",h.style.display="none",h.setAttribute("value","100"),h.setAttribute("type","number"),h.setAttribute("min","50"),h.setAttribute("step","50"),!1),C=(mxEvent.addListener(g,"change",function(){"custom"==this.value?(this.style.display="none",h.style.display="",h.focus()):(h.value=this.value,x||(u.value=this.value))}),mxEvent.addListener(h,"change",function(){var e=parseInt(h.value);isNaN(e)||e<=0?h.style.backgroundColor="red":(h.style.backgroundColor="",x||(u.value=e))}),(t=document.createElement("td")).appendChild(g),t.appendChild(h),e.appendChild(t),n.appendChild(e),e=document.createElement("tr"),(t=document.createElement("td")).style.fontSize="10pt",mxUtils.write(t,mxResources.get("background")+":"),e.appendChild(t),document.createElement("input")),b=(C.setAttribute("type","checkbox"),C.checked=null==r.background||r.background==mxConstants.NONE,(t=document.createElement("td")).appendChild(C),mxUtils.write(t,mxResources.get("transparent")),e.appendChild(t),n.appendChild(e),e=document.createElement("tr"),(t=document.createElement("td")).style.fontSize="10pt",mxUtils.write(t,mxResources.get("borderWidth")+":"),e.appendChild(t),document.createElement("input"));function y(){var e=d.value,t=e.lastIndexOf(".");d.value=0<t?e.substring(0,t+1)+s.value:e+"."+s.value,"xml"===s.value?(u.setAttribute("disabled","true"),p.setAttribute("disabled","true"),m.setAttribute("disabled","true"),b.setAttribute("disabled","true")):(u.removeAttribute("disabled"),p.removeAttribute("disabled"),m.removeAttribute("disabled"),b.removeAttribute("disabled")),"png"===s.value||"svg"===s.value?C.removeAttribute("disabled"):C.setAttribute("disabled","disabled"),"png"===s.value?(g.removeAttribute("disabled"),h.removeAttribute("disabled")):(g.setAttribute("disabled","disabled"),h.setAttribute("disabled","disabled"))}function v(){p.value*m.value>MAX_AREA||p.value<=0?p.style.backgroundColor="red":p.style.backgroundColor="",p.value*m.value>MAX_AREA||m.value<=0?m.style.backgroundColor="red":m.style.backgroundColor=""}b.setAttribute("type","number"),b.setAttribute("value",ExportDialog.lastBorderValue),b.style.width="180px",(t=document.createElement("td")).appendChild(b),e.appendChild(t),n.appendChild(e),l.appendChild(n),mxEvent.addListener(s,"change",y),y(),mxEvent.addListener(u,"change",function(){x=!0;var e=Math.max(0,parseFloat(u.value)||100)/100;u.value=parseFloat((100*e).toFixed(2)),0<i?(p.value=Math.floor(i*e),m.value=Math.floor(o*e)):(u.value="100",p.value=i,m.value=o),v()}),mxEvent.addListener(p,"change",function(){var e=parseInt(p.value)/i;0<e?(u.value=parseFloat((100*e).toFixed(2)),m.value=Math.floor(o*e)):(u.value="100",p.value=i,m.value=o),v()}),mxEvent.addListener(m,"change",function(){var e=parseInt(m.value)/o;0<e?(u.value=parseFloat((100*e).toFixed(2)),p.value=Math.floor(i*e)):(u.value="100",p.value=i,m.value=o),v()}),e=document.createElement("tr"),(t=document.createElement("td")).setAttribute("align","right"),t.style.paddingTop="22px",t.colSpan=2;var c=mxUtils.button(mxResources.get("export"),mxUtils.bind(this,function(){var e,t,l,n,i,o;parseInt(u.value)<=0?mxUtils.alert(mxResources.get("drawingEmpty")):(e=d.value,t=s.value,l=Math.max(0,parseFloat(u.value)||100)/100,n=Math.max(0,parseInt(b.value)),i=r.background,o=Math.max(1,parseInt(h.value)),"svg"!=t&&"png"!=t||!C.checked?null!=i&&i!=mxConstants.NONE||(i="#ffffff"):i=null,ExportDialog.lastBorderValue=n,ExportDialog.exportFile(a,e,t,i,l,n,o))})),f=(c.className="geBtn gePrimaryBtn",mxUtils.button(mxResources.get("cancel"),function(){a.hideDialog()}));f.className="geBtn",a.editor.cancelFirst?(t.appendChild(f),t.appendChild(c)):(t.appendChild(c),t.appendChild(f)),e.appendChild(t),n.appendChild(e),l.appendChild(n),this.container=l}),EditDataDialog=(ExportDialog.lastBorderValue=0,ExportDialog.showGifOption=!0,ExportDialog.showXmlOption=!0,ExportDialog.exportFile=function(e,t,l,n,i,o,a){var r,d,s,c=e.editor.graph;"xml"==l?ExportDialog.saveLocalFile(e,mxUtils.getXml(e.editor.getGraphXml()),t,l):"svg"==l?ExportDialog.saveLocalFile(e,mxUtils.getXml(c.getSvg(n,i,o)),t,l):(s=c.getGraphBounds(),d=(r=mxUtils.createXmlDocument()).createElement("output"),r.appendChild(d),(r=new mxXmlCanvas2D(d)).translate(Math.floor((o/i-s.x)/c.view.scale),Math.floor((o/i-s.y)/c.view.scale)),r.scale(i/c.view.scale),(new mxImageExport).drawState(c.getView().getState(c.model.root),r),r="xml="+encodeURIComponent(mxUtils.getXml(d)),d=Math.ceil(s.width*i/c.view.scale+2*o),s=Math.ceil(s.height*i/c.view.scale+2*o),r.length<=MAX_REQUEST_SIZE&&d*s<MAX_AREA?(e.hideDialog(),new mxXmlRequest(EXPORT_URL,"format="+l+"&filename="+encodeURIComponent(t)+"&bg="+(null!=n?n:"none")+"&w="+d+"&h="+s+"&"+r+"&dpi="+a).simulate(document,"_blank")):mxUtils.alert(mxResources.get("drawingTooLarge")))},ExportDialog.saveLocalFile=function(e,t,l,n){t.length<MAX_REQUEST_SIZE?(e.hideDialog(),new mxXmlRequest(SAVE_URL,"xml="+encodeURIComponent(t)+"&filename="+encodeURIComponent(l)+"&format="+n).simulate(document,"_blank")):(mxUtils.alert(mxResources.get("drawingTooLarge")),mxUtils.popup(xml))},function(l,n){for(var e=document.createElement("div"),i=l.editor.graph,o=i.getModel().getValue(n),r=(mxUtils.isNode(o)||((b=mxUtils.createXmlDocument().createElement("object")).setAttribute("label",o||""),o=b),new mxForm("properties")),t=(r.table.style.width="100%",o.attributes),d=[],s=[],a=0,c=null!=EditDataDialog.getDisplayIdForCell?EditDataDialog.getDisplayIdForCell(l,n):null,u=function(e,t){function l(){for(var e=0,t=0;t<d.length;t++){if(d[t]==n){s[t]=null,r.table.deleteRow(e+(null!=c?1:0));break}null!=s[t]&&e++}}var n,i=document.createElement("div"),o=(i.style.position="relative",i.style.paddingRight="20px",i.style.boxSizing="border-box",i.style.width="100%",document.createElement("a")),a=mxUtils.createImage(Dialog.prototype.closeImage),a=(a.style.height="9px",a.style.fontSize="9px",a.style.marginBottom=mxClient.IS_IE11?"-1px":"5px",o.className="geButton",o.setAttribute("title",mxResources.get("delete")),o.style.position="absolute",o.style.top="4px",o.style.right="0px",o.style.margin="0px",o.style.width="9px",o.style.height="9px",o.style.cursor="pointer",o.appendChild(a),n=t,mxEvent.addListener(o,"click",l),e.parentNode);i.appendChild(e),i.appendChild(o),a.appendChild(i)},p=[],m=i.getModel().getParent(n)==i.getModel().getRoot(),g=0;g<t.length;g++)!m&&"label"==t[g].nodeName||"placeholders"==t[g].nodeName||p.push({name:t[g].nodeName,value:t[g].nodeValue});p.sort(function(e,t){return e.name<t.name?-1:e.name>t.name?1:0}),null!=c&&((b=document.createElement("div")).style.width="100%",b.style.fontSize="11px",b.style.textAlign="center",mxUtils.write(b,c),r.addField(mxResources.get("id")+":",b));for(var h,x,C,g=0;g<p.length;g++)h=a,x=p[g].name,C=p[g].value,d[h]=x,s[h]=r.addTextarea(d[a]+":",C,2),s[h].style.width="100%",u(s[h],x),a++;var b=document.createElement("div"),y=(b.style.cssText="position:absolute;left:30px;right:30px;overflow-y:auto;top:30px;bottom:80px;",b.appendChild(r.table),document.createElement("div")),v=(y.style.boxSizing="border-box",y.style.paddingRight="160px",y.style.whiteSpace="nowrap",y.style.marginTop="6px",y.style.width="100%",document.createElement("input")),f=(v.setAttribute("placeholder",mxResources.get("enterPropertyName")),v.setAttribute("type","text"),v.setAttribute("size",mxClient.IS_IE||mxClient.IS_IE11?"36":"40"),v.style.boxSizing="border-box",v.style.marginLeft="2px",v.style.width="100%",y.appendChild(v),b.appendChild(y),e.appendChild(b),mxUtils.button(mxResources.get("addProperty"),function(){var e=v.value;if(0<e.length&&"label"!=e&&"placeholders"!=e&&e.indexOf(":")<0)try{var t,l=mxUtils.indexOf(d,e);0<=l&&null!=s[l]?s[l].focus():(o.cloneNode(!1).setAttribute(e,""),0<=l&&(d.splice(l,1),s.splice(l,1)),d.push(e),(t=r.addTextarea(e+":","",2)).style.width="100%",s.push(t),u(t,e),t.focus()),f.setAttribute("disabled","disabled"),v.value=""}catch(e){mxUtils.alert(e)}else mxUtils.alert(mxResources.get("invalidName"))})),b=(this.init=function(){(0<s.length?s[0]:v).focus()},f.setAttribute("title",mxResources.get("addProperty")),f.setAttribute("disabled","disabled"),f.style.textOverflow="ellipsis",f.style.position="absolute",f.style.overflow="hidden",f.style.width="144px",f.style.right="0px",f.className="geBtn",y.appendChild(f),mxUtils.button(mxResources.get("cancel"),function(){l.hideDialog.apply(l,arguments)})),y=(b.className="geBtn",mxUtils.button(mxResources.get("apply"),function(){try{l.hideDialog.apply(l,arguments),o=o.cloneNode(!0);for(var e=!1,t=0;t<d.length;t++)null==s[t]?o.removeAttribute(d[t]):(o.setAttribute(d[t],s[t].value),e=e||"placeholder"==d[t]&&"1"==o.getAttribute("placeholders"));e&&o.removeAttribute("label"),i.getModel().setValue(n,o)}catch(e){mxUtils.alert(e)}}));function E(){0<v.value.length?f.removeAttribute("disabled"):f.setAttribute("disabled","disabled")}y.className="geBtn gePrimaryBtn",mxEvent.addListener(v,"keyup",E),mxEvent.addListener(v,"change",E);var w,F,A,k=document.createElement("div");k.style.cssText="position:absolute;left:30px;right:30px;text-align:right;bottom:30px;height:40px;",(l.editor.graph.getModel().isVertex(n)||l.editor.graph.getModel().isEdge(n))&&((w=document.createElement("span")).style.marginRight="10px",(F=document.createElement("input")).setAttribute("type","checkbox"),F.style.marginRight="6px","1"==o.getAttribute("placeholders")&&(F.setAttribute("checked","checked"),F.defaultChecked=!0),mxEvent.addListener(F,"click",function(){"1"==o.getAttribute("placeholders")?o.removeAttribute("placeholders"):o.setAttribute("placeholders","1")}),w.appendChild(F),mxUtils.write(w,mxResources.get("placeholders")),null!=EditDataDialog.placeholderHelpLink&&((F=document.createElement("a")).setAttribute("href",EditDataDialog.placeholderHelpLink),F.setAttribute("title",mxResources.get("help")),F.setAttribute("target","_blank"),F.style.marginLeft="8px",F.style.cursor="help",A=document.createElement("img"),mxUtils.setOpacity(A,50),A.style.height="16px",A.style.width="16px",A.setAttribute("border","0"),A.setAttribute("valign","middle"),A.style.marginTop=mxClient.IS_IE11?"0px":"-4px",A.setAttribute("src",Editor.helpImage),F.appendChild(A),w.appendChild(F)),k.appendChild(w)),l.editor.cancelFirst?(k.appendChild(b),k.appendChild(y)):(k.appendChild(y),k.appendChild(b)),e.appendChild(k),this.container=e}),LinkDialog=(EditDataDialog.getDisplayIdForCell=function(e,t){var l=null;return l=null!=e.editor.graph.getModel().getParent(t)?t.getId():l},EditDataDialog.placeholderHelpLink=null,function(t,e,l,n){var i=document.createElement("div"),o=(mxUtils.write(i,mxResources.get("editLink")+":"),document.createElement("div")),a=(o.className="geTitle",o.style.backgroundColor="transparent",o.style.borderColor="transparent",o.style.whiteSpace="nowrap",o.style.textOverflow="clip",o.style.cursor="default",mxClient.IS_VML||(o.style.paddingRight="20px"),document.createElement("input")),e=(a.setAttribute("value",e),a.setAttribute("placeholder","http://www.example.com/"),a.setAttribute("type","text"),a.style.marginTop="6px",a.style.width="400px",a.style.backgroundImage="url('"+Dialog.prototype.clearImage+"')",a.style.backgroundRepeat="no-repeat",a.style.backgroundPosition="100% 50%",a.style.paddingRight="14px",document.createElement("div")),e=(e.setAttribute("title",mxResources.get("reset")),e.style.position="relative",e.style.left="-16px",e.style.width="12px",e.style.height="14px",e.style.cursor="pointer",e.style.display=mxClient.IS_VML?"inline":"inline-block",e.style.top=(mxClient.IS_VML?0:3)+"px",e.style.background="url("+IMAGE_PATH+"/transparent.gif)",mxEvent.addListener(e,"click",function(){a.value="",a.focus()}),o.appendChild(a),o.appendChild(e),i.appendChild(o),this.init=function(){a.focus(),mxClient.IS_GC||mxClient.IS_FF||5<=document.documentMode||mxClient.IS_QUIRKS?a.select():document.execCommand("selectAll",!1,null)},document.createElement("div")),o=(e.style.marginTop="18px",e.style.textAlign="right",mxEvent.addListener(a,"keypress",function(e){13==e.keyCode&&(t.hideDialog(),n(a.value))}),mxUtils.button(mxResources.get("cancel"),function(){t.hideDialog()})),l=(o.className="geBtn",t.editor.cancelFirst&&e.appendChild(o),mxUtils.button(l,function(){t.hideDialog(),n(a.value)}));l.className="geBtn gePrimaryBtn",e.appendChild(l),t.editor.cancelFirst||e.appendChild(o),i.appendChild(e),this.container=i}),OutlineWindow=function(e,t,l,n,i){var o,a,r=e.editor.graph,d=document.createElement("div"),s=(d.style.position="absolute",d.style.width="100%",d.style.height="100%",d.style.border="1px solid whiteSmoke",d.style.overflow="hidden",this.window=new mxWindow(mxResources.get("outline"),d,t,l,n,i,!0,!0),this.window.minimumSize=new mxRectangle(0,0,80,80),this.window.destroyOnClose=!1,this.window.setMaximizable(!1),this.window.setResizable(!0),this.window.setClosable(!0),this.window.setVisible(!0),this.window.setLocation=function(e,t){var l=window.innerWidth||document.body.clientWidth||document.documentElement.clientWidth,n=window.innerHeight||document.body.clientHeight||document.documentElement.clientHeight;e=Math.max(0,Math.min(e,l-this.table.clientWidth)),t=Math.max(0,Math.min(t,n-this.table.clientHeight-48)),this.getX()==e&&this.getY()==t||mxWindow.prototype.setLocation.apply(this,arguments)},mxUtils.bind(this,function(){var e=this.window.getX(),t=this.window.getY();this.window.setLocation(e,t)})),c=(mxEvent.addListener(window,"resize",s),e.createOutline(this.window)),u=(this.destroy=function(){mxEvent.removeListener(window,"resize",s),this.window.destroy(),c.destroy()},this.window.addListener(mxEvent.RESIZE,mxUtils.bind(this,function(){c.update(!1),c.outline.sizeDidChange()})),this.window.addListener(mxEvent.SHOW,mxUtils.bind(this,function(){this.window.fit(),c.suspended=!1,c.outline.refresh(),c.update()})),this.window.addListener(mxEvent.HIDE,mxUtils.bind(this,function(){c.suspended=!0})),this.window.addListener(mxEvent.NORMALIZE,mxUtils.bind(this,function(){c.suspended=!1,c.update()})),this.window.addListener(mxEvent.MINIMIZE,mxUtils.bind(this,function(){c.suspended=!0})),c.createGraph);function p(){c.outline.pageScale=r.pageScale,c.outline.pageFormat=r.pageFormat,c.outline.pageVisible=r.pageVisible,c.outline.background=null==r.background||r.background==mxConstants.NONE?r.defaultPageBackgroundColor:r.background;var e=mxUtils.getCurrentStyle(r.container);d.style.backgroundColor=e.backgroundColor,null!=r.view.backgroundPageShape&&null!=c.outline.view.backgroundPageShape&&(c.outline.view.backgroundPageShape.fill=r.view.backgroundPageShape.fill),c.outline.refresh()}c.createGraph=function(e){var t=u.apply(this,arguments),l=(t.gridEnabled=!1,t.pageScale=r.pageScale,t.pageFormat=r.pageFormat,t.background=null==r.background||r.background==mxConstants.NONE?r.defaultPageBackgroundColor:r.background,t.pageVisible=r.pageVisible,mxUtils.getCurrentStyle(r.container));return d.style.backgroundColor=l.backgroundColor,t},c.init(d),e.editor.addListener("resetGraphView",p),e.addListener("pageFormatChanged",p),e.addListener("backgroundColorChanged",p),e.addListener("backgroundImageChanged",p),e.addListener("pageViewChanged",function(){p(),c.update(!0)}),c.outline.dialect==mxConstants.DIALECT_SVG&&(o=e.actions.get("zoomIn"),a=e.actions.get("zoomOut"),mxEvent.addMouseWheelListener(function(e,t){for(var l=!1,n=mxEvent.getSource(e);null!=n;){if(n==c.outline.view.canvas.ownerSVGElement){l=!0;break}n=n.parentNode}l&&(t?o:a).funct()}))},LayersWindow=function(s,e,t,l,n){var c=s.editor.graph,i=document.createElement("div"),o=(i.style.userSelect="none",i.style.background="white"==Dialog.backdropColor?"whiteSmoke":Dialog.backdropColor,i.style.border="1px solid whiteSmoke",i.style.height="100%",i.style.marginBottom="10px",i.style.overflow="auto",EditorUi.compactUi?"26px":"30px"),u=document.createElement("div"),p=(u.style.backgroundColor="white"==Dialog.backdropColor?"#dcdcdc":Dialog.backdropColor,u.style.position="absolute",u.style.overflow="auto",u.style.left="0px",u.style.right="0px",u.style.top="0px",u.style.bottom=parseInt(o)+7+"px",i.appendChild(u),null),m=null,g=(mxEvent.addListener(i,"dragover",function(e){e.dataTransfer.dropEffect="move",m=0,e.stopPropagation(),e.preventDefault()}),mxEvent.addListener(i,"drop",function(e){e.stopPropagation(),e.preventDefault()}),null),h=null,a=document.createElement("div"),o=(a.className="geToolbarContainer",a.style.position="absolute",a.style.bottom="0px",a.style.left="0px",a.style.right="0px",a.style.height=o,a.style.overflow="hidden",a.style.padding=EditorUi.compactUi?"4px 0px 3px 0px":"1px",a.style.backgroundColor="white"==Dialog.backdropColor?"whiteSmoke":Dialog.backdropColor,a.style.borderWidth="1px 0px 0px 0px",a.style.borderColor="#c3c3c3",a.style.borderStyle="solid",a.style.display="block",a.style.whiteSpace="nowrap",mxClient.IS_QUIRKS&&(a.style.filter="none"),document.createElement("a")),r=(o.className="geButton",mxClient.IS_QUIRKS&&(o.style.filter="none"),o.cloneNode()),d=(r.innerHTML='<div class="geSprite geSprite-delete" style="display:inline-block;"></div>',mxEvent.addListener(r,"click",function(e){if(c.isEnabled()){c.model.beginUpdate();try{var t=c.model.root.getIndex(h);c.removeCells([h],!1),0==c.model.getChildCount(c.model.root)?(c.model.add(c.model.root,new mxCell),c.setDefaultParent(null)):0<t&&t<=c.model.getChildCount(c.model.root)?c.setDefaultParent(c.model.getChildAt(c.model.root,t-1)):c.setDefaultParent(null)}finally{c.model.endUpdate()}}mxEvent.consume(e)}),c.isEnabled()||(r.className="geButton mxDisabled"),a.appendChild(r),o.cloneNode()),x=(d.setAttribute("title",mxUtils.trim(mxResources.get("moveSelectionTo",[""]))),d.innerHTML='<div class="geSprite geSprite-insert" style="display:inline-block;"></div>',mxEvent.addListener(d,"click",function(e){var t,l;c.isEnabled()&&!c.isSelectionEmpty()&&(s.editor.graph.popupMenuHandler.hideMenu(),(t=new mxPopupMenu(mxUtils.bind(this,function(l,n){for(var e=g-1;0<=e;e--)mxUtils.bind(this,function(e){var t=l.addItem(c.convertValueToString(e)||mxResources.get("background"),null,mxUtils.bind(this,function(){c.moveCells(c.getSelectionCells(),0,0,!1,e)}),n);1==c.getSelectionCount()&&c.model.isAncestor(e,c.getSelectionCell())&&l.addCheckmark(t,Editor.checkmarkImage)})(c.model.getChildAt(c.model.root,e))}))).div.className+=" geMenubarMenu",t.smartSeparators=!0,t.showDisabled=!0,t.autoExpand=!0,t.hideMenu=mxUtils.bind(this,function(){mxPopupMenu.prototype.hideMenu.apply(t,arguments),t.destroy()}),l=mxUtils.getOffset(d),t.popup(l.x,l.y+d.offsetHeight,null,e),s.setCurrentMenu(t))}),a.appendChild(d),o.cloneNode());x.innerHTML='<div class="geSprite geSprite-dots" style="display:inline-block;"></div>',x.setAttribute("title",mxResources.get("rename")),mxEvent.addListener(x,"click",function(e){c.isEnabled()&&s.showDataDialog(h),mxEvent.consume(e)}),c.isEnabled()||(x.className="geButton mxDisabled"),a.appendChild(x);var C=o.cloneNode(),o=(C.innerHTML='<div class="geSprite geSprite-duplicate" style="display:inline-block;"></div>',mxEvent.addListener(C,"click",function(e){if(c.isEnabled()){var t=null;c.model.beginUpdate();try{t=c.cloneCell(h),c.cellLabelChanged(t,mxResources.get("untitledLayer")),t.setVisible(!0),t=c.addCell(t,c.model.root),c.setDefaultParent(t)}finally{c.model.endUpdate()}null==t||c.isCellLocked(t)||c.selectAll(t)}}),c.isEnabled()||(C.className="geButton mxDisabled"),a.appendChild(C),o.cloneNode());function b(){function t(t,e,l,n){var i=document.createElement("div"),o=(i.className="geToolbarContainer",i.style.overflow="hidden",i.style.position="relative",i.style.padding="4px",i.style.height="22px",i.style.display="block",i.style.backgroundColor="white"==Dialog.backdropColor?"whiteSmoke":Dialog.backdropColor,i.style.borderWidth="0px 0px 1px 0px",i.style.borderColor="#c3c3c3",i.style.borderStyle="solid",i.style.whiteSpace="nowrap",i.setAttribute("title",e),document.createElement("div")),a=(o.style.display="inline-block",o.style.width="100%",o.style.textOverflow="ellipsis",o.style.overflow="hidden",mxEvent.addListener(i,"dragover",function(e){e.dataTransfer.dropEffect="move",m=t,e.stopPropagation(),e.preventDefault()}),mxEvent.addListener(i,"dragstart",function(e){p=i,mxClient.IS_FF&&e.dataTransfer.setData("Text","<layer/>")}),mxEvent.addListener(i,"dragend",function(e){null!=p&&null!=m&&c.addCell(l,c.model.root,m),m=p=null,e.stopPropagation(),e.preventDefault()}),document.createElement("img")),r=(a.setAttribute("draggable","false"),a.setAttribute("align","top"),a.setAttribute("border","0"),a.style.padding="4px",a.setAttribute("title",mxResources.get("lockUnlock")),c.view.getState(l)),d=null!=r?r.style:c.getCellStyle(l),r=("1"==mxUtils.getValue(d,"locked","0")?a.setAttribute("src",Dialog.prototype.lockedImage):a.setAttribute("src",Dialog.prototype.unlockedImage),c.isEnabled()&&(a.style.cursor="pointer"),mxEvent.addListener(a,"click",function(e){if(c.isEnabled()){var t=null;c.getModel().beginUpdate();try{t="1"==mxUtils.getValue(d,"locked","0")?null:"1",c.setCellStyles("locked",t,[l])}finally{c.getModel().endUpdate()}"1"==t&&c.removeSelectionCells(c.getModel().getDescendants(l)),mxEvent.consume(e)}}),o.appendChild(a),document.createElement("input"));r.setAttribute("type","checkbox"),r.setAttribute("title",mxResources.get("hideIt",[l.value||mxResources.get("background")])),r.style.marginLeft="4px",r.style.marginRight="6px",r.style.marginTop="4px",o.appendChild(r),c.model.isVisible(l)&&(r.setAttribute("checked","checked"),r.defaultChecked=!0),mxEvent.addListener(r,"click",function(e){c.model.setVisible(l,!c.model.isVisible(l)),mxEvent.consume(e)}),mxUtils.write(o,e),i.appendChild(o),c.isEnabled()&&((mxClient.IS_TOUCH||mxClient.IS_POINTER||mxClient.IS_VML||mxClient.IS_IE&&document.documentMode<10)&&((a=document.createElement("div")).style.display="block",a.style.textAlign="right",a.style.whiteSpace="nowrap",a.style.position="absolute",a.style.right="6px",a.style.top="6px",0<t&&((r=document.createElement("a")).setAttribute("title",mxResources.get("toBack")),r.className="geButton",r.style.cssFloat="none",r.innerHTML="&#9660;",r.style.width="14px",r.style.height="14px",r.style.fontSize="14px",r.style.margin="0px",r.style.marginTop="-1px",a.appendChild(r),mxEvent.addListener(r,"click",function(e){c.isEnabled()&&c.addCell(l,c.model.root,t-1),mxEvent.consume(e)})),0<=t&&t<g-1&&((e=document.createElement("a")).setAttribute("title",mxResources.get("toFront")),e.className="geButton",e.style.cssFloat="none",e.innerHTML="&#9650;",e.style.width="14px",e.style.height="14px",e.style.fontSize="14px",e.style.margin="0px",e.style.marginTop="-1px",a.appendChild(e),mxEvent.addListener(e,"click",function(e){c.isEnabled()&&c.addCell(l,c.model.root,t+1),mxEvent.consume(e)})),i.appendChild(a)),mxClient.IS_SVG&&(!mxClient.IS_IE||10<=document.documentMode)&&(i.setAttribute("draggable","true"),i.style.cursor="move")),mxEvent.addListener(i,"dblclick",function(e){var t=mxEvent.getSource(e).nodeName;"INPUT"!=t&&"IMG"!=t&&(!function(t){var e;c.isEnabled()&&null!=t&&(e=c.convertValueToString(t),e=new FilenameDialog(s,e||mxResources.get("background"),mxResources.get("rename"),mxUtils.bind(this,function(e){null!=e&&c.cellLabelChanged(t,e)}),mxResources.get("enterName")),s.showDialog(e.container,300,100,!0,!0),e.init())}(l),mxEvent.consume(e))}),c.getDefaultParent()==l?(i.style.background="white"==Dialog.backdropColor?"#e6eff8":"#505759",i.style.fontWeight=c.isEnabled()?"bold":"",h=l):mxEvent.addListener(i,"click",function(e){c.isEnabled()&&(c.setDefaultParent(n),c.view.setCurrentRoot(null),b())}),u.appendChild(i)}g=c.model.getChildCount(c.model.root),u.innerHTML="";for(var l=g-1;0<=l;l--)mxUtils.bind(this,function(e){t(l,c.convertValueToString(e)||mxResources.get("background"),e,e)})(c.model.getChildAt(c.model.root,l));var e=c.convertValueToString(h)||mxResources.get("background");r.setAttribute("title",mxResources.get("removeIt",[e])),C.setAttribute("title",mxResources.get("duplicateIt",[e])),x.setAttribute("title",mxResources.get("editData")),c.isSelectionEmpty()&&(d.className="geButton mxDisabled")}o.innerHTML='<div class="geSprite geSprite-plus" style="display:inline-block;"></div>',o.setAttribute("title",mxResources.get("addLayer")),mxEvent.addListener(o,"click",function(e){if(c.isEnabled()){c.model.beginUpdate();try{var t=c.addCell(new mxCell(mxResources.get("untitledLayer")),c.model.root);c.setDefaultParent(t)}finally{c.model.endUpdate()}}mxEvent.consume(e)}),c.isEnabled()||(o.className="geButton mxDisabled"),a.appendChild(o),i.appendChild(a),b(),c.model.addListener(mxEvent.CHANGE,function(){b()}),c.selectionModel.addListener(mxEvent.CHANGE,function(){c.isSelectionEmpty()?d.className="geButton mxDisabled":d.className="geButton"}),this.window=new mxWindow(mxResources.get("layers"),i,e,t,l,n,!0,!0),this.window.minimumSize=new mxRectangle(0,0,120,120),this.window.destroyOnClose=!1,this.window.setMaximizable(!1),this.window.setResizable(!0),this.window.setClosable(!0),this.window.setVisible(!0),this.window.addListener(mxEvent.SHOW,mxUtils.bind(this,function(){this.window.fit()})),this.refreshLayers=b,this.window.setLocation=function(e,t){var l=window.innerWidth||document.body.clientWidth||document.documentElement.clientWidth,n=window.innerHeight||document.body.clientHeight||document.documentElement.clientHeight;e=Math.max(0,Math.min(e,l-this.table.clientWidth)),t=Math.max(0,Math.min(t,n-this.table.clientHeight-48)),this.getX()==e&&this.getY()==t||mxWindow.prototype.setLocation.apply(this,arguments)};var y=mxUtils.bind(this,function(){var e=this.window.getX(),t=this.window.getY();this.window.setLocation(e,t)});mxEvent.addListener(window,"resize",y),this.destroy=function(){mxEvent.removeListener(window,"resize",y),this.window.destroy()}};