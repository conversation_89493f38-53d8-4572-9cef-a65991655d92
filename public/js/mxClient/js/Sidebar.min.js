function Sidebar(e,t){this.editorUi=e;this.container=t;this.palettes=new Object;this.taglist=new Object;this.showTooltips=true;this.graph=e.createTemporaryGraph(this.editorUi.editor.graph.getStylesheet());this.graph.cellRenderer.minSvgStrokeWidth=this.minThumbStrokeWidth;this.graph.cellRenderer.antiAlias=this.thumbAntiAlias;this.graph.container.style.visibility="hidden";this.graph.foldingEnabled=false;this.pointerUpHandler=mxUtils.bind(this,function(){this.showTooltips=true});mxEvent.addListener(document,mxClient.IS_POINTER?"pointerup":"mouseup",this.pointerUpHandler);this.pointerDownHandler=mxUtils.bind(this,function(){this.showTooltips=false;this.hideTooltip()});mxEvent.addListener(document,mxClient.IS_POINTER?"pointerdown":"mousedown",this.pointerDownHandler);this.pointerMoveHandler=mxUtils.bind(this,function(e){if(!mxEvent){return}var t=mxEvent.getSource(e);while(t!=null){if(t==this.currentElt){return}t=t.parentNode}this.hideTooltip()});mxEvent.addListener(document,mxClient.IS_POINTER?"pointermove":"mousemove",this.pointerMoveHandler);this.pointerOutHandler=mxUtils.bind(this,function(e){if(e.toElement==null&&e.relatedTarget==null){this.hideTooltip()}});mxEvent.addListener(document,mxClient.IS_POINTER?"pointerout":"mouseout",this.pointerOutHandler);mxEvent.addListener(t,"scroll",mxUtils.bind(this,function(){this.showTooltips=true;this.hideTooltip()}));this.init()}Sidebar.prototype.init=function(){var e=STENCIL_PATH;this.addSearchPalette(true);this.addGeneralPalette(true);this.addMiscPalette(false);this.addAdvancedPalette(false);this.addBasicPalette(e);this.addStencilPalette("arrows",mxResources.get("arrows"),e+"/arrows.xml",";whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;strokeWidth=2");this.addUmlPalette(false);this.addBpmnPalette(e,false);this.addStencilPalette("flowchart","Flowchart",e+"/flowchart.xml",";whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;strokeWidth=2");this.addImagePalette("clipart",mxResources.get("clipart"),e+"/clipart/","_128x128.png",["Earth_globe","Empty_Folder","Full_Folder","Gear","Lock","Software","Virus","Email","Database","Router_Icon","iPad","iMac","Laptop","MacBook","Monitor_Tower","Printer","Server_Tower","Workstation","Firewall_02","Wireless_Router_N","Credit_Card","Piggy_Bank","Graph","Safe","Shopping_Cart","Suit1","Suit2","Suit3","Pilot1","Worker1","Soldier1","Doctor1","Tech1","Security1","Telesales1"],null,{Wireless_Router_N:"wireless router switch wap wifi access point wlan",Router_Icon:"router switch"})};Sidebar.prototype.collapsedImage=!mxClient.IS_SVG?IMAGE_PATH+"/collapsed.gif":"data:image/gif;base64,R0lGODlhDQANAIABAJmZmf///yH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IE1hY2ludG9zaCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDozNUQyRTJFNjZGNUYxMUU1QjZEOThCNDYxMDQ2MzNCQiIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDozNUQyRTJFNzZGNUYxMUU1QjZEOThCNDYxMDQ2MzNCQiI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjFERjc3MEUxNkY1RjExRTVCNkQ5OEI0NjEwNDYzM0JCIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjFERjc3MEUyNkY1RjExRTVCNkQ5OEI0NjEwNDYzM0JCIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkEAQAAAQAsAAAAAA0ADQAAAhSMj6lrwAjcC1GyahV+dcZJgeIIFgA7";Sidebar.prototype.expandedImage=!mxClient.IS_SVG?IMAGE_PATH+"/expanded.gif":"data:image/gif;base64,R0lGODlhDQANAIABAJmZmf///yH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IE1hY2ludG9zaCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDoxREY3NzBERjZGNUYxMUU1QjZEOThCNDYxMDQ2MzNCQiIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDoxREY3NzBFMDZGNUYxMUU1QjZEOThCNDYxMDQ2MzNCQiI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjFERjc3MERENkY1RjExRTVCNkQ5OEI0NjEwNDYzM0JCIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjFERjc3MERFNkY1RjExRTVCNkQ5OEI0NjEwNDYzM0JCIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkEAQAAAQAsAAAAAA0ADQAAAhGMj6nL3QAjVHIu6azbvPtWAAA7";Sidebar.prototype.searchImage=!mxClient.IS_SVG?IMAGE_PATH+"/search.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAEaSURBVHjabNGxS5VxFIfxz71XaWuQUJCG/gCHhgTD9VpEETg4aMOlQRp0EoezObgcd220KQiXmpretTAHQRBdojlQEJyukPdt+b1ywfvAGc7wnHP4nlZd1yKijQW8xzNc4Su+ZOYfQ3T6/f4YNvEJYzjELXp4VVXVz263+7cR2niBxAFeZ2YPi3iHR/gYERPDwhpOsd6sz8x/mfkNG3iOlWFhFj8y89J9KvzGXER0GuEaD42mgwHqUtoljbcRsTBCeINpfM/MgZLKPpaxFxGbOCqDXmILN7hoJrTKH+axhxmcYRxP0MIDnOBDZv5q1XUNIuJxifJp+UNV7t7BFM6xeic0RMQ4Bpl5W/ol7GISx/eEUUTECrbx+f8A8xhiZht9zsgAAAAASUVORK5CYII=";Sidebar.prototype.dragPreviewBorder="1px dashed black";Sidebar.prototype.enableTooltips=true;Sidebar.prototype.tooltipBorder=16;Sidebar.prototype.tooltipDelay=300;Sidebar.prototype.dropTargetDelay=200;Sidebar.prototype.gearImage=STENCIL_PATH+"/clipart/Gear_128x128.png";Sidebar.prototype.thumbWidth=42;Sidebar.prototype.thumbHeight=42;Sidebar.prototype.minThumbStrokeWidth=1;Sidebar.prototype.thumbAntiAlias=false;Sidebar.prototype.thumbPadding=document.documentMode>=5?2:3;Sidebar.prototype.thumbBorder=2;if(urlParams["sidebar-entries"]!="large"){Sidebar.prototype.thumbPadding=document.documentMode>=5?0:1;Sidebar.prototype.thumbBorder=1;Sidebar.prototype.thumbWidth=32;Sidebar.prototype.thumbHeight=30;Sidebar.prototype.minThumbStrokeWidth=1.3;Sidebar.prototype.thumbAntiAlias=true}Sidebar.prototype.sidebarTitleSize=9;Sidebar.prototype.sidebarTitles=false;Sidebar.prototype.tooltipTitles=true;Sidebar.prototype.maxTooltipWidth=400;Sidebar.prototype.maxTooltipHeight=400;Sidebar.prototype.addStencilsToIndex=true;Sidebar.prototype.defaultImageWidth=80;Sidebar.prototype.defaultImageHeight=80;Sidebar.prototype.getTooltipOffset=function(){return new mxPoint(0,0)};Sidebar.prototype.showTooltip=function(u,g,f,y,x,w){if(this.enableTooltips&&this.showTooltips){if(this.currentElt!=u){if(this.thread!=null){window.clearTimeout(this.thread);this.thread=null}var e=mxUtils.bind(this,function(){if(this.tooltip==null){this.tooltip=document.createElement("div");this.tooltip.className="geSidebarTooltip";this.tooltip.style.zIndex=mxPopupMenu.prototype.zIndex-1;document.body.appendChild(this.tooltip);this.graph2=new Graph(this.tooltip,null,null,this.editorUi.editor.graph.getStylesheet());this.graph2.resetViewOnRootChange=false;this.graph2.foldingEnabled=false;this.graph2.gridEnabled=false;this.graph2.autoScroll=false;this.graph2.setTooltips(false);this.graph2.setConnectable(false);this.graph2.setEnabled(false);if(!mxClient.IS_SVG){this.graph2.view.canvas.style.position="relative"}}this.graph2.model.clear();this.graph2.view.setTranslate(this.tooltipBorder,this.tooltipBorder);if(f>this.maxTooltipWidth||y>this.maxTooltipHeight){this.graph2.view.scale=Math.round(Math.min(this.maxTooltipWidth/f,this.maxTooltipHeight/y)*100)/100}else{this.graph2.view.scale=1}this.tooltip.style.display="block";this.graph2.labelsVisible=w==null||w;var e=mxClient.NO_FO;mxClient.NO_FO=Editor.prototype.originalNoForeignObject;this.graph2.addCells(g);mxClient.NO_FO=e;var t=this.graph2.getGraphBounds();var l=t.width+2*this.tooltipBorder+4;var r=t.height+2*this.tooltipBorder;if(mxClient.IS_QUIRKS){r+=4;this.tooltip.style.overflow="hidden"}else{this.tooltip.style.overflow="visible"}this.tooltip.style.width=l+"px";var i=l;if(this.tooltipTitles&&x!=null&&x.length>0){if(this.tooltipTitle==null){this.tooltipTitle=document.createElement("div");this.tooltipTitle.style.borderTop="1px solid gray";this.tooltipTitle.style.textAlign="center";this.tooltipTitle.style.width="100%";this.tooltipTitle.style.overflow="hidden";this.tooltipTitle.style.position="absolute";this.tooltipTitle.style.paddingTop="6px";this.tooltipTitle.style.bottom="6px";this.tooltip.appendChild(this.tooltipTitle)}else{this.tooltipTitle.innerHTML=""}this.tooltipTitle.style.display="";mxUtils.write(this.tooltipTitle,x);i=Math.min(this.maxTooltipWidth,Math.max(l,this.tooltipTitle.scrollWidth+4));var n=this.tooltipTitle.offsetHeight+10;r+=n;if(mxClient.IS_SVG){this.tooltipTitle.style.marginTop=2-n+"px"}else{r-=6;this.tooltipTitle.style.top=r-n+"px"}}else if(this.tooltipTitle!=null&&this.tooltipTitle.parentNode!=null){this.tooltipTitle.style.display="none"}if(i>l){this.tooltip.style.width=i+"px"}this.tooltip.style.height=r+"px";var a=-Math.round(t.x-this.tooltipBorder)+(i-l)/2;var o=-Math.round(t.y-this.tooltipBorder);var s=document.body;var m=document.documentElement;var h=this.getTooltipOffset();var d=Math.max(s.clientHeight||0,m.clientHeight);var p=this.container.clientWidth+this.editorUi.splitSize+3+this.editorUi.container.offsetLeft+h.x;var c=Math.min(d-r-20,Math.max(0,this.editorUi.container.offsetTop+this.container.offsetTop+u.offsetTop-this.container.scrollTop-r/2+16))+h.y;if(mxClient.IS_SVG){if(a!=0||o!=0){this.graph2.view.canvas.setAttribute("transform","translate("+a+","+o+")")}else{this.graph2.view.canvas.removeAttribute("transform")}}else{this.graph2.view.drawPane.style.left=a+"px";this.graph2.view.drawPane.style.top=o+"px"}this.tooltip.style.position="absolute";this.tooltip.style.left=p+"px";this.tooltip.style.top=c+"px"});if(this.tooltip!=null&&this.tooltip.style.display!="none"){e()}else{this.thread=window.setTimeout(e,this.tooltipDelay)}this.currentElt=u}}};Sidebar.prototype.hideTooltip=function(){if(this.thread!=null){window.clearTimeout(this.thread);this.thread=null}if(this.tooltip!=null){this.tooltip.style.display="none";this.currentElt=null}};Sidebar.prototype.addDataEntry=function(e,t,l,r,i,n){return this.addEntry(e,mxUtils.bind(this,function(){return this.createVertexTemplateFromData(i,t,l,r,undefined,undefined,undefined,undefined,n)}))};Sidebar.prototype.addEntries=function(e){for(var t=0;t<e.length;t++){mxUtils.bind(this,function(t){var l=t.data;if(l!=null&&t.title!=null){this.addEntry(t.title,mxUtils.bind(this,function(){l=this.editorUi.convertDataUri(l);var e="shape=image;verticalLabelPosition=bottom;verticalAlign=top;imageAspect=0;";if(t.aspect=="fixed"){e+="aspect=fixed;"}return this.createVertexTemplate(e+"image="+l,t.w,t.h,"",t.title||"",false,false,true)}))}else if(t.xml!=null&&t.title!=null){this.addEntry(t.title,mxUtils.bind(this,function(){var e=this.editorUi.stringToCells(Graph.decompress(t.xml));return this.createVertexTemplateFromCells(e,t.w,t.h,t.title||"",true,false,true)}))}})(e[t])}};Sidebar.prototype.addEntry=function(e,l){if(this.taglist!=null&&e!=null&&e.length>0){var t=e.toLowerCase().replace(/[\/\,\(\)]/g," ").split(" ");var r=mxUtils.bind(this,function(e){if(e!=null&&e.length>1){var t=this.taglist[e];if(typeof t!=="object"){t={entries:[],dict:new mxDictionary};this.taglist[e]=t}if(t.dict.get(l)==null){t.dict.put(l,l);t.entries.push(l)}}});for(var i=0;i<t.length;i++){r(t[i]);var n=t[i].replace(/\.*\d*$/,"");if(n!=t[i]){r(n)}}}return l};Sidebar.prototype.searchEntries=function(e,t,l,r,i){if(this.taglist!=null&&e!=null){var n=e.toLowerCase().split(" ");var a=new mxDictionary;var o=(l+1)*t;var s=[];var m=0;for(var h=0;h<n.length;h++){if(n[h].length>0){var d=this.taglist[n[h]];var p=new mxDictionary;if(d!=null){var c=d.entries;s=[];for(var u=0;u<c.length;u++){var d=c[u];if(m==0==(a.get(d)==null)){p.put(d,d);s.push(d);if(h==n.length-1&&s.length==o){r(s.slice(l*t,o),o,true,n);return}}}}else{s=[]}a=p;m++}}var g=s.length;r(s.slice(l*t,(l+1)*t),g,false,n)}else{r([],null,null,n)}};Sidebar.prototype.filterTags=function(e){if(e!=null){var t=e.split(" ");var l=[];var r={};for(var i=0;i<t.length;i++){if(r[t[i]]==null){r[t[i]]="1";l.push(t[i])}}return l.join(" ")}return null};Sidebar.prototype.cloneCell=function(e,t){var l=e.clone();if(t!=null){l.value=t}return l};Sidebar.prototype.addSearchPalette=function(e){var t=document.createElement("div");t.style.visibility="hidden";this.container.appendChild(t);var o=document.createElement("div");o.className="geSidebar";o.style.boxSizing="border-box";o.style.overflow="hidden";o.style.width="100%";o.style.padding="8px";o.style.paddingTop="14px";o.style.paddingBottom="0px";if(!e){o.style.display="none"}var l=document.createElement("div");l.style.whiteSpace="nowrap";l.style.textOverflow="clip";l.style.paddingBottom="8px";l.style.cursor="default";var r=document.createElement("input");r.setAttribute("placeholder",mxResources.get("searchShapes"));r.setAttribute("type","text");r.style.fontSize="12px";r.style.overflow="hidden";r.style.boxSizing="border-box";r.style.border="solid 1px #d5d5d5";r.style.borderRadius="4px";r.style.width="100%";r.style.outline="none";r.style.padding="6px";r.style.paddingRight="20px";l.appendChild(r);var i=document.createElement("img");i.setAttribute("src",Sidebar.prototype.searchImage);i.setAttribute("title",mxResources.get("search"));i.style.position="relative";i.style.left="-18px";if(mxClient.IS_QUIRKS){r.style.height="28px";i.style.top="-4px"}else{i.style.top="1px"}i.style.background="url('"+this.editorUi.editor.transparentImage+"')";var n;l.appendChild(i);o.appendChild(l);var s=document.createElement("center");var m=mxUtils.button(mxResources.get("moreResults"),function(){n()});m.style.display="none";m.style.lineHeight="normal";m.style.marginTop="4px";m.style.marginBottom="8px";s.style.paddingTop="4px";s.style.paddingBottom="4px";s.appendChild(m);o.appendChild(s);var h="";var d=false;var p=false;var c=0;var u=new Object;var g=12;var f=mxUtils.bind(this,function(){d=false;this.currentSearch=null;var e=o.firstChild;while(e!=null){var t=e.nextSibling;if(e!=l&&e!=s){e.parentNode.removeChild(e)}e=t}});mxEvent.addListener(i,"click",function(){if(i.getAttribute("src")==Dialog.prototype.closeImage){i.setAttribute("src",Sidebar.prototype.searchImage);i.setAttribute("title",mxResources.get("search"));m.style.display="none";r.value="";h="";f()}r.focus()});n=mxUtils.bind(this,function(){g=4*Math.max(1,Math.floor(this.container.clientWidth/(this.thumbWidth+10)));this.hideTooltip();if(r.value!=""){if(s.parentNode!=null){if(h!=r.value){f();h=r.value;u=new Object;p=false;c=0}if(!d&&!p){m.setAttribute("disabled","true");m.style.display="";m.style.cursor="wait";m.innerHTML=mxResources.get("loading")+"...";d=true;var a=new Object;this.currentSearch=a;this.searchEntries(h,g,c,mxUtils.bind(this,function(e,t,l,r){if(this.currentSearch==a){e=e!=null?e:[];d=false;c++;this.insertSearchHint(o,h,g,c,e,t,l,r);if(e.length==0&&c==1){h=""}if(s.parentNode!=null){s.parentNode.removeChild(s)}for(var i=0;i<e.length;i++){try{var n=e[i]();if(u[n.innerHTML]==null){u[n.innerHTML]="1";o.appendChild(n)}}catch(e){}}if(l){m.removeAttribute("disabled");m.innerHTML=mxResources.get("moreResults")}else{m.innerHTML=mxResources.get("reset");m.style.display="none";p=true}m.style.cursor="";o.appendChild(s)}}),mxUtils.bind(this,function(){m.style.cursor=""}))}}}else{f();r.value="";h="";u=new Object;m.style.display="none";p=false;r.focus()}});mxEvent.addListener(r,"keydown",mxUtils.bind(this,function(e){if(e.keyCode==13){n();mxEvent.consume(e)}}));mxEvent.addListener(r,"keyup",mxUtils.bind(this,function(e){if(r.value==""){i.setAttribute("src",Sidebar.prototype.searchImage);i.setAttribute("title",mxResources.get("search"))}else{i.setAttribute("src",Dialog.prototype.closeImage);i.setAttribute("title",mxResources.get("reset"))}if(r.value==""){p=true;m.style.display="none"}else if(r.value!=h){m.style.display="none";p=false}else if(!d){if(p){m.style.display="none"}else{m.style.display=""}}}));mxEvent.addListener(r,"mousedown",function(e){if(e.stopPropagation){e.stopPropagation()}e.cancelBubble=true});mxEvent.addListener(r,"selectstart",function(e){if(e.stopPropagation){e.stopPropagation()}e.cancelBubble=true});var a=document.createElement("div");a.appendChild(o);this.container.appendChild(a);this.palettes["search"]=[t,a]};Sidebar.prototype.insertSearchHint=function(e,t,l,r,i,n,a,o){if(i.length==0&&r==1){var s=document.createElement("div");s.className="geTitle";s.style.cssText="background-color:transparent;border-color:transparent;"+"color:gray;padding:6px 0px 0px 0px !important;margin:4px 8px 4px 8px;"+"text-align:center;cursor:default !important";mxUtils.write(s,mxResources.get("noResultsFor",[t]));e.appendChild(s)}};Sidebar.prototype.addCustomPalette=function(e){var t="line lines connector connectors connection connections arrow arrows ";var l=[];var r=document.querySelectorAll(".weapp-de-component-panel .ui-collapse-panel__content > span");r.forEach(mxUtils.bind(this,function(e){l.push(this.createVertexTemplateEntry("rounded=0;whiteSpace=wrap;html=1;",120,60,"","Rectangle",null,null,"rect rectangle box",e))}));this.addPaletteFunctions("general","Custom",e!=null?e:true,l)};Sidebar.prototype.addGeneralPalette=function(e){var t="line lines connector connectors connection connections arrow arrows ";var l=[this.createVertexTemplateEntry("rounded=0;whiteSpace=wrap;html=1;",120,60,"","Rectangle",null,null,"rect rectangle box"),this.createVertexTemplateEntry("rounded=1;whiteSpace=wrap;html=1;",120,60,"","Rounded Rectangle",null,null,"rounded rect rectangle box"),this.createVertexTemplateEntry("text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;",40,20,"Text","Text",null,null,"text textbox textarea label"),this.createVertexTemplateEntry("text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;",190,120,"<h1>Heading</h1><p>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>","Textbox",null,null,"text textbox textarea"),this.createVertexTemplateEntry("ellipse;whiteSpace=wrap;html=1;",120,80,"","Ellipse",null,null,"oval ellipse state"),this.createVertexTemplateEntry("whiteSpace=wrap;html=1;aspect=fixed;",80,80,"","Square",null,null,"square"),this.createVertexTemplateEntry("ellipse;whiteSpace=wrap;html=1;aspect=fixed;",80,80,"","Circle",null,null,"circle"),this.createVertexTemplateEntry("shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;",120,60,"","Process",null,null,"process task"),this.createVertexTemplateEntry("rhombus;whiteSpace=wrap;html=1;",80,80,"","Diamond",null,null,"diamond rhombus if condition decision conditional question test"),this.createVertexTemplateEntry("shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;",120,60,"","Parallelogram"),this.createVertexTemplateEntry("shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;",120,80,"","Hexagon",null,null,"hexagon preparation"),this.createVertexTemplateEntry("triangle;whiteSpace=wrap;html=1;",60,80,"","Triangle",null,null,"triangle logic inverter buffer"),this.createVertexTemplateEntry("shape=cylinder;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;",60,80,"","Cylinder",null,null,"cylinder data database"),this.createVertexTemplateEntry("ellipse;shape=cloud;whiteSpace=wrap;html=1;",120,80,"","Cloud",null,null,"cloud network"),this.createVertexTemplateEntry("shape=document;whiteSpace=wrap;html=1;boundedLbl=1;",120,80,"","Document"),this.createVertexTemplateEntry("shape=internalStorage;whiteSpace=wrap;html=1;backgroundOutline=1;",80,80,"","Internal Storage"),this.createVertexTemplateEntry("shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;",120,80,"","Cube"),this.createVertexTemplateEntry("shape=step;perimeter=stepPerimeter;whiteSpace=wrap;html=1;fixedSize=1;",120,80,"","Step"),this.createVertexTemplateEntry("shape=trapezoid;perimeter=trapezoidPerimeter;whiteSpace=wrap;html=1;",120,60,"","Trapezoid"),this.createVertexTemplateEntry("shape=tape;whiteSpace=wrap;html=1;",120,100,"","Tape"),this.createVertexTemplateEntry("shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;",80,100,"","Note"),this.createVertexTemplateEntry("shape=card;whiteSpace=wrap;html=1;",80,100,"","Card"),this.createVertexTemplateEntry("shape=callout;whiteSpace=wrap;html=1;perimeter=calloutPerimeter;",120,80,"","Callout",null,null,"bubble chat thought speech message"),this.createVertexTemplateEntry("shape=umlActor;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;html=1;outlineConnect=0;",30,60,"Actor","Actor",false,null,"user person human stickman"),this.createVertexTemplateEntry("shape=xor;whiteSpace=wrap;html=1;",60,80,"","Or",null,null,"logic or"),this.createVertexTemplateEntry("shape=or;whiteSpace=wrap;html=1;",60,80,"","And",null,null,"logic and"),this.createVertexTemplateEntry("shape=dataStorage;whiteSpace=wrap;html=1;",100,80,"","Data Storage"),this.addEntry("curve",mxUtils.bind(this,function(){var e=new mxCell("",new mxGeometry(0,0,50,50),"curved=1;endArrow=classic;html=1;");e.geometry.setTerminalPoint(new mxPoint(0,50),true);e.geometry.setTerminalPoint(new mxPoint(50,0),false);e.geometry.points=[new mxPoint(50,50),new mxPoint(0,0)];e.geometry.relative=true;e.edge=true;return this.createEdgeTemplateFromCells([e],e.geometry.width,e.geometry.height,"Curve")})),this.createEdgeTemplateEntry("shape=flexArrow;endArrow=classic;startArrow=classic;html=1;",50,50,"","Bidirectional Arrow",null,t+"bidirectional"),this.createEdgeTemplateEntry("shape=flexArrow;endArrow=classic;html=1;",50,50,"","Arrow",null,t+"directional directed"),this.createEdgeTemplateEntry("shape=link;html=1;",50,50,"","Link",null,t+"link"),this.createEdgeTemplateEntry("endArrow=none;dashed=1;html=1;",50,50,"","Dashed Line",null,t+"dashed undirected no"),this.createEdgeTemplateEntry("endArrow=none;html=1;",50,50,"","Line",null,t+"simple undirected plain blank no"),this.createEdgeTemplateEntry("endArrow=classic;startArrow=classic;html=1;",50,50,"","Bidirectional Connector",null,t+"bidirectional"),this.createEdgeTemplateEntry("endArrow=classic;html=1;",50,50,"","Directional Connector",null,t+"directional directed")];this.addPaletteFunctions("general",mxResources.get("general"),e!=null?e:true,l)};Sidebar.prototype.addBasicPalette=function(e){this.addStencilPalette("basic",mxResources.get("basic"),e+"/basic.xml",";whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;strokeWidth=2",null,null,null,null,[this.createVertexTemplateEntry("shape=partialRectangle;whiteSpace=wrap;html=1;top=0;bottom=0;fillColor=none;",120,60,"","Partial Rectangle"),this.createVertexTemplateEntry("shape=partialRectangle;whiteSpace=wrap;html=1;right=0;top=0;bottom=0;fillColor=none;routingCenterX=-0.5;",120,60,"","Partial Rectangle"),this.createVertexTemplateEntry("shape=partialRectangle;whiteSpace=wrap;html=1;bottom=0;right=0;fillColor=none;",120,60,"","Partial Rectangle"),this.createVertexTemplateEntry("shape=partialRectangle;whiteSpace=wrap;html=1;top=0;left=0;fillColor=none;",120,60,"","Partial Rectangle")])};Sidebar.prototype.addMiscPalette=function(e){var l=this;var t="line lines connector connectors connection connections arrow arrows ";var r=[this.createVertexTemplateEntry("text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;",100,40,"Title","Title",null,null,"text heading title"),this.createVertexTemplateEntry("text;strokeColor=none;fillColor=none;html=1;whiteSpace=wrap;verticalAlign=middle;overflow=hidden;",100,80,"<ul><li>Value 1</li><li>Value 2</li><li>Value 3</li></ul>","Unordered List"),this.createVertexTemplateEntry("text;strokeColor=none;fillColor=none;html=1;whiteSpace=wrap;verticalAlign=middle;overflow=hidden;",100,80,"<ol><li>Value 1</li><li>Value 2</li><li>Value 3</li></ol>","Ordered List"),this.createVertexTemplateEntry("text;html=1;strokeColor=#c0c0c0;fillColor=#ffffff;overflow=fill;rounded=0;",280,160,'<table border="1" width="100%" height="100%" cellpadding="4" style="width:100%;height:100%;border-collapse:collapse;">'+'<tr style="background-color:#A7C942;color:#ffffff;border:1px solid #98bf21;"><th align="left">Title 1</th><th align="left">Title 2</th><th align="left">Title 3</th></tr>'+'<tr style="border:1px solid #98bf21;"><td>Value 1</td><td>Value 2</td><td>Value 3</td></tr>'+'<tr style="background-color:#EAF2D3;border:1px solid #98bf21;"><td>Value 4</td><td>Value 5</td><td>Value 6</td></tr>'+'<tr style="border:1px solid #98bf21;"><td>Value 7</td><td>Value 8</td><td>Value 9</td></tr>'+'<tr style="background-color:#EAF2D3;border:1px solid #98bf21;"><td>Value 10</td><td>Value 11</td><td>Value 12</td></tr></table>',"Table 1"),this.createVertexTemplateEntry("text;html=1;strokeColor=#c0c0c0;fillColor=none;overflow=fill;",180,140,'<table border="0" width="100%" height="100%" style="width:100%;height:100%;border-collapse:collapse;">'+'<tr><td align="center">Value 1</td><td align="center">Value 2</td><td align="center">Value 3</td></tr>'+'<tr><td align="center">Value 4</td><td align="center">Value 5</td><td align="center">Value 6</td></tr>'+'<tr><td align="center">Value 7</td><td align="center">Value 8</td><td align="center">Value 9</td></tr></table>',"Table 2"),this.createVertexTemplateEntry("text;html=1;strokeColor=none;fillColor=none;overflow=fill;",180,140,'<table border="1" width="100%" height="100%" style="width:100%;height:100%;border-collapse:collapse;">'+'<tr><td align="center">Value 1</td><td align="center">Value 2</td><td align="center">Value 3</td></tr>'+'<tr><td align="center">Value 4</td><td align="center">Value 5</td><td align="center">Value 6</td></tr>'+'<tr><td align="center">Value 7</td><td align="center">Value 8</td><td align="center">Value 9</td></tr></table>',"Table 3"),this.createVertexTemplateEntry("text;html=1;strokeColor=none;fillColor=none;overflow=fill;",160,140,'<table border="1" width="100%" height="100%" cellpadding="4" style="width:100%;height:100%;border-collapse:collapse;">'+'<tr><th align="center"><b>Title</b></th></tr>'+'<tr><td align="center">Section 1.1\nSection 1.2\nSection 1.3</td></tr>'+'<tr><td align="center">Section 2.1\nSection 2.2\nSection 2.3</td></tr></table>',"Table 4"),this.addEntry("link hyperlink",mxUtils.bind(this,function(){var e=new mxCell("Link",new mxGeometry(0,0,60,40),"text;html=1;strokeColor=none;fillColor=none;whiteSpace=wrap;align=center;verticalAlign=middle;fontColor=#0000EE;fontStyle=4;");e.vertex=true;this.graph.setLinkForCell(e,"https://www.draw.io");return this.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Link")})),this.addEntry("timestamp date time text label",mxUtils.bind(this,function(){var e=new mxCell("%date{ddd mmm dd yyyy HH:MM:ss}%",new mxGeometry(0,0,160,20),"text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;overflow=hidden;");e.vertex=true;this.graph.setAttributeForCell(e,"placeholders","1");return this.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Timestamp")})),this.addEntry("variable placeholder metadata hello world text label",mxUtils.bind(this,function(){var e=new mxCell("%name% Text",new mxGeometry(0,0,80,20),"text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;overflow=hidden;");e.vertex=true;this.graph.setAttributeForCell(e,"placeholders","1");this.graph.setAttributeForCell(e,"name","Variable");return this.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Variable")})),this.createVertexTemplateEntry("shape=ext;double=1;rounded=0;whiteSpace=wrap;html=1;",120,80,"","Double Rectangle",null,null,"rect rectangle box double"),this.createVertexTemplateEntry("shape=ext;double=1;rounded=1;whiteSpace=wrap;html=1;",120,80,"","Double Rounded Rectangle",null,null,"rounded rect rectangle box double"),this.createVertexTemplateEntry("ellipse;shape=doubleEllipse;whiteSpace=wrap;html=1;",100,60,"","Double Ellipse",null,null,"oval ellipse start end state double"),this.createVertexTemplateEntry("shape=ext;double=1;whiteSpace=wrap;html=1;aspect=fixed;",80,80,"","Double Square",null,null,"double square"),this.createVertexTemplateEntry("ellipse;shape=doubleEllipse;whiteSpace=wrap;html=1;aspect=fixed;",80,80,"","Double Circle",null,null,"double circle"),this.createEdgeTemplateEntry("rounded=0;comic=1;strokeWidth=2;endArrow=blockThin;html=1;fontFamily=Comic Sans MS;fontStyle=1;",50,50,"","Comic Arrow"),this.createVertexTemplateEntry("html=1;whiteSpace=wrap;comic=1;strokeWidth=2;fontFamily=Comic Sans MS;fontStyle=1;",120,60,"RECTANGLE","Comic Rectangle",true,null,"comic rectangle rect box text retro"),this.createVertexTemplateEntry("rhombus;html=1;align=center;whiteSpace=wrap;comic=1;strokeWidth=2;fontFamily=Comic Sans MS;fontStyle=1;",100,100,"DIAMOND","Comic Diamond",true,null,"comic diamond rhombus if condition decision conditional question test retro"),this.createVertexTemplateEntry("html=1;whiteSpace=wrap;aspect=fixed;shape=isoRectangle;",150,90,"","Isometric Square",true,null,"rectangle rect box iso isometric"),this.createVertexTemplateEntry("html=1;whiteSpace=wrap;aspect=fixed;shape=isoCube;backgroundOutline=1;",90,100,"","Isometric Cube",true,null,"cube box iso isometric"),this.createEdgeTemplateEntry("edgeStyle=isometricEdgeStyle;endArrow=none;html=1;",50,100,"","Isometric Edge 1"),this.createEdgeTemplateEntry("edgeStyle=isometricEdgeStyle;endArrow=none;html=1;elbow=vertical;",50,100,"","Isometric Edge 2"),this.createVertexTemplateEntry("shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;",20,120,"","Curly Bracket"),this.createVertexTemplateEntry("line;strokeWidth=2;html=1;",160,10,"","Horizontal Line"),this.createVertexTemplateEntry("line;strokeWidth=2;direction=south;html=1;",10,160,"","Vertical Line"),this.createVertexTemplateEntry("line;strokeWidth=4;html=1;perimeter=backbonePerimeter;points=[];outlineConnect=0;",160,10,"","Horizontal Backbone",false,null,"backbone bus network"),this.createVertexTemplateEntry("line;strokeWidth=4;direction=south;html=1;perimeter=backbonePerimeter;points=[];outlineConnect=0;",10,160,"","Vertical Backbone",false,null,"backbone bus network"),this.createVertexTemplateEntry("shape=crossbar;whiteSpace=wrap;html=1;rounded=1;",120,20,"","Crossbar",false,null,"crossbar distance measure dimension unit"),this.createVertexTemplateEntry("shape=image;html=1;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;imageAspect=1;aspect=fixed;image="+this.gearImage,52,61,"","Image (Fixed Aspect)",false,null,"fixed image icon symbol"),this.createVertexTemplateEntry("shape=image;html=1;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;imageAspect=0;image="+this.gearImage,50,60,"","Image (Variable Aspect)",false,null,"strechted image icon symbol"),this.createVertexTemplateEntry("icon;html=1;image="+this.gearImage,60,60,"Icon","Icon",false,null,"icon image symbol"),this.createVertexTemplateEntry("label;whiteSpace=wrap;html=1;image="+this.gearImage,140,60,"Label","Label 1",null,null,"label image icon symbol"),this.createVertexTemplateEntry("label;whiteSpace=wrap;html=1;align=center;verticalAlign=bottom;spacingLeft=0;spacingBottom=4;imageAlign=center;imageVerticalAlign=top;image="+this.gearImage,120,80,"Label","Label 2",null,null,"label image icon symbol"),this.addEntry("shape group container",function(){var e=new mxCell("Label",new mxGeometry(0,0,160,70),"html=1;whiteSpace=wrap;container=1;recursiveResize=0;collapsible=0;");e.vertex=true;var t=new mxCell("",new mxGeometry(20,20,20,30),"triangle;html=1;whiteSpace=wrap;");t.vertex=true;e.insert(t);return l.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Shape Group")}),this.createVertexTemplateEntry("shape=partialRectangle;whiteSpace=wrap;html=1;left=0;right=0;fillColor=none;",120,60,"","Partial Rectangle"),this.createVertexTemplateEntry("shape=partialRectangle;whiteSpace=wrap;html=1;bottom=1;right=1;left=1;top=0;fillColor=none;routingCenterX=-0.5;",120,60,"","Partial Rectangle"),this.createEdgeTemplateEntry("edgeStyle=segmentEdgeStyle;endArrow=classic;html=1;",50,50,"","Manual Line",null,t+"manual"),this.createEdgeTemplateEntry("shape=filledEdge;rounded=0;fixDash=1;endArrow=none;strokeWidth=10;fillColor=#ffffff;edgeStyle=orthogonalEdgeStyle;",60,40,"","Filled Edge"),this.createEdgeTemplateEntry("edgeStyle=elbowEdgeStyle;elbow=horizontal;endArrow=classic;html=1;",50,50,"","Horizontal Elbow",null,t+"elbow horizontal"),this.createEdgeTemplateEntry("edgeStyle=elbowEdgeStyle;elbow=vertical;endArrow=classic;html=1;",50,50,"","Vertical Elbow",null,t+"elbow vertical")];this.addPaletteFunctions("misc",mxResources.get("misc"),e!=null?e:true,r)};Sidebar.prototype.addAdvancedPalette=function(e){this.addPaletteFunctions("advanced",mxResources.get("advanced"),e!=null?e:false,this.createAdvancedShapes())};Sidebar.prototype.createAdvancedShapes=function(){var t=this;var l=new mxCell("List Item",new mxGeometry(0,0,60,26),"text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;");l.vertex=true;return[this.createVertexTemplateEntry("shape=tapeData;whiteSpace=wrap;html=1;perimeter=ellipsePerimeter;",80,80,"","Tape Data"),this.createVertexTemplateEntry("shape=manualInput;whiteSpace=wrap;html=1;",80,80,"","Manual Input"),this.createVertexTemplateEntry("shape=loopLimit;whiteSpace=wrap;html=1;",100,80,"","Loop Limit"),this.createVertexTemplateEntry("shape=offPageConnector;whiteSpace=wrap;html=1;",80,80,"","Off Page Connector"),this.createVertexTemplateEntry("shape=delay;whiteSpace=wrap;html=1;",80,40,"","Delay"),this.createVertexTemplateEntry("shape=display;whiteSpace=wrap;html=1;",80,40,"","Display"),this.createVertexTemplateEntry("shape=singleArrow;direction=west;whiteSpace=wrap;html=1;",100,60,"","Arrow Left"),this.createVertexTemplateEntry("shape=singleArrow;whiteSpace=wrap;html=1;",100,60,"","Arrow Right"),this.createVertexTemplateEntry("shape=singleArrow;direction=north;whiteSpace=wrap;html=1;",60,100,"","Arrow Up"),this.createVertexTemplateEntry("shape=singleArrow;direction=south;whiteSpace=wrap;html=1;",60,100,"","Arrow Down"),this.createVertexTemplateEntry("shape=doubleArrow;whiteSpace=wrap;html=1;",100,60,"","Double Arrow"),this.createVertexTemplateEntry("shape=doubleArrow;direction=south;whiteSpace=wrap;html=1;",60,100,"","Double Arrow Vertical",null,null,"double arrow"),this.createVertexTemplateEntry("shape=actor;whiteSpace=wrap;html=1;",40,60,"","User",null,null,"user person human"),this.createVertexTemplateEntry("shape=cross;whiteSpace=wrap;html=1;",80,80,"","Cross"),this.createVertexTemplateEntry("shape=corner;whiteSpace=wrap;html=1;",80,80,"","Corner"),this.createVertexTemplateEntry("shape=tee;whiteSpace=wrap;html=1;",80,80,"","Tee"),this.createVertexTemplateEntry("shape=datastore;whiteSpace=wrap;html=1;",60,60,"","Data Store",null,null,"data store cylinder database"),this.createVertexTemplateEntry("shape=orEllipse;perimeter=ellipsePerimeter;whiteSpace=wrap;html=1;backgroundOutline=1;",80,80,"","Or",null,null,"or circle oval ellipse"),this.createVertexTemplateEntry("shape=sumEllipse;perimeter=ellipsePerimeter;whiteSpace=wrap;html=1;backgroundOutline=1;",80,80,"","Sum",null,null,"sum circle oval ellipse"),this.createVertexTemplateEntry("shape=lineEllipse;perimeter=ellipsePerimeter;whiteSpace=wrap;html=1;backgroundOutline=1;",80,80,"","Ellipse with horizontal divider",null,null,"circle oval ellipse"),this.createVertexTemplateEntry("shape=lineEllipse;line=vertical;perimeter=ellipsePerimeter;whiteSpace=wrap;html=1;backgroundOutline=1;",80,80,"","Ellipse with vertical divider",null,null,"circle oval ellipse"),this.createVertexTemplateEntry("shape=sortShape;perimeter=rhombusPerimeter;whiteSpace=wrap;html=1;",80,80,"","Sort",null,null,"sort"),this.createVertexTemplateEntry("shape=collate;whiteSpace=wrap;html=1;",80,80,"","Collate",null,null,"collate"),this.createVertexTemplateEntry("shape=switch;whiteSpace=wrap;html=1;",60,60,"","Switch",null,null,"switch router"),this.addEntry("process bar",function(){return t.createVertexTemplateFromData("zZXRaoMwFIafJpcDjbNrb2233rRQ8AkyPdPQaCRJV+3T7yTG2rUVBoOtgpDzn/xJzncCIdGyateKNeVW5iBI9EqipZLS9KOqXYIQhAY8J9GKUBrgT+jbRDZ02aBhCmrzEwPtDZ9MHKBXdkpmoDWKCVN9VptO+Kw+8kqwGqMkK7nIN6yTB7uTNizbD1FSSsVPsjYMC1qFKHxwIZZSSIVxLZ1/nJNar5+oQPMT7IYCrqUta1ENzuqGaeOFTArBGs3f3Vmtoo2Se7ja1h00kSoHK4bBIKUNy3hdoPYU0mF91i9mT8EEL2ocZ3gKa00ayWujLZY4IfHKFonVDLsRGgXuQ90zBmWgneyTk3yT1iArMKrDKUeem9L3ajHrbSXwohxsQd/ggOleKM7ese048J2/fwuim1uQGmhQCW8vQMkacP3GCQgBFMftHEsr7cYYe95CnmKTPMFbYD8CQ++DGQy+/M5X4ku5wHYmdIktfvk9tecpavThqS3m/0YtnqIWPTy1cD77K2wYjo+Ay317I74A",296,100,"Process Bar")}),this.createVertexTemplateEntry("swimlane;",200,200,"Container","Container",null,null,"container swimlane lane pool group"),this.addEntry("list group erd table",function(){var e=new mxCell("List",new mxGeometry(0,0,140,110),"swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=26;fillColor=none;horizontalStack=0;"+"resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;");e.vertex=true;e.insert(t.cloneCell(l,"Item 1"));e.insert(t.cloneCell(l,"Item 2"));e.insert(t.cloneCell(l,"Item 3"));return t.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"List")}),this.addEntry("list item entry value group erd table",function(){return t.createVertexTemplateFromCells([t.cloneCell(l,"List Item")],l.geometry.width,l.geometry.height,"List Item")})]};Sidebar.prototype.addUmlPalette=function(e){var r=this;var t=new mxCell("+ field: type",new mxGeometry(0,0,100,26),"text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;");t.vertex=true;var l=new mxCell("",new mxGeometry(0,0,40,8),"line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;");l.vertex=true;var i="uml static class ";var n=[this.createVertexTemplateEntry("html=1;",110,50,"Object","Object",null,null,i+"object instance"),this.createVertexTemplateEntry("html=1;",110,50,"&laquo;interface&raquo;<br><b>Name</b>","Interface",null,null,i+"interface object instance annotated annotation"),this.addEntry(i+"object instance",function(){var e=new mxCell("Classname",new mxGeometry(0,0,160,90),"swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;");e.vertex=true;e.insert(t.clone());e.insert(l.clone());e.insert(r.cloneCell(t,"+ method(type): type"));return r.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Class")}),this.addEntry(i+"section subsection",function(){var e=new mxCell("Classname",new mxGeometry(0,0,140,110),"swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=26;fillColor=none;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;");e.vertex=true;e.insert(t.clone());e.insert(t.clone());e.insert(t.clone());return r.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Class 2")}),this.addEntry(i+"item member method function variable field attribute label",function(){return r.createVertexTemplateFromCells([r.cloneCell(t,"+ item: attribute")],t.geometry.width,t.geometry.height,"Item 1")}),this.addEntry(i+"item member method function variable field attribute label",function(){var e=new mxCell("item: attribute",new mxGeometry(0,0,120,t.geometry.height),"label;fontStyle=0;strokeColor=none;fillColor=none;align=left;verticalAlign=top;overflow=hidden;"+"spacingLeft=28;spacingRight=4;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;imageWidth=16;imageHeight=16;image="+r.gearImage);e.vertex=true;return r.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Item 2")}),this.addEntry(i+"divider hline line separator",function(){return r.createVertexTemplateFromCells([l.clone()],l.geometry.width,l.geometry.height,"Divider")}),this.addEntry(i+"spacer space gap separator",function(){var e=new mxCell("",new mxGeometry(0,0,20,14),"text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=4;spacingRight=4;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;");e.vertex=true;return r.createVertexTemplateFromCells([e.clone()],e.geometry.width,e.geometry.height,"Spacer")}),this.createVertexTemplateEntry("text;align=center;fontStyle=1;verticalAlign=middle;spacingLeft=3;spacingRight=3;strokeColor=none;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;",80,26,"Title","Title",null,null,i+"title label"),this.addEntry(i+"component",function(){var e=new mxCell("&laquo;Annotation&raquo;<br/><b>Component</b>",new mxGeometry(0,0,180,90),"html=1;");e.vertex=true;var t=new mxCell("",new mxGeometry(1,0,20,20),"shape=component;jettyWidth=8;jettyHeight=4;");t.vertex=true;t.geometry.relative=true;t.geometry.offset=new mxPoint(-27,7);e.insert(t);return r.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Component")}),this.addEntry(i+"component",function(){var e=new mxCell('<p style="margin:0px;margin-top:6px;text-align:center;"><b>Component</b></p>'+'<hr/><p style="margin:0px;margin-left:8px;">+ Attribute1: Type<br/>+ Attribute2: Type</p>',new mxGeometry(0,0,180,90),"align=left;overflow=fill;html=1;");e.vertex=true;var t=new mxCell("",new mxGeometry(1,0,20,20),"shape=component;jettyWidth=8;jettyHeight=4;");t.vertex=true;t.geometry.relative=true;t.geometry.offset=new mxPoint(-24,4);e.insert(t);return r.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Component with Attributes")}),this.createVertexTemplateEntry("verticalAlign=top;align=left;spacingTop=8;spacingLeft=2;spacingRight=12;shape=cube;size=10;direction=south;fontStyle=4;html=1;",180,120,"Block","Block",null,null,i+"block"),this.createVertexTemplateEntry("shape=component;align=left;spacingLeft=36;",120,60,"Module","Module",null,null,i+"module"),this.createVertexTemplateEntry("shape=folder;fontStyle=1;spacingTop=10;tabWidth=40;tabHeight=14;tabPosition=left;html=1;",70,50,"package","Package",null,null,i+"package"),this.createVertexTemplateEntry("verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;",160,90,'<p style="margin:0px;margin-top:4px;text-align:center;text-decoration:underline;"><b>Object:Type</b></p><hr/>'+'<p style="margin:0px;margin-left:8px;">field1 = value1<br/>field2 = value2<br>field3 = value3</p>',"Object",null,null,i+"object instance"),this.createVertexTemplateEntry("verticalAlign=top;align=left;overflow=fill;html=1;",180,90,'<div style="box-sizing:border-box;width:100%;background:#e4e4e4;padding:2px;">Tablename</div>'+'<table style="width:100%;font-size:1em;" cellpadding="2" cellspacing="0">'+"<tr><td>PK</td><td>uniqueId</td></tr><tr><td>FK1</td><td>"+"foreignKey</td></tr><tr><td></td><td>fieldname</td></tr></table>","Entity",null,null,"er entity table"),this.addEntry(i+"object instance",function(){var e=new mxCell('<p style="margin:0px;margin-top:4px;text-align:center;">'+"<b>Class</b></p>"+'<hr size="1"/><div style="height:2px;"></div>',new mxGeometry(0,0,140,60),"verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;");e.vertex=true;return r.createVertexTemplateFromCells([e.clone()],e.geometry.width,e.geometry.height,"Class 3")}),this.addEntry(i+"object instance",function(){var e=new mxCell('<p style="margin:0px;margin-top:4px;text-align:center;">'+"<b>Class</b></p>"+'<hr size="1"/><div style="height:2px;"></div><hr size="1"/><div style="height:2px;"></div>',new mxGeometry(0,0,140,60),"verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;");e.vertex=true;return r.createVertexTemplateFromCells([e.clone()],e.geometry.width,e.geometry.height,"Class 4")}),this.addEntry(i+"object instance",function(){var e=new mxCell('<p style="margin:0px;margin-top:4px;text-align:center;">'+"<b>Class</b></p>"+'<hr size="1"/><p style="margin:0px;margin-left:4px;">+ field: Type</p><hr size="1"/>'+'<p style="margin:0px;margin-left:4px;">+ method(): Type</p>',new mxGeometry(0,0,160,90),"verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;");e.vertex=true;return r.createVertexTemplateFromCells([e.clone()],e.geometry.width,e.geometry.height,"Class 5")}),this.addEntry(i+"object instance",function(){var e=new mxCell('<p style="margin:0px;margin-top:4px;text-align:center;">'+"<i>&lt;&lt;Interface&gt;&gt;</i><br/><b>Interface</b></p>"+'<hr size="1"/><p style="margin:0px;margin-left:4px;">+ field1: Type<br/>'+"+ field2: Type</p>"+'<hr size="1"/><p style="margin:0px;margin-left:4px;">'+"+ method1(Type): Type<br/>"+"+ method2(Type, Type): Type</p>",new mxGeometry(0,0,190,140),"verticalAlign=top;align=left;overflow=fill;fontSize=12;fontFamily=Helvetica;html=1;");e.vertex=true;return r.createVertexTemplateFromCells([e.clone()],e.geometry.width,e.geometry.height,"Interface 2")}),this.createVertexTemplateEntry("shape=providedRequiredInterface;html=1;verticalLabelPosition=bottom;",20,20,"","Provided/Required Interface",null,null,"uml provided required interface lollipop notation"),this.createVertexTemplateEntry("shape=requiredInterface;html=1;verticalLabelPosition=bottom;",10,20,"","Required Interface",null,null,"uml required interface lollipop notation"),this.addEntry("uml lollipop notation provided required interface",function(){return r.createVertexTemplateFromData("zVTBrptADPyavVYEkt4b0uQd3pMq5dD2uAUD27dgZJwE8vX1spsQlETtpVWRIjFjex3PmFVJWvc70m31hjlYlXxWSUqI7N/qPgVrVRyZXCUbFceR/FS8fRJdjNGo1QQN/0lB7AuO2h7AM57oeLCBIDw0Obj8SCVrJK6wxEbbV8RWyIWQP4F52Juzq9AHRqEqrm2IQpN/IsKTwAYb8MzWWBuO9B0hL2E2BGsqIQyxvJ9rzApD7QBrYBokhcBqNsf5UbrzsLzmXUu/oJET42jwGat5QYcHyiDkTDLKy03TiRrFfSx08m+FrrQtUkOZvZdbFKThmwMfVhf4fQ43/W3uZriiPPT+KKhjwnf4anKuQv//wsg+NPJ7/9d9Xf7eVykwbeeMOFWGYd/qzEVO8tHP/Suw4a2ujXV/+gXsEdhkOgSC8os44BQt0tggicZHeG1N2QiXibhAV48epRayEDd8MT7Ct06TUaXVWq027tCuhcx5VZjebeeaoDNn/WMcb/p+j0AM/dNr6InLl4Lgzylsk6OCgRWYsuI592gNZh5OhgmcblPv7+1l+ws=",40,10,"Lollipop Notation")}),this.createVertexTemplateEntry("shape=umlBoundary;whiteSpace=wrap;html=1;",100,80,"Boundary Object","Boundary Object",null,null,"uml boundary object"),this.createVertexTemplateEntry("ellipse;shape=umlEntity;whiteSpace=wrap;html=1;",80,80,"Entity Object","Entity Object",null,null,"uml entity object"),this.createVertexTemplateEntry("ellipse;shape=umlControl;whiteSpace=wrap;html=1;",70,80,"Control Object","Control Object",null,null,"uml control object"),this.createVertexTemplateEntry("shape=umlActor;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;html=1;",30,60,"Actor","Actor",false,null,"uml actor"),this.createVertexTemplateEntry("ellipse;whiteSpace=wrap;html=1;",140,70,"Use Case","Use Case",null,null,"uml use case usecase"),this.addEntry("uml activity state start",function(){var e=new mxCell("",new mxGeometry(0,0,30,30),"ellipse;html=1;shape=startState;fillColor=#000000;strokeColor=#ff0000;");e.vertex=true;var t=new mxCell("",new mxGeometry(0,0,0,0),"edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;");t.geometry.setTerminalPoint(new mxPoint(15,90),false);t.geometry.relative=true;t.edge=true;e.insertEdge(t,true);return r.createVertexTemplateFromCells([e,t],30,90,"Start")}),this.addEntry("uml activity state",function(){var e=new mxCell("Activity",new mxGeometry(0,0,120,40),"rounded=1;whiteSpace=wrap;html=1;arcSize=40;fontColor=#000000;fillColor=#ffffc0;strokeColor=#ff0000;");e.vertex=true;var t=new mxCell("",new mxGeometry(0,0,0,0),"edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;");t.geometry.setTerminalPoint(new mxPoint(60,100),false);t.geometry.relative=true;t.edge=true;e.insertEdge(t,true);return r.createVertexTemplateFromCells([e,t],120,100,"Activity")}),this.addEntry("uml activity composite state",function(){var e=new mxCell("Composite State",new mxGeometry(0,0,160,60),"swimlane;html=1;fontStyle=1;align=center;verticalAlign=middle;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=0;resizeLast=1;container=0;fontColor=#000000;collapsible=0;rounded=1;arcSize=30;strokeColor=#ff0000;fillColor=#ffffc0;swimlaneFillColor=#ffffc0;");e.vertex=true;var t=new mxCell("Subtitle",new mxGeometry(0,0,200,26),"text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=0;fontColor=#000000;");t.vertex=true;e.insert(t);var l=new mxCell("",new mxGeometry(0,0,0,0),"edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;");l.geometry.setTerminalPoint(new mxPoint(80,120),false);l.geometry.relative=true;l.edge=true;e.insertEdge(l,true);return r.createVertexTemplateFromCells([e,l],160,120,"Composite State")}),this.addEntry("uml activity condition",function(){var e=new mxCell("Condition",new mxGeometry(0,0,80,40),"rhombus;whiteSpace=wrap;html=1;fillColor=#ffffc0;strokeColor=#ff0000;");e.vertex=true;var t=new mxCell("no",new mxGeometry(0,0,0,0),"edgeStyle=orthogonalEdgeStyle;html=1;align=left;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;");t.geometry.setTerminalPoint(new mxPoint(180,20),false);t.geometry.relative=true;t.geometry.x=-1;t.edge=true;e.insertEdge(t,true);var l=new mxCell("yes",new mxGeometry(0,0,0,0),"edgeStyle=orthogonalEdgeStyle;html=1;align=left;verticalAlign=top;endArrow=open;endSize=8;strokeColor=#ff0000;");l.geometry.setTerminalPoint(new mxPoint(40,100),false);l.geometry.relative=true;l.geometry.x=-1;l.edge=true;e.insertEdge(l,true);return r.createVertexTemplateFromCells([e,t,l],180,100,"Condition")}),this.addEntry("uml activity fork join",function(){var e=new mxCell("",new mxGeometry(0,0,200,10),"shape=line;html=1;strokeWidth=6;strokeColor=#ff0000;");e.vertex=true;var t=new mxCell("",new mxGeometry(0,0,0,0),"edgeStyle=orthogonalEdgeStyle;html=1;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#ff0000;");t.geometry.setTerminalPoint(new mxPoint(100,80),false);t.geometry.relative=true;t.edge=true;e.insertEdge(t,true);return r.createVertexTemplateFromCells([e,t],200,80,"Fork/Join")}),this.createVertexTemplateEntry("ellipse;html=1;shape=endState;fillColor=#000000;strokeColor=#ff0000;",30,30,"","End",null,null,"uml activity state end"),this.createVertexTemplateEntry("shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;",100,300,":Object","Lifeline",null,null,"uml sequence participant lifeline"),this.createVertexTemplateEntry("shape=umlLifeline;participant=umlActor;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;verticalAlign=top;spacingTop=36;labelBackgroundColor=#ffffff;outlineConnect=0;",20,300,"","Actor Lifeline",null,null,"uml sequence participant lifeline actor"),this.createVertexTemplateEntry("shape=umlLifeline;participant=umlBoundary;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;verticalAlign=top;spacingTop=36;labelBackgroundColor=#ffffff;outlineConnect=0;",50,300,"","Boundary Lifeline",null,null,"uml sequence participant lifeline boundary"),this.createVertexTemplateEntry("shape=umlLifeline;participant=umlEntity;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;verticalAlign=top;spacingTop=36;labelBackgroundColor=#ffffff;outlineConnect=0;",40,300,"","Entity Lifeline",null,null,"uml sequence participant lifeline entity"),this.createVertexTemplateEntry("shape=umlLifeline;participant=umlControl;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;verticalAlign=top;spacingTop=36;labelBackgroundColor=#ffffff;outlineConnect=0;",40,300,"","Control Lifeline",null,null,"uml sequence participant lifeline control"),this.createVertexTemplateEntry("shape=umlFrame;whiteSpace=wrap;html=1;",300,200,"frame","Frame",null,null,"uml sequence frame"),this.createVertexTemplateEntry("shape=umlDestroy;whiteSpace=wrap;html=1;strokeWidth=3;",30,30,"","Destruction",null,null,"uml sequence destruction destroy"),this.createVertexTemplateEntry("shape=note;whiteSpace=wrap;html=1;size=14;verticalAlign=top;align=left;spacingTop=-6;",100,70,"Note","Note",null,null,"uml note"),this.addEntry("uml sequence invoke invocation call activation",function(){var e=new mxCell("",new mxGeometry(0,0,10,80),"html=1;points=[];perimeter=orthogonalPerimeter;");e.vertex=true;var t=new mxCell("dispatch",new mxGeometry(0,0,0,0),"html=1;verticalAlign=bottom;startArrow=oval;endArrow=block;startSize=8;");t.geometry.setTerminalPoint(new mxPoint(-60,0),true);t.geometry.relative=true;t.edge=true;e.insertEdge(t,false);return r.createVertexTemplateFromCells([e,t],10,80,"Found Message")}),this.addEntry("uml sequence invoke call delegation synchronous invocation activation",function(){var e=new mxCell("",new mxGeometry(0,0,10,80),"html=1;points=[];perimeter=orthogonalPerimeter;");e.vertex=true;var t=new mxCell("dispatch",new mxGeometry(0,0,0,0),"html=1;verticalAlign=bottom;endArrow=block;entryX=0;entryY=0;");t.geometry.setTerminalPoint(new mxPoint(-70,0),true);t.geometry.relative=true;t.edge=true;e.insertEdge(t,false);var l=new mxCell("return",new mxGeometry(0,0,0,0),"html=1;verticalAlign=bottom;endArrow=open;dashed=1;endSize=8;exitX=0;exitY=0.95;");l.geometry.setTerminalPoint(new mxPoint(-70,76),false);l.geometry.relative=true;l.edge=true;e.insertEdge(l,true);return r.createVertexTemplateFromCells([e,t,l],10,80,"Synchronous Invocation")}),this.addEntry("uml sequence self call recursion delegation activation",function(){var e=new mxCell("",new mxGeometry(0,20,10,40),"html=1;points=[];perimeter=orthogonalPerimeter;");e.vertex=true;var t=new mxCell("self call",new mxGeometry(0,0,0,0),"edgeStyle=orthogonalEdgeStyle;html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;entryX=1;entryY=0;");t.geometry.setTerminalPoint(new mxPoint(5,0),true);t.geometry.points=[new mxPoint(30,0)];t.geometry.relative=true;t.edge=true;e.insertEdge(t,false);return r.createVertexTemplateFromCells([e,t],10,60,"Self Call")}),this.addEntry("uml sequence invoke call delegation callback activation",function(){return r.createVertexTemplateFromData("xZRNT8MwDIZ/Ta6oaymD47rBTkiTuMAxW6wmIm0q19s6fj1OE3V0Y2iCA4dK8euP2I+riGxedUuUjX52CqzIHkU2R+conKpuDtaKNDFKZAuRpgl/In264J303qSRCDVdk5CGhJ20WwhKEFo62ChoqritxURkReNMTa2X80LkC68AmgoIkEWHpF3pamlXR7WIFwASdBeb7KXY4RIc5+KBQ/ZGkY4RYY5Egyl1zLqLmmyDXQ6Zx4n5EIf+HkB2BmAjrV3LzftPIPw4hgNn1pQ1a2tH5Cp2QK1miG7vNeu4iJe4pdeY2BtvbCQDGlAljMCQxBJotJ8rWCFYSWY3LvUdmZi68rvkkLiU6QnL1m1xAzHoBOdw61WEb88II9AW67/ydQ2wq1Cy1aAGvOrFfPh6997qDA3g+dxzv3nIL6MPU/8T+kMw8+m4QPgdfrEJNo8PSQj/+s58Ag==",10,60,"Callback")}),this.createVertexTemplateEntry("html=1;points=[];perimeter=orthogonalPerimeter;",10,80,"","Activation",null,null,"uml sequence activation"),this.createEdgeTemplateEntry("html=1;verticalAlign=bottom;startArrow=oval;startFill=1;endArrow=block;startSize=8;",60,0,"dispatch","Found Message 1",null,"uml sequence message call invoke dispatch"),this.createEdgeTemplateEntry("html=1;verticalAlign=bottom;startArrow=circle;startFill=1;endArrow=open;startSize=6;endSize=8;",80,0,"dispatch","Found Message 2",null,"uml sequence message call invoke dispatch"),this.createEdgeTemplateEntry("html=1;verticalAlign=bottom;endArrow=block;",80,0,"dispatch","Message",null,"uml sequence message call invoke dispatch"),this.addEntry("uml sequence return message",function(){var e=new mxCell("return",new mxGeometry(0,0,0,0),"html=1;verticalAlign=bottom;endArrow=open;dashed=1;endSize=8;");e.geometry.setTerminalPoint(new mxPoint(80,0),true);e.geometry.setTerminalPoint(new mxPoint(0,0),false);e.geometry.relative=true;e.edge=true;return r.createEdgeTemplateFromCells([e],80,0,"Return")}),this.addEntry("uml relation",function(){var e=new mxCell("name",new mxGeometry(0,0,0,0),"endArrow=block;endFill=1;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=top;");e.geometry.setTerminalPoint(new mxPoint(0,0),true);e.geometry.setTerminalPoint(new mxPoint(160,0),false);e.geometry.relative=true;e.geometry.x=-1;e.edge=true;var t=new mxCell("1",new mxGeometry(-1,0,0,0),"resizable=0;html=1;align=left;verticalAlign=bottom;labelBackgroundColor=#ffffff;fontSize=10;");t.geometry.relative=true;t.setConnectable(false);t.vertex=true;e.insert(t);return r.createEdgeTemplateFromCells([e],160,0,"Relation 1")}),this.addEntry("uml association",function(){var e=new mxCell("",new mxGeometry(0,0,0,0),"endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;");e.geometry.setTerminalPoint(new mxPoint(0,0),true);e.geometry.setTerminalPoint(new mxPoint(160,0),false);e.geometry.relative=true;e.edge=true;var t=new mxCell("parent",new mxGeometry(-1,0,0,0),"resizable=0;html=1;align=left;verticalAlign=bottom;labelBackgroundColor=#ffffff;fontSize=10;");t.geometry.relative=true;t.setConnectable(false);t.vertex=true;e.insert(t);var l=new mxCell("child",new mxGeometry(1,0,0,0),"resizable=0;html=1;align=right;verticalAlign=bottom;labelBackgroundColor=#ffffff;fontSize=10;");l.geometry.relative=true;l.setConnectable(false);l.vertex=true;e.insert(l);return r.createEdgeTemplateFromCells([e],160,0,"Association 1")}),this.addEntry("uml aggregation",function(){var e=new mxCell("1",new mxGeometry(0,0,0,0),"endArrow=open;html=1;endSize=12;startArrow=diamondThin;startSize=14;startFill=0;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;");e.geometry.setTerminalPoint(new mxPoint(0,0),true);e.geometry.setTerminalPoint(new mxPoint(160,0),false);e.geometry.relative=true;e.geometry.x=-1;e.geometry.y=3;e.edge=true;return r.createEdgeTemplateFromCells([e],160,0,"Aggregation 1")}),this.addEntry("uml composition",function(){var e=new mxCell("1",new mxGeometry(0,0,0,0),"endArrow=open;html=1;endSize=12;startArrow=diamondThin;startSize=14;startFill=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;");e.geometry.setTerminalPoint(new mxPoint(0,0),true);e.geometry.setTerminalPoint(new mxPoint(160,0),false);e.geometry.relative=true;e.geometry.x=-1;e.geometry.y=3;e.edge=true;return r.createEdgeTemplateFromCells([e],160,0,"Composition 1")}),this.addEntry("uml relation",function(){var e=new mxCell("Relation",new mxGeometry(0,0,0,0),"endArrow=open;html=1;endSize=12;startArrow=diamondThin;startSize=14;startFill=0;edgeStyle=orthogonalEdgeStyle;");e.geometry.setTerminalPoint(new mxPoint(0,0),true);e.geometry.setTerminalPoint(new mxPoint(160,0),false);e.geometry.relative=true;e.edge=true;var t=new mxCell("0..n",new mxGeometry(-1,0,0,0),"resizable=0;html=1;align=left;verticalAlign=top;labelBackgroundColor=#ffffff;fontSize=10;");t.geometry.relative=true;t.setConnectable(false);t.vertex=true;e.insert(t);var l=new mxCell("1",new mxGeometry(1,0,0,0),"resizable=0;html=1;align=right;verticalAlign=top;labelBackgroundColor=#ffffff;fontSize=10;");l.geometry.relative=true;l.setConnectable(false);l.vertex=true;e.insert(l);return r.createEdgeTemplateFromCells([e],160,0,"Relation 2")}),this.createEdgeTemplateEntry("endArrow=open;endSize=12;dashed=1;html=1;",160,0,"Use","Dependency",null,"uml dependency use"),this.createEdgeTemplateEntry("endArrow=block;endSize=16;endFill=0;html=1;",160,0,"Extends","Generalization",null,"uml generalization extend"),this.createEdgeTemplateEntry("endArrow=block;startArrow=block;endFill=1;startFill=1;html=1;",160,0,"","Association 2",null,"uml association"),this.createEdgeTemplateEntry("endArrow=open;startArrow=circlePlus;endFill=0;startFill=0;endSize=8;html=1;",160,0,"","Inner Class",null,"uml inner class"),this.createEdgeTemplateEntry("endArrow=open;startArrow=cross;endFill=0;startFill=0;endSize=8;startSize=10;html=1;",160,0,"","Terminate",null,"uml terminate"),this.createEdgeTemplateEntry("endArrow=block;dashed=1;endFill=0;endSize=12;html=1;",160,0,"","Implementation",null,"uml realization implementation"),this.createEdgeTemplateEntry("endArrow=diamondThin;endFill=0;endSize=24;html=1;",160,0,"","Aggregation 2",null,"uml aggregation"),this.createEdgeTemplateEntry("endArrow=diamondThin;endFill=1;endSize=24;html=1;",160,0,"","Composition 2",null,"uml composition"),this.createEdgeTemplateEntry("endArrow=open;endFill=1;endSize=12;html=1;",160,0,"","Association 3",null,"uml association")];this.addPaletteFunctions("uml",mxResources.get("uml"),e||false,n)};Sidebar.prototype.addBpmnPalette=function(e,t){var r=this;var l=[this.createVertexTemplateEntry("shape=ext;rounded=1;html=1;whiteSpace=wrap;",120,80,"Task","Process",null,null,"bpmn task process"),this.createVertexTemplateEntry("shape=ext;rounded=1;html=1;whiteSpace=wrap;double=1;",120,80,"Transaction","Transaction",null,null,"bpmn transaction"),this.createVertexTemplateEntry("shape=ext;rounded=1;html=1;whiteSpace=wrap;dashed=1;dashPattern=1 4;",120,80,"Event\nSub-Process","Event Sub-Process",null,null,"bpmn event subprocess sub process sub-process"),this.createVertexTemplateEntry("shape=ext;rounded=1;html=1;whiteSpace=wrap;strokeWidth=3;",120,80,"Call Activity","Call Activity",null,null,"bpmn call activity"),this.addEntry("bpmn subprocess sub process sub-process",function(){var e=new mxCell("Sub-Process",new mxGeometry(0,0,120,80),"html=1;whiteSpace=wrap;rounded=1;");e.vertex=true;var t=new mxCell("",new mxGeometry(.5,1,14,14),"html=1;shape=plus;outlineConnect=0;");t.vertex=true;t.geometry.relative=true;t.geometry.offset=new mxPoint(-7,-14);e.insert(t);return r.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Sub-Process")}),this.addEntry(this.getTagsForStencil("mxgraph.bpmn","loop","subprocess sub process sub-process looped").join(" "),function(){var e=new mxCell("Looped\nSub-Process",new mxGeometry(0,0,120,80),"html=1;whiteSpace=wrap;rounded=1");e.vertex=true;var t=new mxCell("",new mxGeometry(.5,1,14,14),"html=1;shape=mxgraph.bpmn.loop;outlineConnect=0;");t.vertex=true;t.geometry.relative=true;t.geometry.offset=new mxPoint(-15,-14);e.insert(t);var l=new mxCell("",new mxGeometry(.5,1,14,14),"html=1;shape=plus;");l.vertex=true;l.geometry.relative=true;l.geometry.offset=new mxPoint(1,-14);e.insert(l);return r.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Looped Sub-Process")}),this.addEntry("bpmn receive task",function(){var e=new mxCell("Receive",new mxGeometry(0,0,120,80),"html=1;whiteSpace=wrap;rounded=1;");e.vertex=true;var t=new mxCell("",new mxGeometry(0,0,20,14),"html=1;shape=message;outlineConnect=0;");t.vertex=true;t.geometry.relative=true;t.geometry.offset=new mxPoint(7,7);e.insert(t);return r.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Receive Task")}),this.addEntry(this.getTagsForStencil("mxgraph.bpmn","user_task").join(" "),function(){var e=new mxCell("User",new mxGeometry(0,0,120,80),"html=1;whiteSpace=wrap;rounded=1;");e.vertex=true;var t=new mxCell("",new mxGeometry(0,0,14,14),"html=1;shape=mxgraph.bpmn.user_task;outlineConnect=0;");t.vertex=true;t.geometry.relative=true;t.geometry.offset=new mxPoint(7,7);e.insert(t);var l=new mxCell("",new mxGeometry(.5,1,14,14),"html=1;shape=plus;outlineConnect=0;");l.vertex=true;l.geometry.relative=true;l.geometry.offset=new mxPoint(-7,-14);e.insert(l);return r.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"User Task")}),this.addEntry(this.getTagsForStencil("mxgraph.bpmn","timer_start","attached").join(" "),function(){var e=new mxCell("Process",new mxGeometry(0,0,120,80),"html=1;whiteSpace=wrap;rounded=1;");e.vertex=true;var t=new mxCell("",new mxGeometry(1,1,30,30),"shape=mxgraph.bpmn.timer_start;perimeter=ellipsePerimeter;html=1;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;outlineConnect=0;");t.vertex=true;t.geometry.relative=true;t.geometry.offset=new mxPoint(-40,-15);e.insert(t);return r.createVertexTemplateFromCells([e],120,95,"Attached Timer Event 1")}),this.addEntry(this.getTagsForStencil("mxgraph.bpmn","timer_start","attached").join(" "),function(){var e=new mxCell("Process",new mxGeometry(0,0,120,80),"html=1;whiteSpace=wrap;rounded=1;");e.vertex=true;var t=new mxCell("",new mxGeometry(1,0,30,30),"shape=mxgraph.bpmn.timer_start;perimeter=ellipsePerimeter;html=1;labelPosition=right;labelBackgroundColor=#ffffff;align=left;outlineConnect=0;");t.vertex=true;t.geometry.relative=true;t.geometry.offset=new mxPoint(-15,10);e.insert(t);return r.createVertexTemplateFromCells([e],135,80,"Attached Timer Event 2")}),this.createVertexTemplateEntry("swimlane;html=1;horizontal=0;startSize=20;",320,240,"Pool","Pool",null,null,"bpmn pool"),this.createVertexTemplateEntry("swimlane;html=1;horizontal=0;swimlaneLine=0;",300,120,"Lane","Lane",null,null,"bpmn lane"),this.createVertexTemplateEntry("shape=hexagon;html=1;whiteSpace=wrap;perimeter=hexagonPerimeter;rounded=0;",60,50,"","Conversation",null,null,"bpmn conversation"),this.createVertexTemplateEntry("shape=hexagon;html=1;whiteSpace=wrap;perimeter=hexagonPerimeter;strokeWidth=4;rounded=0;",60,50,"","Call Conversation",null,null,"bpmn call conversation"),this.addEntry("bpmn subconversation sub conversation sub-conversation",function(){var e=new mxCell("",new mxGeometry(0,0,60,50),"shape=hexagon;whiteSpace=wrap;html=1;perimeter=hexagonPerimeter;rounded=0;");e.vertex=true;var t=new mxCell("",new mxGeometry(.5,1,14,14),"html=1;shape=plus;");t.vertex=true;t.geometry.relative=true;t.geometry.offset=new mxPoint(-7,-14);e.insert(t);return r.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Sub-Conversation")}),this.addEntry("bpmn data object",function(){var e=new mxCell("",new mxGeometry(0,0,40,60),"shape=note;whiteSpace=wrap;size=16;html=1;");e.vertex=true;var t=new mxCell("",new mxGeometry(0,0,14,14),"html=1;shape=singleArrow;arrowWidth=0.4;arrowSize=0.4;outlineConnect=0;");t.vertex=true;t.geometry.relative=true;t.geometry.offset=new mxPoint(2,2);e.insert(t);var l=new mxCell("",new mxGeometry(.5,1,14,14),"html=1;whiteSpace=wrap;shape=parallelMarker;outlineConnect=0;");l.vertex=true;l.geometry.relative=true;l.geometry.offset=new mxPoint(-7,-14);e.insert(l);return r.createVertexTemplateFromCells([e],e.geometry.width,e.geometry.height,"Data Object")}),this.createVertexTemplateEntry("shape=datastore;whiteSpace=wrap;html=1;",60,60,"","Data Store",null,null,"bpmn data store"),this.createVertexTemplateEntry("shape=plus;html=1;outlineConnect=0;",14,14,"","Sub-Process Marker",null,null,"bpmn subprocess sub process sub-process marker"),this.createVertexTemplateEntry("shape=mxgraph.bpmn.loop;html=1;outlineConnect=0;",14,14,"","Loop Marker",null,null,"bpmn loop marker"),this.createVertexTemplateEntry("shape=parallelMarker;html=1;outlineConnect=0;",14,14,"","Parallel MI Marker",null,null,"bpmn parallel mi marker"),this.createVertexTemplateEntry("shape=parallelMarker;direction=south;html=1;outlineConnect=0;",14,14,"","Sequential MI Marker",null,null,"bpmn sequential mi marker"),this.createVertexTemplateEntry("shape=mxgraph.bpmn.ad_hoc;fillColor=#000000;html=1;outlineConnect=0;",14,14,"","Ad Hoc Marker",null,null,"bpmn ad hoc marker"),this.createVertexTemplateEntry("shape=mxgraph.bpmn.compensation;html=1;outlineConnect=0;",14,14,"","Compensation Marker",null,null,"bpmn compensation marker"),this.createVertexTemplateEntry("shape=message;whiteSpace=wrap;html=1;outlineConnect=0;fillColor=#000000;strokeColor=#ffffff;strokeWidth=2;",40,30,"","Send Task",null,null,"bpmn send task"),this.createVertexTemplateEntry("shape=message;whiteSpace=wrap;html=1;outlineConnect=0;",40,30,"","Receive Task",null,null,"bpmn receive task"),this.createVertexTemplateEntry("shape=mxgraph.bpmn.user_task;html=1;outlineConnect=0;",14,14,"","User Task",null,null,this.getTagsForStencil("mxgraph.bpmn","user_task").join(" ")),this.createVertexTemplateEntry("shape=mxgraph.bpmn.manual_task;html=1;outlineConnect=0;",14,14,"","Manual Task",null,null,this.getTagsForStencil("mxgraph.bpmn","user_task").join(" ")),this.createVertexTemplateEntry("shape=mxgraph.bpmn.business_rule_task;html=1;outlineConnect=0;",14,14,"","Business Rule Task",null,null,this.getTagsForStencil("mxgraph.bpmn","business_rule_task").join(" ")),this.createVertexTemplateEntry("shape=mxgraph.bpmn.service_task;html=1;outlineConnect=0;",14,14,"","Service Task",null,null,this.getTagsForStencil("mxgraph.bpmn","service_task").join(" ")),this.createVertexTemplateEntry("shape=mxgraph.bpmn.script_task;html=1;outlineConnect=0;",14,14,"","Script Task",null,null,this.getTagsForStencil("mxgraph.bpmn","script_task").join(" ")),this.createVertexTemplateEntry("html=1;shape=mxgraph.flowchart.annotation_2;align=left;labelPosition=right;",50,100,"","Annotation",null,null,this.getTagsForStencil("bpmn","annotation_1","bpmn business process model ").join(" ")),this.addDataEntry("container swimlane pool horizontal",480,380,"Horizontal Pool 1","zZRLbsIwEIZP4709TlHXhJYNSEicwCIjbNWJkWNKwumZxA6IlrRUaisWlmb+eX8LM5mXzdyrnV66Ai2TL0zm3rkQrbLJ0VoG3BRMzhgAp8fgdSQq+ijfKY9VuKcAYsG7snuMyso5G8U6tDaJ9cGUVlXkTXUoacuZIHOjjS0WqnX7blYd1OZt8KYea3PE1bCI+CAtVUMq7/o5b46uCmroSn18WFMm+XCdse5GpLq0OPqAzejxvZQun6MrMfiWUg6mCDpmZM8RENdotjqVyUFUdRS259oLSzISztto5Se0i44gcHEn3i9A/IQB3GbQpmi69DskAn4BSTaGBB4Jicj+k8nTGBP5SExg8odMyL38eH3s6kM8AQ=="),this.addDataEntry("container swimlane pool horizontal",480,360,"Horizontal Pool 2","zZTBbsIwDIafJvfU6dDOlI0LSEg8QUQtEi1tUBJGy9PPbcJQWTsxaZs4VLJ//07sT1WYKKpm6eRBrW2JhokXJgpnbYhR1RRoDAOuSyYWDIDTx+B1opr1VX6QDutwTwPEhndpjhiVjbUmij60Jon+pCsja8rmKlQ05SKjcKe0KVeytcfuLh/k7u2SzR16fcbNZZDsRlrLhlTenWedPts6SJMEOseFLTkph6Fj212RbGlwdAGbyeV7KW2+RFthcC1ZTroMKjry5wiIK9R7ldrELInSR2H/2XtlSUHCOY5WfEG76ggCz+7E+w2InzCAcQapIf0fAySzESQZ/AKSfAoJPCKS9mbzf0H0NIVIPDAiyP8QEaXX97CvDZ7LDw=="),this.createVertexTemplateEntry("swimlane;startSize=20;horizontal=0;",320,120,"Lane","Horizontal Swimlane",null,null,"swimlane lane pool"),this.addDataEntry("container swimlane pool horizontal",360,480,"Vertical Pool 1","xZRBbsIwEEVP4709ThFrQssGJKSewCIjbNXGyDEl4fSdxKa0NJFQVTULSzP/e+T5b2EmS9esgjrqja/QMvnMZBm8j6lyTYnWMuCmYnLJADgdBi8jruhdflQBD/GRAUgD78qeMClb720S69jaLNZn46w6ULfQ0dGWS0HlThtbrVXrT91bdVS7t2u3CFibC26vi4g7aaMaUjmpNBbiKxnUQyfkjTBEbEZT9VKOtELvMIaWrpxNFXW6IWcpOddo9jqPFfMsqjoJ+8/ZGyQqMqdhZvIHs3WHBrh4kNvvIsNw5Da7OdgXAgKGCMz+gEAxRgCmINDcxZ2CyNMYETkhESj+jwi1t1+r9759ah8="),this.addDataEntry("container swimlane pool vertical",380,480,"Vertical Pool 2","xZTPbsIwDMafJvf86dDOlI0LSEg8QUQtEi1pUBJGy9PPbdJ1G1TqhXGoZH/219g/RSGitM3ay5PaugoMEW9ElN65mCLblGAM4VRXRKwI5xQ/wt8nqqyv0pP0UMc5Bp4Mn9KcISk750wSQ2xNFsNFWyNrzJYqWpxyxTA8KG2qjWzduTsrRHn4GLKlh6CvsBsGYX+krWxQpaiizcc9FjDnnaCc11dXR2lyxyjsuyPy3/Lg4CM0k8v3Ut58Dc5C9C22XHQVVeoQrwkQVaCPKtuKQZQhCcdv78gSg4zzPlpxg3bTEeSUzcR7Q2bWyvz+ytmQr8NPAow/ikAxRYA/kQAr/hPByxQC8cxLsHggAkzH56uv/XrdvgA="),this.createVertexTemplateEntry("swimlane;startSize=20;",120,320,"Lane","Vertical Swimlane",null,null,"swimlane lane pool"),this.createVertexTemplateEntry("rounded=1;arcSize=10;dashed=1;strokeColor=#000000;fillColor=none;gradientColor=none;dashPattern=8 3 1 3;strokeWidth=2;",200,200,"","Group",null,null,this.getTagsForStencil("bpmn","group","bpmn business process model ").join(" ")),this.createEdgeTemplateEntry("endArrow=block;endFill=1;endSize=6;html=1;",100,0,"","Sequence Flow",null,"bpmn sequence flow"),this.createEdgeTemplateEntry("startArrow=dash;startSize=8;endArrow=block;endFill=1;endSize=6;html=1;",100,0,"","Default Flow",null,"bpmn default flow"),this.createEdgeTemplateEntry("startArrow=diamondThin;startFill=0;startSize=14;endArrow=block;endFill=1;endSize=6;html=1;",100,0,"","Conditional Flow",null,"bpmn conditional flow"),this.createEdgeTemplateEntry("startArrow=oval;startFill=0;startSize=7;endArrow=block;endFill=0;endSize=10;dashed=1;html=1;",100,0,"","Message Flow 1",null,"bpmn message flow"),this.addEntry("bpmn message flow",function(){var e=new mxCell("",new mxGeometry(0,0,0,0),"startArrow=oval;startFill=0;startSize=7;endArrow=block;endFill=0;endSize=10;dashed=1;html=1;");e.geometry.setTerminalPoint(new mxPoint(0,0),true);e.geometry.setTerminalPoint(new mxPoint(100,0),false);e.geometry.relative=true;e.edge=true;var t=new mxCell("",new mxGeometry(0,0,20,14),"shape=message;html=1;outlineConnect=0;");t.geometry.relative=true;t.vertex=true;t.geometry.offset=new mxPoint(-10,-7);e.insert(t);return r.createEdgeTemplateFromCells([e],100,0,"Message Flow 2")}),this.createEdgeTemplateEntry("shape=link;html=1;",100,0,"","Link",null,"bpmn link")];this.addPaletteFunctions("bpmn","BPMN "+mxResources.get("general"),false,l)};Sidebar.prototype.createTitle=function(e){var t=document.createElement("a");t.setAttribute("title",mxResources.get("sidebarTooltip"));t.className="geTitle";mxUtils.write(t,e);return t};Sidebar.prototype.createThumb=function(e,t,l,r,i,n,a,o,s,m){this.graph.labelsVisible=n==null||n;var h=mxClient.NO_FO;mxClient.NO_FO=Editor.prototype.originalNoForeignObject;this.graph.view.scaleAndTranslate(1,0,0);this.graph.addCells(e);var d=this.graph.getGraphBounds();var p=Math.floor(Math.min((t-2*this.thumbBorder)/d.width,(l-2*this.thumbBorder)/d.height)*100)/100;this.graph.view.scaleAndTranslate(p,Math.floor((t-d.width*p)/2/p-d.x),Math.floor((l-d.height*p)/2/p-d.y));var c=null;if(this.graph.dialect==mxConstants.DIALECT_SVG&&!mxClient.NO_FO&&this.graph.view.getCanvas().ownerSVGElement!=null){c=this.graph.view.getCanvas().ownerSVGElement.cloneNode(true)}else{c=this.graph.container.cloneNode(false);c.innerHTML=this.graph.container.innerHTML;if(mxClient.IS_QUIRKS||document.documentMode==8){c.firstChild.style.overflow="visible"}}this.graph.getModel().clear();mxClient.NO_FO=h;if(mxClient.IS_IE6){r.style.backgroundImage="url("+this.editorUi.editor.transparentImage+")"}c.style.position="relative";c.style.overflow="hidden";c.style.left=this.thumbBorder+"px";c.style.top=this.thumbBorder+"px";c.style.width=t+"px";c.style.height=l+"px";c.style.visibility="";c.style.minWidth="";c.style.minHeight="";if(!m){r.appendChild(c)}if(this.sidebarTitles&&i!=null&&a!=false){var u=mxClient.IS_QUIRKS?2*this.thumbPadding+2:0;r.style.height=this.thumbHeight+u+this.sidebarTitleSize+8+"px";var g=document.createElement("div");g.style.fontSize=this.sidebarTitleSize+"px";g.style.color="#303030";g.style.textAlign="center";g.style.whiteSpace="nowrap";if(mxClient.IS_IE){g.style.height=this.sidebarTitleSize+12+"px"}g.style.paddingTop="4px";mxUtils.write(g,i);r.appendChild(g)}return d};Sidebar.prototype.createItem=function(e,t,l,r,i,n,a,o,s){var m=o&&o.ref||s&&s.ref||document.createElement("a");if(!(o&&o.ref||s&&s.ref)){m.className="geItem";m.style.overflow="hidden";var h=mxClient.IS_QUIRKS?8+2*this.thumbPadding:2*this.thumbBorder;m.style.width=this.thumbWidth+h+"px";m.style.height=this.thumbHeight+h+"px";m.style.padding=this.thumbPadding+"px";if(mxClient.IS_IE6){m.style.border="none"}}mxEvent.addListener(m,"click",function(e){mxEvent.consume(e)});this.createThumb(e,this.thumbWidth,this.thumbHeight,m,t,l,r,i,n,o&&o.ref||s&&s.ref);var d=new mxRectangle(0,0,i,n);if(e.length>1||e[0].vertex){var p=this.createDragSource(m,this.createDropHandler(e,true,a,d,o,s),this.createDragPreview(i,n,o&&o.ref||s&&s.ref),e,d);this.addClickHandler(m,p,e);p.isGuidesEnabled=mxUtils.bind(this,function(){return this.editorUi.editor.graph.graphHandler.guidesEnabled})}else if(e[0]!=null&&e[0].edge){var p=this.createDragSource(m,this.createDropHandler(e,false,a,d,o,s),this.createDragPreview(i,n,o&&o.ref||s&&s.ref),e,d);this.addClickHandler(m,p,e)}return m};Sidebar.prototype.updateShapes=function(e,t){var l=this.editorUi.editor.graph;var r=l.getCellStyle(e);var i=[];l.model.beginUpdate();try{var n=l.getModel().getStyle(e);var a=["shadow","dashed","dashPattern","fontFamily","fontSize","fontColor","align","startFill","startSize","endFill","endSize","strokeColor","strokeWidth","fillColor","gradientColor","html","part","noEdgeStyle","edgeStyle","elbow","childLayout","recursiveResize","container","collapsible","connectable"];for(var o=0;o<t.length;o++){var s=t[o];if(l.getModel().isVertex(s)==l.getModel().isVertex(e)||l.getModel().isEdge(s)==l.getModel().isEdge(e)){var m=l.view.getState(s);var h=m!=null?m.style:l.getCellStyle(t[o]);l.getModel().setStyle(s,n);if(m!=null&&mxUtils.getValue(m.style,"composite","0")=="1"){var d=l.model.getChildCount(s);for(var p=d;p>=0;p--){l.model.remove(l.model.getChildAt(s,p))}}if(h!=null){if(h[mxConstants.STYLE_SHAPE]=="umlLifeline"&&r[mxConstants.STYLE_SHAPE]!="umlLifeline"){l.setCellStyles(mxConstants.STYLE_SHAPE,"umlLifeline",[s]);l.setCellStyles("participant",r[mxConstants.STYLE_SHAPE],[s])}for(var p=0;p<a.length;p++){var c=h[a[p]];if(c!=null){l.setCellStyles(a[p],c,[s])}}}i.push(s)}}}finally{l.model.endUpdate()}return i};var ceshiIndex=0;Sidebar.prototype.createDropHandler=function(f,y,x,w,v,b){x=x!=null?x:true;return mxUtils.bind(this,function(e,t,l,r,i,n){var a=n?null:mxEvent.isTouchEvent(t)||mxEvent.isPenEvent(t)?document.elementFromPoint(mxEvent.getClientX(t),mxEvent.getClientY(t)):mxEvent.getSource(t);while(a!=null&&a!=this.container){a=a.parentNode}if(a==null&&e.isEnabled()){f=e.getImportableCells(f);if(f.length>0){e.stopEditing();var o=l!=null&&!mxEvent.isAltDown(t)?e.isValidDropTarget(l,f,t):false;var s=null;if(l!=null&&!o){l=null}if(!e.isCellLocked(l||e.getDefaultParent())){e.model.beginUpdate();try{r=Math.round(r);i=Math.round(i);if(y&&e.isSplitTarget(l,f,t)){var m=e.cloneCells(f);e.splitEdge(l,m,null,r-w.width/2,i-w.height/2);s=m}else if(f.length>0){s=e.importCells(f,r,i,l,undefined,undefined,b)}if(e.layoutManager!=null){var h=e.layoutManager.getLayout(l);if(h!=null){var d=e.view.scale;var p=e.view.translate;var c=(r+p.x)*d;var u=(i+p.y)*d;for(var g=0;g<s.length;g++){h.moveCell(s[g],c,u)}}}if(x&&(t==null||!mxEvent.isShiftDown(t))){e.fireEvent(new mxEventObject("cellsInserted","cells",s))}}catch(e){this.editorUi.handleError(e)}finally{if(s&&s[0]&&s[0].beforeMountValue){delete s[0].beforeMountValue}if(v){if(e&&!e.customDom){e.customDom=mxUtils.clone(v,undefined,undefined,true);e.customDom.ref=null}if(v.ref&&s&&s[0]&&!s[0].customDom){if(typeof v.idCreat==="function"){v.id=v.idCreat&&v.idCreat()||""}s[0].customDom=mxUtils.clone(v,undefined,undefined,true)}}else if(b){if(e&&!e.sceneComponent){e.sceneComponent=mxUtils.clone(b,undefined,undefined,true);delete e.sceneComponent.ref;delete e.sceneComponent.sceneComponentType}if(b.ref&&s&&s[0]&&!s[0].sceneComponent){s[0].sceneComponent=mxUtils.clone(b,undefined,undefined,true);mxUtils.recursiveSetChildren(s,function(e){if(!e.sceneComponent){e.sceneComponent=mxUtils.clone(b,undefined,undefined,true)}});if(window.__customSceneComponentId&&typeof s[0].sceneComponent.idCreat==="function"){s[0].sceneComponentId=s[0].sceneComponent.idCreat()}}}e.model.endUpdate()}if(s!=null&&s.length>0){e.scrollCellToVisible(s[0]);e.setSelectionCells(s)}if(e.editAfterInsert&&t!=null&&mxEvent.isMouseEvent(t)&&s!=null&&s.length==1){window.setTimeout(function(){e.startEditing(s[0])},0)}}}mxEvent.consume(t)}})};Sidebar.prototype.createDragPreview=function(e,t,l){var r=l?l.cloneNode(true):document.createElement("div");if(!l){r.style.border=this.dragPreviewBorder}else{r.className="thumb"}r.style.width=e+"px";r.style.height=t+"px";return r};Sidebar.prototype.dropAndConnect=function(e,t,l,r,i){var n=this.getDropAndConnectGeometry(e,t[r],l,t);var a=[];if(n!=null){var o=this.editorUi.editor.graph;var s=null;o.model.beginUpdate();try{var m=o.getCellGeometry(e);var h=o.getCellGeometry(t[r]);var d=o.model.getParent(e);var p=true;if(o.layoutManager!=null){var c=o.layoutManager.getLayout(d);if(c!=null&&c.constructor==mxStackLayout){p=false;var a=o.view.getState(d);if(a!=null){var u=new mxPoint(a.x/o.view.scale-o.view.translate.x,a.y/o.view.scale-o.view.translate.y);n.x+=u.x;n.y+=u.y;var g=n.getTerminalPoint(false);if(g!=null){g.x+=u.x;g.y+=u.y}}}}var f=h.x;var y=h.y;if(o.model.isEdge(t[r])){f=0;y=0}var x=o.model.isEdge(e)||m!=null&&!m.relative&&p;t=o.importCells(t,n.x-(x?f:0),n.y-(x?y:0),x?d:null);a=t;if(o.model.isEdge(e)){o.model.setTerminal(e,t[r],l==mxConstants.DIRECTION_NORTH)}else if(o.model.isEdge(t[r])){o.model.setTerminal(t[r],e,true);var w=o.getCellGeometry(t[r]);w.points=null;if(w.getTerminalPoint(false)!=null){w.setTerminalPoint(n.getTerminalPoint(false),false)}else if(x&&o.model.isVertex(d)){var v=o.view.getState(d);var u=v.cell!=o.view.currentRoot?new mxPoint(v.x/o.view.scale-o.view.translate.x,v.y/o.view.scale-o.view.translate.y):new mxPoint(0,0);o.cellsMoved(t,u.x,u.y,null,null,true)}}else{h=o.getCellGeometry(t[r]);f=n.x-Math.round(h.x);y=n.y-Math.round(h.y);n.x=Math.round(h.x);n.y=Math.round(h.y);o.model.setGeometry(t[r],n);o.cellsMoved(t,f,y,null,null,true);a=t.slice();s=a.length==1?a[0]:null;t.push(o.insertEdge(null,null,"",e,t[r],o.createCurrentEdgeStyle()))}if(i==null||!mxEvent.isShiftDown(i)){o.fireEvent(new mxEventObject("cellsInserted","cells",t))}}catch(e){this.editorUi.handleError(e)}finally{o.model.endUpdate()}if(o.editAfterInsert&&i!=null&&mxEvent.isMouseEvent(i)&&s!=null){window.setTimeout(function(){o.startEditing(s)},0)}}return a};Sidebar.prototype.getDropAndConnectGeometry=function(e,t,l,r){var i=this.editorUi.editor.graph;var n=i.view;var a=r.length>1;var o=i.getCellGeometry(e);var s=i.getCellGeometry(t);if(o!=null&&s!=null){s=s.clone();if(i.model.isEdge(e)){var m=i.view.getState(e);var h=m.absolutePoints;var d=h[0];var p=h[h.length-1];if(l==mxConstants.DIRECTION_NORTH){s.x=d.x/n.scale-n.translate.x-s.width/2;s.y=d.y/n.scale-n.translate.y-s.height/2}else{s.x=p.x/n.scale-n.translate.x-s.width/2;s.y=p.y/n.scale-n.translate.y-s.height/2}}else{if(o.relative){var m=i.view.getState(e);o=o.clone();o.x=(m.x-n.translate.x)/n.scale;o.y=(m.y-n.translate.y)/n.scale}var c=i.defaultEdgeLength;if(i.model.isEdge(t)&&s.getTerminalPoint(true)!=null&&s.getTerminalPoint(false)!=null){var d=s.getTerminalPoint(true);var p=s.getTerminalPoint(false);var u=p.x-d.x;var g=p.y-d.y;c=Math.sqrt(u*u+g*g);s.x=o.getCenterX();s.y=o.getCenterY();s.width=1;s.height=1;if(l==mxConstants.DIRECTION_NORTH){s.height=c;s.y=o.y-c;s.setTerminalPoint(new mxPoint(s.x,s.y),false)}else if(l==mxConstants.DIRECTION_EAST){s.width=c;s.x=o.x+o.width;s.setTerminalPoint(new mxPoint(s.x+s.width,s.y),false)}else if(l==mxConstants.DIRECTION_SOUTH){s.height=c;s.y=o.y+o.height;s.setTerminalPoint(new mxPoint(s.x,s.y+s.height),false)}else if(l==mxConstants.DIRECTION_WEST){s.width=c;s.x=o.x-c;s.setTerminalPoint(new mxPoint(s.x,s.y),false)}}else{if(!a&&s.width>45&&s.height>45&&o.width>45&&o.height>45){s.width=s.width*(o.height/s.height);s.height=o.height}s.x=o.x+o.width/2-s.width/2;s.y=o.y+o.height/2-s.height/2;if(l==mxConstants.DIRECTION_NORTH){s.y=s.y-o.height/2-s.height/2-c}else if(l==mxConstants.DIRECTION_EAST){s.x=s.x+o.width/2+s.width/2+c}else if(l==mxConstants.DIRECTION_SOUTH){s.y=s.y+o.height/2+s.height/2+c}else if(l==mxConstants.DIRECTION_WEST){s.x=s.x-o.width/2-s.width/2-c}if(i.model.isEdge(t)&&s.getTerminalPoint(true)!=null&&t.getTerminal(false)!=null){var f=i.getCellGeometry(t.getTerminal(false));if(f!=null){if(l==mxConstants.DIRECTION_NORTH){s.x-=f.getCenterX();s.y-=f.getCenterY()+f.height/2}else if(l==mxConstants.DIRECTION_EAST){s.x-=f.getCenterX()-f.width/2;s.y-=f.getCenterY()}else if(l==mxConstants.DIRECTION_SOUTH){s.x-=f.getCenterX();s.y-=f.getCenterY()-f.height/2}else if(l==mxConstants.DIRECTION_WEST){s.x-=f.getCenterX()+f.width/2;s.y-=f.getCenterY()}}}}}}return s};Sidebar.prototype.isDropStyleEnabled=function(e,t){var l=true;if(t!=null&&e.length==1){var r=this.graph.getCellStyle(e[t]);if(r!=null){l=mxUtils.getValue(r,mxConstants.STYLE_STROKECOLOR,mxConstants.NONE)!=mxConstants.NONE||mxUtils.getValue(r,mxConstants.STYLE_FILLCOLOR,mxConstants.NONE)!=mxConstants.NONE}}return l};Sidebar.prototype.isDropStyleTargetIgnored=function(e){return this.graph.isSwimlane(e.cell)};Sidebar.prototype.createDragSource=function(e,s,t,b,c){var l=this.editorUi;var r=l.editor.graph;var u=null;var C=null;var g=this;for(var i=0;i<b.length;i++){if(C==null&&this.editorUi.editor.graph.model.isVertex(b[i])){C=i}else if(u==null&&this.editorUi.editor.graph.model.isEdge(b[i])&&this.editorUi.editor.graph.model.getTerminal(b[i],true)==null){u=i}if(C!=null&&u!=null){break}}var E=this.isDropStyleEnabled(b,C);var S=mxUtils.makeDraggable(e,this.editorUi.editor.graph,mxUtils.bind(this,function(e,t,l,r,i){if(this.updateThread!=null){window.clearTimeout(this.updateThread)}if(b!=null&&P!=null&&G==L){var n=e.isCellSelected(P.cell)?e.getSelectionCells():[P.cell];var a=this.updateShapes(e.model.isEdge(P.cell)?b[0]:b[C],n);e.setSelectionCells(a)}else if(b!=null&&G!=null&&T!=null&&G!=L){var o=e.model.isEdge(T.cell)||u==null?C:u;e.setSelectionCells(this.dropAndConnect(T.cell,b,U,o,t))}else{s.apply(this,arguments)}if(this.editorUi.hoverIcons!=null){this.editorUi.hoverIcons.update(e.view.getState(e.getSelectionCell()))}}),t,0,0,r.autoscroll,true,true);r.addListener(mxEvent.ESCAPE,function(e,t){if(S.isActive()){S.reset()}});var n=S.mouseDown;S.mouseDown=function(e){if(!mxEvent.isPopupTrigger(e)&&!mxEvent.isMultiTouchEvent(e)){r.stopEditing();n.apply(this,arguments)}};function a(e,t){var l=null;if(mxClient.IS_IE&&!mxClient.IS_SVG){if(mxClient.IS_IE6&&document.compatMode!="CSS1Compat"){l=document.createElement(mxClient.VML_PREFIX+":image");l.setAttribute("src",e.src);l.style.borderStyle="none"}else{l=document.createElement("div");l.style.backgroundImage="url("+e.src+")";l.style.backgroundPosition="center";l.style.backgroundRepeat="no-repeat"}l.style.width=e.width+4+"px";l.style.height=e.height+4+"px";l.style.display=mxClient.IS_QUIRKS?"inline":"inline-block"}else{l=mxUtils.createImage(e.src);l.style.width=e.width+"px";l.style.height=e.height+"px"}if(t!=null){l.setAttribute("title",t)}mxUtils.setOpacity(l,e==this.refreshTarget?30:20);l.style.position="absolute";l.style.cursor="crosshair";return l}var T=null;var A=null;var P=null;var V=false;var I=a(this.triangleUp,mxResources.get("connect"));var k=a(this.triangleRight,mxResources.get("connect"));var R=a(this.triangleDown,mxResources.get("connect"));var D=a(this.triangleLeft,mxResources.get("connect"));var L=a(this.refreshTarget,mxResources.get("replace"));var N=null;var z=a(this.roundDrop);var M=a(this.roundDrop);var U=mxConstants.DIRECTION_NORTH;var G=null;function O(e,t,l,r){if(r.parentNode!=null){if(mxUtils.contains(l,e,t)){mxUtils.setOpacity(r,100);G=r}else{mxUtils.setOpacity(r,r==L?30:20)}}return l}var o=S.createPreviewElement;S.createPreviewElement=function(e){var t=o.apply(this,arguments);if(mxClient.IS_SVG){t.style.pointerEvents="none"}this.previewElementWidth=t.style.width;this.previewElementHeight=t.style.height;return t};var m=S.dragEnter;S.dragEnter=function(e,t){if(l.hoverIcons!=null){l.hoverIcons.setDisplay("none")}m.apply(this,arguments)};var h=S.dragExit;S.dragExit=function(e,t){if(l.hoverIcons!=null){l.hoverIcons.setDisplay("")}h.apply(this,arguments)};S.dragOver=function(e,t){mxDragSource.prototype.dragOver.apply(this,arguments);if(this.currentGuide!=null&&G!=null){this.currentGuide.hide()}if(this.previewElement!=null){var l=e.view;if(P!=null&&G==L){this.previewElement.style.display=e.model.isEdge(P.cell)?"none":"";this.previewElement.style.left=P.x+"px";this.previewElement.style.top=P.y+"px";this.previewElement.style.width=P.width+"px";this.previewElement.style.height=P.height+"px"}else if(T!=null&&G!=null){var r=e.model.isEdge(T.cell)||u==null?C:u;var i=g.getDropAndConnectGeometry(T.cell,b[r],U,b);var n=!e.model.isEdge(T.cell)?e.getCellGeometry(T.cell):null;var a=e.getCellGeometry(b[r]);var o=e.model.getParent(T.cell);var s=l.translate.x*l.scale;var m=l.translate.y*l.scale;if(n!=null&&!n.relative&&e.model.isVertex(o)&&o!=l.currentRoot){var h=l.getState(o);s=h.x;m=h.y}var d=a.x;var p=a.y;if(e.model.isEdge(b[r])){d=0;p=0}this.previewElement.style.left=(i.x-d)*l.scale+s+"px";this.previewElement.style.top=(i.y-p)*l.scale+m+"px";if(b.length==1){this.previewElement.style.width=i.width*l.scale+"px";this.previewElement.style.height=i.height*l.scale+"px"}this.previewElement.style.display=""}else if(S.currentHighlight.state!=null&&e.model.isEdge(S.currentHighlight.state.cell)){this.previewElement.style.left=Math.round(parseInt(this.previewElement.style.left)-c.width*l.scale/2)+"px";this.previewElement.style.top=Math.round(parseInt(this.previewElement.style.top)-c.height*l.scale/2)+"px"}else{this.previewElement.style.width=this.previewElementWidth;this.previewElement.style.height=this.previewElementHeight;this.previewElement.style.display=""}}};var F=(new Date).getTime();var j=0;var H=null;var B=this.editorUi.editor.graph.getCellStyle(b[0]);S.getDropTarget=mxUtils.bind(this,function(e,t,l,r){var i=!mxEvent.isAltDown(r)&&b!=null?e.getCellAt(t,l):null;if(i!=null&&!this.graph.isCellConnectable(i)){var n=this.graph.getModel().getParent(i);if(this.graph.getModel().isVertex(n)&&this.graph.isCellConnectable(n)){i=n}}if(e.isCellLocked(i)){i=null}var a=e.view.getState(i);G=null;var o=null;if(H!=a){H=a;F=(new Date).getTime();j=0;if(this.updateThread!=null){window.clearTimeout(this.updateThread)}if(a!=null){this.updateThread=window.setTimeout(function(){if(G==null){H=a;S.getDropTarget(e,t,l,r)}},this.dropTargetDelay+10)}}else{j=(new Date).getTime()-F}if(E&&j<2500&&a!=null&&!mxEvent.isShiftDown(r)&&(mxUtils.getValue(a.style,mxConstants.STYLE_SHAPE)!=mxUtils.getValue(B,mxConstants.STYLE_SHAPE)&&(mxUtils.getValue(a.style,mxConstants.STYLE_STROKECOLOR,mxConstants.NONE)!=mxConstants.NONE||mxUtils.getValue(a.style,mxConstants.STYLE_FILLCOLOR,mxConstants.NONE)!=mxConstants.NONE||mxUtils.getValue(a.style,mxConstants.STYLE_GRADIENTCOLOR,mxConstants.NONE)!=mxConstants.NONE)||mxUtils.getValue(B,mxConstants.STYLE_SHAPE)=="image"||j>1500||e.model.isEdge(a.cell))&&j>this.dropTargetDelay&&!this.isDropStyleTargetIgnored(a)&&(e.model.isVertex(a.cell)&&C!=null||e.model.isEdge(a.cell)&&e.model.isEdge(b[0]))){P=a;var s=e.model.isEdge(a.cell)?e.view.getPoint(a):new mxPoint(a.getCenterX(),a.getCenterY());s=new mxRectangle(s.x-this.refreshTarget.width/2,s.y-this.refreshTarget.height/2,this.refreshTarget.width,this.refreshTarget.height);L.style.left=Math.floor(s.x)+"px";L.style.top=Math.floor(s.y)+"px";if(N==null){e.container.appendChild(L);N=L.parentNode}O(t,l,s,L)}else if(P==null||!mxUtils.contains(P,t,l)||j>1500&&!mxEvent.isShiftDown(r)){P=null;if(N!=null){L.parentNode.removeChild(L);N=null}}else if(P!=null&&N!=null){var s=e.model.isEdge(P.cell)?e.view.getPoint(P):new mxPoint(P.getCenterX(),P.getCenterY());s=new mxRectangle(s.x-this.refreshTarget.width/2,s.y-this.refreshTarget.height/2,this.refreshTarget.width,this.refreshTarget.height);O(t,l,s,L)}if(V&&T!=null&&!mxEvent.isAltDown(r)&&G==null){o=mxRectangle.fromRectangle(T);if(e.model.isEdge(T.cell)){var m=T.absolutePoints;if(z.parentNode!=null){var h=m[0];o.add(O(t,l,new mxRectangle(h.x-this.roundDrop.width/2,h.y-this.roundDrop.height/2,this.roundDrop.width,this.roundDrop.height),z))}if(M.parentNode!=null){var d=m[m.length-1];o.add(O(t,l,new mxRectangle(d.x-this.roundDrop.width/2,d.y-this.roundDrop.height/2,this.roundDrop.width,this.roundDrop.height),M))}}else{var p=mxRectangle.fromRectangle(T);if(T.shape!=null&&T.shape.boundingBox!=null){p=mxRectangle.fromRectangle(T.shape.boundingBox)}p.grow(this.graph.tolerance);p.grow(HoverIcons.prototype.arrowSpacing);var c=this.graph.selectionCellsHandler.getHandler(T.cell);if(c!=null){p.x-=c.horizontalOffset/2;p.y-=c.verticalOffset/2;p.width+=c.horizontalOffset;p.height+=c.verticalOffset;if(c.rotationShape!=null&&c.rotationShape.node!=null&&c.rotationShape.node.style.visibility!="hidden"&&c.rotationShape.node.style.display!="none"&&c.rotationShape.boundingBox!=null){p.add(c.rotationShape.boundingBox)}}o.add(O(t,l,new mxRectangle(T.getCenterX()-this.triangleUp.width/2,p.y-this.triangleUp.height,this.triangleUp.width,this.triangleUp.height),I));o.add(O(t,l,new mxRectangle(p.x+p.width,T.getCenterY()-this.triangleRight.height/2,this.triangleRight.width,this.triangleRight.height),k));o.add(O(t,l,new mxRectangle(T.getCenterX()-this.triangleDown.width/2,p.y+p.height,this.triangleDown.width,this.triangleDown.height),R));o.add(O(t,l,new mxRectangle(p.x-this.triangleLeft.width,T.getCenterY()-this.triangleLeft.height/2,this.triangleLeft.width,this.triangleLeft.height),D))}if(o!=null){o.grow(10)}}U=mxConstants.DIRECTION_NORTH;if(G==k){U=mxConstants.DIRECTION_EAST}else if(G==R||G==M){U=mxConstants.DIRECTION_SOUTH}else if(G==D){U=mxConstants.DIRECTION_WEST}if(P!=null&&G==L){a=P}var u=(C==null||e.isCellConnectable(b[C]))&&(e.model.isEdge(i)&&C!=null||e.model.isVertex(i)&&e.isCellConnectable(i));if(T!=null&&j>=5e3||T!=a&&(o==null||!mxUtils.contains(o,t,l)||j>500&&G==null&&u)){V=false;T=j<5e3&&j>this.dropTargetDelay||e.model.isEdge(i)?a:null;if(T!=null&&u){var g=[z,M,I,k,R,D];for(var f=0;f<g.length;f++){if(g[f].parentNode!=null){g[f].parentNode.removeChild(g[f])}}if(e.model.isEdge(i)){var m=a.absolutePoints;if(m!=null){var h=m[0];var d=m[m.length-1];var y=e.tolerance;var x=new mxRectangle(t-y,l-y,2*y,2*y);z.style.left=Math.floor(h.x-this.roundDrop.width/2)+"px";z.style.top=Math.floor(h.y-this.roundDrop.height/2)+"px";M.style.left=Math.floor(d.x-this.roundDrop.width/2)+"px";M.style.top=Math.floor(d.y-this.roundDrop.height/2)+"px";if(e.model.getTerminal(i,true)==null){e.container.appendChild(z)}if(e.model.getTerminal(i,false)==null){e.container.appendChild(M)}}}else{var p=mxRectangle.fromRectangle(a);if(a.shape!=null&&a.shape.boundingBox!=null){p=mxRectangle.fromRectangle(a.shape.boundingBox)}p.grow(this.graph.tolerance);p.grow(HoverIcons.prototype.arrowSpacing);var c=this.graph.selectionCellsHandler.getHandler(a.cell);if(c!=null){p.x-=c.horizontalOffset/2;p.y-=c.verticalOffset/2;p.width+=c.horizontalOffset;p.height+=c.verticalOffset;if(c.rotationShape!=null&&c.rotationShape.node!=null&&c.rotationShape.node.style.visibility!="hidden"&&c.rotationShape.node.style.display!="none"&&c.rotationShape.boundingBox!=null){p.add(c.rotationShape.boundingBox)}}I.style.left=Math.floor(a.getCenterX()-this.triangleUp.width/2)+"px";I.style.top=Math.floor(p.y-this.triangleUp.height)+"px";k.style.left=Math.floor(p.x+p.width)+"px";k.style.top=Math.floor(a.getCenterY()-this.triangleRight.height/2)+"px";R.style.left=I.style.left;R.style.top=Math.floor(p.y+p.height)+"px";D.style.left=Math.floor(p.x-this.triangleLeft.width)+"px";D.style.top=k.style.top;if(a.style["portConstraint"]!="eastwest"){e.container.appendChild(I);e.container.appendChild(R)}e.container.appendChild(k);e.container.appendChild(D)}if(a!=null){A=e.selectionCellsHandler.getHandler(a.cell);if(A!=null&&A.setHandlesVisible!=null){A.setHandlesVisible(false)}}V=true}else{var g=[z,M,I,k,R,D];for(var f=0;f<g.length;f++){if(g[f].parentNode!=null){g[f].parentNode.removeChild(g[f])}}}}if(!V&&A!=null){A.setHandlesVisible(true)}var w=(!mxEvent.isAltDown(r)||mxEvent.isShiftDown(r))&&!(P!=null&&G==L)?mxDragSource.prototype.getDropTarget.apply(this,arguments):null;var v=e.getModel();if(w!=null){if(G!=null||!e.isSplitTarget(w,b,r)){while(w!=null&&!e.isValidDropTarget(w,b,r)&&v.isVertex(v.getParent(w))){w=v.getParent(w)}if(e.view.currentRoot==w||!e.isValidRoot(w)&&e.getModel().getChildCount(w)==0||e.isCellLocked(w)||v.isEdge(w)){w=null}}}return w});S.stopDrag=function(){mxDragSource.prototype.stopDrag.apply(this,arguments);var e=[z,M,L,I,k,R,D];for(var t=0;t<e.length;t++){if(e[t].parentNode!=null){e[t].parentNode.removeChild(e[t])}}if(T!=null&&A!=null){A.reset()}A=null;T=null;P=null;N=null;G=null};return S};Sidebar.prototype.itemClicked=function(e,t,l,r){var i=this.editorUi.editor.graph;i.container.focus();if(mxEvent.isAltDown(l)&&i.getSelectionCount()==1&&i.model.isVertex(i.getSelectionCell())){var n=null;for(var a=0;a<e.length&&n==null;a++){if(i.model.isVertex(e[a])){n=a}}if(n!=null){i.setSelectionCells(this.dropAndConnect(i.getSelectionCell(),e,mxEvent.isMetaDown(l)||mxEvent.isControlDown(l)?mxEvent.isShiftDown(l)?mxConstants.DIRECTION_WEST:mxConstants.DIRECTION_NORTH:mxEvent.isShiftDown(l)?mxConstants.DIRECTION_EAST:mxConstants.DIRECTION_SOUTH,n,l));i.scrollCellToVisible(i.getSelectionCell())}}else if(mxEvent.isShiftDown(l)&&!i.isSelectionEmpty()){this.updateShapes(e[0],i.getSelectionCells());i.scrollCellToVisible(i.getSelectionCell())}else{var o=i.getFreeInsertPoint();if(mxEvent.isAltDown(l)){var s=i.getGraphBounds();var m=i.view.translate;var h=i.view.scale;o.x=s.x/h-m.x+s.width/h+i.gridSize;o.y=s.y/h-m.y}if(i.renderConfig&&i.scrollObj&&i.renderConfig.diagramContainer){var h=i.view.scale;var m=i.view.translate;o.x=(i.scrollObj.x+i.renderConfig.diagramContainer.offsetWidth/2)/h-m.x;o.y=(i.scrollObj.y+i.renderConfig.diagramContainer.offsetHeight/2)/h-m.y}t.drop(i,l,null,o.x,o.y,true);if(this.editorUi.hoverIcons!=null&&(mxEvent.isTouchEvent(l)||mxEvent.isPenEvent(l))){this.editorUi.hoverIcons.update(i.view.getState(i.getSelectionCell()))}}};Sidebar.prototype.addClickHandler=function(t,l,r){var e=this.editorUi.editor.graph;var i=l.mouseDown;var n=l.mouseMove;var a=l.mouseUp;var o=e.tolerance;var s=null;var m=this;l.mouseDown=function(e){i.apply(this,arguments);s=new mxPoint(mxEvent.getClientX(e),mxEvent.getClientY(e));if(this.dragElement!=null){this.dragElement.style.display="none";mxUtils.setOpacity(t,50)}};l.mouseMove=function(e){if(this.dragElement!=null&&this.dragElement.style.display=="none"&&s!=null&&(Math.abs(s.x-mxEvent.getClientX(e))>o||Math.abs(s.y-mxEvent.getClientY(e))>o)){this.dragElement.style.display="";mxUtils.setOpacity(t,100)}n.apply(this,arguments)};l.mouseUp=function(e){if(!mxEvent.isPopupTrigger(e)&&this.currentGraph==null&&this.dragElement!=null&&this.dragElement.style.display=="none"){m.itemClicked(r,l,e,t)}a.apply(l,arguments);mxUtils.setOpacity(t,100);s=null;m.currentElt=t}};Sidebar.prototype.createVertexTemplateEntry=function(e,t,l,r,i,n,a,o,s,m,h){o=o!=null&&o.length>0?o:i!=null?i.toLowerCase():"";return this.addEntry(o,mxUtils.bind(this,function(){return this.createVertexTemplate(e,t,l,r,i,n,a,undefined,s,m,h)}))};Sidebar.prototype.createVertexTemplate=function(e,t,l,r,i,n,a,o,s,h,m){var d=[new mxCell(r!=null?r:"",new mxGeometry(0,0,t,l),e)];d[0].vertex=true;if(m&&m.length&&d[0]){m.forEach(function(e){if(e&&e.config){var t=e.config;var l=t.value;var r=t.width;var i=t.height;var n=t.style;var a=t.type;var o=t.x;var s=t.y;var m=new mxCell(l!=null?l:"",new mxGeometry(o||0,s||0,r,i),n);m.sceneComponent=mxUtils.clone(h,undefined,undefined,true);m.sceneComponent.sceneComponentType=a;m.sceneComponent.sceneComponentConfig=e.sceneComponentConfig;if(t.isEdge){m.setEdge(1)}else{m.setVertex(1)}d[0].insert(m)}})}return this.createVertexTemplateFromCells(d,t,l,i,n,a,o,s,h)};Sidebar.prototype.createVertexTemplateFromData=function(e,t,l,r,i,n,a,o,s){var m=mxUtils.parseXml(Graph.decompress(e));var h=new mxCodec(m);var d=new mxGraphModel;h.decode(m.documentElement,d);var p=this.graph.cloneCells(d.root.getChildAt(0).children);return this.createVertexTemplateFromCells(p,t,l,r,i,n,a,o,s)};Sidebar.prototype.createVertexTemplateFromCells=function(e,t,l,r,i,n,a,o,s){return this.createItem(e,r,i,n,t,l,a,o,s)};Sidebar.prototype.createEdgeTemplateEntry=function(e,t,l,r,i,n,a,o,s,m){a=a!=null&&a.length>0?a:i.toLowerCase();return this.addEntry(a,mxUtils.bind(this,function(){return this.createEdgeTemplate(e,t,l,r,i,n,o,s,m)}))};Sidebar.prototype.createEdgeTemplate=function(e,t,l,r,i,n,a,o,s){var m=new mxCell(r!=null?r:"",new mxGeometry(0,0,t,l),e);m.geometry.setTerminalPoint(new mxPoint(0,l),true);m.geometry.setTerminalPoint(new mxPoint(t,0),false);m.geometry.relative=true;m.edge=true;return this.createEdgeTemplateFromCells([m],t,l,i,n,a,o,s)};Sidebar.prototype.createEdgeTemplateFromCells=function(e,t,l,r,i,n,a,o){return this.createItem(e,r,i,true,t,l,n,a,o)};Sidebar.prototype.addPaletteFunctions=function(e,t,l,r){this.addPalette(e,t,l,mxUtils.bind(this,function(e){for(var t=0;t<r.length;t++){e.appendChild(r[t](e))}}))};Sidebar.prototype.addPalette=function(e,t,l,r){var i=this.createTitle(t);this.container.appendChild(i);var n=document.createElement("div");n.className="geSidebar";if(mxClient.IS_POINTER){n.style.touchAction="none"}if(l){r(n);r=null}else{n.style.display="none"}this.addFoldingHandler(i,n,r);var a=document.createElement("div");a.appendChild(n);this.container.appendChild(a);if(e!=null){this.palettes[e]=[i,a]}return n};Sidebar.prototype.addFoldingHandler=function(l,r,i){var n=false;if(!mxClient.IS_IE||document.documentMode>=8){l.style.backgroundImage=r.style.display=="none"?"url('"+this.collapsedImage+"')":"url('"+this.expandedImage+"')"}l.style.backgroundRepeat="no-repeat";l.style.backgroundPosition="0% 50%";mxEvent.addListener(l,"click",mxUtils.bind(this,function(e){if(r.style.display=="none"){if(!n){n=true;if(i!=null){l.style.cursor="wait";var t=l.innerHTML;l.innerHTML=mxResources.get("loading")+"...";window.setTimeout(function(){r.style.display="block";l.style.cursor="";l.innerHTML=t;var e=mxClient.NO_FO;mxClient.NO_FO=Editor.prototype.originalNoForeignObject;i(r,l);mxClient.NO_FO=e},mxClient.IS_FF?20:0)}else{r.style.display="block"}}else{r.style.display="block"}l.style.backgroundImage="url('"+this.expandedImage+"')"}else{l.style.backgroundImage="url('"+this.collapsedImage+"')";r.style.display="none"}mxEvent.consume(e)}));if(!mxClient.IS_QUIRKS){mxEvent.addListener(l,mxClient.IS_POINTER?"pointerdown":"mousedown",mxUtils.bind(this,function(e){e.preventDefault()}))}};Sidebar.prototype.removePalette=function(e){var t=this.palettes[e];if(t!=null){this.palettes[e]=null;for(var l=0;l<t.length;l++){this.container.removeChild(t[l])}return true}return false};Sidebar.prototype.addImagePalette=function(e,t,n,a,l,r,i){var o=r!=null;var s=[];for(var m=0;m<l.length;m++){mxUtils.bind(this,function(e,t,l){if(l==null){var r=e.lastIndexOf("/");var i=e.lastIndexOf(".");l=e.substring(r>=0?r+1:0,i>=0?i:e.length).replace(/[-_]/g," ")}s.push(this.createVertexTemplateEntry("image;html=1;labelBackgroundColor=#ffffff;image="+n+e+a,this.defaultImageWidth,this.defaultImageHeight,"",t,t!=null,null,this.filterTags(l)))})(l[m],r!=null?r[m]:null,i!=null?i[l[m]]:null)}this.addPaletteFunctions(e,t,false,s)};Sidebar.prototype.getTagsForStencil=function(e,t,l){var r=e.split(".");for(var i=1;i<r.length;i++){r[i]=r[i].replace(/_/g," ")}r.push(t.replace(/_/g," "));if(l!=null){r.push(l)}return r.slice(1,r.length)};Sidebar.prototype.addStencilPalette=function(e,t,l,o,s,r,m,h,i){m=m!=null?m:1;if(this.addStencilsToIndex){var d=[];if(i!=null){for(var n=0;n<i.length;n++){d.push(i[n])}}mxStencilRegistry.loadStencilSet(l,mxUtils.bind(this,function(e,t,l,r,i){if(s==null||mxUtils.indexOf(s,t)<0){var n=this.getTagsForStencil(e,t);var a=h!=null?h[t]:null;if(a!=null){n.push(a)}d.push(this.createVertexTemplateEntry("shape="+e+t.toLowerCase()+o,Math.round(r*m),Math.round(i*m),"",t.replace(/_/g," "),null,null,this.filterTags(n.join(" "))))}}),true,true);this.addPaletteFunctions(e,t,false,d)}else{this.addPalette(e,t,false,mxUtils.bind(this,function(n){if(o==null){o=""}if(r!=null){r.call(this,n)}if(i!=null){for(var e=0;e<i.length;e++){i[e](n)}}mxStencilRegistry.loadStencilSet(l,mxUtils.bind(this,function(e,t,l,r,i){if(s==null||mxUtils.indexOf(s,t)<0){n.appendChild(this.createVertexTemplate("shape="+e+t.toLowerCase()+o,Math.round(r*m),Math.round(i*m),"",t.replace(/_/g," "),true))}}),true)}))}};Sidebar.prototype.destroy=function(){if(this.graph!=null){if(this.graph.container!=null&&this.graph.container.parentNode!=null){this.graph.container.parentNode.removeChild(this.graph.container)}this.graph.destroy();this.graph=null}if(this.pointerUpHandler!=null){mxEvent.removeListener(document,mxClient.IS_POINTER?"pointerup":"mouseup",this.pointerUpHandler);this.pointerUpHandler=null}if(this.pointerDownHandler!=null){mxEvent.removeListener(document,mxClient.IS_POINTER?"pointerdown":"mousedown",this.pointerDownHandler);this.pointerDownHandler=null}if(this.pointerMoveHandler!=null){mxEvent.removeListener(document,mxClient.IS_POINTER?"pointermove":"mousemove",this.pointerMoveHandler);this.pointerMoveHandler=null}if(this.pointerOutHandler!=null){mxEvent.removeListener(document,mxClient.IS_POINTER?"pointerout":"mouseout",this.pointerOutHandler);this.pointerOutHandler=null}};