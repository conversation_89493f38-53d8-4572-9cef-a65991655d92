function ChangePageSetup(t,e,i,n,o){this.ui=t,this.color=e,this.previousColor=e,this.image=i,this.previousImage=i,this.format=n,this.previousFormat=n,this.pageScale=o,this.previousPageScale=o,this.ignoreColor=!1,this.ignoreImage=!1}EditorUi=function(t,e,i,n){mxEventSource.call(this),this.destroyFunctions=[],this.editor=t||new Editor,this.container=e||document.body,this.renderConfig=n;var C=this.editor.graph,e=(C.lightbox=i,C.renderConfig=n,C.useCssTransforms&&(this.lazyZoomDelay=0),this.renderConfig&&this.renderConfig.customRenderValueFns);if(e&&(window.customRenderValueFns=e),mxClient.IS_SVG?mxPopupMenu.prototype.submenuImage="data:image/gif;base64,R0lGODlhCQAJAIAAAP///zMzMyH5BAEAAAAALAAAAAAJAAkAAAIPhI8WebHsHopSOVgb26AAADs=":(new Image).src=mxPopupMenu.prototype.submenuImage,mxClient.IS_SVG||null==mxConnectionHandler.prototype.connectImage||((new Image).src=mxConnectionHandler.prototype.connectImage.src),this.editor.chromeless&&!this.editor.editable&&(this.footerHeight=0,C.isEnabled=function(){return!1},C.panningHandler.isForcePanningEvent=function(t){return!mxEvent.isPopupTrigger(t.getEvent())}),this.actions=new Actions(this),this.menus=this.createMenus(),!C.standalone){this.createDivs(),this.createUi(),this.refresh();var o=mxUtils.bind(this,function(t){return null==t&&(t=window.event),C.isEditing()||null!=t&&this.isSelectionAllowed(t)}),r=(this.container==document.body&&(this.menubarContainer.onselectstart=o,this.menubarContainer.onmousedown=o,this.toolbarContainer.onselectstart=o,this.toolbarContainer.onmousedown=o,this.diagramContainer.onselectstart=o,this.diagramContainer.onmousedown=o,this.sidebarContainer.onselectstart=o,this.sidebarContainer.onmousedown=o,this.formatContainer.onselectstart=o,this.formatContainer.onmousedown=o,this.footerContainer.onselectstart=o,this.footerContainer.onmousedown=o,null!=this.tabContainer)&&(this.tabContainer.onselectstart=o),!this.editor.chromeless||this.editor.editable?(i=function(t){if(null!=t){var e=mxEvent.getSource(t);if("A"==e.nodeName)for(;null!=e;){if("geHint"==e.className)return!0;e=e.parentNode}}return o(t)},mxClient.IS_IE&&(void 0===document.documentMode||document.documentMode<9)?mxEvent.addListener(this.diagramContainer,"contextmenu",i):this.diagramContainer.oncontextmenu=i):C.panningHandler.usePopupTrigger=!1,C.init(this.diagramContainer),mxClient.IS_SVG&&null!=C.view.getDrawPane()&&null!=(e=C.view.getDrawPane().ownerSVGElement)&&(e.style.position="absolute"),this.hoverIcons=this.createHoverIcons(),mxEvent.addListener(this.diagramContainer,"mousemove",mxUtils.bind(this,function(t){var e=mxUtils.getOffset(this.diagramContainer);0<mxEvent.getClientX(t)-e.x-this.diagramContainer.clientWidth||0<mxEvent.getClientY(t)-e.y-this.diagramContainer.clientHeight?this.diagramContainer.setAttribute("title",mxResources.get("panTooltip")):this.diagramContainer.removeAttribute("title")})),!1),s=this.hoverIcons.isResetEvent,l=(this.hoverIcons.isResetEvent=function(t,e){return r||s.apply(this,arguments)},this.keydownHandler=mxUtils.bind(this,function(t){mxUtils&&mxEvent&&(32!=t.which||C.isEditing()?mxEvent.isConsumed(t)||27!=t.keyCode||this.hideDialog(null,!0):(r=!0,this.hoverIcons.reset(),C.container.style.cursor="move",C.isEditing()||mxEvent.getSource(t)!=C.container||mxEvent.consume(t)))}),mxEvent.addListener(document,"keydown",this.keydownHandler),this.keyupHandler=mxUtils.bind(this,function(t){C.container.style.cursor="",r=!1}),mxEvent.addListener(document,"keyup",this.keyupHandler),C.panningHandler.isForcePanningEvent),a=(C.panningHandler.isForcePanningEvent=function(t){return l.apply(this,arguments)||r||mxEvent.isMouseEvent(t.getEvent())&&(this.usePopupTrigger||!mxEvent.isPopupTrigger(t.getEvent()))&&(!mxEvent.isControlDown(t.getEvent())&&mxEvent.isRightMouseButton(t.getEvent())||mxEvent.isMiddleMouseButton(t.getEvent()))},C.cellEditor.isStopEditingEvent),d=(C.cellEditor.isStopEditingEvent=function(t){return a.apply(this,arguments)||13==t.keyCode&&(!mxClient.IS_SF&&mxEvent.isControlDown(t)||mxClient.IS_MAC&&mxEvent.isMetaDown(t)||mxClient.IS_SF&&mxEvent.isShiftDown(t))},C.isZoomWheelEvent),h=!(C.isZoomWheelEvent=function(){return r||d.apply(this,arguments)}),c=null,u=null,g=null,m=mxUtils.bind(this,function(){if(null!=this.toolbar&&h!=C.cellEditor.isContentEditing()){for(var t=this.toolbar.container.firstChild,e=[];null!=t;){var i=t.nextSibling;mxUtils.indexOf(this.toolbar.staticElements,t)<0&&(t.parentNode.removeChild(t),e.push(t)),t=i}var n=this.toolbar.fontMenu,o=this.toolbar.sizeMenu;if(null==g)this.toolbar.createTextToolbar();else{for(var r=0;r<g.length;r++)this.toolbar.container.appendChild(g[r]);this.toolbar.fontMenu=c,this.toolbar.sizeMenu=u}h=C.cellEditor.isContentEditing(),c=n,u=o,g=e}}),p=this,f=C.cellEditor.startEditing,x=(C.cellEditor.startEditing=function(){var n,t;f.apply(this,arguments),m(),C.cellEditor.isContentEditing()&&(n=!1,t=function(){n||(n=!0,window.setTimeout(function(){for(var t,e,i=C.getSelectedElement();null!=i&&i.nodeType!=mxConstants.NODETYPE_ELEMENT;)i=i.parentNode;null!=i&&null!=(t=mxUtils.getCurrentStyle(i))&&null!=p.toolbar&&("'"==(e="'"==(e=t.fontFamily).charAt(0)?e.substring(1):e).charAt(e.length-1)&&(e=e.substring(0,e.length-1)),p.toolbar.setFontName(e),p.toolbar.setFontSize(parseInt(t.fontSize))),n=!1},0))},mxEvent.addListener(C.cellEditor.textarea,"input",t),mxEvent.addListener(C.cellEditor.textarea,"touchend",t),mxEvent.addListener(C.cellEditor.textarea,"mouseup",t),mxEvent.addListener(C.cellEditor.textarea,"keyup",t),t())},C.cellEditor.stopEditing);if(C.cellEditor.stopEditing=function(t,e){x.apply(this,arguments),m()},C.container.setAttribute("tabindex","0"),C.container.style.cursor="default",window.self===window.top&&null!=C.container.parentNode)try{C.container.focus()}catch(t){}for(var y=C.fireMouseEvent,E=(C.fireMouseEvent=function(t,e,i){t==mxEvent.MOUSE_DOWN&&this.container.focus(),y.apply(this,arguments)},C.popupMenuHandler.autoExpand=!0,null!=this.menus&&(C.popupMenuHandler.factoryMethod=mxUtils.bind(this,function(t,e,i){this.menus.createPopupMenu(t,e,i)})),mxEvent.addGestureListeners(document,mxUtils.bind(this,function(t){C.popupMenuHandler.hideMenu()})),this.keyHandler=this.createKeyHandler(t),this.getKeyHandler=function(){return keyHandler},["rounded","shadow","glass","dashed","dashPattern","comic","labelBackgroundColor"]),S=["shape","edgeStyle","curved","rounded","elbow","comic","jumpStyle","jumpSize"],b=(this.setDefaultStyle=function(t){try{var e=C.view.getState(t);if(null!=e){var i=t.clone(),n=(i.style="",C.getCellStyle(i)),o=[],r=[];for(d in e.style)n[d]!=e.style[d]&&(o.push(e.style[d]),r.push(d));for(var s=C.getModel().getStyle(e.cell),l=null!=s?s.split(";"):[],a=0;a<l.length;a++){var d,h,c=l[a],u=c.indexOf("=");0<=u&&(d=c.substring(0,u),h=c.substring(u+1),null!=n[d])&&"none"==h&&(o.push(h),r.push(d))}C.getModel().isEdge(e.cell)?C.currentEdgeStyle={}:C.currentVertexStyle={},this.fireEvent(new mxEventObject("styleChanged","keys",r,"values",o,"cells",[e.cell]))}}catch(t){this.handleError(t)}},this.clearDefaultStyle=function(){C.currentEdgeStyle=mxUtils.clone(C.defaultEdgeStyle),C.currentVertexStyle=mxUtils.clone(C.defaultVertexStyle),this.fireEvent(new mxEventObject("styleChanged","keys",[],"values",[],"cells",[]))},["fontFamily","fontSize","fontColor"]),v=["edgeStyle","startArrow","startFill","startSize","endArrow","endFill","endSize"],w=[["startArrow","startFill","startSize","sourcePerimeterSpacing","endArrow","endFill","endSize","targetPerimeterSpacing"],["strokeColor","strokeWidth"],["fillColor","gradientColor"],b,["opacity"],["align"],["html"]],U=0;U<w.length;U++)for(var M=0;M<w[U].length;M++)E.push(w[U][M]);for(U=0;U<S.length;U++)mxUtils.indexOf(E,S[U])<0&&E.push(S[U]);function T(t,e){var i=C.getModel();i.beginUpdate();try{for(var n=0;n<t.length;n++){var o=t[n];if(e)l=["fontSize","fontFamily","fontColor"];else for(var r=i.getStyle(o),s=null!=r?r.split(";"):[],l=E.slice(),a=0;a<s.length;a++){var d=s[a],h=d.indexOf("=");if(0<=h){var c=d.substring(0,h),u=mxUtils.indexOf(l,c);0<=u&&l.splice(u,1);for(var g=0;g<w.length;g++){var m=w[g];if(0<=mxUtils.indexOf(m,c))for(var p=0;p<m.length;p++){var f=mxUtils.indexOf(l,m[p]);0<=f&&l.splice(f,1)}}}}for(var x=i.isEdge(o),y=x?C.currentEdgeStyle:C.currentVertexStyle,b=i.getStyle(o),a=0;a<l.length;a++){var v=y[c=l[a]];null==v||"shape"==c&&!x||(!x||mxUtils.indexOf(S,c)<0)&&(b=mxUtils.setStyle(b,c,v))}i.setStyle(o,b)}}finally{i.endUpdate()}}C.addListener("cellsInserted",function(t,e){T(e.getProperty("cells"))}),C.addListener("textInserted",function(t,e){T(e.getProperty("cells"),!0)}),C.connectionHandler.addListener(mxEvent.CONNECT,function(t,e){var i=[e.getProperty("cell")];e.getProperty("terminalInserted")&&i.push(e.getProperty("terminal")),T(i)}),this.addListener("styleChanged",mxUtils.bind(this,function(t,e){var i=e.getProperty("cells"),n=!1,o=!1;if(0<i.length)for(var r=0;r<i.length&&(n=C.getModel().isVertex(i[r])||n,!(o=C.getModel().isEdge(i[r])||o)||!n);r++);else o=n=!0;for(var s=e.getProperty("keys"),l=e.getProperty("values"),r=0;r<s.length;r++){var a=0<=mxUtils.indexOf(b,s[r]);("strokeColor"!=s[r]||null!=l[r]&&"none"!=l[r])&&(0<=mxUtils.indexOf(S,s[r])?o||0<=mxUtils.indexOf(v,s[r])?null==l[r]?delete C.currentEdgeStyle[s[r]]:C.currentEdgeStyle[s[r]]=l[r]:n&&0<=mxUtils.indexOf(E,s[r])&&(null==l[r]?delete C.currentVertexStyle[s[r]]:C.currentVertexStyle[s[r]]=l[r]):0<=mxUtils.indexOf(E,s[r])&&((n||a)&&(null==l[r]?delete C.currentVertexStyle[s[r]]:C.currentVertexStyle[s[r]]=l[r]),o||a||0<=mxUtils.indexOf(v,s[r]))&&(null==l[r]?delete C.currentEdgeStyle[s[r]]:C.currentEdgeStyle[s[r]]=l[r]))}null!=this.toolbar&&(this.toolbar.setFontName(C.currentVertexStyle.fontFamily||Menus.prototype.defaultFont),this.toolbar.setFontSize(C.currentVertexStyle.fontSize||Menus.prototype.defaultFontSize),null!=this.toolbar.edgeStyleMenu&&(e=this.toolbar.edgeStyleMenu.getElementsByTagName("div")[0],"orthogonalEdgeStyle"==C.currentEdgeStyle.edgeStyle&&"1"==C.currentEdgeStyle.curved?e.className="geSprite geSprite-curved":"straight"==C.currentEdgeStyle.edgeStyle||"none"==C.currentEdgeStyle.edgeStyle||null==C.currentEdgeStyle.edgeStyle?e.className="geSprite geSprite-straight":"entityRelationEdgeStyle"==C.currentEdgeStyle.edgeStyle?e.className="geSprite geSprite-entity":"elbowEdgeStyle"==C.currentEdgeStyle.edgeStyle?e.className="geSprite geSprite-"+("vertical"==C.currentEdgeStyle.elbow?"verticalelbow":"horizontalelbow"):"isometricEdgeStyle"==C.currentEdgeStyle.edgeStyle?e.className="geSprite geSprite-"+("vertical"==C.currentEdgeStyle.elbow?"verticalisometric":"horizontalisometric"):e.className="geSprite geSprite-orthogonal"),null!=this.toolbar.edgeShapeMenu&&(e=this.toolbar.edgeShapeMenu.getElementsByTagName("div")[0],"link"==C.currentEdgeStyle.shape?e.className="geSprite geSprite-linkedge":"flexArrow"==C.currentEdgeStyle.shape?e.className="geSprite geSprite-arrow":"arrow"==C.currentEdgeStyle.shape?e.className="geSprite geSprite-simplearrow":e.className="geSprite geSprite-connection"),null!=this.toolbar.lineStartMenu&&(this.toolbar.lineStartMenu.getElementsByTagName("div")[0].className=this.getCssClassForMarker("start",C.currentEdgeStyle.shape,C.currentEdgeStyle[mxConstants.STYLE_STARTARROW],mxUtils.getValue(C.currentEdgeStyle,"startFill","1"))),null!=this.toolbar.lineEndMenu)&&(this.toolbar.lineEndMenu.getElementsByTagName("div")[0].className=this.getCssClassForMarker("end",C.currentEdgeStyle.shape,C.currentEdgeStyle[mxConstants.STYLE_ENDARROW],mxUtils.getValue(C.currentEdgeStyle,"endFill","1")))})),null!=this.toolbar&&(i=mxUtils.bind(this,function(){var t=C.currentVertexStyle.fontFamily||"Helvetica",e=String(C.currentVertexStyle.fontSize||"12"),i=C.getView().getState(C.getSelectionCell());null!=i&&(t=i.style[mxConstants.STYLE_FONTFAMILY]||t,e=i.style[mxConstants.STYLE_FONTSIZE]||e,10<t.length)&&(t=t.substring(0,8)+"..."),this.toolbar.setFontName(t),this.toolbar.setFontSize(e)}),C.getSelectionModel().addListener(mxEvent.CHANGE,i),C.getModel().addListener(mxEvent.CHANGE,i)),C.addListener(mxEvent.CELLS_ADDED,function(t,e){var i=e.getProperty("cells"),e=e.getProperty("parent");C.getModel().isLayer(e)&&!C.isCellVisible(e)&&null!=i&&0<i.length&&C.getModel().setVisible(e,!0)}),this.gestureHandler=mxUtils.bind(this,function(t){null!=this.currentMenu&&mxEvent.getSource(t)!=this.currentMenu.div&&this.hideCurrentMenu()}),mxEvent.addGestureListeners(document,this.gestureHandler),this.resizeHandler=mxUtils.bind(this,function(){mxUtils&&window.setTimeout(mxUtils.bind(this,function(){null!=this.editor.graph&&this.refresh()}),0)}),mxEvent.addListener(window,"resize",this.resizeHandler),this.orientationChangeHandler=mxUtils.bind(this,function(){this.refresh()}),mxEvent.addListener(window,"orientationchange",this.orientationChangeHandler),mxClient.IS_IOS&&!window.navigator.standalone&&(this.scrollHandler=mxUtils.bind(this,function(){window.scrollTo(0,0)}),mxEvent.addListener(window,"scroll",this.scrollHandler)),this.editor.addListener("resetGraphView",mxUtils.bind(this,function(){this.resetScrollbars()})),this.addListener("gridEnabledChanged",mxUtils.bind(this,function(){C.view.validateBackground()})),this.addListener("backgroundColorChanged",mxUtils.bind(this,function(){C.view.validateBackground()})),C.addListener("gridSizeChanged",mxUtils.bind(this,function(){C.isGridEnabled()&&C.view.validateBackground()})),this.editor.resetGraph()}this.init(n&&n.customRenderLayoutConfig),C.standalone||this.open()},mxUtils.extend(EditorUi,mxEventSource),EditorUi.compactUi=!0,EditorUi.prototype.splitSize=mxClient.IS_TOUCH||mxClient.IS_POINTER?12:8,EditorUi.prototype.menubarHeight=30,EditorUi.prototype.formatEnabled=!0,EditorUi.prototype.formatWidth=240,EditorUi.prototype.toolbarHeight=38,EditorUi.prototype.footerHeight=28,EditorUi.prototype.sidebarFooterHeight=34,EditorUi.prototype.hsplitPosition=screen.width<=640?118:"large"!=urlParams["sidebar-entries"]?212:240,EditorUi.prototype.allowAnimation=!0,EditorUi.prototype.lightboxMaxFitScale=2,EditorUi.prototype.lightboxVerticalDivider=4,EditorUi.prototype.hsplitClickEnabled=!1,EditorUi.prototype.init=function(t){var e,i,n=this.editor.graph;n.standalone||(mxEvent.addListener(n.container,"scroll",mxUtils.bind(this,function(){n.tooltipHandler.hide(),null!=n.connectionHandler&&null!=n.connectionHandler.constraintHandler&&n.connectionHandler.constraintHandler.reset()})),n.addListener(mxEvent.ESCAPE,mxUtils.bind(this,function(){n.tooltipHandler.hide()})),mxEvent.addListener(n.container,"keydown",mxUtils.bind(this,function(t){this.onKeyDown(t)})),mxEvent.addListener(n.container,"keypress",mxUtils.bind(this,function(t){this.onKeyPress(t)})),this.addUndoListener(),this.addBeforeUnloadListener(),n.getSelectionModel().addListener(mxEvent.CHANGE,mxUtils.bind(this,function(){this.updateActionStates()})),n.getModel().addListener(mxEvent.CHANGE,mxUtils.bind(this,function(){this.updateActionStates()})),e=n.setDefaultParent,(i=this).editor.graph.setDefaultParent=function(){e.apply(this,arguments),i.updateActionStates()},n.editLink=i.actions.get("editLink").funct,this.updateActionStates(),this.initClipboard(),this.initCanvas(t),null!=this.format&&this.format.init())},EditorUi.prototype.onKeyDown=function(t){var e=this.editor.graph;9==t.which&&e.isEnabled()&&!mxEvent.isAltDown(t)&&(e.isEditing()?e.stopEditing(!1):e.selectCell(!mxEvent.isShiftDown(t)),mxEvent.consume(t))},EditorUi.prototype.onKeyPress=function(t){var e=this.editor.graph;!this.isImmediateEditingEvent(t)||e.isEditing()||e.isSelectionEmpty()||0===t.which||27===t.which||mxEvent.isAltDown(t)||mxEvent.isControlDown(t)||mxEvent.isMetaDown(t)||(e.escape(),e.startEditing(),mxClient.IS_FF&&((e=e.cellEditor).textarea.innerHTML=String.fromCharCode(t.which),(t=document.createRange()).selectNodeContents(e.textarea),t.collapse(!1),(e=window.getSelection()).removeAllRanges(),e.addRange(t)))},EditorUi.prototype.isImmediateEditingEvent=function(t){return!0},EditorUi.prototype.getCssClassForMarker=function(t,e,i,n){return"flexArrow"==e?null!=i&&i!=mxConstants.NONE?"geSprite geSprite-"+t+"blocktrans":"geSprite geSprite-noarrow":i==mxConstants.ARROW_CLASSIC?"1"==n?"geSprite geSprite-"+t+"classic":"geSprite geSprite-"+t+"classictrans":i==mxConstants.ARROW_CLASSIC_THIN?"1"==n?"geSprite geSprite-"+t+"classicthin":"geSprite geSprite-"+t+"classicthintrans":i==mxConstants.ARROW_OPEN?"geSprite geSprite-"+t+"open":i==mxConstants.ARROW_OPEN_THIN?"geSprite geSprite-"+t+"openthin":i==mxConstants.ARROW_BLOCK?"1"==n?"geSprite geSprite-"+t+"block":"geSprite geSprite-"+t+"blocktrans":i==mxConstants.ARROW_BLOCK_THIN?"1"==n?"geSprite geSprite-"+t+"blockthin":"geSprite geSprite-"+t+"blockthintrans":i==mxConstants.ARROW_OVAL?"1"==n?"geSprite geSprite-"+t+"oval":"geSprite geSprite-"+t+"ovaltrans":i==mxConstants.ARROW_DIAMOND?"1"==n?"geSprite geSprite-"+t+"diamond":"geSprite geSprite-"+t+"diamondtrans":i==mxConstants.ARROW_DIAMOND_THIN?"1"==n?"geSprite geSprite-"+t+"thindiamond":"geSprite geSprite-"+t+"thindiamondtrans":"openAsync"==i?"geSprite geSprite-"+t+"openasync":"dash"==i?"geSprite geSprite-"+t+"dash":"cross"==i?"geSprite geSprite-"+t+"cross":"async"==i?"1"==n?"geSprite geSprite-"+t+"async":"geSprite geSprite-"+t+"asynctrans":"circle"==i||"circlePlus"==i?"1"==n||"circle"==i?"geSprite geSprite-"+t+"circle":"geSprite geSprite-"+t+"circleplus":"ERone"==i?"geSprite geSprite-"+t+"erone":"ERmandOne"==i?"geSprite geSprite-"+t+"eronetoone":"ERmany"==i?"geSprite geSprite-"+t+"ermany":"ERoneToMany"==i?"geSprite geSprite-"+t+"eronetomany":"ERzeroToOne"==i?"geSprite geSprite-"+t+"eroneopt":"ERzeroToMany"==i?"geSprite geSprite-"+t+"ermanyopt":"geSprite geSprite-noarrow"},EditorUi.prototype.createMenus=function(){return null},EditorUi.prototype.updatePasteActionStates=function(){var t=this.editor.graph,e=this.actions.get("paste"),i=this.actions.get("pasteHere");e.setEnabled(this.editor.graph.cellEditor.isContentEditing()||!mxClipboard.isEmpty()&&t.isEnabled()&&!t.isCellLocked(t.getDefaultParent())),i.setEnabled(e.isEnabled())},EditorUi.prototype.initClipboard=function(){var a=this,e=mxClipboard.cut,i=(mxClipboard.cut=function(t){t.cellEditor.isContentEditing()?document.execCommand("cut",!1,null):e.apply(this,arguments),a.updatePasteActionStates()},mxClipboard.copy,mxClipboard.copy=function(t){var e=null;if(t.cellEditor.isContentEditing())document.execCommand("copy",!1,null);else{e=e||t.getSelectionCells();for(var e=t.getExportableCells(t.model.getTopmostCells(e)),i=new Object,n=t.createCellLookup(e),o=t.cloneCells(e,null,i),r=new mxGraphModel,s=r.getChildAt(r.getRoot(),0),l=0;l<o.length;l++)r.add(s,o[l]);t.updateCustomLinks(t.createCellMapping(i,n),o),mxClipboard.insertCount=1,mxClipboard.setCells(o)}return a.updatePasteActionStates(),e},mxClipboard.paste),t=(mxClipboard.paste=function(t){var e=null;return t.cellEditor.isContentEditing()?document.execCommand("paste",!1,null):e=i.apply(this,arguments),a.updatePasteActionStates(),e},this.editor.graph.cellEditor.startEditing),n=(this.editor.graph.cellEditor.startEditing=function(){t.apply(this,arguments),a.updatePasteActionStates()},this.editor.graph.cellEditor.stopEditing);this.editor.graph.cellEditor.stopEditing=function(t,e){n.apply(this,arguments),a.updatePasteActionStates()},this.updatePasteActionStates()},EditorUi.prototype.lazyZoomDelay=20,EditorUi.prototype.wheelZoomDelay=400,EditorUi.prototype.buttonZoomDelay=600,EditorUi.prototype.initCanvas=function(t){var i,s,g=this.editor.graph,r=(g.timerAutoScroll=!0,g.getPagePadding=function(){return t&&t.pad?new mxPoint(t.pad.x||0,t.pad.y||0):new mxPoint(Math.max(0,Math.round((g.container.offsetWidth-34)/g.view.scale)),Math.max(0,Math.round((g.container.offsetHeight-34)/g.view.scale)))},g.view.getBackgroundPageBounds=function(){var t=this.graph.getPageLayout(),e=this.graph.getPageSize();return new mxRectangle(this.scale*(this.translate.x+t.x*e.width),this.scale*(this.translate.y+t.y*e.height),this.scale*t.width*e.width,this.scale*t.height*e.height)},g.getPreferredPageSize=function(t,e,i){var n=this.getPageLayout(),o=this.getPageSize();return new mxRectangle(0,0,n.width*o.width,n.height*o.height)},null),l=this;if(this.editor.isChromelessView()){r=mxUtils.bind(this,function(t,e,i,n){var o,r,s,l,a,d,h,c,u;null==g.container||g.isViewer()||(i=null!=i?i:0,n=null!=n?n:0,o=g.pageVisible?g.view.getBackgroundPageBounds():g.getGraphBounds(),u=mxUtils.hasScrollbars(g.container),l=g.view.translate,r=g.view.scale,(s=mxRectangle.fromRectangle(o)).x=s.x/r-l.x,s.y=s.y/r-l.y,s.width/=r,s.height/=r,l=g.container.scrollTop,a=g.container.scrollLeft,h=mxClient.IS_QUIRKS||8<=document.documentMode?20:14,8!=document.documentMode&&9!=document.documentMode||(h+=3),d=g.container.offsetWidth-h,h=g.container.offsetHeight-h,e=(d-(t=t?Math.max(.3,Math.min(e||1,d/s.width)):r)*s.width)/2/t,c=0==this.lightboxVerticalDivider?0:(h-t*s.height)/this.lightboxVerticalDivider/t,u&&(e=Math.max(e,0),c=Math.max(c,0)),u||o.width<d||o.height<h?(g.view.scaleAndTranslate(t,Math.floor(e-s.x),Math.floor(c-s.y)),g.container.scrollTop=l*t/r,g.container.scrollLeft=a*t/r):0==i&&0==n||(u=g.view.translate,g.view.setTranslate(Math.floor(u.x+i/r),Math.floor(u.y+n/r))))}),this.chromelessResize=r,this.chromelessWindowResize=mxUtils.bind(this,function(){this.chromelessResize(!1)});var e=mxUtils.bind(this,function(){this.chromelessWindowResize(!1)});if(mxEvent.addListener(window,"resize",e),this.destroyFunctions.push(function(){mxEvent.removeListener(window,"resize",e)}),this.editor.addListener("resetGraphView",mxUtils.bind(this,function(){this.chromelessResize(!0)})),this.actions.get("zoomIn").funct=mxUtils.bind(this,function(t){g.zoomIn(),this.chromelessResize(!1)}),this.actions.get("zoomOut").funct=mxUtils.bind(this,function(t){g.zoomOut(),this.chromelessResize(!1)}),"0"!=urlParams.toolbar){var n,o,a=JSON.parse(decodeURIComponent(urlParams["toolbar-config"]||"{}")),d=(this.chromelessToolbar=document.createElement("div"),this.chromelessToolbar.style.position="fixed",this.chromelessToolbar.style.overflow="hidden",this.chromelessToolbar.style.boxSizing="border-box",this.chromelessToolbar.style.whiteSpace="nowrap",this.chromelessToolbar.style.backgroundColor="#000000",this.chromelessToolbar.style.padding="10px 10px 8px 10px",this.chromelessToolbar.style.left=g.isViewer()?"0":"50%",mxClient.IS_VML||(mxUtils.setPrefixedStyle(this.chromelessToolbar.style,"borderRadius","20px"),mxUtils.setPrefixedStyle(this.chromelessToolbar.style,"transition","opacity 600ms ease-in-out")),mxUtils.bind(this,function(){var t=mxUtils.getCurrentStyle(g.container);g.isViewer()?this.chromelessToolbar.style.top="0":this.chromelessToolbar.style.bottom=(null!=t?parseInt(t["margin-bottom"]||0):0)+(null!=this.tabContainer?20+parseInt(this.tabContainer.style.height):20)+"px"})),h=(this.editor.addListener("resetGraphView",d),d(),mxUtils.bind(this,function(t,e,i){0;var n=document.createElement("span"),t=(n.style.paddingLeft="8px",n.style.paddingRight="8px",n.style.cursor="pointer",mxEvent.addListener(n,"click",t),null!=i&&n.setAttribute("title",i),document.createElement("img"));return t.setAttribute("border","0"),t.setAttribute("src",e),n.appendChild(t),this.chromelessToolbar.appendChild(n),n})),c=(null!=a.backBtn&&h(mxUtils.bind(this,function(t){window.location.href=a.backBtn.url,mxEvent.consume(t)}),Editor.backLargeImage,mxResources.get("back",null,"Back")),h(mxUtils.bind(this,function(t){this.actions.get("previousPage").funct(),mxEvent.consume(t)}),Editor.previousLargeImage,mxResources.get("previousPage"))),u=document.createElement("div"),m=(u.style.display="inline-block",u.style.verticalAlign="top",u.style.fontFamily="Helvetica,Arial",u.style.marginTop="8px",u.style.fontSize="14px",u.style.color="#ffffff",this.chromelessToolbar.appendChild(u),h(mxUtils.bind(this,function(t){this.actions.get("nextPage").funct(),mxEvent.consume(t)}),Editor.nextLargeImage,mxResources.get("nextPage"))),p=mxUtils.bind(this,function(){null!=this.pages&&1<this.pages.length&&null!=this.currentPage&&(u.innerHTML="",mxUtils.write(u,mxUtils.indexOf(this.pages,this.currentPage)+1+" / "+this.pages.length))}),d=(c.style.paddingLeft="0px",c.style.paddingRight="4px",m.style.paddingLeft="4px",m.style.paddingRight="0px",mxUtils.bind(this,function(){null!=this.pages&&1<this.pages.length&&null!=this.currentPage?(m.style.display="",c.style.display="",u.style.display="inline-block"):(m.style.display="none",c.style.display="none",u.style.display="none"),p()})),f=(this.editor.addListener("resetGraphView",d),this.editor.addListener("pageSelected",p),h(mxUtils.bind(this,function(t){this.actions.get("zoomOut").funct(),mxEvent.consume(t)}),Editor.zoomOutLargeImage,mxResources.get("zoomOut")+" (Alt+Mousewheel)"),h(mxUtils.bind(this,function(t){this.actions.get("zoomIn").funct(),mxEvent.consume(t)}),Editor.zoomInLargeImage,mxResources.get("zoomIn")+" (Alt+Mousewheel)"),h(mxUtils.bind(this,function(t){g.isLightboxView()?(1==g.view.scale?this.lightboxFit():g.zoomTo(1),this.chromelessResize(!1)):this.chromelessResize(!0),mxEvent.consume(t)}),Editor.actualSizeLargeImage,mxResources.get("fit")),null),x=null,y=mxUtils.bind(this,function(t){null!=f&&(window.clearTimeout(f),fadeThead=null),null!=x&&(window.clearTimeout(x),fadeThead2=null),f=window.setTimeout(mxUtils.bind(this,function(){mxUtils.setOpacity(this.chromelessToolbar,0),f=null,x=window.setTimeout(mxUtils.bind(this,function(){this.chromelessToolbar.style.display="none",x=null}),600)}),t||200)}),b=mxUtils.bind(this,function(t){null!=f&&(window.clearTimeout(f),fadeThead=null),null!=x&&(window.clearTimeout(x),fadeThead2=null),this.chromelessToolbar.style.display="",mxUtils.setOpacity(this.chromelessToolbar,t||30)});if("1"==urlParams.layers&&(this.layersDialog=null,n=h(mxUtils.bind(this,function(t){var e;null!=this.layersDialog?(this.layersDialog.parentNode.removeChild(this.layersDialog),this.layersDialog=null):(this.layersDialog=g.createLayersDialog(),mxEvent.addListener(this.layersDialog,"mouseleave",mxUtils.bind(this,function(){this.layersDialog.parentNode.removeChild(this.layersDialog),this.layersDialog=null})),e=n.getBoundingClientRect(),mxUtils.setPrefixedStyle(this.layersDialog.style,"borderRadius","5px"),this.layersDialog.style.position="fixed",this.layersDialog.style.fontFamily="Helvetica,Arial",this.layersDialog.style.backgroundColor="#000000",this.layersDialog.style.width="160px",this.layersDialog.style.padding="4px 2px 4px 2px",this.layersDialog.style.color="#ffffff",mxUtils.setOpacity(this.layersDialog,70),this.layersDialog.style.left=e.left+"px",this.layersDialog.style.bottom=parseInt(this.chromelessToolbar.style.bottom)+this.chromelessToolbar.offsetHeight+4+"px",e=mxUtils.getCurrentStyle(this.editor.graph.container),this.layersDialog.style.zIndex=e.zIndex,document.body.appendChild(this.layersDialog)),mxEvent.consume(t)}),Editor.layersLargeImage,mxResources.get("layers")),(o=g.getModel()).addListener(mxEvent.CHANGE,function(){n.style.display=1<o.getChildCount(o.root)?"":"none"})),this.addChromelessToolbarItems(h),null==this.editor.editButtonLink&&null==this.editor.editButtonFunc||h(mxUtils.bind(this,function(t){null!=this.editor.editButtonFunc?this.editor.editButtonFunc():"_blank"==this.editor.editButtonLink?this.editor.editAsNew(this.getEditBlankXml()):g.openLink(this.editor.editButtonLink,"editWindow"),mxEvent.consume(t)}),Editor.editLargeImage,mxResources.get("edit")),null!=this.lightboxToolbarActions)for(var v=0;v<this.lightboxToolbarActions.length;v++){var C=this.lightboxToolbarActions[v];h(C.fn,C.icon,C.tooltip)}null!=a.refreshBtn&&h(mxUtils.bind(this,function(t){a.refreshBtn.url?window.location.href=a.refreshBtn.url:window.location.reload(),mxEvent.consume(t)}),Editor.refreshLargeImage,mxResources.get("refresh",null,"Refresh")),null!=a.fullscreenBtn&&window.self!==window.top&&h(mxUtils.bind(this,function(t){a.fullscreenBtn.url?g.openLink(a.fullscreenBtn.url):g.openLink(window.location.href),mxEvent.consume(t)}),Editor.fullscreenLargeImage,mxResources.get("openInNewWindow",null,"Open in New Window")),(a.closeBtn&&window.self===window.top||g.lightbox&&("1"==urlParams.close||this.container!=document.body))&&h(mxUtils.bind(this,function(t){"1"==urlParams.close||a.closeBtn?window.close():(this.destroy(),mxEvent.consume(t))}),Editor.closeLargeImage,mxResources.get("close")+" (Escape)"),this.chromelessToolbar.style.display="none",g.isViewer()||mxUtils.setPrefixedStyle(this.chromelessToolbar.style,"transform","translate(-50%,0)"),g.container.appendChild(this.chromelessToolbar),mxEvent.addListener(g.container,mxClient.IS_POINTER?"pointermove":"mousemove",mxUtils.bind(this,function(t){mxEvent.isTouchEvent(t)||(mxEvent.isShiftDown(t)||b(30),y())})),mxEvent.addListener(this.chromelessToolbar,mxClient.IS_POINTER?"pointermove":"mousemove",function(t){mxEvent.consume(t)}),mxEvent.addListener(this.chromelessToolbar,"mouseenter",mxUtils.bind(this,function(t){mxEvent.isShiftDown(t)?y():b(100)})),mxEvent.addListener(this.chromelessToolbar,"mousemove",mxUtils.bind(this,function(t){mxEvent.isShiftDown(t)?y():b(100),mxEvent.consume(t)})),mxEvent.addListener(this.chromelessToolbar,"mouseleave",mxUtils.bind(this,function(t){mxEvent.isTouchEvent(t)||b(30)}));var E=g.getTolerance();g.addMouseListener({startX:0,startY:0,scrollLeft:0,scrollTop:0,mouseDown:function(t,e){this.startX=e.getGraphX(),this.startY=e.getGraphY(),this.scrollLeft=g.container.scrollLeft,this.scrollTop=g.container.scrollTop},mouseMove:function(t,e){},mouseUp:function(t,e){mxEvent.isTouchEvent(e.getEvent())&&Math.abs(this.scrollLeft-g.container.scrollLeft)<E&&Math.abs(this.scrollTop-g.container.scrollTop)<E&&Math.abs(this.startX-e.getGraphX())<E&&Math.abs(this.startY-e.getGraphY())<E&&(0<parseFloat(l.chromelessToolbar.style.opacity||0)?y():b(30))}})}this.editor.editable||this.addChromelessClickHandler()}else this.editor.extendCanvas&&(i=g.view.validate,g.view.validate=function(){var t,e;null!=this.graph.container&&mxUtils.hasScrollbars(this.graph.container)&&(t=this.graph.getPagePadding(),e=this.graph.getPageSize(),this.translate.x,this.translate.y,this.translate.x=t.x-(this.x0||0)*e.width,this.translate.y=t.y-(this.y0||0)*e.height),i.apply(this,arguments)},g.isViewer()||(s=g.sizeDidChange,g.sizeDidChange=function(){var t,e,i,n,o,r;null!=this.container&&mxUtils.hasScrollbars(this.container)?(t=this.getPageLayout(),r=this.getPagePadding(),e=this.getPageSize(),n=Math.ceil(2*r.x+t.width*e.width),o=Math.ceil(2*r.y+t.height*e.height),null!=(i=g.minimumGraphSize)&&i.width==n&&i.height==o||(g.minimumGraphSize=new mxRectangle(0,0,n,o)),i=r.x-t.x*e.width,n=r.y-t.y*e.height,this.autoTranslate||this.view.translate.x==i&&this.view.translate.y==n?s.apply(this,arguments):(this.autoTranslate=!0,this.view.x0=t.x,this.view.y0=t.y,o=g.view.translate.x,r=g.view.translate.y,g.view.setTranslate(i,n),g.container.scrollLeft+=Math.round((i-o)*g.view.scale),g.container.scrollTop+=Math.round((n-r)*g.view.scale),this.autoTranslate=!1)):this.fireEvent(new mxEventObject(mxEvent.SIZE,"bounds",this.getGraphBounds()))}));function S(t){null!=M&&window.clearTimeout(M),window.setTimeout(function(){g.isMouseDown||(M=window.setTimeout(mxUtils.bind(this,function(){g.isFastZoomEnabled()&&(null!=g.view.backgroundPageShape&&null!=g.view.backgroundPageShape.node&&(mxUtils.setPrefixedStyle(g.view.backgroundPageShape.node.style,"transform-origin",null),mxUtils.setPrefixedStyle(g.view.backgroundPageShape.node.style,"transform",null)),U.style.transformOrigin="",w.style.transformOrigin="",mxClient.IS_SF?(U.style.transform="scale(1)",w.style.transform="scale(1)",window.setTimeout(function(){U.style.transform="",w.style.transform=""},0)):(U.style.transform="",w.style.transform=""),g.view.getDecoratorPane().style.opacity="",g.view.getOverlayPane().style.opacity="");var t=new mxPoint(g.container.scrollLeft,g.container.scrollTop),e=mxUtils.getOffset(g.container),i=g.view.scale,n=0,o=0;null!=T&&(n=g.container.offsetWidth/2-T.x+e.x,o=g.container.offsetHeight/2-T.y+e.y),g.zoom(g.cumulativeZoomFactor),g.view.scale!=i&&(null!=I&&(n+=t.x-I.x,o+=t.y-I.y),null!=r&&l.chromelessResize(!1,null,n*(g.cumulativeZoomFactor-1),o*(g.cumulativeZoomFactor-1)),!mxUtils.hasScrollbars(g.container)||0==n&&0==o||(g.container.scrollLeft-=n*(g.cumulativeZoomFactor-1),g.container.scrollTop-=o*(g.cumulativeZoomFactor-1))),g.cumulativeZoomFactor=1,T=I=M=null}),null!=t?t:g.isFastZoomEnabled()?l.wheelZoomDelay:l.lazyZoomDelay))},0)}var w=g.view.getBackgroundPane(),U=g.view.getDrawPane(),M=(g.cumulativeZoomFactor=1,null),T=null,I=null;g.lazyZoom=function(t,e,i){var n;(e=e||!g.scrollbars)&&(T=new mxPoint(g.container.offsetLeft+g.container.clientWidth/2,g.container.offsetTop+g.container.clientHeight/2)),t?this.view.scale*this.cumulativeZoomFactor<=.15?this.cumulativeZoomFactor*=(this.view.scale+.05)/this.view.scale:(this.cumulativeZoomFactor*=this.zoomFactor,this.cumulativeZoomFactor=Math.round(this.view.scale*this.cumulativeZoomFactor*20)/20/this.view.scale):this.view.scale*this.cumulativeZoomFactor<=.15?this.cumulativeZoomFactor*=(this.view.scale-.05)/this.view.scale:(this.cumulativeZoomFactor/=this.zoomFactor,this.cumulativeZoomFactor=Math.round(this.view.scale*this.cumulativeZoomFactor*20)/20/this.view.scale),this.cumulativeZoomFactor=Math.max(.05,Math.min(this.view.scale*this.cumulativeZoomFactor,160))/this.view.scale,g.isFastZoomEnabled()&&(I=new mxPoint(g.container.scrollLeft,g.container.scrollTop),t=e?g.container.scrollLeft+g.container.clientWidth/2:T.x+g.container.scrollLeft-g.container.offsetLeft,n=e?g.container.scrollTop+g.container.clientHeight/2:T.y+g.container.scrollTop-g.container.offsetTop,U.style.transformOrigin=t+"px "+n+"px",U.style.transform="scale("+this.cumulativeZoomFactor+")",w.style.transformOrigin=t+"px "+n+"px",w.style.transform="scale("+this.cumulativeZoomFactor+")",null!=g.view.backgroundPageShape&&null!=g.view.backgroundPageShape.node&&(t=g.view.backgroundPageShape.node,mxUtils.setPrefixedStyle(t.style,"transform-origin",(e?g.container.clientWidth/2+g.container.scrollLeft-t.offsetLeft+"px":T.x+g.container.scrollLeft-t.offsetLeft-g.container.offsetLeft+"px")+" "+(e?g.container.clientHeight/2+g.container.scrollTop-t.offsetTop+"px":T.y+g.container.scrollTop-t.offsetTop-g.container.offsetTop+"px")),mxUtils.setPrefixedStyle(t.style,"transform","scale("+this.cumulativeZoomFactor+")")),g.view.getDecoratorPane().style.opacity="0",g.view.getOverlayPane().style.opacity="0",null!=l.hoverIcons)&&l.hoverIcons.reset(),S(i)},mxEvent.addGestureListeners(g.container,function(t){null!=M&&window.clearTimeout(M)},null,function(t){1!=g.cumulativeZoomFactor&&S(0)}),mxEvent.addListener(g.container,"scroll",function(){M&&!g.isMouseDown&&1!=g.cumulativeZoomFactor&&S(0)}),mxEvent.addMouseWheelListener(mxUtils.bind(this,function(t,e,i){if(null==this.dialogs||0==this.dialogs.length)if(g.scrollbars||g.isZoomWheelEvent(t)){if(i||g.isZoomWheelEvent(t))for(var n=mxEvent.getSource(t);null!=n;){if(n==g.container)return g.tooltipHandler.hideTooltip(),T=new mxPoint(mxEvent.getClientX(t),mxEvent.getClientY(t)),g.lazyZoom(e),mxEvent.consume(t),!1;n=n.parentNode}}else{var i=g.view.getTranslate(),o=40/g.view.scale;mxEvent.isShiftDown(t)?g.view.setTranslate(i.x+(e?-o:o),i.y):g.view.setTranslate(i.x,i.y+(e?o:-o))}}),g.container),g.panningHandler.zoomGraph=function(t){g.cumulativeZoomFactor=t.scale,g.lazyZoom(0<t.scale,!0),mxEvent.consume(t)}},EditorUi.prototype.addChromelessToolbarItems=function(t){t(mxUtils.bind(this,function(t){this.actions.get("print").funct(),mxEvent.consume(t)}),Editor.printLargeImage,mxResources.get("print"))},EditorUi.prototype.createTemporaryGraph=function(t){t=new Graph(document.createElement("div"),null,null,t);return t.resetViewOnRootChange=!1,t.setConnectable(!1),t.gridEnabled=!1,t.autoScroll=!1,t.setTooltips(!1),t.setEnabled(!1),t.container.style.visibility="hidden",t.container.style.position="absolute",t.container.style.overflow="hidden",t.container.style.height="1px",t.container.style.width="1px",t},EditorUi.prototype.addChromelessClickHandler=function(){var t=urlParams.highlight;null!=t&&0<t.length&&(t="#"+t),this.editor.graph.addClickHandler(t)},EditorUi.prototype.toggleFormatPanel=function(t){null!=this.format&&(this.formatWidth=t||0<this.formatWidth?0:240,this.renderConfig&&this.renderConfig.formatContainer||(this.formatContainer.style.display=t||0<this.formatWidth?"":"none"),this.refresh(),this.format.refresh(),this.fireEvent(new mxEventObject("formatWidthChanged")))},EditorUi.prototype.lightboxFit=function(t){var e,i;this.isDiagramEmpty()?this.editor.graph.view.setScale(1):(i=60,null!=(e=urlParams.border)&&(i=parseInt(e)),this.editor.graph.maxFitScale=this.lightboxMaxFitScale,this.editor.graph.fit(i,null,null,null,null,null,t),this.editor.graph.maxFitScale=null)},EditorUi.prototype.isDiagramEmpty=function(){var t=this.editor.graph.getModel();return 1==t.getChildCount(t.root)&&0==t.getChildCount(t.getChildAt(t.root,0))},EditorUi.prototype.isSelectionAllowed=function(t){return"SELECT"==mxEvent.getSource(t).nodeName||"INPUT"==mxEvent.getSource(t).nodeName&&mxUtils.isAncestorNode(this.formatContainer,mxEvent.getSource(t))},EditorUi.prototype.addBeforeUnloadListener=function(){},EditorUi.prototype.onBeforeUnload=function(){if(this.editor.modified)return mxResources.get("allChangesLost")},EditorUi.prototype.open=function(){try{null!=window.opener&&null!=window.opener.openFile&&window.opener.openFile.setConsumer(mxUtils.bind(this,function(t,e){try{var i=mxUtils.parseXml(t);this.editor.setGraphXml(i.documentElement),this.editor.setModified(!1),this.editor.undoManager.clear(),null!=e&&(this.editor.setFilename(e),this.updateDocumentTitle())}catch(t){mxUtils.alert(mxResources.get("invalidOrMissingFile")+": "+t.message)}}))}catch(t){}this.editor.graph.view.validate(),this.editor.graph.sizeDidChange(),this.editor.fireEvent(new mxEventObject("resetGraphView"))},EditorUi.prototype.setCurrentMenu=function(t,e){this.currentMenuElt=e,this.currentMenu=t},EditorUi.prototype.resetCurrentMenu=function(){this.currentMenuElt=null,this.currentMenu=null},EditorUi.prototype.hideCurrentMenu=function(){null!=this.currentMenu&&(this.currentMenu.hideMenu(),this.resetCurrentMenu())},EditorUi.prototype.updateDocumentTitle=function(){var t=this.editor.getOrCreateFilename();null!=this.editor.appName&&(t+=" - "+this.editor.appName),document.title=t},EditorUi.prototype.createHoverIcons=function(){return new HoverIcons(this.editor.graph)},EditorUi.prototype.redo=function(){try{this.editor.graph.isEditing()?document.execCommand("redo",!1,null):this.editor.undoManager.redo()}catch(t){}},EditorUi.prototype.undo=function(){try{var t,e=this.editor.graph;e.isEditing()?(t=e.cellEditor.textarea.innerHTML,document.execCommand("undo",!1,null),t==e.cellEditor.textarea.innerHTML&&(e.stopEditing(!0),this.editor.undoManager.undo())):this.editor.undoManager.undo()}catch(t){}},EditorUi.prototype.canRedo=function(){return this.editor.graph.isEditing()||this.editor.undoManager.canRedo()},EditorUi.prototype.canUndo=function(){return this.editor.graph.isEditing()||this.editor.undoManager.canUndo()},EditorUi.prototype.getEditBlankXml=function(){return mxUtils.getXml(this.editor.getGraphXml())},EditorUi.prototype.getUrl=function(t){var e,i=null!=t?t:window.location.pathname,n=0<i.indexOf("?")?1:0;for(e in urlParams)i=(i+=0==n?"?":"&")+e+"="+urlParams[e],n++;return i},EditorUi.prototype.setScrollbars=function(t){var e=this.editor.graph,i=e.container.style.overflow;e.scrollbars=t,this.editor.updateGraphComponents(),i!=e.container.style.overflow&&(e.container.scrollTop=0,e.container.scrollLeft=0,e.view.scaleAndTranslate(1,0,0),this.resetScrollbars()),this.fireEvent(new mxEventObject("scrollbarsChanged"))},EditorUi.prototype.hasScrollbars=function(){return this.editor.graph.scrollbars},EditorUi.prototype.resetScrollbars=function(){var t,e,i,n=this.editor.graph;this.editor.extendCanvas?this.editor.isChromelessView()||(mxUtils.hasScrollbars(n.container)?n.pageVisible?(i=n.getPagePadding(),n.container.scrollTop=Math.floor(i.y-this.editor.initialTopSpacing)-1,n.container.scrollLeft=Math.floor(Math.min(i.x,(n.container.scrollWidth-n.container.clientWidth)/2))-1,0<(i=n.getGraphBounds()).width&&0<i.height&&(i.x>n.container.scrollLeft+.9*n.container.clientWidth&&(n.container.scrollLeft=Math.min(i.x+i.width-n.container.clientWidth,i.x-10)),i.y>n.container.scrollTop+.9*n.container.clientHeight)&&(n.container.scrollTop=Math.min(i.y+i.height-n.container.clientHeight,i.y-10))):(i=n.getGraphBounds(),e=Math.max(i.width,n.scrollTileSize.width*n.view.scale),t=Math.max(i.height,n.scrollTileSize.height*n.view.scale),n.container.scrollTop=Math.floor(Math.max(0,i.y-Math.max(20,(n.container.clientHeight-t)/4))),n.container.scrollLeft=Math.floor(Math.max(0,i.x-Math.max(0,(n.container.clientWidth-e)/2)))):(t=mxRectangle.fromRectangle(n.pageVisible?n.view.getBackgroundPageBounds():n.getGraphBounds()),i=n.view.translate,e=n.view.scale,t.x=t.x/e-i.x,t.y=t.y/e-i.y,t.width/=e,t.height/=e,i=n.pageVisible?0:Math.max(0,(n.container.clientHeight-t.height)/4),n.view.setTranslate(Math.floor(Math.max(0,(n.container.clientWidth-t.width)/2)-t.x+2),Math.floor(i-t.y+1)))):(n.container.scrollTop=0,n.container.scrollLeft=0,mxUtils.hasScrollbars(n.container)||n.view.setTranslate(0,0))},EditorUi.prototype.setPageVisible=function(t){var e=this.editor.graph,i=mxUtils.hasScrollbars(e.container),n=0,o=0;i&&(n=e.view.translate.x*e.view.scale-e.container.scrollLeft,o=e.view.translate.y*e.view.scale-e.container.scrollTop),e.pageVisible=t,e.pageBreaksVisible=t,e.preferPageSize=t,e.view.validateBackground(),i&&(t=e.getSelectionCells(),e.clearSelection(),e.setSelectionCells(t)),e.sizeDidChange(),i&&(e.container.scrollLeft=e.view.translate.x*e.view.scale-n,e.container.scrollTop=e.view.translate.y*e.view.scale-o),this.fireEvent(new mxEventObject("pageViewChanged"))},ChangePageSetup.prototype.execute=function(){var t,e=this.ui.editor.graph;this.ignoreColor||(this.color=this.previousColor,t=e.background,this.ui.setBackgroundColor(this.previousColor),this.previousColor=t),this.ignoreImage||(this.image=this.previousImage,t=e.backgroundImage,this.ui.setBackgroundImage(this.previousImage),this.previousImage=t),null!=this.previousFormat&&(this.format=this.previousFormat,t=e.pageFormat,this.previousFormat.width==t.width&&this.previousFormat.height==t.height||(this.ui.setPageFormat(this.previousFormat),this.previousFormat=t)),null!=this.foldingEnabled&&this.foldingEnabled!=this.ui.editor.graph.foldingEnabled&&(this.ui.setFoldingEnabled(this.foldingEnabled),this.foldingEnabled=!this.foldingEnabled),null!=this.previousPageScale&&(e=this.ui.editor.graph.pageScale,this.previousPageScale!=e)&&(this.ui.setPageScale(this.previousPageScale),this.previousPageScale=e)},function(){var t=new mxObjectCodec(new ChangePageSetup,["ui","previousColor","previousImage","previousFormat","previousPageScale"]);t.afterDecode=function(t,e,i){return i.previousColor=i.color,i.previousImage=i.image,i.previousFormat=i.format,i.previousPageScale=i.pageScale,null!=i.foldingEnabled&&(i.foldingEnabled=!i.foldingEnabled),i},mxCodecRegistry.register(t)}(),EditorUi.prototype.setBackgroundColor=function(t){this.editor.graph.background=t,this.editor.graph.view.validateBackground(),this.fireEvent(new mxEventObject("backgroundColorChanged"))},EditorUi.prototype.setFoldingEnabled=function(t){this.editor.graph.foldingEnabled=t,this.editor.graph.view.revalidate(),this.fireEvent(new mxEventObject("foldingEnabledChanged"))},EditorUi.prototype.setPageFormat=function(t){this.editor.graph.pageFormat=t,this.editor.graph.pageVisible?(this.editor.graph.view.validateBackground(),this.editor.graph.sizeDidChange()):this.actions.get("pageView").funct(),this.fireEvent(new mxEventObject("pageFormatChanged"))},EditorUi.prototype.setPageScale=function(t){this.editor.graph.pageScale=t,this.editor.graph.pageVisible?(this.editor.graph.view.validateBackground(),this.editor.graph.sizeDidChange()):this.actions.get("pageView").funct(),this.fireEvent(new mxEventObject("pageScaleChanged"))},EditorUi.prototype.setGridColor=function(t){this.editor.graph.view.gridColor=t,this.editor.graph.view.validateBackground(),this.fireEvent(new mxEventObject("gridColorChanged"))},EditorUi.prototype.addUndoListener=function(){var t=this.actions.get("undo"),e=this.actions.get("redo"),i=this.editor.undoManager,n=mxUtils.bind(this,function(){t.setEnabled(this.canUndo()),e.setEnabled(this.canRedo())}),o=(i.addListener(mxEvent.ADD,n),i.addListener(mxEvent.UNDO,n),i.addListener(mxEvent.REDO,n),i.addListener(mxEvent.CLEAR,n),this.editor.graph.cellEditor.startEditing),r=(this.editor.graph.cellEditor.startEditing=function(){o.apply(this,arguments),n()},this.editor.graph.cellEditor.stopEditing);this.editor.graph.cellEditor.stopEditing=function(t,e){r.apply(this,arguments),n()},n()},EditorUi.prototype.updateActionStates=function(){var t=this.editor.graph,e=!t.isSelectionEmpty(),i=!1,n=!1,o=t.getSelectionCells();if(null!=o)for(var r=0;r<o.length;r++){var s=o[r];if(t.getModel().isEdge(s)&&(n=!0),t.getModel().isVertex(s)&&(i=!0),n&&i)break}for(var l=["cut","copy","bold","italic","underline","delete","duplicate","editStyle","editTooltip","editLink","backgroundColor","borderColor","edit","toFront","toBack","lockUnlock","solid","dashed","pasteSize","dotted","fillColor","gradientColor","shadow","fontColor","formattedText","rounded","toggleRounded","sharp","strokeColor"],r=0;r<l.length;r++)this.actions.get(l[r]).setEnabled(e);this.actions.get("setAsDefaultStyle").setEnabled(1==t.getSelectionCount()),this.actions.get("clearWaypoints").setEnabled(!t.isSelectionEmpty()),this.actions.get("copySize").setEnabled(1==t.getSelectionCount()),this.actions.get("turn").setEnabled(!t.isSelectionEmpty()),this.actions.get("curved").setEnabled(n),this.actions.get("rotation").setEnabled(i),this.actions.get("wordWrap").setEnabled(i),this.actions.get("autosize").setEnabled(i);var a=i&&1==t.getSelectionCount(),a=(this.actions.get("group").setEnabled(1<t.getSelectionCount()||a&&!t.isContainer(t.getSelectionCell())),this.actions.get("ungroup").setEnabled(1==t.getSelectionCount()&&(0<t.getModel().getChildCount(t.getSelectionCell())||a&&t.isContainer(t.getSelectionCell()))),this.actions.get("removeFromGroup").setEnabled(a&&t.getModel().isVertex(t.getModel().getParent(t.getSelectionCell()))),t.view.getState(t.getSelectionCell()),this.menus.get("navigation").setEnabled(e||null!=t.view.currentRoot),this.actions.get("collapsible").setEnabled(i&&(t.isContainer(t.getSelectionCell())||0<t.model.getChildCount(t.getSelectionCell()))),this.actions.get("home").setEnabled(null!=t.view.currentRoot),this.actions.get("exitGroup").setEnabled(null!=t.view.currentRoot),this.actions.get("enterGroup").setEnabled(1==t.getSelectionCount()&&t.isValidRoot(t.getSelectionCell())),1==t.getSelectionCount()&&t.isCellFoldable(t.getSelectionCell())),a=(this.actions.get("expand").setEnabled(a),this.actions.get("collapse").setEnabled(a),this.actions.get("editLink").setEnabled(1==t.getSelectionCount()),this.actions.get("openLink").setEnabled(1==t.getSelectionCount()&&null!=t.getLinkForCell(t.getSelectionCell())),this.actions.get("guides").setEnabled(t.isEnabled()),this.actions.get("grid").setEnabled(!this.editor.chromeless||this.editor.editable),t.isEnabled()&&!t.isCellLocked(t.getDefaultParent()));this.menus.get("layout").setEnabled(a),this.menus.get("insert").setEnabled(a),this.menus.get("direction").setEnabled(a&&i),this.menus.get("align").setEnabled(a&&i&&1<t.getSelectionCount()),this.menus.get("distribute").setEnabled(a&&i&&1<t.getSelectionCount()),this.actions.get("selectVertices").setEnabled(a),this.actions.get("selectEdges").setEnabled(a),this.actions.get("selectAll").setEnabled(a),this.actions.get("selectNone").setEnabled(a),this.updatePasteActionStates()},EditorUi.prototype.zeroOffset=new mxPoint(0,0),EditorUi.prototype.getDiagramContainerOffset=function(){return this.zeroOffset},EditorUi.prototype.refresh=function(t){t=null==t||t;var e=mxClient.IS_IE&&(null==document.documentMode||5==document.documentMode),i=this.container.clientWidth,n=this.container.clientHeight,o=(this.container==document.body&&(i=document.body.clientWidth||document.documentElement.clientWidth,n=e&&document.body.clientHeight||document.documentElement.clientHeight),0),r=(mxClient.IS_IOS&&!window.navigator.standalone&&window.innerHeight!=document.documentElement.clientHeight&&(o=document.documentElement.clientHeight-window.innerHeight,window.scrollTo(0,0)),Math.max(0,Math.min(this.hsplitPosition,i-this.splitSize-20))),s=0,l=(null!=this.menubar&&(this.menubarContainer.style.height=this.menubarHeight+"px",s+=this.menubarHeight),null!=this.toolbar&&(this.toolbarContainer.style.top=this.menubarHeight+"px",this.toolbarContainer.style.height=this.toolbarHeight+"px",s+=this.toolbarHeight),0<s&&!mxClient.IS_QUIRKS&&(s+=1),0),a=(null!=this.sidebarFooterContainer&&(a=this.footerHeight+o,l=Math.max(0,Math.min(n-s-a,this.sidebarFooterHeight)),this.sidebarFooterContainer.style.width=r+"px",this.sidebarFooterContainer.style.height=l+"px",this.sidebarFooterContainer.style.bottom=a+"px"),null!=this.format?this.formatWidth:0),d=(this.renderConfig&&this.renderConfig.sidebarContainer||(this.sidebarContainer.style.top=s+"px",this.sidebarContainer.style.width=r+"px"),this.renderConfig&&this.renderConfig.formatContainer||(this.formatContainer.style.top=s+"px",this.formatContainer.style.width=a+"px",this.formatContainer.style.display=null!=this.format?"":"none"),this.getDiagramContainerOffset()),h=null!=this.hsplit.parentNode?r+this.splitSize:0;this.renderConfig&&this.renderConfig.diagramContainer||(this.diagramContainer.style.left=h+d.x+"px",this.diagramContainer.style.top=s+d.y+"px"),this.footerContainer.style.height=this.footerHeight+"px",this.hsplit.style.top=this.sidebarContainer.style.top,this.hsplit.style.bottom=this.footerHeight+o+"px",this.hsplit.style.left=r+"px",this.footerContainer.style.display=0==this.footerHeight?"none":"",null!=this.tabContainer&&(this.tabContainer.style.left=h+"px"),e?(this.menubarContainer.style.width=i+"px",this.toolbarContainer.style.width=this.menubarContainer.style.width,s=Math.max(0,n-this.footerHeight-this.menubarHeight-this.toolbarHeight),this.sidebarContainer.style.height=s-l+"px",this.renderConfig&&this.renderConfig.formatContainer||(this.formatContainer.style.height=s+"px"),this.renderConfig&&this.renderConfig.diagramContainer||(this.diagramContainer.style.width=null!=this.hsplit.parentNode?Math.max(0,i-r-this.splitSize-a)+"px":i+"px"),this.footerContainer.style.width=this.menubarContainer.style.width,d=Math.max(0,n-this.footerHeight-this.menubarHeight-this.toolbarHeight),null!=this.tabContainer&&(this.tabContainer.style.width=this.diagramContainer.style.width,this.tabContainer.style.bottom=this.footerHeight+o+"px",d-=this.tabContainer.clientHeight),this.renderConfig&&this.renderConfig.diagramContainer||(this.diagramContainer.style.height=d+"px"),this.hsplit.style.height=d+"px"):(0<this.footerHeight&&(this.footerContainer.style.bottom=o+"px"),this.renderConfig&&this.renderConfig.diagramContainer||(this.diagramContainer.style.right=a+"px"),h=0,null!=this.tabContainer&&(this.tabContainer.style.bottom=this.footerHeight+o+"px",this.tabContainer.style.right=this.diagramContainer.style.right,h=this.tabContainer.clientHeight),this.sidebarContainer.style.bottom=this.footerHeight+l+o+"px",this.renderConfig&&this.renderConfig.formatContainer||(this.formatContainer.style.bottom=this.footerHeight+o+"px"),this.renderConfig&&this.renderConfig.diagramContainer||(this.diagramContainer.style.bottom=this.footerHeight+o+h+"px")),t&&this.editor.graph.sizeDidChange()},EditorUi.prototype.createTabContainer=function(){return null},EditorUi.prototype.createDivs=function(){this.menubarContainer=this.createDiv("geMenubarContainer"),this.toolbarContainer=this.createDiv("geToolbarContainer"),this.sidebarContainer=this.renderConfig&&this.renderConfig.sidebarContainer||this.createDiv("geSidebarContainer"),this.formatContainer=this.renderConfig&&this.renderConfig.formatContainer||this.createDiv("geSidebarContainer geFormatContainer"),this.diagramContainer=this.renderConfig&&this.renderConfig.diagramContainer||this.createDiv("geDiagramContainer"),this.footerContainer=this.createDiv("geFooterContainer"),this.hsplit=this.createDiv("geHsplit"),this.hsplit.setAttribute("title",mxResources.get("collapseExpand")),this.menubarContainer.style.top="0px",this.menubarContainer.style.left="0px",this.menubarContainer.style.right="0px",this.toolbarContainer.style.left="0px",this.toolbarContainer.style.right="0px",this.renderConfig&&this.renderConfig.sidebarContainer||(this.sidebarContainer.style.left="0px"),this.renderConfig&&this.renderConfig.formatContainer||(this.formatContainer.style.right="0px",this.formatContainer.style.zIndex="1"),this.renderConfig&&this.renderConfig.diagramContainer||(this.diagramContainer.style.right=(null!=this.format?this.formatWidth:0)+"px"),this.footerContainer.style.left="0px",this.footerContainer.style.right="0px",this.footerContainer.style.bottom="0px",this.footerContainer.style.zIndex=mxPopupMenu.prototype.zIndex-2,this.hsplit.style.width=this.splitSize+"px",this.sidebarFooterContainer=this.createSidebarFooterContainer(),this.sidebarFooterContainer&&(this.sidebarFooterContainer.style.left="0px"),this.editor.chromeless?this.renderConfig&&this.renderConfig.diagramContainer||(this.diagramContainer.style.border="none"):this.tabContainer=this.createTabContainer()},EditorUi.prototype.createSidebarFooterContainer=function(){return null},EditorUi.prototype.createUi=function(){this.menubar=this.editor.chromeless?null:this.menus.createMenubar(this.createDiv("geMenubar")),null!=this.menubar&&this.menubarContainer.appendChild(this.menubar.container),null!=this.menubar&&(this.statusContainer=this.createStatusContainer(),this.editor.addListener("statusChanged",mxUtils.bind(this,function(){this.setStatusText(this.editor.getStatus())})),this.setStatusText(this.editor.getStatus()),this.menubar.container.appendChild(this.statusContainer),this.container.appendChild(this.menubarContainer)),this.sidebar=this.editor.chromeless?null:this.createSidebar(this.sidebarContainer),null==this.sidebar||this.renderConfig&&this.renderConfig.sidebarContainer||this.container.appendChild(this.sidebarContainer),this.format=this.editor.chromeless||!this.formatEnabled?null:this.createFormat(this.formatContainer,this.renderConfig&&this.renderConfig.formatContainer),null==this.format||this.renderConfig&&this.renderConfig.formatContainer||this.container.appendChild(this.formatContainer);var t=this.editor.chromeless?null:this.createFooter();null!=t&&(this.footerContainer.appendChild(t),this.container.appendChild(this.footerContainer)),null!=this.sidebar&&this.sidebarFooterContainer&&this.container.appendChild(this.sidebarFooterContainer),this.renderConfig&&this.renderConfig.diagramContainer||this.container.appendChild(this.diagramContainer),this.renderConfig&&this.renderConfig.diagramContainer&&this.renderConfig.comsContainer&&this.diagramContainer.addEventListener("scroll",mxUtils.bind(this,function(t){var t=t.target,e=t.scrollLeft,t=t.scrollTop;this.renderConfig.comsContainer.style.transform="translate(-"+e+"px,-"+t+"px)",this.renderConfig.diagramContainerScroll&&this.renderConfig.diagramContainerScroll(e,t)})),null!=this.container&&null!=this.tabContainer&&this.container.appendChild(this.tabContainer),this.toolbar=this.editor.chromeless?null:this.createToolbar(this.createDiv("geToolbar")),null!=this.toolbar&&(this.toolbarContainer.appendChild(this.toolbar.container),this.container.appendChild(this.toolbarContainer)),null!=this.sidebar&&(this.container.appendChild(this.hsplit),this.addSplitHandler(this.hsplit,!0,0,mxUtils.bind(this,function(t){this.hsplitPosition=t,this.refresh()})))},EditorUi.prototype.createStatusContainer=function(){var t=document.createElement("a");return t.className="geItem geStatus",screen.width<420&&(t.style.maxWidth=Math.max(20,screen.width-320)+"px",t.style.overflow="hidden"),t},EditorUi.prototype.setStatusText=function(t){this.statusContainer.innerHTML=t},EditorUi.prototype.createToolbar=function(t){return new Toolbar(this,t)},EditorUi.prototype.createSidebar=function(t){return new Sidebar(this,t)},EditorUi.prototype.createFormat=function(t,e){return new Format(this,t,e)},EditorUi.prototype.createFooter=function(){return this.createDiv("geFooter")},EditorUi.prototype.createDiv=function(t){var e=document.createElement("div");return e.className=t,e},EditorUi.prototype.addSplitHandler=function(e,i,n,o){var r=null,s=null,l=!0,a=null,d=(mxClient.IS_POINTER&&(e.style.touchAction="none"),mxUtils.bind(this,function(){var t=parseInt(i?e.style.left:e.style.bottom);return t=i?t:t+n-this.footerHeight}));function h(t){var e;null!=r&&(e=new mxPoint(mxEvent.getClientX(t),mxEvent.getClientY(t)),o(Math.max(0,s+(i?e.x-r.x:r.y-e.y)-n)),mxEvent.consume(t),s!=d())&&(l=!0,a=null)}function t(t){h(t),r=s=null}mxEvent.addGestureListeners(e,function(t){r=new mxPoint(mxEvent.getClientX(t),mxEvent.getClientY(t)),s=d(),l=!1,mxEvent.consume(t)}),mxEvent.addListener(e,"click",mxUtils.bind(this,function(t){var e;!l&&this.hsplitClickEnabled&&(e=null!=a?a-n:0,a=d(),o(e),mxEvent.consume(t))})),mxEvent.addGestureListeners(document,null,h,t),this.destroyFunctions.push(function(){mxEvent.removeGestureListeners(document,null,h,t)})},EditorUi.prototype.handleError=function(t,e,i,n,o){var r,s,t=null!=t&&null!=t.error?t.error:t;null!=t||null!=e?(r=mxUtils.htmlEntities(mxResources.get("unknownError")),s=mxResources.get("ok"),e=null!=e?e:mxResources.get("error"),null!=t&&null!=t.message&&(r=mxUtils.htmlEntities(t.message)),this.showError(e,r,s,i,null,null,null,null,null,null,null,null,n?i:null)):null!=i&&i()},EditorUi.prototype.showError=function(t,e,i,n,o,r,s,l,a,d,h,c,u){t=new ErrorDialog(this,t,e,i||mxResources.get("ok"),n,o,r,s,c,l,a),i=Math.ceil(null!=e?e.length/50:1);this.showDialog(t.container,d||340,h||100+20*i,!0,!1,u),t.init()},EditorUi.prototype.showDialog=function(t,e,i,n,o,r,s,l,a,d){this.editor.graph.tooltipHandler.hideTooltip(),null==this.dialogs&&(this.dialogs=[]),this.dialog=new Dialog(this,t,e,i,n,o,r,s,l,a,d),this.dialogs.push(this.dialog)},EditorUi.prototype.hideDialog=function(t,e){var i;null!=this.dialogs&&0<this.dialogs.length&&(0==(i=this.dialogs.pop()).close(t,e)?this.dialogs.push(i):(this.dialog=0<this.dialogs.length?this.dialogs[this.dialogs.length-1]:null,this.editor.fireEvent(new mxEventObject("hideDialog")),null==this.dialog&&"hidden"!=this.editor.graph.container.style.visibility&&window.setTimeout(mxUtils.bind(this,function(){(this.editor.graph.isEditing()&&null!=this.editor.graph.cellEditor.textarea?this.editor.graph.cellEditor.textarea:(mxUtils.clearSelection(),this.editor.graph.container)).focus()}),0)))},EditorUi.prototype.pickColor=function(t,e){var i=this.editor.graph,n=i.cellEditor.saveSelection(),o=226+17*(Math.ceil(ColorDialog.prototype.presetColors.length/12)+Math.ceil(ColorDialog.prototype.defaultColors.length/12)),t=new ColorDialog(this,t||"none",function(t){i.cellEditor.restoreSelection(n),e(t)},function(){i.cellEditor.restoreSelection(n)});this.showDialog(t.container,230,o,!0,!1),t.init()},EditorUi.prototype.openFile=function(){window.openFile=new OpenFile(mxUtils.bind(this,function(t){this.hideDialog(t)})),this.showDialog(new OpenDialog(this).container,Editor.useLocalStorage?640:320,Editor.useLocalStorage?480:220,!0,!0,function(){window.openFile=null})},EditorUi.prototype.extractGraphModelFromHtml=function(t){var e=null;try{var i,n=t.indexOf("&lt;mxGraphModel ");0<=n&&n<(i=t.lastIndexOf("&lt;/mxGraphModel&gt;"))&&(e=t.substring(n,i+21).replace(/&gt;/g,">").replace(/&lt;/g,"<").replace(/\\&quot;/g,'"').replace(/\n/g,""))}catch(t){}return e},EditorUi.prototype.extractGraphModelFromEvent=function(t){var e=null,i=null;return e=null!=(i=null!=t&&null!=(t=null!=t.dataTransfer?t.dataTransfer:t.clipboardData)&&(10==document.documentMode||11==document.documentMode?i=t.getData("Text"):(i=0<=mxUtils.indexOf(t.types,"text/html")?t.getData("text/html"):null,mxUtils.indexOf(t.types,null==i||0==i.length)&&(i=t.getData("text/plain"))),null!=i)&&(i=Graph.zapGremlins(mxUtils.trim(i)),null!=(t=this.extractGraphModelFromHtml(i)))?t:i)&&this.isCompatibleString(i)?i:e},EditorUi.prototype.isCompatibleString=function(t){return!1},EditorUi.prototype.saveFile=function(t){t||null==this.editor.filename?(t=new FilenameDialog(this,this.editor.getOrCreateFilename(),mxResources.get("save"),mxUtils.bind(this,function(t){this.save(t)}),null,mxUtils.bind(this,function(t){return null!=t&&0<t.length||(mxUtils.confirm(mxResources.get("invalidName")),!1)})),this.showDialog(t.container,300,100,!0,!0),t.init()):this.save(this.editor.getOrCreateFilename())},EditorUi.prototype.save=function(t){if(null!=t){this.editor.graph.isEditing()&&this.editor.graph.stopEditing();var e=mxUtils.getXml(this.editor.getGraphXml());try{if(Editor.useLocalStorage||this.renderConfig){if(!this.renderConfig&&null!=localStorage.getItem(t)&&!mxUtils.confirm(mxResources.get("replaceIt",[t])))return;localStorage.setItem(t,e),this.editor.graph.sceneComponent&&localStorage.setItem(t+"configChange",JSON.stringify(this.editor.graph.sceneComponent.configChange)),this.editor.setStatus(mxUtils.htmlEntities(mxResources.get("saved"))+" "+new Date)}else{if(!(e.length<MAX_REQUEST_SIZE))return mxUtils.alert(mxResources.get("drawingTooLarge")),void mxUtils.popup(e);new mxXmlRequest(SAVE_URL,"filename="+encodeURIComponent(t)+"&xml="+encodeURIComponent(e)).simulate(document,"_blank")}this.editor.setModified(!1),this.editor.setFilename(t),this.renderConfig||this.updateDocumentTitle()}catch(t){this.editor.setStatus(mxUtils.htmlEntities(mxResources.get("errorSavingFile")))}}},EditorUi.prototype.executeLayout=function(t,e,i){var n=this.editor.graph;if(n.isEnabled()){n.getModel().beginUpdate();try{t()}catch(t){throw t}finally{this.allowAnimation&&e&&navigator.userAgent.indexOf("Camino")<0?((t=new mxMorphing(n)).addListener(mxEvent.DONE,mxUtils.bind(this,function(){n.getModel().endUpdate(),null!=i&&i()})),t.startAnimation()):(n.getModel().endUpdate(),null!=i&&i())}}},EditorUi.prototype.showImageDialog=function(t,e,i,n){var o,r=this.editor.graph.cellEditor,s=r.saveSelection(),l=mxUtils.prompt(t,e);r.restoreSelection(s),null!=l&&0<l.length?((o=new Image).onload=function(){i(l,o.width,o.height)},o.onerror=function(){i(null),mxUtils.alert(mxResources.get("fileNotFound"))},o.src=l):i(null)},EditorUi.prototype.showLinkDialog=function(t,e,i){t=new LinkDialog(this,t,e,i);this.showDialog(t.container,420,90,!0,!0),t.init()},EditorUi.prototype.showDataDialog=function(t){null!=t&&(t=new EditDataDialog(this,t),this.showDialog(t.container,480,420,!0,!1,null,!1),t.init())},EditorUi.prototype.showBackgroundImageDialog=function(t){t=null!=t?t:mxUtils.bind(this,function(t){t=new ChangePageSetup(this,null,t);t.ignoreColor=!0,this.editor.graph.model.execute(t)});var e,i=mxUtils.prompt(mxResources.get("backgroundImage"),"");null!=i&&0<i.length?((e=new Image).onload=function(){t(new mxImage(i,e.width,e.height))},e.onerror=function(){t(null),mxUtils.alert(mxResources.get("fileNotFound"))},e.src=i):t(null)},EditorUi.prototype.setBackgroundImage=function(t){this.editor.graph.setBackgroundImage(t),this.editor.graph.view.validateBackgroundImage(),this.fireEvent(new mxEventObject("backgroundImageChanged"))},EditorUi.prototype.confirm=function(t,e,i){mxUtils.confirm(t)?null!=e&&e():null!=i&&i()},EditorUi.prototype.createOutline=function(t){var e=new mxOutline(this.editor.graph);return e.border=20,mxEvent.addListener(window,"resize",function(){e&&e.update()}),this.addListener("pageFormatChanged",function(){e.update()}),e},EditorUi.prototype.altShiftActions={67:"clearWaypoints",65:"connectionArrows",76:"editLink",80:"connectionPoints",84:"editTooltip",86:"pasteSize",88:"copySize"},EditorUi.prototype.createKeyHandler=function(t){var i=this,d=this.editor.graph,r=new mxKeyHandler(d),e=r.isEventIgnored,n=(r.isEventIgnored=function(t){return(!this.isControlDown(t)||mxEvent.isShiftDown(t)||90!=t.keyCode&&89!=t.keyCode&&188!=t.keyCode&&190!=t.keyCode&&85!=t.keyCode)&&(66!=t.keyCode&&73!=t.keyCode||!this.isControlDown(t)||this.graph.cellEditor.isContentEditing()&&!mxClient.IS_FF&&!mxClient.IS_SF)&&e.apply(this,arguments)},r.isEnabledForEvent=function(t){if(mxUtils&&mxEvent)return!mxEvent.isConsumed(t)&&this.isGraphEvent(t)&&this.isEnabled()&&(null==i.dialogs||0==i.dialogs.length)},r.isControlDown=function(t){return mxEvent.isControlDown(t)||mxClient.IS_MAC&&t.metaKey},[]),o=null;function s(s,l,a){n.push(function(){if(!d.isSelectionEmpty()&&d.isEnabled())if(l=null!=l?l:1,a){d.getModel().beginUpdate();try{for(var t,e=d.getSelectionCells(),i=0;i<e.length;i++)d.getModel().isVertex(e[i])&&d.isCellResizable(e[i])&&null!=(t=d.getCellGeometry(e[i]))&&(t=t.clone(),37==s?t.width=Math.max(0,t.width-l):38==s?t.height=Math.max(0,t.height-l):39==s?t.width+=l:40==s&&(t.height+=l),d.getModel().setGeometry(e[i],t))}finally{d.getModel().endUpdate()}}else{var n=d.getSelectionCell(),o=d.model.getParent(n),r=null;null!=(r=1==d.getSelectionCount()&&d.model.isVertex(n)&&null!=d.layoutManager&&!d.isCellLocked(n)?d.layoutManager.getLayout(o):r)&&r.constructor==mxStackLayout?(r=o.getIndex(n),37==s||38==s?d.model.add(o,n,Math.max(0,r-1)):39!=s&&40!=s||d.model.add(o,n,Math.min(d.model.getChildCount(o),r+1))):(o=n=0,37==s?n=-l:38==s?o=-l:39==s?n=l:40==s&&(o=l),d.moveCells(d.getMovableCells(d.getSelectionCells()),n,o))}}),null!=o&&window.clearTimeout(o),o=window.setTimeout(function(){if(0<n.length){d.getModel().beginUpdate();try{for(var t=0;t<n.length;t++)n[t]();n=[]}finally{d.getModel().endUpdate()}}},200)}var l={37:mxConstants.DIRECTION_WEST,38:mxConstants.DIRECTION_NORTH,39:mxConstants.DIRECTION_EAST,40:mxConstants.DIRECTION_SOUTH},a=r.getFunction,h=(mxKeyHandler.prototype.getFunction=function(e){if(d.isEnabled()){if(mxEvent.isShiftDown(e)&&mxEvent.isAltDown(e)){var t=i.actions.get(i.altShiftActions[e.keyCode]);if(null!=t)return t.funct}if(9==e.keyCode&&mxEvent.isAltDown(e))return mxEvent.isShiftDown(e)?function(){d.selectParentCell()}:function(){d.selectChildCell()};if(null!=l[e.keyCode]&&!d.isSelectionEmpty()){if(this.isControlDown(e)||!mxEvent.isShiftDown(e)||!mxEvent.isAltDown(e))return this.isControlDown(e)?function(){s(e.keyCode,mxEvent.isShiftDown(e)?d.gridSize:null,!0)}:function(){s(e.keyCode,mxEvent.isShiftDown(e)?d.gridSize:null)};if(d.model.isVertex(d.getSelectionCell()))return function(){var t=d.connectVertex(d.getSelectionCell(),l[e.keyCode],d.defaultEdgeLength,e,!0);null!=t&&0<t.length&&(1==t.length&&d.model.isEdge(t[0])?d.setSelectionCell(d.model.getTerminal(t[0],!1)):d.setSelectionCell(t[t.length-1]),d.scrollCellToVisible(d.getSelectionCell()),null!=i.hoverIcons)&&i.hoverIcons.update(d.view.getState(d.getSelectionCell()))}}}return a.apply(this,arguments)},r.bindAction=mxUtils.bind(this,function(t,e,i,n){var o=this.actions.get(i);null!=o&&(i=function(){o.isEnabled()&&o.funct()},e?n?r.bindControlShiftKey(t,i):r.bindControlKey(t,i):n?r.bindShiftKey(t,i):r.bindKey(t,i))}),this),c=r.escape;return r.escape=function(t){c.apply(this,arguments)},r.enter=function(){},r.bindControlShiftKey(36,function(){d.exitGroup()}),r.bindControlShiftKey(35,function(){d.enterGroup()}),r.bindKey(36,function(){d.home()}),r.bindKey(35,function(){d.refresh()}),r.bindAction(107,!0,"zoomIn"),r.bindAction(109,!0,"zoomOut"),r.bindAction(80,!0,"print"),r.bindAction(79,!0,"outline",!0),r.bindAction(112,!1,"about"),this.editor.chromeless&&!this.editor.editable||(r.bindControlKey(36,function(){d.isEnabled()&&d.foldCells(!0)}),r.bindControlKey(35,function(){d.isEnabled()&&d.foldCells(!1)}),r.bindControlKey(13,function(){if(d.isEnabled())try{d.setSelectionCells(d.duplicateCells(d.getSelectionCells(),!1))}catch(t){h.handleError(t)}}),r.bindAction(8,!1,"delete"),r.bindAction(8,!0,"deleteAll"),r.bindAction(46,!1,"delete"),r.bindAction(46,!0,"deleteAll"),r.bindAction(72,!0,"resetView"),r.bindAction(72,!0,"fitWindow",!0),r.bindAction(74,!0,"fitPage"),r.bindAction(74,!0,"fitTwoPages",!0),r.bindAction(48,!0,"customZoom"),r.bindAction(82,!0,"turn"),r.bindAction(82,!0,"clearDefaultStyle",!0),r.bindAction(83,!0,"save"),r.bindAction(83,!0,"saveAs",!0),r.bindAction(65,!0,"selectAll"),r.bindAction(65,!0,"selectNone",!0),r.bindAction(73,!0,"selectVertices",!0),r.bindAction(69,!0,"selectEdges",!0),r.bindAction(69,!0,"editStyle"),r.bindAction(66,!0,"bold"),r.bindAction(66,!0,"toBack",!0),r.bindAction(70,!0,"toFront",!0),r.bindAction(68,!0,"duplicate"),r.bindAction(68,!0,"setAsDefaultStyle",!0),r.bindAction(90,!0,"undo"),r.bindAction(89,!0,"autosize",!0),r.bindAction(88,!0,"cut"),r.bindAction(67,!0,"copy"),r.bindAction(86,!0,"paste"),r.bindAction(71,!0,"group"),r.bindAction(77,!0,"editData"),r.bindAction(71,!0,"grid",!0),r.bindAction(73,!0,"italic"),r.bindAction(76,!0,"lockUnlock"),r.bindAction(76,!0,"layers",!0),r.bindAction(80,!0,"formatPanel",!0),r.bindAction(85,!0,"underline"),r.bindAction(85,!0,"ungroup",!0),r.bindAction(190,!0,"superscript"),r.bindAction(188,!0,"subscript"),r.bindKey(13,function(){d.isEnabled()&&d.startEditingAtCell()}),r.bindKey(113,function(){d.isEnabled()&&d.startEditingAtCell()})),mxClient.IS_WIN?r.bindAction(89,!0,"redo"):r.bindAction(90,!0,"redo",!0),r},EditorUi.prototype.destroy=function(){if(null!=this.editor&&(this.editor.destroy(),this.editor=null),null!=this.menubar&&(this.menubar.destroy(),this.menubar=null),null!=this.toolbar&&(this.toolbar.destroy(),this.toolbar=null),null!=this.sidebar&&(this.sidebar.destroy(),this.sidebar=null),null!=this.keyHandler&&(this.keyHandler.destroy(),this.keyHandler=null),null!=this.keydownHandler&&(mxEvent.removeListener(document,"keydown",this.keydownHandler),this.keydownHandler=null),null!=this.keyupHandler&&(mxEvent.removeListener(document,"keyup",this.keyupHandler),this.keyupHandler=null),null!=this.resizeHandler&&(mxEvent.removeListener(window,"resize",this.resizeHandler),this.resizeHandler=null),null!=this.gestureHandler&&(mxEvent.removeGestureListeners(document,this.gestureHandler),this.gestureHandler=null),null!=this.orientationChangeHandler&&(mxEvent.removeListener(window,"orientationchange",this.orientationChangeHandler),this.orientationChangeHandler=null),null!=this.scrollHandler&&(mxEvent.removeListener(window,"scroll",this.scrollHandler),this.scrollHandler=null),null!=this.destroyFunctions){for(var t=0;t<this.destroyFunctions.length;t++)this.destroyFunctions[t]();this.destroyFunctions=null}for(var e=[this.menubarContainer,this.toolbarContainer,this.sidebarContainer,this.formatContainer,this.diagramContainer,this.footerContainer,this.chromelessToolbar,this.hsplit,this.sidebarFooterContainer,this.layersDialog],t=0;t<e.length;t++)null!=e[t]&&null!=e[t].parentNode&&e[t].parentNode.removeChild(e[t])},EditorUi.prototype.base64ToBlob=function(t,e){e=e||"";for(var i=atob(t),n=i.length,o=Math.ceil(n/1024),r=Array(o),s=0;s<o;++s){for(var l=1024*s,a=Math.min(l+1024,n),d=Array(a-l),h=0;l<a;++h,++l)d[h]=i[l].charCodeAt(0);r[s]=new Uint8Array(d)}return new Blob(r,{type:e})},EditorUi.prototype.doSaveLocalFile=function(t,e,i,n,o){if(window.Blob&&navigator.msSaveOrOpenBlob)t=n?this.base64ToBlob(t,i):new Blob([t],{type:i}),navigator.msSaveOrOpenBlob(t,e);else if(mxClient.IS_IE)null==(i=window.open("about:blank","_blank"))?mxUtils.popup(t,!0):(i.document.write(t),i.document.close(),i.document.execCommand("SaveAs",!0,e),i.close());else if(mxClient.IS_IOS&&this.isOffline())navigator.standalone||null==i||"image/"!=i.substring(0,6)?this.showTextDialog(e+":",t):this.openInNewWindow(t,i,n);else{var r,s=document.createElement("a"),l=(null==navigator.userAgent||navigator.userAgent.indexOf("PaleMoon/")<0)&&!mxClient.IS_IOS&&void 0!==s.download;if((l=mxClient.IS_GC&&null!=navigator.userAgent?65!=(!!(r=navigator.userAgent.match(/Chrom(e|ium)\/([0-9]+)\./))&&parseInt(r[2],10))&&l:l)||this.isOffline()){s.href=URL.createObjectURL(n?this.base64ToBlob(t,i):new Blob([t],{type:i})),l?s.download=e+(o?"."+o:""):s.setAttribute("target","_blank"),document.body.appendChild(s);try{window.setTimeout(function(){URL.revokeObjectURL(s.href)},0),s.click(),s.parentNode.removeChild(s)}catch(t){}}else this.createEchoRequest(t,e,i,n,o).simulate(document,"_blank")}},EditorUi.prototype.saveLocalFile=function(t,e,i,n,o,r,s){this.doSaveLocalFile(t,e,i,n,o)},EditorUi.prototype.isLocalFileSave=function(){return"remote"!=urlParams.save&&(mxClient.IS_IE||void 0!==window.Blob&&void 0!==window.URL)&&9!=document.documentMode&&8!=document.documentMode&&7!=document.documentMode&&!mxClient.IS_QUIRKS||this.isOfflineApp()||mxClient.IS_IOS},EditorUi.prototype.saveData=function(t,i,n,o,r){this.isLocalFileSave()?this.saveLocalFile(n,t,o,r,i):this.saveRequest(t,i,mxUtils.bind(this,function(t,e){return this.createEchoRequest(n,t,o,r,i,e)}),n,r,o)},EditorUi.prototype.createImageDataUri=function(t,e,i,n){var o=t.toDataURL("image/"+i);if(o.length<=6||o==t.cloneNode(!1).toDataURL("image/"+i))throw{message:"Invalid image"};return null!=e&&(o=this.writeGraphModelToPng(o,"tEXt","mxfile",encodeURIComponent(e))),o=0<n?this.writeGraphModelToPng(o,"pHYs","dpi",n):o},EditorUi.prototype.saveCanvas=function(t,e,i,n,o,r){var s="jpeg"==i?"jpg":i;n=r||this.getBaseFilename(n)+"."+s,t=this.createImageDataUri(t,e,i,o),this.saveData(n,s,t.substring(t.lastIndexOf(",")+1),"image/"+i,!0,r)},EditorUi.prototype.createSvgDataUri=function(t){return"data:image/svg+xml;base64,"+btoa(unescape(encodeURIComponent(t)))},EditorUi.prototype.loadFonts=function(e){null!=this.editor.fontCss&&null==this.editor.resolvedFontCss?this.embedCssFonts(this.editor.fontCss,mxUtils.bind(this,function(t){this.editor.resolvedFontCss=t,e()})):e()},EditorUi.prototype.embedExtFonts=function(t){var e=this.editor.graph.extFonts;if(null!=e&&0<e.length){var i="",n=0;null==this.cachedGoogleFonts&&(this.cachedGoogleFonts={});for(var o=mxUtils.bind(this,function(){0==n&&this.embedCssFonts(i,t)}),r=0;r<e.length;r++)mxUtils.bind(this,function(t,e){0==e.indexOf(Editor.GOOGLE_FONTS)?null==this.cachedGoogleFonts[e]?(n++,this.loadUrl(e,mxUtils.bind(this,function(t){this.cachedGoogleFonts[e]=t,i+=t,n--,o()}),mxUtils.bind(this,function(t){n--,i+="@import url("+e+");",o()}))):i+=this.cachedGoogleFonts[e]:i+='@font-face {font-family: "'+t+'";src: url("'+e+'");}'})(e[r].name,e[r].url);o()}else t()},EditorUi.prototype.timeout=25e3,EditorUi.prototype.crossOriginImages=!mxClient.IS_IE,EditorUi.prototype.createImageUrlConverter=function(){var i=new mxUrlConverter,n=(i.updateBaseUrl(),i.convert),o=this;return i.convert=function(t){var e;return null!=t&&((e="http://"==t.substring(0,7)||"https://"==t.substring(0,8))&&!navigator.onLine?t=o.svgBrokenImage.src:!e||t.substring(0,i.baseUrl.length)==i.baseUrl||o.crossOriginImages&&o.editor.isCorsEnabledForUrl(t)?"chrome-extension://"==t.substring(0,19)||mxClient.IS_CHROMEAPP||(t=n.apply(this,arguments)):t=PROXY_URL+"?url="+encodeURIComponent(t)),t},i},EditorUi.prototype.convertImageToDataUri=function(t,i){try{var n,o,r=!0,s=window.setTimeout(mxUtils.bind(this,function(){r=!1,i(this.svgBrokenImage.src)}),this.timeout);/(\.svg)$/i.test(t)?mxUtils.get(t,mxUtils.bind(this,function(t){window.clearTimeout(s),r&&i(this.createSvgDataUri(t.getText()))}),function(){window.clearTimeout(s),r&&i(this.svgBrokenImage.src)}):(n=new Image,(o=this).crossOriginImages&&(n.crossOrigin="anonymous"),n.onload=function(){if(window.clearTimeout(s),r)try{var t=document.createElement("canvas"),e=t.getContext("2d");t.height=n.height,t.width=n.width,e.drawImage(n,0,0),i(t.toDataURL())}catch(t){i(o.svgBrokenImage.src)}},n.onerror=function(){window.clearTimeout(s),r&&i(o.svgBrokenImage.src)},n.src=t)}catch(t){i(this.svgBrokenImage.src)}},EditorUi.prototype.convertImages=function(o,r,t,s){null==s&&(s=this.createImageUrlConverter());var l=0,a=t||{};(t=mxUtils.bind(this,function(t,n){for(var e=o.getElementsByTagName(t),i=0;i<e.length;i++)mxUtils.bind(this,function(e){try{var i,t;null!=e&&(null!=(i=s.convert(e.getAttribute(n)))&&"data:"!=i.substring(0,5)?null==(t=a[i])?(l++,this.convertImageToDataUri(i,function(t){null!=t&&(a[i]=t,e.setAttribute(n,t)),0==--l&&r(o)})):e.setAttribute(n,t):null!=i&&e.setAttribute(n,i))}catch(t){}})(e[i])}))("image","xlink:href"),t("img","src"),0==l&&r(o)},EditorUi.prototype.exportToCanvas=function(t,e,i,x,y,n,o,b,v,r,C,s,l,a,E,S,d,w){try{v=null==v||v,r=null==r||r,E=null!=E?E:this.editor.graph,S=null!=S?S:0;var U=s?null:E.background,h=(null==(U=null==(U=U==mxConstants.NONE?null:U)?o:U)&&0==s&&(U="#ffffff"),t.view.backgroundPageShape.node),c=this,M=null,T=null,u=t.getChildCells(t.getDefaultParent(),!0,!0),I=t.getBoundingBoxFromGeometry(u,!0);this.convertImages(E.getSvg(null,null,null,d,null,r,null,null,null,l),mxUtils.bind(c,function(f){try{e&&e.formatSvg&&"function"==typeof e.formatSvg&&e.formatSvg(f);var o=mxUtils.bind(c,function(){var p=new Image,e=(p.onload=mxUtils.bind(c,function(){try{function n(){mxClient.IS_SF?window.setTimeout(function(){m.drawImage(p,S/C+I.x,S/C+I.y),x(c)},0):(m.drawImage(p,S/C+I.x,S/C+I.y),x(c))}var t,e,i,o,r,s,l,a,d,h,c=T||document.createElement("canvas"),u=parseInt(f.getAttribute("width")),g=parseInt(f.getAttribute("height")),m=(C=null!=C?C:1,null!=y&&(C=v?Math.min(1,Math.min(3*y/(4*g),y/u)):y/u),u=Math.ceil(C*u)+2*S,g=Math.ceil(C*g)+2*S,T||(c.setAttribute("width",u),c.setAttribute("height",g)),M||c.getContext("2d"));null!=U&&(m.beginPath(),m.rect(0,0,u,g),m.fillStyle=U,m.fill()),m.scale(C,C),w?(e=(t=E.view).scale,t.scale=1,i=btoa(unescape(encodeURIComponent(t.createSvgGrid(t.gridColor)))),t.scale=e,i="data:image/svg+xml;base64,"+i,o=E.gridSize*t.gridSteps*C,r=E.getGraphBounds(),s=t.translate.x*e,l=t.translate.y*e,a=s+(r.x-s)/e,d=l+(r.y-l)/e,(h=new Image).onload=function(){try{for(var t=-Math.round(o-mxUtils.mod((s-a)*C,o)),e=-Math.round(o-mxUtils.mod((l-d)*C,o));t<u;t+=o)for(var i=e;i<g;i+=o)m.drawImage(h,t/C,i/C);n()}catch(t){null!=b&&b(t)}},h.onerror=function(t){null!=b&&b(t)},h.src=i):n()}catch(t){null!=b&&b(t)}}),p.onerror=function(t){null!=b&&b(t)},l&&c.editor.graph.addSvgShadow(f),c.editor.graph.mathEnabled&&c.editor.addMathCss(f),mxUtils.bind(c,function(){try{null!=c.editor.resolvedFontCss&&c.editor.addFontCss(f,c.editor.resolvedFontCss),p.src=c.createSvgDataUri(mxUtils.getXml(f))}catch(t){null!=b&&b(t)}}));c.embedExtFonts(mxUtils.bind(c,function(t){try{null!=t&&c.editor.addFontCss(f,t),c.loadFonts(e)}catch(t){null!=b&&b(t)}}))});h?i({el:h,android:{},ios:{useCORS:!0,logging:!1},handle:function(t){return!0},success:function(t,e){var n=new Image;n.src=e,n.onload=function(){var t=document.createElement("canvas"),e=parseInt(f.getAttribute("width")),i=parseInt(f.getAttribute("height")),e=(C=null!=C?C:1,null!=y&&(C=v?Math.min(1,Math.min(3*y/(4*i),y/e)):y/e),e=Math.ceil(C*e)+2*S+I.x+20,i=Math.ceil(C*i)+2*S+I.y+20,t.setAttribute("width",e),t.setAttribute("height",i),t.getContext("2d"));T=t,(M=e).drawImage(n,S/C,S/C),o()},n.onerror=o},error:function(t){o()}}):o()}catch(t){null!=b&&b(t)}}),n,a)}catch(t){null!=b&&b(t)}},EditorUi.prototype.getFileData=function(t,e,i,n,o,r,s,l,a,d){o=null==o||o,r=null!=r&&r;var h,c,u=this.editor.graph;return(e||!t&&null!=a&&/(\.svg)$/i.test(a.getTitle()))&&(d=!1,null!=this.pages)&&this.currentPage!=this.pages[0]&&(h=u.getGlobalVariable,u=this.createTemporaryGraph(u.getStylesheet()),c=this.pages[0],u.getGlobalVariable=function(t){return"page"==t?c.getName():"pagenumber"==t?1:h.apply(this,arguments)},document.body.appendChild(u.container),u.model.setRoot(c.root)),s=null!=s?s:this.getXmlFileData(o,r,d),a=null!=a?a:this.getCurrentFile(),t=this.createFileData(s,u,a,window.location.href,t,e,i,n,o,l,d),u!=this.editor.graph&&u.container.parentNode.removeChild(u.container),t},EditorUi.prototype.exportImage=function(t,e,i,n,o,r,s,l,a,d,h,c,u,g,m){c=null!=c?c:"png";var p=this.editor.graph.isSelectionEmpty();r=null!=r?r:p,null==this.thumbImageCache&&(this.thumbImageCache={}),e&&e.start&&"function"==typeof e.start&&e.start();try{this.exportToCanvas(t,e,i,mxUtils.bind(this,function(t){try{this.saveCanvas(t,l?this.getFileData(!0,null,null,null,r,h):null,c,null==this.pages||0==this.pages.length,g,m)}catch(t){"Invalid image"==t.message?this.downloadFile(c):this.handleError(t)}e&&e.end&&"function"==typeof e.end&&e.end()}),null,this.thumbImageCache,null,mxUtils.bind(this,function(t){this.handleError(t)}),null,r,n||1,o,s,null,null,a,d,u)}catch(t){}};