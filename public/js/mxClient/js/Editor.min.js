Editor=function(e,t,i,a,n,r){mxEventSource.call(this);this.chromeless=e!=null?e:this.chromeless;this.initStencilRegistry();this.graph=a||this.createGraph(t,i,r);this.editable=n!=null?n:!e;this.undoManager=this.createUndoManager();this.status="";this.getOrCreateFilename=function(){return this.filename||mxResources.get("drawing",[Editor.pageCounter])+".xml"};this.getFilename=function(){return this.filename};this.setStatus=function(e){this.status=e;this.fireEvent(new mxEventObject("statusChanged"))};this.getStatus=function(){return this.status};this.graphChangeListener=function(e,t){var i=t!=null?t.getProperty("edit"):null;if(i==null||!i.ignoreEdit){this.setModified(true)}};this.graph.getModel().addListener(mxEvent.CHANGE,mxUtils.bind(this,function(){this.graphChangeListener.apply(this,arguments)}));this.graph.resetViewOnRootChange=false;this.init()};Editor.pageCounter=0;(function(){try{var e=window;while(e.opener!=null&&typeof e.opener.Editor!=="undefined"&&!isNaN(e.opener.Editor.pageCounter)&&e.opener!=e){e=e.opener}if(e!=null){e.Editor.pageCounter++;Editor.pageCounter=e.Editor.pageCounter}}catch(e){}})();Editor.useLocalStorage=typeof Storage!="undefined"&&mxClient.IS_IOS;Editor.helpImage=mxClient.IS_SVG?"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDI0djI0SDB6Ii8+PHBhdGggZD0iTTExIDE4aDJ2LTJoLTJ2MnptMS0xNkM2LjQ4IDIgMiA2LjQ4IDIgMTJzNC40OCAxMCAxMCAxMCAxMC00LjQ4IDEwLTEwUzE3LjUyIDIgMTIgMnptMCAxOGMtNC40MSAwLTgtMy41OS04LThzMy41OS04IDgtOCA4IDMuNTkgOCA4LTMuNTkgOC04IDh6bTAtMTRjLTIuMjEgMC00IDEuNzktNCA0aDJjMC0xLjEuOS0yIDItMnMyIC45IDIgMmMwIDItMyAxLjc1LTMgNWgyYzAtMi4yNSAzLTIuNSAzLTUgMC0yLjIxLTEuNzktNC00LTR6Ii8+PC9zdmc+":IMAGE_PATH+"/help.png";Editor.checkmarkImage=mxClient.IS_SVG?"data:image/gif;base64,R0lGODlhFQAVAMQfAGxsbHx8fIqKioaGhvb29nJycvr6+sDAwJqamltbW5OTk+np6YGBgeTk5Ly8vJiYmP39/fLy8qWlpa6ursjIyOLi4vj4+N/f3+3t7fT09LCwsHZ2dubm5r6+vmZmZv///yH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6OEY4NTZERTQ5QUFBMTFFMUE5MTVDOTM5MUZGMTE3M0QiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6OEY4NTZERTU5QUFBMTFFMUE5MTVDOTM5MUZGMTE3M0QiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo4Rjg1NkRFMjlBQUExMUUxQTkxNUM5MzkxRkYxMTczRCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo4Rjg1NkRFMzlBQUExMUUxQTkxNUM5MzkxRkYxMTczRCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PgH//v38+/r5+Pf29fTz8vHw7+7t7Ovq6ejn5uXk4+Lh4N/e3dzb2tnY19bV1NPS0dDPzs3My8rJyMfGxcTDwsHAv769vLu6ubi3trW0s7KxsK+urayrqqmop6alpKOioaCfnp2cm5qZmJeWlZSTkpGQj46NjIuKiYiHhoWEg4KBgH9+fXx7enl4d3Z1dHNycXBvbm1sa2ppaGdmZWRjYmFgX15dXFtaWVhXVlVUU1JRUE9OTUxLSklIR0ZFRENCQUA/Pj08Ozo5ODc2NTQzMjEwLy4tLCsqKSgnJiUkIyIhIB8eHRwbGhkYFxYVFBMSERAPDg0MCwoJCAcGBQQDAgEAACH5BAEAAB8ALAAAAAAVABUAAAVI4CeOZGmeaKqubKtylktSgCOLRyLd3+QJEJnh4VHcMoOfYQXQLBcBD4PA6ngGlIInEHEhPOANRkaIFhq8SuHCE1Hb8Lh8LgsBADs=":IMAGE_PATH+"/checkmark.gif";Editor.maximizeImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVBAMAAABbObilAAAAElBMVEUAAAAAAAAAAAAAAAAAAAAAAADgKxmiAAAABXRSTlMA758vX1Pw3BoAAABJSURBVAjXY8AJQkODGBhUQ0MhbAUGBiYY24CBgRnGFmZgMISwgwwDGRhEhVVBbAVmEQYGRwMmBjIAQi/CTIRd6G5AuA3dzYQBAHj0EFdHkvV4AAAAAElFTkSuQmCC";Editor.zoomOutImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVBAMAAABbObilAAAAElBMVEUAAAAAAAAsLCxxcXEhISFgYGChjTUxAAAAAXRSTlMAQObYZgAAAEdJREFUCNdjIAMwCQrB2YKCggJQJqMwA7MglK1owMBgqABVApITgLJZXFxgbIQ4Qj3CHIT5ggoIe5kgNkM1KSDYKBKqxPkDAPo5BAZBE54hAAAAAElFTkSuQmCC";Editor.zoomInImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVBAMAAABbObilAAAAElBMVEUAAAAAAAAsLCwhISFxcXFgYGBavKaoAAAAAXRSTlMAQObYZgAAAElJREFUCNdjIAMwCQrB2YKCggJQJqMIA4sglK3owMzgqABVwsDMwCgAZTMbG8PYCHGEeoQ5CPMFFRD2MkFshmpSQLBRJFSJ8wcAEqcEM2uhl2MAAAAASUVORK5CYII=";Editor.zoomFitImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVBAMAAABbObilAAAAD1BMVEUAAAAAAAAwMDBwcHBgYGC1xl09AAAAAXRSTlMAQObYZgAAAEFJREFUCNdjIAMwCQrB2YKCggJQJqMwA7MglK1owMBgqABVApITwMdGqEeYgzBfUAFhLxPEZqgmBQQbRUKFOH8AAK5OA3lA+FFOAAAAAElFTkSuQmCC";Editor.layersImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVCAMAAACeyVWkAAAAaVBMVEUAAAAgICAICAgdHR0PDw8WFhYICAgLCwsXFxcvLy8ODg4uLi4iIiIqKiokJCQYGBgKCgonJycFBQUCAgIqKiocHBwcHBwODg4eHh4cHBwnJycJCQkUFBQqKiojIyMuLi4ZGRkgICAEBATOWYXAAAAAGnRSTlMAD7+fnz8/H7/ff18/77+vr5+fn39/b28fH2xSoKsAAACQSURBVBjTrYxJEsMgDARZZMAY73sgCcn/HxnhKtnk7j6oRq0psfuoyndZ/SuODkHPLzfVT6KeyPePnJ7KrnkRjWMXTn4SMnN8mXe2SSM3ts8L/ZUxxrbAULSYJJULE0Iw9pjpenoICcgcX61mGgTgtCv9Be99pzCoDhNQWQnchD1mup5++CYGcoQexajZbfwAj/0MD8ZOaUgAAAAASUVORK5CYII=";Editor.previousImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAABmJLR0QA/wD/AP+gvaeTAAAAh0lEQVQ4je3UsQnCUBCA4U8hpa1NsoEjpHQJS0dxADdwEMuMIJkgA1hYChbGQgMi+JC8q4L/AB/vDu7x74cWWEZhJU44RmA1zujR5GIbXF9YNrjD/Q0bDRY4fEBZ4P4LlgTnCbAf84pUM8/9hY08tMUtEoQ1LpEgrNBFglChFXR6Q6GfwwR6AGKJMF74Vtt3AAAAAElFTkSuQmCC";Editor.nextImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAABmJLR0QA/wD/AP+gvaeTAAAAi0lEQVQ4jeXUIQ7CUAwA0MeGxWI2yylwnALJUdBcgYvM7QYLmjOQIAkIPmJZghiIvypoUtX0tfnJL38X5ZfaEgUeUcManFBHgS0SLlhHggk3bCPBhCf2keCQR8wjwYTDp6YiZxJmOU1jGw7vGALescuBxsArNlOwd/CM1VSM/ut1qCIw+uOwiMJ+OF4CQzBCXm3hyAAAAABJRU5ErkJggg==";Editor.editImage=mxClient.IS_SVG?"data:image/gif;base64,R0lGODlhCwALAIABAFdXV////yH5BAEAAAEALAAAAAALAAsAAAIZjB8AiKuc4jvLOGqzrjX6zmkWyChXaUJBAQA7":IMAGE_PATH+"/edit.gif";Editor.zoomOutLargeImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAilBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////2N2iNAAAALXRSTlMA+vTcKMM96GRBHwXxi0YaX1HLrKWhiHpWEOnOr52Vb2xKSDcT19PKv5l/Ngdk8+viAAABJklEQVQ4y4WT2XaDMAxEvWD2nSSUNEnTJN3r//+9Sj7ILAY6L0ijC4ONYVZRpo6cByrz2YKSUGorGTpz71lPVHvT+avoB5wIkU/mxk8veceSuNoLg44IzziXjvpih72wKQnm8yc2UoiP/LAd8jQfe2Xf4Pq+2EyYIvv9wbzHHCgwxDdlBtWZOdqDfTCVgqpygQpsZaojVAVc9UjQxnAJDIBhiQv84tq3gMQCAVTxVoSibXJf8tMuc7e1TB/DCmejBNg/w1Y3c+AM5vv4w7xM59/oXamrHaLVqPQ+OTCnmMZxgz0SdL5zji0/ld6j88qGa5KIiBB6WeJGKfUKwSMKLuXgvl1TW0tm5R9UQL/efSDYsnzxD8CinhBsTTdugJatKpJwf8v+ADb8QmvW7AeAAAAAAElFTkSuQmCC";Editor.zoomInLargeImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAilBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////2N2iNAAAALXRSTlMA+vTcKMM96GRBHwXxi0YaX1HLrKWhiHpWEOnOr52Vb2xKSDcT19PKv5l/Ngdk8+viAAABKElEQVQ4y4WT6WKCMBCENwkBwn2oFKvWqr3L+79es4EkQIDOH2d3Pxk2ABiJlB8JCXjqw4LikHVGLHTm3nM3UeVN5690GBBN0GwyV/3kkrUQR+WeKnREeKpzaXWd77CmJiXGfPIEI4V4yQ9TIW/ntlcMBe731Vts9w5TWG8F5j3mQI4hvrKpdGeYA7CX9qAcl650gVJartxRuhyHVghF8idQAIbFLvCLu28BsQEC6aKtCK6Pyb3JT7PmbmtNH8Ny56CotD/2qOs5cJbuffxgXmCib+xddVU5RNOhkvvkhTlFehzVWCOh3++MYElOhfdovaImnRYVmqDdsuhNp1QrBBE6uGC2+3ZNjGdg5B94oD+9uyVgWT79BwAxEBTWdOu3bWBVgsn/N/AHUD9IC01Oe40AAAAASUVORK5CYII=";Editor.actualSizeLargeImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAilBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////2N2iNAAAALXRSTlMA+vTcKMM96GRBHwXxi0YaX1HLrKWhiHpWEOnOr52Vb2xKSDcT19PKv5l/Ngdk8+viAAABIUlEQVQ4y4WT2XqDIBCFBxDc9yTWNEnTJN3r+79eGT4BEbXnaubMr8dBBaM450dCQp4LWFAascGIRd48eB4cNYE7f6XjgGiCFs5c+dml6CFN6j1V6IQIlHPpdV/usKcmJcV88gQTRXjLD9Mhb+fWq8YG9/uCmTCFjeeDeY85UGKIUGUuqzN42kv7oCouq9oHamlzVR1lVfpAIu1QVRiW+sAv7r4FpAYIZZVsRXB9TP5Dfpo1d1trCgzz1iiptH/sUbdz4CzN9+mLeXHn3+hdddd4RDegsrvzwZwSs2GLPRJidAqCLTlVwaMPqpYMWjTWBB2WRW86pVkhSKyDK2bdt2tmagZG4sBD/evdLQHLEvQfAOKRoLCmG1FAB6uKmby+gz+REDn7O5+EwQAAAABJRU5ErkJggg==";Editor.printLargeImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAXVBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9RKvvlAAAAHnRSTlMAydnl77qbMLT093H7K4Nd4Ktn082+lYt5bkklEgP44nQSAAAApUlEQVQ4y73P2Q6DIBRF0cOgbRHHzhP//5m9mBAQKjG1cT0Yc7ITAMu1LNQgUZiQ2DYoNQ0sCQb6qgHAfRx48opq3J9AZ6xuF7uOew8Ik1OsCZRS2UAC9V+D9a+QZYxNA45YFQftPtSkATOhw7dAc0vPBwKWiIOjP0JZ0yMuQJ27g36DipOUsqRAM0dR8KD1/ILHaHSE/w8DIx09E3g/BTce6rHUB5sAPKvfF+JdAAAAAElFTkSuQmCC";Editor.layersLargeImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAmVBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////+/v7///+bnZkkAAAAMnRSTlMABPr8ByiD88KsTi/rvJb272mjeUA1CuPe1M/KjVxYHxMP6KZ0S9nYzGRGGRaznpGIbzaGUf0AAAHESURBVDjLbZLZYoIwEEVDgLCjbKIgAlqXqt3m/z+uNwu1rcyDhjl3ktnYL7OY254C0VX3yWFZfzDrOClbbgKxi0YDHjwl4jbnRkXxJS/C1YP3DbBhD1n7Ex4uaAqdVDb3yJ/4J/3nJD2to/ngQz/DfUvzMp4JJ5sSCaF5oXmemgQDfDxzbi+Kq4sU+vNcuAmx94JtyOP2DD4Epz2asWSCz4Z/4fECxyNj9zC9xNLHcdPEO+awDKeSaUu0W4twZQiO2hYVisTR3RCtK/c1X6t4xMEpiGqXqVntEBLolkZZsKY4QtwH6jzq67dEHlJysB1aNOD3XT7n1UkasQN59L4yC2RELMDSeCRtz3yV22Ub3ozIUTknYx8JWqDdQxbUes98cR2kZtUSveF/bAhcedwEWmlxIkpZUy4XOCb6VBjjxHvbwo/1lBAHHi2JCr0NI570QhyHq/DhJoE2lLgyA4RVe6KmZ47O/3b86MCP0HWa73A8/C3SUc5Qc1ajt6fgpXJ+RGpMvDSchepZDOOQRcZVIKcK90x2D7etqtI+56+u6n3sPriO6nfphitR4+O2m3EbM7lh3me1FM1o+LMI887rN+s3/wZdTFlpNVJiOAAAAABJRU5ErkJggg==";Editor.closeLargeImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAUVBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////8IN+deAAAAGnRSTlMAuvAIg/dDM/QlOeuFhj0S5s4vKgzjxJRQNiLSey0AAADNSURBVDjLfZLbEoMgDEQjRRRs1XqX///QNmOHJSnjPkHOGR7IEmeoGtJZstnwjqbRfIsmgEdtPCqe9Ynz7ZSc07rE2QiSc+qv8TvjRXA2PDUm3dpe82iJhOEUfxJJo3aCv+jKmRmH4lcCjCjeh9GWOdL/GZZkXH3PYYDrHBnfc4D/RVZf5sjoC1was+Y6HQxwaUxFvq/a0Pv343VCTxfBSRiB+ab3M3eiQZXmMNBJ3Y8pGRZtYQ7DgHMXJEdPLTaN/qBjzJOBc3nmNcbsA16bMR0oLqf+AAAAAElFTkSuQmCC";Editor.editLargeImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAgVBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9d3yJTAAAAKnRSTlMA+hzi3nRQWyXzkm0h2j3u54gzEgSXjlYoTBgJxL2loGpAOS3Jt7Wxm35Ga7gRAAAA6UlEQVQ4y63Q2XaCMBSF4Q0JBasoQ5DJqbXjfv8HbCK2BZNwo/8FXHx7rcMC7lQu0iX8qU/qtvAWCpoqH8dYzS0SwaV5eK/UAf8X9pd2CWKzuF5Jrftp1owXwnIGLUaL3PYndOHf4kNNXWrXK/m7CHunk7K8LE6YtBpcknwG9GKxnroY+ylBXcx4xKyx/u/EuXi509cP9V7OO1oyHnzrdFTcqLG/4ibBA5pIMr/4xvKzuQDkVy9wW8SgBFD6HDvuzMvrZcC9QlkfMzI7w64m+b4PqBMNHB05lH21PVxJo2/fBXxV4hB38PcD+5AkI4FuETsAAAAASUVORK5CYII=";Editor.previousLargeImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAPFBMVEUAAAD////////////////////////////////////////////////////////////////////////////YSWgTAAAAE3RSTlMA7fci493c0MW8uJ6CZks4MxQHEZL6ewAAAFZJREFUOMvdkskRgDAMA4lDwg2B7b9XOlge/KKvdsa25KFb5XlRvxXC/DNBEv8IFNjBgGdDgXtFgTyhwDXiQAUHCvwa4Uv6mR6UR+1led2mVonvl+tML45qCQNQLIx7AAAAAElFTkSuQmCC";Editor.nextLargeImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAPFBMVEUAAAD////////////////////////////////////////////////////////////////////////////YSWgTAAAAE3RSTlMA7fci493c0MW8uJ6CZks4MxQHEZL6ewAAAFRJREFUOMvd0skRgCAQBVEFwQ0V7fxzNQP6wI05v6pZ/kyj1b7FNgik2gQzzLcAwiUAigHOTwDHK4A1CmB5BJANJG1hQ9qafYcqFlZP3IFc9eVGrR+iIgkDQRUXIAAAAABJRU5ErkJggg==";Editor.refreshLargeImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAolBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////8ELnaCAAAANXRSTlMABfyE2QKU+dfNyyDyoVYKwnTv7N+6rntsYlFNQjEqEw316uSzf2c1JB3GvqebiVw6GAjQB4DQr10AAAE7SURBVDjLvZLXcoMwEABPIgRCx3TT3A3udqL//7UgAdGRcR4yk8k+idsdmgS/QyWEqD/axS2JDV33zlnzLHIzQ2MDq9OeJ3m8l76KKENYlxrmM/b65Ys1+8YxnTEZFIEY0vVhszFWfUGZDJpQTDznTgAe5k4XhQxILB7ruzBQn+kkyDXuHfRtjoYDEvH7J9Lz98dBZXXL94X0Ofco2PFlChKbjVzEdakoSlKjoNoqPYkJ/wUZAYwc+PpLj1Ei7+jdoBWlwQZoJv2H1w3CWgRvo7dd9DP5btgwCWz0M02+oVoxCcIWeY9PNmR6B++m9prMxYEISpCBYBlfy9bc745is7UUULAem1Ww7FfalsiA2uaJsgmWP3pQI9q9/yMLkaaHAp2fxhHff/cNq7dBdHXhGW7l+Mo2zU0Cf8knJ2xA0oJ8enwAAAAASUVORK5CYII=";Editor.backLargeImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAclBMVEUAAAD////////////////+/v7////////////////////////////////////////////+/v7///////////////////////////////////////////////////////////////////////////////8vKLfTAAAAJXRSTlMACh7h9gby3NLIwzwZ55uVJgH57b+8tbCljYV1RRMQ46FrTzQw+vtxOQAAAJ5JREFUOMuF00cWgzAQA1DRDQFCbwFSdf8rZpdVrNH2z3tuMv7mldZQ2WN2yi8x+TT8JvyTkqvwpiKvwsOIrA1fWr+XGTklfj8dOQR+D3KyUF6QufBkJN0hfCazEv6sZBRCJDUcPasGKpu1RLtYE8lkHAPBQLoTsK/SfAyRw5FjAuhCzC2MSj0gJ+66lHatgXdKboD9tfREB5m9/+3iC9jHDYvsGNcUAAAAAElFTkSuQmCC";Editor.fullscreenLargeImage="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAllBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////AJcWoAAAAMXRSTlMA+wIFxPWPCIb446tnUxmsoIykgxTe29jQnpKBe2MNsZhVTR/KyLuWbFhEPjUq7L9z+bQj+gAAAWxJREFUOMttk4l2gkAMRTODCO4FtQgIbnWpS9v8/881iZFh8R51NO8GJ+gAjMN8zuTRFSw04cIOHQcqFHH6oaQFGxf0jeBjEgB8Y52TpW9Ag4zB5QICWOtHrgwGuFZBcw+gPP0MFS7+iiD5inOmDIQS9sZgTwUzwEzyxhxHVEEU7NdDUXsqUPtqjIgR2IZSCT4upzSeIeOdcMHnfDsx3giPoezfU6MrQGB5//SckLEG2xYscK4GfnUFqaix39zrwooaOD/cXoYuvHKQIc7pzd3HVPusp6t2FAW/RmjMonbl8vwHDeZo/GkleJC7e+p5XA/rAq1X/V10wKag04rBpa2/d0LL4OYYceOEtsG5jyMntI1wS+N1BGcQBl/CoLoPOl9ABrW/BP53e1bwSJHHlkIVchJwmHwyyfJ4kIvEnKtwkxNSEct83KSChT7WiWgDZ3ccZ0BM4tloJow2YUAtifNT3njnyD+y/pMsnP4DN3Y4yl1Gyk0AAAAASUVORK5CYII=";Editor.ctrlKey=mxClient.IS_MAC?"Cmd":"Ctrl";Editor.hintOffset=20;Editor.popupsAllowed=true;mxUtils.extend(Editor,mxEventSource);Editor.prototype.originalNoForeignObject=mxClient.NO_FO;Editor.prototype.transparentImage=mxClient.IS_SVG?"data:image/gif;base64,R0lGODlhMAAwAIAAAP///wAAACH5BAEAAAAALAAAAAAwADAAAAIxhI+py+0Po5y02ouz3rz7D4biSJbmiabqyrbuC8fyTNf2jef6zvf+DwwKh8Si8egpAAA7":IMAGE_PATH+"/transparent.gif";Editor.prototype.extendCanvas=true;Editor.prototype.chromeless=false;Editor.prototype.cancelFirst=true;Editor.prototype.enabled=true;Editor.prototype.filename=null;Editor.prototype.modified=false;Editor.prototype.autosave=true;Editor.prototype.initialTopSpacing=0;Editor.prototype.appName=document.title;Editor.prototype.editBlankUrl=window.location.protocol+"//"+window.location.host+"/";Editor.prototype.defaultGraphOverflow="hidden";Editor.prototype.init=function(){};Editor.prototype.isChromelessView=function(){return this.chromeless};Editor.prototype.setAutosave=function(e){this.autosave=e;this.fireEvent(new mxEventObject("autosaveChanged"))};Editor.prototype.getEditBlankUrl=function(e){return this.editBlankUrl+e};Editor.prototype.editAsNew=function(t,e){var i=e!=null?"?title="+encodeURIComponent(e):"";if(urlParams["ui"]!=null){i+=(i.length>0?"&":"?")+"ui="+urlParams["ui"]}if(typeof window.postMessage!=="undefined"&&(document.documentMode==null||document.documentMode>=10)){var a=null;var n=mxUtils.bind(this,function(e){if(e.data=="ready"&&e.source==a){mxEvent.removeListener(window,"message",n);a.postMessage(t,"*")}});mxEvent.addListener(window,"message",n);a=this.graph.openLink(this.getEditBlankUrl(i+(i.length>0?"&":"?")+"client=1"),null,true)}else{this.graph.openLink(this.getEditBlankUrl(i)+"#R"+encodeURIComponent(t))}};Editor.prototype.createGraph=function(e,t,i){var a=new Graph(null,t,null,null,e,null,i);a.transparentBackground=false;if(!this.chromeless){a.isBlankLink=function(e){return!this.isExternalProtocol(e)}}return a};Editor.prototype.resetGraph=function(){this.graph.gridEnabled=!this.isChromelessView()||urlParams["grid"]=="1";this.graph.graphHandler.guidesEnabled=true;this.graph.setTooltips(true);this.graph.setConnectable(true);this.graph.foldingEnabled=true;this.graph.scrollbars=this.graph.defaultScrollbars;this.graph.pageVisible=this.graph.defaultPageVisible;this.graph.pageBreaksVisible=this.graph.pageVisible;this.graph.preferPageSize=this.graph.pageBreaksVisible;this.graph.background=null;this.graph.pageScale=mxGraph.prototype.pageScale;this.graph.pageFormat=mxGraph.prototype.pageFormat;this.graph.currentScale=1;this.graph.currentTranslate.x=0;this.graph.currentTranslate.y=0;this.updateGraphComponents();this.graph.view.setScale(1)};Editor.prototype.readGraphState=function(e){this.graph.gridEnabled=e.getAttribute("grid")!="0"&&(!this.isChromelessView()||urlParams["grid"]=="1");this.graph.gridSize=parseFloat(e.getAttribute("gridSize"))||mxGraph.prototype.gridSize;this.graph.graphHandler.guidesEnabled=e.getAttribute("guides")!="0";this.graph.setTooltips(e.getAttribute("tooltips")!="0");this.graph.setConnectable(e.getAttribute("connect")!="0");this.graph.connectionArrowsEnabled=e.getAttribute("arrows")!="0";this.graph.foldingEnabled=e.getAttribute("fold")!="0";if(this.isChromelessView()&&this.graph.foldingEnabled){this.graph.foldingEnabled=urlParams["nav"]=="1";this.graph.cellRenderer.forceControlClickHandler=this.graph.foldingEnabled}var t=parseFloat(e.getAttribute("pageScale"));if(!isNaN(t)&&t>0){this.graph.pageScale=t}else{this.graph.pageScale=mxGraph.prototype.pageScale}if(!this.graph.isLightboxView()&&!this.graph.isViewer()){var i=e.getAttribute("page");if(i!=null){this.graph.pageVisible=i!="0"}else{this.graph.pageVisible=this.graph.defaultPageVisible}}else{this.graph.pageVisible=false}this.graph.pageBreaksVisible=this.graph.pageVisible;this.graph.preferPageSize=this.graph.pageBreaksVisible;var a=parseFloat(e.getAttribute("pageWidth"));var n=parseFloat(e.getAttribute("pageHeight"));if(!isNaN(a)&&!isNaN(n)){this.graph.pageFormat=new mxRectangle(0,0,a,n)}var r=e.getAttribute("background");if(r!=null&&r.length>0){this.graph.background=r}else{this.graph.background=null}};Editor.prototype.setGraphXml=function(e){if(e!=null){var t=new mxCodec(e.ownerDocument);if(e.nodeName=="mxGraphModel"){this.graph.model.beginUpdate();try{this.graph.model.clear();this.graph.view.scale=1;this.readGraphState(e);this.updateGraphComponents();t.decode(e,this.graph.getModel())}finally{this.graph.model.endUpdate()}this.fireEvent(new mxEventObject("resetGraphView"))}else if(e.nodeName=="root"){this.resetGraph();var i=t.document.createElement("mxGraphModel");i.appendChild(e);t.decode(i,this.graph.getModel());this.updateGraphComponents();this.fireEvent(new mxEventObject("resetGraphView"))}else{throw{message:mxResources.get("cannotOpenFile"),node:e,toString:function(){return this.message}}}}else{this.resetGraph();this.graph.model.clear();this.fireEvent(new mxEventObject("resetGraphView"))}};Editor.prototype.getGraphXml=function(e){e=e!=null?e:true;var t=null;if(e){var i=new mxCodec(mxUtils.createXmlDocument());t=i.encode(this.graph.getModel())}else{t=this.graph.encodeCells(mxUtils.sortCells(this.graph.model.getTopmostCells(this.graph.getSelectionCells())))}if(this.graph.view.translate.x!=0||this.graph.view.translate.y!=0){t.setAttribute("dx",Math.round(this.graph.view.translate.x*100)/100);t.setAttribute("dy",Math.round(this.graph.view.translate.y*100)/100)}t.setAttribute("grid",this.graph.isGridEnabled()?"1":"0");t.setAttribute("gridSize",this.graph.gridSize);t.setAttribute("guides",this.graph.graphHandler.guidesEnabled?"1":"0");t.setAttribute("tooltips",this.graph.tooltipHandler.isEnabled()?"1":"0");t.setAttribute("connect",this.graph.connectionHandler.isEnabled()?"1":"0");t.setAttribute("arrows",this.graph.connectionArrowsEnabled?"1":"0");t.setAttribute("fold",this.graph.foldingEnabled?"1":"0");t.setAttribute("page",this.graph.pageVisible?"1":"0");t.setAttribute("pageScale",this.graph.pageScale);t.setAttribute("pageWidth",this.graph.pageFormat.width);t.setAttribute("pageHeight",this.graph.pageFormat.height);if(this.graph.background!=null){t.setAttribute("background",this.graph.background)}return t};Editor.prototype.updateGraphComponents=function(){var e=this.graph;if(e.container!=null){e.view.validateBackground();e.container.style.overflow=e.scrollbars?"auto":this.defaultGraphOverflow;this.fireEvent(new mxEventObject("updateGraphComponents"))}};Editor.prototype.setModified=function(e){this.modified=e};Editor.prototype.setFilename=function(e){this.filename=e};Editor.prototype.createUndoManager=function(){var l=this.graph;var i=new mxUndoManager;this.undoListener=function(e,t){i.undoableEditHappened(t.getProperty("edit"))};var e=mxUtils.bind(this,function(e,t){this.undoListener.apply(this,arguments)});l.getModel().addListener(mxEvent.UNDO,e);l.getView().addListener(mxEvent.UNDO,e);var t=function(e,t){var i=l.getSelectionCellsForChanges(t.getProperty("edit").changes);var a=l.getModel();var n=[];for(var r=0;r<i.length;r++){if(l.view.getState(i[r])!=null){n.push(i[r])}}l.setSelectionCells(n)};i.addListener(mxEvent.UNDO,t);i.addListener(mxEvent.REDO,t);return i};Editor.prototype.initStencilRegistry=function(){};Editor.prototype.destroy=function(){if(this.graph!=null){this.graph.destroy();this.graph=null}};Editor.prototype.addFontCss=function(e,t){t=null!=t?t:this.fontCss;if(null!=t){var i=e.getElementsByTagName("defs"),a=e.ownerDocument;0==i.length?(i=null!=a.createElementNS?a.createElementNS(mxConstants.NS_SVG,"defs"):a.createElement("defs"),null!=e.firstChild?e.insertBefore(i,e.firstChild):e.appendChild(i)):i=i[0];a=null!=a.createElementNS?a.createElementNS(mxConstants.NS_SVG,"style"):a.createElement("style");a.setAttribute("type","text/css");mxUtils.setTextContent(a,t);i.appendChild(a)}};Editor.prototype.addMathCss=function(e){e=e.getElementsByTagName("defs");if(null!=e&&0<e.length)for(var t=document.getElementsByTagName("style"),i=0;i<t.length;i++)0<mxUtils.getTextContent(t[i]).indexOf("MathJax")&&e[0].appendChild(t[i].cloneNode(!0))};Editor.prototype.createImageUrlConverter=function(){var i=new mxUrlConverter;i.updateBaseUrl();var a=i.convert,n=this;i.convert=function(e){if(null!=e){var t="http://"==e.substring(0,7)||"https://"==e.substring(0,8);t&&!navigator.onLine?e=EditorUi.prototype.svgBrokenImage.src:!t||e.substring(0,i.baseUrl.length)==i.baseUrl||EditorUi.prototype.crossOriginImages&&n.isCorsEnabledForUrl(e)?"chrome-extension://"==e.substring(0,19)||mxClient.IS_CHROMEAPP||(e=a.apply(this,arguments)):e=PROXY_URL+"?url="+encodeURIComponent(e)}return e};return i};Editor.prototype.isCorsEnabledForUrl=function(e){if(mxClient.IS_CHROMEAPP||EditorUi.isElectronApp)return!0;null!=urlParams.cors&&null==this.corsRegExp&&(this.corsRegExp=new RegExp(decodeURIComponent(urlParams.cors)));return null!=this.corsRegExp&&this.corsRegExp.test(e)||true};Editor.prototype.timeout=25e3;OpenFile=function(e){this.producer=null;this.consumer=null;this.done=e;this.args=null};OpenFile.prototype.setConsumer=function(e){this.consumer=e;this.execute()};OpenFile.prototype.setData=function(){this.args=arguments;this.execute()};OpenFile.prototype.error=function(e){this.cancel(true);mxUtils.alert(e)};OpenFile.prototype.execute=function(){if(this.consumer!=null&&this.args!=null){this.cancel(false);this.consumer.apply(this,this.args)}};OpenFile.prototype.cancel=function(e){if(this.done!=null){this.done(e!=null?e:true)}};function Dialog(a,n,r,l,e,t,i,o,s,A,g){var d=0;if(mxClient.IS_VML&&(document.documentMode==null||document.documentMode<8)){d=80}r+=d;l+=d;var h=r;var p=l;var c=mxUtils.getDocumentSize();if(window.innerHeight!=null){c.height=window.innerHeight}var u=c.height;var m=Math.max(1,Math.round((c.width-r-64)/2));var v=Math.max(1,Math.round((u-l-a.footerHeight)/3));if(!mxClient.IS_QUIRKS){n.style.maxHeight="100%"}r=document.body!=null?Math.min(r,document.body.scrollWidth-64):r;l=Math.min(l,u-64);if(a.dialogs.length>0){this.zIndex+=a.dialogs.length*2}if(this.bg==null){this.bg=a.createDiv("background");this.bg.style.position="absolute";this.bg.style.background=Dialog.backdropColor;this.bg.style.height=u+"px";this.bg.style.right="0px";this.bg.style.zIndex=this.zIndex-2;mxUtils.setOpacity(this.bg,this.bgOpacity);if(mxClient.IS_QUIRKS){new mxDivResizer(this.bg)}}var E=mxUtils.getDocumentScrollOrigin(document);this.bg.style.left=E.x+"px";this.bg.style.top=E.y+"px";m+=E.x;v+=E.y;if(e){document.body.appendChild(this.bg)}var x=a.createDiv(s?"geTransDialog":"geDialog");var b=this.getPosition(m,v,r,l);m=b.x;v=b.y;x.style.width=r+"px";x.style.height=l+"px";x.style.left=m+"px";x.style.top=v+"px";x.style.zIndex=this.zIndex;x.appendChild(n);document.body.appendChild(x);if(!o&&n.clientHeight>x.clientHeight-64){n.style.overflowY="auto"}if(t){var M=document.createElement("img");M.setAttribute("src",Dialog.prototype.closeImage);M.setAttribute("title",mxResources.get("close"));M.className="geDialogClose";M.style.top=v+14+"px";M.style.left=m+r+38-d+"px";M.style.zIndex=this.zIndex;mxEvent.addListener(M,"click",mxUtils.bind(this,function(){a.hideDialog(true)}));document.body.appendChild(M);this.dialogImg=M;if(!g){var y=false;mxEvent.addGestureListeners(this.bg,mxUtils.bind(this,function(e){y=true}),null,mxUtils.bind(this,function(e){if(y){a.hideDialog(true);y=false}}))}}this.resizeListener=mxUtils.bind(this,function(){if(!mxUtils){return}if(A!=null){var e=A();if(e!=null){h=r=e.w;p=l=e.h}}var t=mxUtils.getDocumentSize();u=t.height;this.bg.style.height=u+"px";m=Math.max(1,Math.round((t.width-r-64)/2));v=Math.max(1,Math.round((u-l-a.footerHeight)/3));r=document.body!=null?Math.min(h,document.body.scrollWidth-64):h;l=Math.min(p,u-64);var i=this.getPosition(m,v,r,l);m=i.x;v=i.y;x.style.left=m+"px";x.style.top=v+"px";x.style.width=r+"px";x.style.height=l+"px";if(!o&&n.clientHeight>x.clientHeight-64){n.style.overflowY="auto"}if(this.dialogImg!=null){this.dialogImg.style.top=v+14+"px";this.dialogImg.style.left=m+r+38-d+"px"}});mxEvent.addListener(window,"resize",this.resizeListener);this.onDialogClose=i;this.container=x;a.editor.fireEvent(new mxEventObject("showDialog"))}Dialog.backdropColor="white";Dialog.prototype.zIndex=mxPopupMenu.prototype.zIndex-1;Dialog.prototype.noColorImage=!mxClient.IS_SVG?IMAGE_PATH+"/nocolor.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkEzRDlBMUUwODYxMTExRTFCMzA4RDdDMjJBMEMxRDM3IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkEzRDlBMUUxODYxMTExRTFCMzA4RDdDMjJBMEMxRDM3Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QTNEOUExREU4NjExMTFFMUIzMDhEN0MyMkEwQzFEMzciIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QTNEOUExREY4NjExMTFFMUIzMDhEN0MyMkEwQzFEMzciLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5xh3fmAAAABlBMVEX////MzMw46qqDAAAAGElEQVR42mJggAJGKGAYIIGBth8KAAIMAEUQAIElnLuQAAAAAElFTkSuQmCC";Dialog.prototype.closeImage=!mxClient.IS_SVG?IMAGE_PATH+"/close.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAJAQMAAADaX5RTAAAABlBMVEV7mr3///+wksspAAAAAnRSTlP/AOW3MEoAAAAdSURBVAgdY9jXwCDDwNDRwHCwgeExmASygSL7GgB12QiqNHZZIwAAAABJRU5ErkJggg==";Dialog.prototype.clearImage=!mxClient.IS_SVG?IMAGE_PATH+"/clear.gif":"data:image/gif;base64,R0lGODlhDQAKAIABAMDAwP///yH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6OUIzOEM1NzI4NjEyMTFFMUEzMkNDMUE3NjZERDE2QjIiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6OUIzOEM1NzM4NjEyMTFFMUEzMkNDMUE3NjZERDE2QjIiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo5QjM4QzU3MDg2MTIxMUUxQTMyQ0MxQTc2NkREMTZCMiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo5QjM4QzU3MTg2MTIxMUUxQTMyQ0MxQTc2NkREMTZCMiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PgH//v38+/r5+Pf29fTz8vHw7+7t7Ovq6ejn5uXk4+Lh4N/e3dzb2tnY19bV1NPS0dDPzs3My8rJyMfGxcTDwsHAv769vLu6ubi3trW0s7KxsK+urayrqqmop6alpKOioaCfnp2cm5qZmJeWlZSTkpGQj46NjIuKiYiHhoWEg4KBgH9+fXx7enl4d3Z1dHNycXBvbm1sa2ppaGdmZWRjYmFgX15dXFtaWVhXVlVUU1JRUE9OTUxLSklIR0ZFRENCQUA/Pj08Ozo5ODc2NTQzMjEwLy4tLCsqKSgnJiUkIyIhIB8eHRwbGhkYFxYVFBMSERAPDg0MCwoJCAcGBQQDAgEAACH5BAEAAAEALAAAAAANAAoAAAIXTGCJebD9jEOTqRlttXdrB32PJ2ncyRQAOw==";Dialog.prototype.lockedImage=!mxClient.IS_SVG?IMAGE_PATH+"/locked.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAMAAABhq6zVAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBNYWNpbnRvc2giIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MzdDMDZCODExNzIxMTFFNUI0RTk5NTg4OTcyMUUyODEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MzdDMDZCODIxNzIxMTFFNUI0RTk5NTg4OTcyMUUyODEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDozN0MwNkI3RjE3MjExMUU1QjRFOTk1ODg5NzIxRTI4MSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDozN0MwNkI4MDE3MjExMUU1QjRFOTk1ODg5NzIxRTI4MSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PvqMCFYAAAAVUExURZmZmb+/v7KysqysrMzMzLGxsf///4g8N1cAAAAHdFJOU////////wAaSwNGAAAAPElEQVR42lTMQQ4AIQgEwUa0//9kTQirOweYOgDqAMbZUr10AGlAwx4/BJ2QJ4U0L5brYjovvpv32xZgAHZaATFtMbu4AAAAAElFTkSuQmCC";Dialog.prototype.unlockedImage=!mxClient.IS_SVG?IMAGE_PATH+"/unlocked.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAMAAABhq6zVAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBNYWNpbnRvc2giIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MzdDMDZCN0QxNzIxMTFFNUI0RTk5NTg4OTcyMUUyODEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MzdDMDZCN0UxNzIxMTFFNUI0RTk5NTg4OTcyMUUyODEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDozN0MwNkI3QjE3MjExMUU1QjRFOTk1ODg5NzIxRTI4MSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDozN0MwNkI3QzE3MjExMUU1QjRFOTk1ODg5NzIxRTI4MSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PkKMpVwAAAAYUExURZmZmbKysr+/v6ysrOXl5czMzLGxsf///zHN5lwAAAAIdFJOU/////////8A3oO9WQAAADxJREFUeNpUzFESACAEBNBVsfe/cZJU+8Mzs8CIABCidtfGOndnYsT40HDSiCcbPdoJo10o9aI677cpwACRoAF3dFNlswAAAABJRU5ErkJggg==";Dialog.prototype.bgOpacity=80;Dialog.prototype.getPosition=function(e,t){return new mxPoint(e,t)};Dialog.prototype.close=function(e,t){if(this.onDialogClose!=null){if(this.onDialogClose(e,t)==false){return false}this.onDialogClose=null}if(this.dialogImg!=null){this.dialogImg.parentNode.removeChild(this.dialogImg);this.dialogImg=null}if(this.bg!=null&&this.bg.parentNode!=null){this.bg.parentNode.removeChild(this.bg)}mxEvent.removeListener(window,"resize",this.resizeListener);this.container.parentNode.removeChild(this.container)};var ErrorDialog=function(e,t,i,a,n,r,l,o,s,A,g){s=s!=null?s:true;var d=document.createElement("div");d.style.textAlign="center";if(t!=null){var h=document.createElement("div");h.style.padding="0px";h.style.margin="0px";h.style.fontSize="18px";h.style.paddingBottom="16px";h.style.marginBottom="10px";h.style.borderBottom="1px solid #c0c0c0";h.style.color="gray";h.style.whiteSpace="nowrap";h.style.textOverflow="ellipsis";h.style.overflow="hidden";mxUtils.write(h,t);h.setAttribute("title",t);d.appendChild(h)}var p=document.createElement("div");p.style.lineHeight="1.2em";p.style.padding="6px";p.innerHTML=i;d.appendChild(p);var c=document.createElement("div");c.style.marginTop="12px";c.style.textAlign="center";if(r!=null){var u=mxUtils.button(mxResources.get("tryAgain"),function(){e.hideDialog();r()});u.className="geBtn";c.appendChild(u);c.style.textAlign="center"}if(A!=null){var m=mxUtils.button(A,function(){if(g!=null){g()}});m.className="geBtn";c.appendChild(m)}var v=mxUtils.button(a,function(){if(s){e.hideDialog()}if(n!=null){n()}});v.className="geBtn";c.appendChild(v);if(l!=null){var E=mxUtils.button(l,function(){if(s){e.hideDialog()}if(o!=null){o()}});E.className="geBtn gePrimaryBtn";c.appendChild(E)}this.init=function(){v.focus()};d.appendChild(c);this.container=d};var PrintDialog=function(e,t){this.create(e,t)};PrintDialog.prototype.create=function(e){var h=e.editor.graph;var t,i;var a=document.createElement("table");a.style.width="100%";a.style.height="100%";var n=document.createElement("tbody");t=document.createElement("tr");var p=document.createElement("input");p.setAttribute("type","checkbox");i=document.createElement("td");i.setAttribute("colspan","2");i.style.fontSize="10pt";i.appendChild(p);var r=document.createElement("span");mxUtils.write(r," "+mxResources.get("fitPage"));i.appendChild(r);mxEvent.addListener(r,"click",function(e){p.checked=!p.checked;c.checked=!p.checked;mxEvent.consume(e)});mxEvent.addListener(p,"change",function(){c.checked=!p.checked});t.appendChild(i);n.appendChild(t);t=t.cloneNode(false);var c=document.createElement("input");c.setAttribute("type","checkbox");i=document.createElement("td");i.style.fontSize="10pt";i.appendChild(c);var r=document.createElement("span");mxUtils.write(r," "+mxResources.get("posterPrint")+":");i.appendChild(r);mxEvent.addListener(r,"click",function(e){c.checked=!c.checked;p.checked=!c.checked;mxEvent.consume(e)});t.appendChild(i);var u=document.createElement("input");u.setAttribute("value","1");u.setAttribute("type","number");u.setAttribute("min","1");u.setAttribute("size","4");u.setAttribute("disabled","disabled");u.style.width="50px";i=document.createElement("td");i.style.fontSize="10pt";i.appendChild(u);mxUtils.write(i," "+mxResources.get("pages")+" (max)");t.appendChild(i);n.appendChild(t);mxEvent.addListener(c,"change",function(){if(c.checked){u.removeAttribute("disabled")}else{u.setAttribute("disabled","disabled")}p.checked=!c.checked});t=t.cloneNode(false);i=document.createElement("td");mxUtils.write(i,mxResources.get("pageScale")+":");t.appendChild(i);i=document.createElement("td");var m=document.createElement("input");m.setAttribute("value","100 %");m.setAttribute("size","5");m.style.width="50px";i.appendChild(m);t.appendChild(i);n.appendChild(t);t=document.createElement("tr");i=document.createElement("td");i.colSpan=2;i.style.paddingTop="20px";i.setAttribute("align","right");function l(e){var t=p.checked||c.checked;var i=parseInt(m.value)/100;if(isNaN(i)){i=1;m.value="100%"}i*=.75;var a=h.pageFormat||mxConstants.PAGE_FORMAT_A4_PORTRAIT;var n=1/h.pageScale;if(t){var r=p.checked?1:parseInt(u.value);if(!isNaN(r)){n=mxUtils.getScaleForPageCount(r,h,a)}}var l=h.getGraphBounds();var o=0;var s=0;var A=0;a=mxRectangle.fromRectangle(a);a.width=Math.ceil(a.width*i);a.height=Math.ceil(a.height*i);n*=i;if(!t&&h.pageVisible){var g=h.getPageLayout();s-=g.x*a.width;A-=g.y*a.height}else{t=true}var d=PrintDialog.createPrintPreview(h,n,a,o,s,A,t);d.open();if(e){PrintDialog.printPreview(d)}}var o=mxUtils.button(mxResources.get("cancel"),function(){e.hideDialog()});o.className="geBtn";if(e.editor.cancelFirst){i.appendChild(o)}if(PrintDialog.previewEnabled){var s=mxUtils.button(mxResources.get("preview"),function(){e.hideDialog();l(false)});s.className="geBtn";i.appendChild(s)}var A=mxUtils.button(mxResources.get(!PrintDialog.previewEnabled?"ok":"print"),function(){e.hideDialog();l(true)});A.className="geBtn gePrimaryBtn";i.appendChild(A);if(!e.editor.cancelFirst){i.appendChild(o)}t.appendChild(i);n.appendChild(t);a.appendChild(n);this.container=a};PrintDialog.printPreview=function(e){try{if(e.wnd!=null){var t=function(){e.wnd.focus();e.wnd.print();e.wnd.close()};if(mxClient.IS_GC){window.setTimeout(t,500)}else{t()}}}catch(e){}};PrintDialog.createPrintPreview=function(e,t,i,a,n,r,l){var o=new mxPrintPreview(e,t,i,a,n,r);o.title=mxResources.get("preview");o.printBackgroundImage=true;o.autoOrigin=l;var s=e.background;if(s==null||s==""||s==mxConstants.NONE){s="#ffffff"}o.backgroundColor=s;var A=o.writeHead;o.writeHead=function(e){A.apply(this,arguments);e.writeln('<style type="text/css">');e.writeln("@media screen {");e.writeln("  body > div { padding:30px;box-sizing:content-box; }");e.writeln("}");e.writeln("</style>")};return o};PrintDialog.previewEnabled=true;var PageSetupDialog=function(a){var n=a.editor.graph;var e,t;var i=document.createElement("table");i.style.width="100%";i.style.height="100%";var r=document.createElement("tbody");e=document.createElement("tr");t=document.createElement("td");t.style.verticalAlign="top";t.style.fontSize="10pt";mxUtils.write(t,mxResources.get("paperSize")+":");e.appendChild(t);t=document.createElement("td");t.style.verticalAlign="top";t.style.fontSize="10pt";var l=PageSetupDialog.addPageFormatPanel(t,"pagesetupdialog",n.pageFormat);e.appendChild(t);r.appendChild(e);e=document.createElement("tr");t=document.createElement("td");mxUtils.write(t,mxResources.get("background")+":");e.appendChild(t);t=document.createElement("td");t.style.whiteSpace="nowrap";var o=document.createElement("input");o.setAttribute("type","text");var s=document.createElement("button");s.style.width="18px";s.style.height="18px";s.style.marginRight="20px";s.style.backgroundPosition="center center";s.style.backgroundRepeat="no-repeat";var A=n.background;function g(){if(A==null||A==mxConstants.NONE){s.style.backgroundColor="";s.style.backgroundImage="url('"+Dialog.prototype.noColorImage+"')"}else{s.style.backgroundColor=A;s.style.backgroundImage=""}}g();mxEvent.addListener(s,"click",function(e){a.pickColor(A||"none",function(e){A=e;g()});mxEvent.consume(e)});t.appendChild(s);mxUtils.write(t,mxResources.get("gridSize")+":");var d=document.createElement("input");d.setAttribute("type","number");d.setAttribute("min","0");d.style.width="40px";d.style.marginLeft="6px";d.value=n.getGridSize();t.appendChild(d);mxEvent.addListener(d,"change",function(){var e=parseInt(d.value);d.value=Math.max(1,isNaN(e)?n.getGridSize():e)});e.appendChild(t);r.appendChild(e);e=document.createElement("tr");t=document.createElement("td");mxUtils.write(t,mxResources.get("image")+":");e.appendChild(t);t=document.createElement("td");var h=document.createElement("a");h.style.textDecoration="underline";h.style.cursor="pointer";h.style.color="#a0a0a0";var p=n.backgroundImage;function c(){if(p==null){h.removeAttribute("title");h.style.fontSize="";h.innerHTML=mxResources.get("change")+"..."}else{h.setAttribute("title",p.src);h.style.fontSize="11px";h.innerHTML=p.src.substring(0,42)+"..."}}mxEvent.addListener(h,"click",function(e){a.showBackgroundImageDialog(function(e){p=e;c()});mxEvent.consume(e)});c();t.appendChild(h);e.appendChild(t);r.appendChild(e);e=document.createElement("tr");t=document.createElement("td");t.colSpan=2;t.style.paddingTop="16px";t.setAttribute("align","right");var u=mxUtils.button(mxResources.get("cancel"),function(){a.hideDialog()});u.className="geBtn";if(a.editor.cancelFirst){t.appendChild(u)}var m=mxUtils.button(mxResources.get("apply"),function(){a.hideDialog();if(n.gridSize!==d.value){n.setGridSize(parseInt(d.value))}var e=new ChangePageSetup(a,A,p,l.get());e.ignoreColor=n.background==A;var t=n.backgroundImage!=null?n.backgroundImage.src:null;var i=p!=null?p.src:null;e.ignoreImage=t===i;if(n.pageFormat.width!=e.previousFormat.width||n.pageFormat.height!=e.previousFormat.height||!e.ignoreColor||!e.ignoreImage){n.model.execute(e)}});m.className="geBtn gePrimaryBtn";t.appendChild(m);if(!a.editor.cancelFirst){t.appendChild(u)}e.appendChild(t);r.appendChild(e);i.appendChild(r);this.container=i};PageSetupDialog.addPageFormatPanel=function(e,t,l,o){var i="format-"+t;var s=document.createElement("input");s.setAttribute("name",i);s.setAttribute("type","radio");s.setAttribute("value","portrait");var A=document.createElement("input");A.setAttribute("name",i);A.setAttribute("type","radio");A.setAttribute("value","landscape");var g=document.createElement("select");g.style.marginBottom="8px";g.style.width="202px";var d=document.createElement("div");d.style.marginLeft="4px";d.style.width="210px";d.style.height="24px";s.style.marginRight="6px";d.appendChild(s);var a=document.createElement("span");a.style.maxWidth="100px";mxUtils.write(a,mxResources.get("portrait"));d.appendChild(a);A.style.marginLeft="10px";A.style.marginRight="6px";d.appendChild(A);var n=document.createElement("span");n.style.width="100px";mxUtils.write(n,mxResources.get("landscape"));d.appendChild(n);var h=document.createElement("div");h.style.marginLeft="4px";h.style.width="210px";h.style.height="24px";var p=document.createElement("input");p.setAttribute("size","7");p.style.textAlign="right";h.appendChild(p);mxUtils.write(h," in x ");var c=document.createElement("input");c.setAttribute("size","7");c.style.textAlign="right";h.appendChild(c);mxUtils.write(h," in");d.style.display="none";h.style.display="none";var u=new Object;var m=PageSetupDialog.getFormats();for(var r=0;r<m.length;r++){var v=m[r];u[v.key]=v;var E=document.createElement("option");E.setAttribute("value",v.key);mxUtils.write(E,v.title);g.appendChild(E)}var x=false;function b(e,t,i){if(i||p!=document.activeElement&&c!=document.activeElement){var a=false;for(var n=0;n<m.length;n++){var r=m[n];if(x){if(r.key=="custom"){g.value=r.key;x=false}}else if(r.format!=null){if(r.key=="a4"){if(l.width==826){l=mxRectangle.fromRectangle(l);l.width=827}else if(l.height==826){l=mxRectangle.fromRectangle(l);l.height=827}}else if(r.key=="a5"){if(l.width==584){l=mxRectangle.fromRectangle(l);l.width=583}else if(l.height==584){l=mxRectangle.fromRectangle(l);l.height=583}}if(l.width==r.format.width&&l.height==r.format.height){g.value=r.key;s.setAttribute("checked","checked");s.defaultChecked=true;s.checked=true;A.removeAttribute("checked");A.defaultChecked=false;A.checked=false;a=true}else if(l.width==r.format.height&&l.height==r.format.width){g.value=r.key;s.removeAttribute("checked");s.defaultChecked=false;s.checked=false;A.setAttribute("checked","checked");A.defaultChecked=true;A.checked=true;a=true}}}if(!a){p.value=l.width/100;c.value=l.height/100;s.setAttribute("checked","checked");g.value="custom";d.style.display="none";h.style.display=""}else{d.style.display="";h.style.display="none"}}}b();e.appendChild(g);mxUtils.br(e);e.appendChild(d);e.appendChild(h);var M=l;var y=function(e,t){var i=u[g.value];if(i.format!=null){p.value=i.format.width/100;c.value=i.format.height/100;h.style.display="none";d.style.display=""}else{d.style.display="none";h.style.display=""}var a=parseFloat(p.value);if(isNaN(a)||a<=0){p.value=l.width/100}var n=parseFloat(c.value);if(isNaN(n)||n<=0){c.value=l.height/100}var r=new mxRectangle(0,0,Math.floor(parseFloat(p.value)*100),Math.floor(parseFloat(c.value)*100));if(g.value!="custom"&&A.checked){r=new mxRectangle(0,0,r.height,r.width)}if((!t||!x)&&(r.width!=M.width||r.height!=M.height)){M=r;if(o!=null){o(M)}}};mxEvent.addListener(a,"click",function(e){s.checked=true;y(e);mxEvent.consume(e)});mxEvent.addListener(n,"click",function(e){A.checked=true;y(e);mxEvent.consume(e)});mxEvent.addListener(p,"blur",y);mxEvent.addListener(p,"click",y);mxEvent.addListener(c,"blur",y);mxEvent.addListener(c,"click",y);mxEvent.addListener(A,"change",y);mxEvent.addListener(s,"change",y);mxEvent.addListener(g,"change",function(e){x=g.value=="custom";y(e,true)});y();return{set:function(e){l=e;b(null,null,true)},get:function(){return M},widthInput:p,heightInput:c}};PageSetupDialog.getFormats=function(){return[{key:"letter",title:'US-Letter (8,5" x 11")',format:mxConstants.PAGE_FORMAT_LETTER_PORTRAIT},{key:"legal",title:'US-Legal (8,5" x 14")',format:new mxRectangle(0,0,850,1400)},{key:"tabloid",title:'US-Tabloid (11" x 17")',format:new mxRectangle(0,0,1100,1700)},{key:"executive",title:'US-Executive (7" x 10")',format:new mxRectangle(0,0,700,1e3)},{key:"a0",title:"A0 (841 mm x 1189 mm)",format:new mxRectangle(0,0,3300,4681)},{key:"a1",title:"A1 (594 mm x 841 mm)",format:new mxRectangle(0,0,2339,3300)},{key:"a2",title:"A2 (420 mm x 594 mm)",format:new mxRectangle(0,0,1654,2336)},{key:"a3",title:"A3 (297 mm x 420 mm)",format:new mxRectangle(0,0,1169,1654)},{key:"a4",title:"A4 (210 mm x 297 mm)",format:mxConstants.PAGE_FORMAT_A4_PORTRAIT},{key:"a5",title:"A5 (148 mm x 210 mm)",format:new mxRectangle(0,0,583,827)},{key:"a6",title:"A6 (105 mm x 148 mm)",format:new mxRectangle(0,0,413,583)},{key:"a7",title:"A7 (74 mm x 105 mm)",format:new mxRectangle(0,0,291,413)},{key:"b4",title:"B4 (250 mm x 353 mm)",format:new mxRectangle(0,0,980,1390)},{key:"b5",title:"B5 (176 mm x 250 mm)",format:new mxRectangle(0,0,690,980)},{key:"16-9",title:"16:9 (1600 x 900)",format:new mxRectangle(0,0,1600,900)},{key:"16-10",title:"16:10 (1920 x 1200)",format:new mxRectangle(0,0,1920,1200)},{key:"4-3",title:"4:3 (1600 x 1200)",format:new mxRectangle(0,0,1600,1200)},{key:"custom",title:mxResources.get("custom"),format:null}]};(function(){mxGraphView.prototype.validateBackgroundPage=function(){var t=this.graph;if(t.container!=null&&!t.transparentBackground){if(t.pageVisible||t.backgroundVisible){var e=this.getBackgroundPageBounds();if(t===window.graph){}if(this.backgroundPageShape==null){var i=t.container.firstChild;while(i!=null&&i.nodeType!=mxConstants.NODETYPE_ELEMENT){i=i.nextSibling}if(i!=null){this.backgroundPageShape=this.createBackgroundPageShape(e,t);this.backgroundPageShape.scale=1;this.backgroundPageShape.isShadow=!mxClient.IS_QUIRKS;this.backgroundPageShape.dialect=mxConstants.DIALECT_STRICTHTML;this.backgroundPageShape.init(t.container);i.style.position="absolute";t.container.insertBefore(this.backgroundPageShape.node,i);this.backgroundPageShape.redraw();this.backgroundPageShape.node.className="geBackgroundPage";mxEvent.addListener(this.backgroundPageShape.node,"dblclick",mxUtils.bind(this,function(e){t.dblClick(e)}));mxEvent.addGestureListeners(this.backgroundPageShape.node,mxUtils.bind(this,function(e){t.fireMouseEvent(mxEvent.MOUSE_DOWN,new mxMouseEvent(e))}),mxUtils.bind(this,function(e){if(!mxUtils||!mxEvent){return}if(t.tooltipHandler!=null&&t.tooltipHandler.isHideOnHover()){t.tooltipHandler.hide()}if(t.isMouseDown&&!mxEvent.isConsumed(e)){t.fireMouseEvent(mxEvent.MOUSE_MOVE,new mxMouseEvent(e))}}),mxUtils.bind(this,function(e){t.fireMouseEvent(mxEvent.MOUSE_UP,new mxMouseEvent(e))}))}}else{this.backgroundPageShape.scale=1;this.backgroundPageShape.bounds=e;this.backgroundPageShape.redraw()}}else if(this.backgroundPageShape!=null){this.backgroundPageShape.destroy();this.backgroundPageShape=null}this.validateBackgroundStyles()}};mxGraphView.prototype.validateBackgroundStyles=function(){var e=this.graph;var t=e.background==null||e.background==mxConstants.NONE?e.defaultPageBackgroundColor:e.background;var i=t!=null&&this.gridColor!=t.toLowerCase()?this.gridColor:"#ffffff";var a="none";var n="";var r=e.backgroundDomImage;if(e.isGridEnabled()){var l=10;if(mxClient.IS_SVG){a=unescape(encodeURIComponent(this.createSvgGrid(i)));a=window.btoa?btoa(a):Base64.encode(a,true);a="url("+"data:image/svg+xml;base64,"+a+")";l=e.gridSize*this.scale*this.gridSteps}else{a="url("+this.gridImage+")"}var o=0;var s=0;if(e.view.backgroundPageShape!=null){var A=this.getBackgroundPageBounds();o=1+A.x;s=1+A.y}n=-Math.round(l-mxUtils.mod(this.translate.x*this.scale-o,l))+"px "+-Math.round(l-mxUtils.mod(this.translate.y*this.scale-s,l))+"px"}var g=e.view.canvas;if(g.ownerSVGElement!=null){g=g.ownerSVGElement}if(e.view.backgroundPageShape!=null){e.view.backgroundPageShape.node.style.backgroundPosition=n;e.view.backgroundPageShape.node.style.backgroundImage=a;e.view.backgroundPageShape.node.style.backgroundColor=t;if(r){if(!e.view.backgroundPageShape.node.bgNode){var d=document.createElement("div");e.view.backgroundPageShape.node.bgNode=d;d.style.cssText="position:absolute;width:100%;height:100%;"}var d=e.view.backgroundPageShape.node.bgNode;if(!d.parentElement){e.view.backgroundPageShape.node.appendChild(d)}var h=false;for(key in r){if(Object.prototype.hasOwnProperty.call(d.style,key)){h=true;d.style[key]=r[key]}}if(!h){d.parentElement.removeChild(d);e.view.backgroundPageShape.node.bgNode=null}}else{if(e.view.backgroundPageShape.node&&e.view.backgroundPageShape.node.bgNode){var d=e.view.backgroundPageShape.node.bgNode;if(d.parentElement){d.parentElement.removeChild(d);e.view.backgroundPageShape.node.bgNode=null}}}e.container.className="geDiagramContainer geDiagramBackdrop";g.style.backgroundImage="none";g.style.backgroundColor=""}else{e.container.className="geDiagramContainer";g.style.backgroundPosition=n;g.style.backgroundColor=t;g.style.backgroundImage=a}};mxGraphView.prototype.createSvgGrid=function(e){var t=this.graph.gridSize*this.scale;while(t<this.minGridSize){t*=2}var i=this.gridSteps*t;var a=[];for(var n=1;n<this.gridSteps;n++){var r=n*t;a.push("M 0 "+r+" L "+i+" "+r+" M "+r+" 0 L "+r+" "+i)}var l=i;var o='<svg width="'+l+'" height="'+l+'" xmlns="'+mxConstants.NS_SVG+'">'+'<defs><pattern id="grid" width="'+i+'" height="'+i+'" patternUnits="userSpaceOnUse">'+'<path d="'+a.join(" ")+'" fill="none" stroke="'+e+'" opacity="0.2" stroke-width="1"/>'+'<path d="M '+i+" 0 L 0 0 0 "+i+'" fill="none" stroke="'+e+'" stroke-width="1"/>'+'</pattern></defs><rect width="100%" height="100%" fill="url(#grid)"/></svg>';return o};var r=mxGraph.prototype.panGraph;mxGraph.prototype.panGraph=function(e,t){r.apply(this,arguments);if(this.shiftPreview1!=null){var i=this.view.canvas;if(i.ownerSVGElement!=null){i=i.ownerSVGElement}var a=this.gridSize*this.view.scale*this.view.gridSteps;var n=-Math.round(a-mxUtils.mod(this.view.translate.x*this.view.scale+e,a))+"px "+-Math.round(a-mxUtils.mod(this.view.translate.y*this.view.scale+t,a))+"px";i.style.backgroundPosition=n}};mxGraph.prototype.updatePageBreaks=function(e,t,i){var a=this.view.scale;var n=this.view.translate;var r=this.pageFormat;var l=a*this.pageScale;var o=this.view.getBackgroundPageBounds();t=o.width;i=o.height;var s=new mxRectangle(a*n.x,a*n.y,r.width*l,r.height*l);e=e&&Math.min(s.width,s.height)>this.minPageBreakDist;var A=e?Math.ceil(i/s.height)-1:0;var g=e?Math.ceil(t/s.width)-1:0;var d=o.x+t;var h=o.y+i;if(this.horizontalPageBreaks==null&&A>0){this.horizontalPageBreaks=[]}if(this.verticalPageBreaks==null&&g>0){this.verticalPageBreaks=[]}var p=mxUtils.bind(this,function(e){if(e!=null){var t=e==this.horizontalPageBreaks?A:g;for(var i=0;i<=t;i++){var a=e==this.horizontalPageBreaks?[new mxPoint(Math.round(o.x),Math.round(o.y+(i+1)*s.height)),new mxPoint(Math.round(d),Math.round(o.y+(i+1)*s.height))]:[new mxPoint(Math.round(o.x+(i+1)*s.width),Math.round(o.y)),new mxPoint(Math.round(o.x+(i+1)*s.width),Math.round(h))];if(e[i]!=null){e[i].points=a;e[i].redraw()}else{var n=new mxPolyline(a,this.pageBreakColor);n.dialect=this.dialect;n.isDashed=this.pageBreakDashed;n.pointerEvents=false;n.init(this.view.backgroundPane);n.redraw();e[i]=n}}for(var i=t;i<e.length;i++){e[i].destroy()}e.splice(t,e.length-t)}});p(this.horizontalPageBreaks);p(this.verticalPageBreaks)};var l=mxGraphHandler.prototype.shouldRemoveCellsFromParent;mxGraphHandler.prototype.shouldRemoveCellsFromParent=function(e,t,i){for(var a=0;a<t.length;a++){if(this.graph.getModel().isVertex(t[a])){var n=this.graph.getCellGeometry(t[a]);if(n!=null&&n.relative){return false}}}return l.apply(this,arguments)};var e=mxConnectionHandler.prototype.createMarker;mxConnectionHandler.prototype.createMarker=function(){var i=e.apply(this,arguments);i.intersects=mxUtils.bind(this,function(e,t){if(this.isConnecting()){return true}return mxCellMarker.prototype.intersects.apply(i,arguments)});return i};mxGraphView.prototype.createBackgroundPageShape=function(e,t){return new mxRectangleShape(e,"#ffffff",this.graph.defaultPageBorderColor,undefined,t)};mxGraphView.prototype.getBackgroundPageBounds=function(){var e=this.getGraphBounds();var t=e.width>0?e.x/this.scale-this.translate.x:0;var i=e.height>0?e.y/this.scale-this.translate.y:0;var a=e.width/this.scale;var n=e.height/this.scale;var r=this.graph.pageFormat;var l=this.graph.pageScale;var o=r.width*l;var s=r.height*l;var A=Math.floor(Math.min(0,t)/o);var g=Math.floor(Math.min(0,i)/s);var d=Math.ceil(Math.max(1,t+a)/o);var h=Math.ceil(Math.max(1,i+n)/s);if(this.graph.pageViewType){A=0;g=0}var p=d-A;var c=h-g;var u=new mxRectangle(this.scale*(this.translate.x+A*o),this.scale*(this.translate.y+g*s),this.scale*p*o,this.scale*c*s);return u};var i=mxGraph.prototype.panGraph;mxGraph.prototype.panGraph=function(e,t){i.apply(this,arguments);if(this.dialect!=mxConstants.DIALECT_SVG&&this.view.backgroundPageShape!=null&&(!this.useScrollbarsForPanning||!mxUtils.hasScrollbars(this.container))){this.view.backgroundPageShape.node.style.marginLeft=e+"px";this.view.backgroundPageShape.node.style.marginTop=t+"px"}};var o=mxPopupMenu.prototype.addItem;mxPopupMenu.prototype.addItem=function(e,t,i,a,n,r){var l=o.apply(this,arguments);if(r!=null&&!r){mxEvent.addListener(l,"mousedown",function(e){mxEvent.consume(e)})}return l};var s=mxGraphHandler.prototype.getInitialCellForEvent;mxGraphHandler.prototype.getInitialCellForEvent=function(e){var t=this.graph.getModel();var i=t.getParent(this.graph.getSelectionCell());var a=s.apply(this,arguments);var n=t.getParent(a);if(i==null||i!=a&&i!=n){while(!this.graph.isCellSelected(a)&&!this.graph.isCellSelected(n)&&t.isVertex(n)&&!this.graph.isContainer(n)){a=n;n=this.graph.getModel().getParent(a)}}return a};var A=mxGraphHandler.prototype.isDelayedSelection;mxGraphHandler.prototype.isDelayedSelection=function(e,t){var i=A.apply(this,arguments);if(!i){var a=this.graph.getModel();var n=a.getParent(e);while(n!=null){if(this.graph.isCellSelected(n)&&a.isVertex(n)){i=true;break}n=a.getParent(n)}}return i};mxGraphHandler.prototype.selectDelayed=function(e){if(!this.graph.popupMenuHandler.isPopupTrigger(e)){var t=e.getCell();if(t==null){t=this.cell}var i=this.graph.view.getState(t);if(i!=null&&e.isSource(i.control)){this.graph.selectCellForEvent(t,e.getEvent())}else{var a=this.graph.getModel();var n=a.getParent(t);while(!this.graph.isCellSelected(n)&&a.isVertex(n)){t=n;n=a.getParent(t)}this.graph.selectCellForEvent(t,e.getEvent())}}};mxPopupMenuHandler.prototype.getCellForPopupEvent=function(e){var t=e.getCell();var i=this.graph.getModel();var a=i.getParent(t);while(i.isVertex(a)&&!this.graph.isContainer(a)){if(this.graph.isCellSelected(a)){t=a}a=i.getParent(a)}return t}})();