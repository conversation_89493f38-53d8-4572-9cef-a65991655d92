if(typeof html4!=="undefined"){html4.ATTRIBS["a::target"]=0;html4.ATTRIBS["source::src"]=0;html4.ATTRIBS["video::src"]=0}(function(){var i=[["nbsp","160"],["shy","173"]];var n=mxUtils.parseXml;mxUtils.parseXml=function(t){for(var e=0;e<i.length;e++){t=t.replace(new RegExp("&"+i[e][0]+";","g"),"&#"+i[e][1]+";")}return n(t)}})();if(!Date.prototype.toISOString){(function(){function t(t){var e=String(t);if(e.length===1){e="0"+e}return e}Date.prototype.toISOString=function(){return this.getUTCFullYear()+"-"+t(this.getUTCMonth()+1)+"-"+t(this.getUTCDate())+"T"+t(this.getUTCHours())+":"+t(this.getUTCMinutes())+":"+t(this.getUTCSeconds())+"."+String((this.getUTCMilliseconds()/1e3).toFixed(3)).slice(2,5)+"Z"}})()}if(!Date.now){Date.now=function(){return(new Date).getTime()}}mxConstants.POINTS=1;mxConstants.MILLIMETERS=2;mxConstants.INCHES=3;mxConstants.PIXELS_PER_MM=3.937;mxConstants.PIXELS_PER_INCH=100;mxConstants.SHADOW_OPACITY=.25;mxConstants.SHADOWCOLOR="#000000";mxConstants.VML_SHADOWCOLOR="#d0d0d0";mxGraph.prototype.pageBreakColor="#c0c0c0";mxGraph.prototype.pageScale=1;(function(){try{if(navigator!=null&&navigator.language!=null){var t=navigator.language.toLowerCase();mxGraph.prototype.pageFormat=t==="en-us"||t==="en-ca"||t==="es-mx"?mxConstants.PAGE_FORMAT_LETTER_PORTRAIT:mxConstants.PAGE_FORMAT_A4_PORTRAIT}}catch(t){}})();mxText.prototype.baseSpacingTop=5;mxText.prototype.baseSpacingBottom=1;mxGraphModel.prototype.ignoreRelativeEdgeParent=false;mxGraphView.prototype.gridImage=mxClient.IS_SVG?"data:image/gif;base64,R0lGODlhCgAKAJEAAAAAAP///8zMzP///yH5BAEAAAMALAAAAAAKAAoAAAIJ1I6py+0Po2wFADs=":IMAGE_PATH+"/grid.gif";mxGraphView.prototype.gridSteps=4;mxGraphView.prototype.minGridSize=4;mxGraphView.prototype.defaultGridColor="#d0d0d0";mxGraphView.prototype.gridColor=mxGraphView.prototype.defaultGridColor;mxGraphView.prototype.unit=mxConstants.POINTS;mxGraphView.prototype.setUnit=function(t){if(this.unit!=t){this.unit=t;this.fireEvent(new mxEventObject("unitChanged","unit",t))}};mxSvgCanvas2D.prototype.foAltText="[Not supported by viewer]";mxShape.prototype.getConstraints=function(t,e,i){return null};Graph=function(t,e,i,n,r,l,a){mxGraph.call(this,t,e,i,n,a);this.themes=r||this.defaultThemes;this.currentEdgeStyle=mxUtils.clone(this.defaultEdgeStyle);this.currentVertexStyle=mxUtils.clone(this.defaultVertexStyle);this.standalone=l!=null?l:false;var s=this.baseUrl;var o=s.indexOf("//");this.domainUrl="";this.domainPathUrl="";if(o>0){var h=s.indexOf("/",o+2);if(h>0){this.domainUrl=s.substring(0,h)}h=s.lastIndexOf("/");if(h>0){this.domainPathUrl=s.substring(0,h+1)}}this.isHtmlLabel=function(t){var e=this.view.getState(t);var i=e!=null?e.style:this.getCellStyle(t);return i!=null?i["html"]=="1"||i[mxConstants.STYLE_WHITE_SPACE]=="wrap":false};if(this.edgeMode){var v={point:null,event:null,state:null,handle:null,selected:false};this.addListener(mxEvent.FIRE_MOUSE_EVENT,mxUtils.bind(this,function(t,e){if(e.getProperty("eventName")=="mouseDown"&&this.isEnabled()){var i=e.getProperty("event");if(!mxEvent.isControlDown(i.getEvent())&&!mxEvent.isShiftDown(i.getEvent())){var n=i.getState();if(n!=null){if(this.model.isEdge(n.cell)){v.point=new mxPoint(i.getGraphX(),i.getGraphY());v.selected=this.isCellSelected(n.cell);v.state=n;v.event=i;if(n.text!=null&&n.text.boundingBox!=null&&mxUtils.contains(n.text.boundingBox,i.getGraphX(),i.getGraphY())){v.handle=mxEvent.LABEL_HANDLE}else{var r=this.selectionCellsHandler.getHandler(n.cell);if(r!=null&&r.bends!=null&&r.bends.length>0){v.handle=r.getHandleForEvent(i)}}}}}}}));var u=null;this.addMouseListener({mouseDown:function(t,e){},mouseMove:mxUtils.bind(this,function(t,e){var i=this.selectionCellsHandler.handlers.map;for(var n in i){if(i[n].index!=null){return}}if(this.isEnabled()&&!this.panningHandler.isActive()&&!mxEvent.isControlDown(e.getEvent())&&!mxEvent.isShiftDown(e.getEvent())&&!mxEvent.isAltDown(e.getEvent())){var r=this.tolerance;if(v.point!=null&&v.state!=null&&v.event!=null){var l=v.state;if(Math.abs(v.point.x-e.getGraphX())>r||Math.abs(v.point.y-e.getGraphY())>r){if(!this.isCellSelected(l.cell)){this.setSelectionCell(l.cell)}var a=this.selectionCellsHandler.getHandler(l.cell);if(a!=null&&a.bends!=null&&a.bends.length>0){var s=a.getHandleForEvent(v.event);var o=this.view.getEdgeStyle(l);var h=o==mxEdgeStyle.EntityRelation;if(!v.selected&&v.handle==mxEvent.LABEL_HANDLE){s=v.handle}if(!h||s==0||s==a.bends.length-1||s==mxEvent.LABEL_HANDLE){if(s==mxEvent.LABEL_HANDLE||s==0||l.visibleSourceState!=null||s==a.bends.length-1||l.visibleTargetState!=null){if(!h&&s!=mxEvent.LABEL_HANDLE){var u=l.absolutePoints;if(u!=null&&(o==null&&s==null||o==mxEdgeStyle.OrthConnector)){s=v.handle;if(s==null){var p=new mxRectangle(v.point.x,v.point.y);p.grow(mxEdgeHandler.prototype.handleImage.width/2);if(mxUtils.contains(p,u[0].x,u[0].y)){s=0}else if(mxUtils.contains(p,u[u.length-1].x,u[u.length-1].y)){s=a.bends.length-1}else{var c=o!=null&&(u.length==2||u.length==3&&(Math.round(u[0].x-u[1].x)==0&&Math.round(u[1].x-u[2].x)==0||Math.round(u[0].y-u[1].y)==0&&Math.round(u[1].y-u[2].y)==0));if(c){s=2}else{s=mxUtils.findNearestSegment(l,v.point.x,v.point.y);if(o==null){s=mxEvent.VIRTUAL_HANDLE-s}else{s+=1}}}}}if(s==null){s=mxEvent.VIRTUAL_HANDLE}}a.start(e.getGraphX(),e.getGraphX(),s);v.state=null;v.event=null;v.point=null;v.handle=null;v.selected=false;e.consume();this.graphHandler.reset()}}else if(h&&(l.visibleSourceState!=null||l.visibleTargetState!=null)){this.graphHandler.reset();e.consume()}}}}else{var l=e.getState();if(l!=null){if(this.model.isEdge(l.cell)){var g=null;var u=l.absolutePoints;if(u!=null){var p=new mxRectangle(e.getGraphX(),e.getGraphY());p.grow(mxEdgeHandler.prototype.handleImage.width/2);if(l.text!=null&&l.text.boundingBox!=null&&mxUtils.contains(l.text.boundingBox,e.getGraphX(),e.getGraphY())){g="move"}else if(mxUtils.contains(p,u[0].x,u[0].y)||mxUtils.contains(p,u[u.length-1].x,u[u.length-1].y)){g="pointer"}else if(l.visibleSourceState!=null||l.visibleTargetState!=null){var d=this.view.getEdgeStyle(l);g="crosshair";if(d!=mxEdgeStyle.EntityRelation&&this.isOrthogonal(l)){var m=mxUtils.findNearestSegment(l,e.getGraphX(),e.getGraphY());if(m<u.length-1&&m>=0){g=Math.round(u[m].x-u[m+1].x)==0?"col-resize":"row-resize"}}}}if(g!=null){l.setCursor(g)}}}}}}),mouseUp:mxUtils.bind(this,function(t,e){v.state=null;v.event=null;v.point=null;v.handle=null})})}this.cellRenderer.getLabelValue=function(t){var e=mxCellRenderer.prototype.getLabelValue.apply(this,arguments);if(t.view.graph.isHtmlLabel(t.cell)){if(t.style["html"]!=1){e=mxUtils.htmlEntities(e,false)}else{e=t.view.graph.sanitizeHtml(e)}}return e};if(typeof mxVertexHandler!=="undefined"){this.setConnectable(true);this.setDropEnabled(true);this.setPanning(true);this.setTooltips(true);this.setAllowLoops(true);this.allowAutoPanning=true;this.resetEdgesOnConnect=false;this.constrainChildren=false;this.constrainRelativeChildren=true;this.graphHandler.scrollOnMove=false;this.graphHandler.scaleGrid=true;this.connectionHandler.setCreateTarget(false);this.connectionHandler.insertBeforeSource=true;this.connectionHandler.isValidSource=function(t,e){return false};this.alternateEdgeStyle="vertical";if(n==null){this.loadStylesheet()}var p=this.graphHandler.getGuideStates;this.graphHandler.getGuideStates=function(){var t=p.apply(this,arguments);if(this.graph.pageVisible){var e=[];var i=this.graph.pageFormat;var n=this.graph.pageScale;var r=i.width*n;var l=i.height*n;var a=this.graph.view.translate;var s=this.graph.view.scale;var o=this.graph.getPageLayout();for(var h=0;h<o.width;h++){e.push(new mxRectangle(((o.x+h)*r+a.x)*s,(o.y*l+a.y)*s,r*s,l*s))}for(var u=0;u<o.height;u++){e.push(new mxRectangle((o.x*r+a.x)*s,((o.y+u)*l+a.y)*s,r*s,l*s))}t=e.concat(t)}return t};mxDragSource.prototype.dragElementZIndex=mxPopupMenu.prototype.zIndex;mxGuide.prototype.getGuideColor=function(t,e){return t.cell==null?"#ffa500":mxConstants.GUIDE_COLOR};this.graphHandler.createPreviewShape=function(t){this.previewColor=this.graph.background=="#000000"?"#ffffff":mxGraphHandler.prototype.previewColor;return mxGraphHandler.prototype.createPreviewShape.apply(this,arguments)};this.graphHandler.getCells=function(t){var e=mxGraphHandler.prototype.getCells.apply(this,arguments);var i=[];for(var n=0;n<e.length;n++){var r=this.graph.view.getState(e[n]);var l=r!=null?r.style:this.graph.getCellStyle(e[n]);if(mxUtils.getValue(l,"part","0")=="1"){var a=this.graph.model.getParent(e[n]);if(this.graph.model.isVertex(a)&&mxUtils.indexOf(e,a)<0){i.push(a)}}else{i.push(e[n])}}return i};this.connectionHandler.createTargetVertex=function(t,e){var i=this.graph.view.getState(e);var n=i!=null?i.style:this.graph.getCellStyle(e);if(mxUtils.getValue(n,"part",false)){var r=this.graph.model.getParent(e);if(this.graph.model.isVertex(r)){e=r}}return mxConnectionHandler.prototype.createTargetVertex.apply(this,arguments)};var c=new mxRubberband(this);this.getRubberband=function(){return c};var g=(new Date).getTime();var d=0;var m=this.connectionHandler.mouseMove;this.connectionHandler.mouseMove=function(){var t=this.currentState;m.apply(this,arguments);if(t!=this.currentState){g=(new Date).getTime();d=0}else{d=(new Date).getTime()-g}};var f=this.connectionHandler.isOutlineConnectEvent;this.connectionHandler.isOutlineConnectEvent=function(t){return this.currentState!=null&&t.getState()==this.currentState&&d>2e3||(this.currentState==null||mxUtils.getValue(this.currentState.style,"outlineConnect","1")!="0")&&f.apply(this,arguments)};var x=this.isToggleEvent;this.isToggleEvent=function(t){return x.apply(this,arguments)||!mxClient.IS_CHROMEOS&&mxEvent.isShiftDown(t)};var y=c.isForceRubberbandEvent;c.isForceRubberbandEvent=function(t){return y.apply(this,arguments)&&!mxEvent.isShiftDown(t.getEvent())&&!mxEvent.isControlDown(t.getEvent())||mxClient.IS_CHROMEOS&&mxEvent.isShiftDown(t.getEvent())||mxUtils.hasScrollbars(this.graph.container)&&mxClient.IS_FF&&mxClient.IS_WIN&&t.getState()==null&&mxEvent.isTouchEvent(t.getEvent())};var C=null;this.panningHandler.addListener(mxEvent.PAN_START,mxUtils.bind(this,function(){if(this.isEnabled()){C=this.container.style.cursor;this.container.style.cursor="move"}}));this.panningHandler.addListener(mxEvent.PAN_END,mxUtils.bind(this,function(){if(this.isEnabled()){this.container.style.cursor=C}}));this.popupMenuHandler.autoExpand=true;this.popupMenuHandler.isSelectOnPopup=function(t){return mxEvent.isMouseEvent(t.getEvent())};var S=this.click;this.click=function(t){if(!mxUtils||!mxEvent){return}var e=t.state==null&&t.sourceState!=null&&this.isCellLocked(t.sourceState.cell);if((!this.isEnabled()||e)&&!t.isConsumed()){var i=e?t.sourceState.cell:t.getCell();if(i!=null){var n=this.getLinkForCell(i);if(n!=null){if(this.isCustomLink(n)){this.customLinkClicked(n)}else{this.openLink(n)}}}if(this.isEnabled()&&e){this.clearSelection()}}else{return S.apply(this,arguments)}};this.tooltipHandler.getStateForEvent=function(t){return t.sourceState};var E=this.getCursorForMouseEvent;this.getCursorForMouseEvent=function(t){var e=t.state==null&&t.sourceState!=null&&this.isCellLocked(t.sourceState.cell);return this.getCursorForCell(e?t.sourceState.cell:t.getCell())};var w=this.getCursorForCell;this.getCursorForCell=function(t){if(!this.isEnabled()||this.isCellLocked(t)){var e=this.getLinkForCell(t);if(e!=null){return"pointer"}else if(this.isCellLocked(t)){return"default"}}return w.apply(this,arguments)};this.selectRegion=function(t,e){var i=this.getAllCells(t.x,t.y,t.width,t.height);this.selectCellsForEvent(i,e);return i};this.getAllCells=function(t,e,i,n,r,l){l=l!=null?l:[];if(i>0||n>0){var a=this.getModel();var s=t+i;var o=e+n;if(r==null){r=this.getCurrentRoot();if(r==null){r=a.getRoot()}}if(r!=null){var h=a.getChildCount(r);for(var u=0;u<h;u++){var p=a.getChildAt(r,u);var c=this.view.getState(p);if(c!=null&&this.isCellVisible(p)&&mxUtils.getValue(c.style,"locked","0")!="1"){var g=mxUtils.getValue(c.style,mxConstants.STYLE_ROTATION)||0;var d=c;if(g!=0){d=mxUtils.getBoundingBox(d,g)}if((a.isEdge(p)||a.isVertex(p))&&d.x>=t&&d.y+d.height<=o&&d.y>=e&&d.x+d.width<=s){l.push(p)}this.getAllCells(t,e,i,n,p,l)}}}}return l};var b=this.graphHandler.shouldRemoveCellsFromParent;this.graphHandler.shouldRemoveCellsFromParent=function(t,e,i){if(this.graph.isCellSelected(t)){return false}return b.apply(this,arguments)};this.isCellLocked=function(t){var e=this.view.getState(t);while(e!=null){if(mxUtils.getValue(e.style,"locked","0")=="1"){return true}e=this.view.getState(this.model.getParent(e.cell))}return false};var H=null;this.addListener(mxEvent.FIRE_MOUSE_EVENT,mxUtils.bind(this,function(t,e){if(e.getProperty("eventName")=="mouseDown"){var i=e.getProperty("event");var n=i.getState();if(n!=null&&!this.isSelectionEmpty()&&!this.isCellSelected(n.cell)){H=this.getSelectionCells()}else{H=null}}}));this.addListener(mxEvent.TAP_AND_HOLD,mxUtils.bind(this,function(t,e){if(!mxEvent.isMultiTouchEvent(e)){var i=e.getProperty("event");var n=e.getProperty("cell");if(n==null){var r=mxUtils.convertPoint(this.container,mxEvent.getClientX(i),mxEvent.getClientY(i));c.start(r.x,r.y)}else if(H!=null){this.addSelectionCells(H)}else if(this.getSelectionCount()>1&&this.isCellSelected(n)){this.removeSelectionCell(n)}H=null;e.consume()}}));this.connectionHandler.selectCells=function(t,e){this.graph.setSelectionCell(e||t)};this.connectionHandler.constraintHandler.isStateIgnored=function(t,e){return e&&t.view.graph.isCellSelected(t.cell)};this.selectionModel.addListener(mxEvent.CHANGE,mxUtils.bind(this,function(){var t=this.connectionHandler.constraintHandler;if(t.currentFocus!=null&&t.isStateIgnored(t.currentFocus,true)){t.currentFocus=null;t.constraints=null;t.destroyIcons()}t.destroyFocusHighlight()}));if(Graph.touchStyle){this.initTouch()}var L=this.updateMouseEvent;this.updateMouseEvent=function(t){t=L.apply(this,arguments);if(t.state!=null&&this.isCellLocked(t.getCell())){t.state=null}return t}}this.currentTranslate=new mxPoint(0,0)};Graph.touchStyle=mxClient.IS_TOUCH||mxClient.IS_FF&&mxClient.IS_WIN||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0||window.urlParams==null||urlParams["touch"]=="1";Graph.fileSupport=window.File!=null&&window.FileReader!=null&&window.FileList!=null&&(window.urlParams==null||urlParams["filesupport"]!="0");Graph.lineJumpsEnabled=true;Graph.defaultJumpSize=6;Graph.createSvgImage=function(t,e,i,n,r){var l=unescape(encodeURIComponent('<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">'+'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="'+t+'px" height="'+e+'px" '+(n!=null&&r!=null?'viewBox="0 0 '+n+" "+r+'" ':"")+'version="1.1">'+i+"</svg>"));return new mxImage("data:image/svg+xml;base64,"+(window.btoa?btoa(l):Base64.encode(l,true)),t,e)};Graph.zapGremlins=function(t){var e=[];for(var i=0;i<t.length;i++){var n=t.charCodeAt(i);if((n>=32||n==9||n==10||n==13)&&n!=65535&&n!=65534){e.push(t.charAt(i))}}return e.join("")};Graph.stringToBytes=function(t){var e=new Array(t.length);for(var i=0;i<t.length;i++){e[i]=t.charCodeAt(i)}return e};Graph.bytesToString=function(t){var e=new Array(t.length);for(var i=0;i<t.length;i++){e[i]=String.fromCharCode(t[i])}return e.join("")};Graph.compressNode=function(t){return Graph.compress(Graph.zapGremlins(mxUtils.getXml(t)))};Graph.compress=function(t,e){if(t==null||t.length==0||typeof pako==="undefined"){return t}else{var i=e?pako.deflate(encodeURIComponent(t),{to:"string"}):pako.deflateRaw(encodeURIComponent(t),{to:"string"});return window.btoa?btoa(i):Base64.encode(i,true)}};Graph.decompress=function(t,e){if(t==null||t.length==0||typeof pako==="undefined"){return t}else{var i=window.atob?atob(t):Base64.decode(t,true);var n=e?pako.inflate(i,{to:"string"}):pako.inflateRaw(i,{to:"string"});return Graph.zapGremlins(decodeURIComponent(n))}};mxUtils.extend(Graph,mxGraph);Graph.prototype.minFitScale=null;Graph.prototype.maxFitScale=null;Graph.prototype.linkPolicy=urlParams["target"]=="frame"?"blank":urlParams["target"]||"auto";Graph.prototype.linkTarget=urlParams["target"]=="frame"?"_self":"_blank";Graph.prototype.linkRelation="nofollow noopener noreferrer";Graph.prototype.defaultScrollbars=!mxClient.IS_IOS;Graph.prototype.defaultPageVisible=true;Graph.prototype.lightbox=false;Graph.prototype.defaultPageBackgroundColor="#ffffff";Graph.prototype.defaultPageBorderColor="#ffffff";Graph.prototype.scrollTileSize=new mxRectangle(0,0,400,400);Graph.prototype.transparentBackground=true;Graph.prototype.selectParentAfterDelete=false;Graph.prototype.defaultEdgeLength=80;Graph.prototype.edgeMode=false;Graph.prototype.connectionArrowsEnabled=true;Graph.prototype.placeholderPattern=new RegExp("%(date{.*}|[^%^{^}]+)%","g");Graph.prototype.absoluteUrlPattern=new RegExp("^(?:[a-z]+:)?//","i");Graph.prototype.defaultThemeName="default";Graph.prototype.defaultThemes={};Graph.prototype.baseUrl=urlParams["base"]!=null?decodeURIComponent(urlParams["base"]):(window!=window.top?document.referrer:document.location.toString()).split("#")[0];Graph.prototype.editAfterInsert=false;Graph.prototype.builtInProperties=["label","tooltip","placeholders","placeholder"];Graph.prototype.standalone=false;Graph.prototype.init=function(t){mxGraph.prototype.init.apply(this,arguments);this.cellRenderer.initializeLabel=function(i,n){mxCellRenderer.prototype.initializeLabel.apply(this,arguments);var e=i.view.graph.tolerance;var r=true;var l=null;var t=mxUtils.bind(this,function(t){r=true;l=new mxPoint(mxEvent.getClientX(t),mxEvent.getClientY(t))});var a=mxUtils.bind(this,function(t){r=r&&l!=null&&Math.abs(l.x-mxEvent.getClientX(t))<e&&Math.abs(l.y-mxEvent.getClientY(t))<e});var s=mxUtils.bind(this,function(t){if(r){var e=mxEvent.getSource(t);while(e!=null&&e!=n.node){if(e.nodeName.toLowerCase()=="a"){i.view.graph.labelLinkClicked(i,e,t);break}e=e.parentNode}}});mxEvent.addGestureListeners(n.node,t,a,s);mxEvent.addListener(n.node,"click",function(t){mxEvent.consume(t)})};this.initLayoutManager()};(function(){Graph.prototype.useCssTransforms=false;Graph.prototype.currentScale=1;Graph.prototype.currentTranslate=new mxPoint(0,0);Graph.prototype.isFastZoomEnabled=function(){return urlParams["zoom"]!="nocss"&&!mxClient.NO_FO&&!mxClient.IS_EDGE&&!this.useCssTransforms&&this.isCssTransformsSupported()};Graph.prototype.isCssTransformsSupported=function(){return this.dialect==mxConstants.DIALECT_SVG&&!mxClient.NO_FO&&(!this.lightbox||!mxClient.IS_SF)};Graph.prototype.getCellAt=function(t,e,i,n,r,l){if(this.useCssTransforms){t=t/this.currentScale-this.currentTranslate.x;e=e/this.currentScale-this.currentTranslate.y}return this.getScaledCellAt.apply(this,arguments)};Graph.prototype.getScaledCellAt=function(t,e,i,n,r,l){n=n!=null?n:true;r=r!=null?r:true;if(i==null){i=this.getCurrentRoot();if(i==null){i=this.getModel().getRoot()}}if(i!=null){var a=this.model.getChildCount(i);for(var s=a-1;s>=0;s--){var o=this.model.getChildAt(i,s);var h=this.getScaledCellAt(t,e,o,n,r,l);if(h!=null){return h}else if(this.isCellVisible(o)&&(r&&this.model.isEdge(o)||n&&this.model.isVertex(o))){var u=this.view.getState(o);if(u!=null&&(l==null||!l(u,t,e))&&this.intersects(u,t,e)){return o}}}}return null};mxCellHighlight.prototype.getStrokeWidth=function(t){var e=this.strokeWidth;if(this.graph.useCssTransforms){e/=this.graph.currentScale}return e};mxGraphView.prototype.getGraphBounds=function(){var t=this.graphBounds;if(this.graph.useCssTransforms){var e=this.graph.currentTranslate;var i=this.graph.currentScale;t=new mxRectangle((t.x+e.x)*i,(t.y+e.y)*i,t.width*i,t.height*i)}return t};mxGraphView.prototype.viewStateChanged=function(){if(this.graph.useCssTransforms){this.validate();this.graph.sizeDidChange()}else{this.revalidate();this.graph.sizeDidChange()}};var e=mxGraphView.prototype.validate;mxGraphView.prototype.validate=function(t){if(this.graph.useCssTransforms){this.graph.currentScale=this.scale;this.graph.currentTranslate.x=this.translate.x;this.graph.currentTranslate.y=this.translate.y;this.scale=1;this.translate.x=0;this.translate.y=0}e.apply(this,arguments);if(this.graph.useCssTransforms){this.graph.updateCssTransform();this.scale=this.graph.currentScale;this.translate.x=this.graph.currentTranslate.x;this.translate.y=this.graph.currentTranslate.y}};Graph.prototype.updateCssTransform=function(){var t=this.view.getDrawPane();if(t!=null){var e=t.parentNode;if(!this.useCssTransforms){e.removeAttribute("transformOrigin");e.removeAttribute("transform")}else{var i=e.getAttribute("transform");e.setAttribute("transformOrigin","0 0");var n=Math.round(this.currentScale*100)/100;var r=Math.round(this.currentTranslate.x*100)/100;var l=Math.round(this.currentTranslate.y*100)/100;e.setAttribute("transform","scale("+n+","+n+")"+"translate("+r+","+l+")");if(i!=e.getAttribute("transform")){try{if(mxClient.IS_EDGE){var a=e.style.display;e.style.display="none";e.getBBox();e.style.display=a}}catch(t){}}}}};var n=mxGraphView.prototype.validateBackgroundPage;mxGraphView.prototype.validateBackgroundPage=function(){var t=this.graph.useCssTransforms,e=this.scale,i=this.translate;if(t){this.scale=this.graph.currentScale;this.translate=this.graph.currentTranslate}n.apply(this,arguments);if(t){this.scale=e;this.translate=i}};var a=mxGraph.prototype.updatePageBreaks;mxGraph.prototype.updatePageBreaks=function(t,e,i){var n=this.useCssTransforms,r=this.view.scale,l=this.view.translate;if(n){this.view.scale=1;this.view.translate=new mxPoint(0,0);this.useCssTransforms=false}a.apply(this,arguments);if(n){this.view.scale=r;this.view.translate=l;this.useCssTransforms=true}}})();Graph.prototype.isLightboxView=function(){return this.lightbox};Graph.prototype.isViewer=function(){return false};Graph.prototype.labelLinkClicked=function(t,e,i){var n=e.getAttribute("href");if(n!=null&&!this.isCustomLink(n)&&mxEvent.isLeftMouseButton(i)&&!mxEvent.isPopupTrigger(i)||mxEvent.isTouchEvent(i)){if(!this.isEnabled()||this.isCellLocked(t.cell)){var r=this.isBlankLink(n)?this.linkTarget:"_top";this.openLink(this.getAbsoluteUrl(n),r)}mxEvent.consume(i)}};Graph.prototype.openLink=function(t,e,i){var n=window;try{if(e=="_self"&&window!=window.top){window.location.href=t}else{if(t.substring(0,this.baseUrl.length)==this.baseUrl&&t.charAt(this.baseUrl.length)=="#"&&e=="_top"&&window==window.top){var r=t.split("#")[1];if(window.location.hash=="#"+r){window.location.hash=""}window.location.hash=r}else{n=window.open(t,e);if(n!=null&&!i){n.opener=null}}}}catch(t){}return n};Graph.prototype.getLinkTitle=function(t){return t.substring(t.lastIndexOf("/")+1)};Graph.prototype.isCustomLink=function(t){return t.substring(0,5)=="data:"};Graph.prototype.customLinkClicked=function(t){return false};Graph.prototype.isExternalProtocol=function(t){return t.substring(0,7)==="mailto:"};Graph.prototype.isBlankLink=function(t){return!this.isExternalProtocol(t)&&(this.linkPolicy==="blank"||this.linkPolicy!=="self"&&!this.isRelativeUrl(t)&&t.substring(0,this.domainUrl.length)!==this.domainUrl)};Graph.prototype.isRelativeUrl=function(t){return t!=null&&!this.absoluteUrlPattern.test(t)&&t.substring(0,5)!=="data:"&&!this.isExternalProtocol(t)};Graph.prototype.getAbsoluteUrl=function(t){if(t!=null&&this.isRelativeUrl(t)){if(t.charAt(0)=="#"){t=this.baseUrl+t}else if(t.charAt(0)=="/"){t=this.domainUrl+t}else{t=this.domainPathUrl+t}}return t};Graph.prototype.initLayoutManager=function(){this.layoutManager=new mxLayoutManager(this);this.layoutManager.getLayout=function(t){var e=this.graph.getCellStyle(t);if(e!=null){if(e["childLayout"]=="stackLayout"){var i=new mxStackLayout(this.graph,true);i.resizeParentMax=mxUtils.getValue(e,"resizeParentMax","1")=="1";i.horizontal=mxUtils.getValue(e,"horizontalStack","1")=="1";i.resizeParent=mxUtils.getValue(e,"resizeParent","1")=="1";i.resizeLast=mxUtils.getValue(e,"resizeLast","0")=="1";i.spacing=e["stackSpacing"]||i.spacing;i.border=e["stackBorder"]||i.border;i.marginLeft=e["marginLeft"]||0;i.marginRight=e["marginRight"]||0;i.marginTop=e["marginTop"]||0;i.marginBottom=e["marginBottom"]||0;i.fill=true;return i}else if(e["childLayout"]=="treeLayout"){var n=new mxCompactTreeLayout(this.graph);n.horizontal=mxUtils.getValue(e,"horizontalTree","1")=="1";n.resizeParent=mxUtils.getValue(e,"resizeParent","1")=="1";n.groupPadding=mxUtils.getValue(e,"parentPadding",20);n.levelDistance=mxUtils.getValue(e,"treeLevelDistance",30);n.maintainParentLocation=true;n.edgeRouting=false;n.resetEdges=false;return n}else if(e["childLayout"]=="flowLayout"){var r=new mxHierarchicalLayout(this.graph,mxUtils.getValue(e,"flowOrientation",mxConstants.DIRECTION_EAST));r.resizeParent=mxUtils.getValue(e,"resizeParent","1")=="1";r.parentBorder=mxUtils.getValue(e,"parentPadding",20);r.maintainParentLocation=true;r.intraCellSpacing=mxUtils.getValue(e,"intraCellSpacing",mxHierarchicalLayout.prototype.intraCellSpacing);r.interRankCellSpacing=mxUtils.getValue(e,"interRankCellSpacing",mxHierarchicalLayout.prototype.interRankCellSpacing);r.interHierarchySpacing=mxUtils.getValue(e,"interHierarchySpacing",mxHierarchicalLayout.prototype.interHierarchySpacing);r.parallelEdgeSpacing=mxUtils.getValue(e,"parallelEdgeSpacing",mxHierarchicalLayout.prototype.parallelEdgeSpacing);return r}else if(e["childLayout"]=="circleLayout"){return new mxCircleLayout(this.graph)}else if(e["childLayout"]=="organicLayout"){return new mxFastOrganicLayout(this.graph)}}return null}};Graph.prototype.getPageSize=function(){return this.pageVisible?new mxRectangle(0,0,this.pageFormat.width*this.pageScale,this.pageFormat.height*this.pageScale):this.scrollTileSize};Graph.prototype.getPageLayout=function(){var t=this.getPageSize();var e=this.getGraphBounds();if(e.width==0||e.height==0){return new mxRectangle(0,0,1,1)}else{var i=Math.ceil(e.x/this.view.scale-this.view.translate.x);var n=Math.ceil(e.y/this.view.scale-this.view.translate.y);var r=Math.floor(e.width/this.view.scale);var l=Math.floor(e.height/this.view.scale);var a=Math.floor(i/t.width);var s=Math.floor(n/t.height);if(this.pageViewType){a=0;s=0}var o=Math.ceil((i+r)/t.width)-a;var h=Math.ceil((n+l)/t.height)-s;if(this.pageViewType){o=Math.max(o,1);h=Math.max(h,1)}return new mxRectangle(a,s,o,h)}};Graph.prototype.sanitizeHtml=function(t,e){function i(t){if(t!=null&&t.toString().toLowerCase().substring(0,11)!=="javascript:"){return t}return null}function n(t){return t}return html_sanitize(t,i,n)};Graph.prototype.updatePlaceholders=function(){var t=this.model;var e=false;for(var i in this.model.cells){var n=this.model.cells[i];if(this.isReplacePlaceholders(n)){this.view.invalidate(n,false,false);e=true}}if(e){this.view.validate()}};Graph.prototype.isReplacePlaceholders=function(t){return t.value!=null&&typeof t.value=="object"&&t.value.getAttribute("placeholders")=="1"};Graph.prototype.isZoomWheelEvent=function(t){return mxEvent.isAltDown(t)||mxEvent.isMetaDown(t)&&mxClient.IS_MAC||mxEvent.isControlDown(t)};Graph.prototype.isTransparentClickEvent=function(t){return mxEvent.isAltDown(t)||mxClient.IS_CHROMEOS&&mxEvent.isShiftDown(t)};Graph.prototype.isIgnoreTerminalEvent=function(t){return mxEvent.isShiftDown(t)&&mxEvent.isControlDown(t)};Graph.prototype.isSplitTarget=function(t,e,i){return!this.model.isEdge(e[0])&&!mxEvent.isAltDown(i)&&!mxEvent.isShiftDown(i)&&mxGraph.prototype.isSplitTarget.apply(this,arguments)};Graph.prototype.getLabel=function(t){var e=mxGraph.prototype.getLabel.apply(this,arguments);if(e!=null&&this.isReplacePlaceholders(t)&&t.getAttribute("placeholder")==null){e=this.replacePlaceholders(t,e)}return e};Graph.prototype.isLabelMovable=function(t){var e=this.view.getState(t);var i=e!=null?e.style:this.getCellStyle(t);return!this.isCellLocked(t)&&(this.model.isEdge(t)&&this.edgeLabelsMovable||this.model.isVertex(t)&&(this.vertexLabelsMovable||mxUtils.getValue(i,"labelMovable","0")=="1"))};Graph.prototype.setGridSize=function(t){this.gridSize=t;this.fireEvent(new mxEventObject("gridSizeChanged"))};Graph.prototype.getGlobalVariable=function(t){var e=null;if(t=="date"){e=(new Date).toLocaleDateString()}else if(t=="time"){e=(new Date).toLocaleTimeString()}else if(t=="timestamp"){e=(new Date).toLocaleString()}else if(t.substring(0,5)=="date{"){var i=t.substring(5,t.length-1);e=this.formatDate(new Date,i)}return e};Graph.prototype.formatDate=function(t,e,i){if(this.dateFormatCache==null){this.dateFormatCache={i18n:{dayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],monthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec","January","February","March","April","May","June","July","August","September","October","November","December"]},masks:{default:"ddd mmm dd yyyy HH:MM:ss",shortDate:"m/d/yy",mediumDate:"mmm d, yyyy",longDate:"mmmm d, yyyy",fullDate:"dddd, mmmm d, yyyy",shortTime:"h:MM TT",mediumTime:"h:MM:ss TT",longTime:"h:MM:ss TT Z",isoDate:"yyyy-mm-dd",isoTime:"HH:MM:ss",isoDateTime:"yyyy-mm-dd'T'HH:MM:ss",isoUtcDateTime:"UTC:yyyy-mm-dd'T'HH:MM:ss'Z'"}}}var n=this.dateFormatCache;var r=/d{1,4}|m{1,4}|yy(?:yy)?|([HhMsTt])\1?|[LloSZ]|"[^"]*"|'[^']*'/g,l=/\b(?:[PMCEA][SDP]T|(?:Pacific|Mountain|Central|Eastern|Atlantic) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\d{4})?)\b/g,a=/[^-+\dA-Z]/g,s=function(t,e){t=String(t);e=e||2;while(t.length<e)t="0"+t;return t};if(arguments.length==1&&Object.prototype.toString.call(t)=="[object String]"&&!/\d/.test(t)){e=t;t=undefined}t=t?new Date(t):new Date;if(isNaN(t))throw SyntaxError("invalid date");e=String(n.masks[e]||e||n.masks["default"]);if(e.slice(0,4)=="UTC:"){e=e.slice(4);i=true}var o=i?"getUTC":"get",h=t[o+"Date"](),u=t[o+"Day"](),p=t[o+"Month"](),c=t[o+"FullYear"](),g=t[o+"Hours"](),d=t[o+"Minutes"](),m=t[o+"Seconds"](),v=t[o+"Milliseconds"](),f=i?0:t.getTimezoneOffset(),x={d:h,dd:s(h),ddd:n.i18n.dayNames[u],dddd:n.i18n.dayNames[u+7],m:p+1,mm:s(p+1),mmm:n.i18n.monthNames[p],mmmm:n.i18n.monthNames[p+12],yy:String(c).slice(2),yyyy:c,h:g%12||12,hh:s(g%12||12),H:g,HH:s(g),M:d,MM:s(d),s:m,ss:s(m),l:s(v,3),L:s(v>99?Math.round(v/10):v),t:g<12?"a":"p",tt:g<12?"am":"pm",T:g<12?"A":"P",TT:g<12?"AM":"PM",Z:i?"UTC":(String(t).match(l)||[""]).pop().replace(a,""),o:(f>0?"-":"+")+s(Math.floor(Math.abs(f)/60)*100+Math.abs(f)%60,4),S:["th","st","nd","rd"][h%10>3?0:(h%100-h%10!=10)*h%10]};return e.replace(r,function(t){return t in x?x[t]:t.slice(1,t.length-1)})};Graph.prototype.createLayersDialog=function(){var r=document.createElement("div");r.style.position="absolute";var l=this.getModel();var t=l.getChildCount(l.root);for(var e=0;e<t;e++){mxUtils.bind(this,function(t){var e=document.createElement("div");e.style.overflow="hidden";e.style.textOverflow="ellipsis";e.style.padding="2px";e.style.whiteSpace="nowrap";var i=document.createElement("input");i.style.display="inline-block";i.setAttribute("type","checkbox");if(l.isVisible(t)){i.setAttribute("checked","checked");i.defaultChecked=true}e.appendChild(i);var n=this.convertValueToString(t)||mxResources.get("background")||"Background";e.setAttribute("title",n);mxUtils.write(e,n);r.appendChild(e);mxEvent.addListener(i,"click",function(){if(i.getAttribute("checked")!=null){i.removeAttribute("checked")}else{i.setAttribute("checked","checked")}l.setVisible(t,i.checked)})})(l.getChildAt(l.root,e))}return r};Graph.prototype.replacePlaceholders=function(t,e){var i=[];if(e!=null){var n=0;while(match=this.placeholderPattern.exec(e)){var r=match[0];if(r.length>2&&r!="%label%"&&r!="%tooltip%"){var l=null;if(match.index>n&&e.charAt(match.index-1)=="%"){l=r.substring(1)}else{var a=r.substring(1,r.length-1);if(a.indexOf("{")<0){var s=t;while(l==null&&s!=null){if(s.value!=null&&typeof s.value=="object"){l=s.hasAttribute(a)?s.getAttribute(a)!=null?s.getAttribute(a):"":null}s=this.model.getParent(s)}}if(l==null){l=this.getGlobalVariable(a)}}i.push(e.substring(n,match.index)+(l!=null?l:r));n=match.index+r.length}}i.push(e.substring(n))}return i.join("")};Graph.prototype.restoreSelection=function(t){if(t!=null&&t.length>0){var e=[];for(var i=0;i<t.length;i++){var n=this.model.getCell(t[i].id);if(n!=null){e.push(n)}}this.setSelectionCells(e)}else{this.clearSelection()}};Graph.prototype.selectCellsForConnectVertex=function(t,e,i){if(t.length==2&&this.model.isVertex(t[1])){this.setSelectionCell(t[1]);this.scrollCellToVisible(t[1]);if(i!=null){if(mxEvent.isTouchEvent(e)){i.update(i.getState(this.view.getState(t[1])))}else{i.reset()}}}else{this.setSelectionCells(t)}};Graph.prototype.connectVertex=function(t,e,i,n,r,l){if(t.geometry.relative&&this.model.isEdge(t.parent)){return[]}l=l?l:false;var a=t.geometry.relative&&t.parent.geometry!=null?new mxPoint(t.parent.geometry.width*t.geometry.x,t.parent.geometry.height*t.geometry.y):new mxPoint(t.geometry.x,t.geometry.y);if(e==mxConstants.DIRECTION_NORTH){a.x+=t.geometry.width/2;a.y-=i}else if(e==mxConstants.DIRECTION_SOUTH){a.x+=t.geometry.width/2;a.y+=t.geometry.height+i}else if(e==mxConstants.DIRECTION_WEST){a.x-=i;a.y+=t.geometry.height/2}else{a.x+=t.geometry.width+i;a.y+=t.geometry.height/2}var s=this.view.getState(this.model.getParent(t));var o=this.view.scale;var h=this.view.translate;var u=h.x*o;var p=h.y*o;if(s!=null&&this.model.isVertex(s.cell)){u=s.x;p=s.y}if(this.model.isVertex(t.parent)&&t.geometry.relative){a.x+=t.parent.geometry.x;a.y+=t.parent.geometry.y}var c=l||mxEvent.isControlDown(n)&&!r?null:this.getCellAt(u+a.x*o,p+a.y*o);if(this.model.isAncestor(c,t)){c=null}var g=c;while(g!=null){if(this.isCellLocked(g)){c=null;break}g=this.model.getParent(g)}if(c!=null){var d=this.view.getState(t);var m=this.view.getState(c);if(d!=null&&m!=null&&mxUtils.intersects(d,m)){c=null}}var v=!mxEvent.isShiftDown(n)||r;if(v){if(e==mxConstants.DIRECTION_NORTH){a.y-=t.geometry.height/2}else if(e==mxConstants.DIRECTION_SOUTH){a.y+=t.geometry.height/2}else if(e==mxConstants.DIRECTION_WEST){a.x-=t.geometry.width/2}else{a.x+=t.geometry.width/2}}if(c!=null&&!this.isCellConnectable(c)){var f=this.getModel().getParent(c);if(this.getModel().isVertex(f)&&this.isCellConnectable(f)){c=f}}if(c==t||this.model.isEdge(c)||!this.isCellConnectable(c)){c=null}var x=[];this.model.beginUpdate();try{var y=c!=null&&this.isSwimlane(c);var C=!y?c:null;if(C==null&&v){var S=t;var E=this.getCellGeometry(t);while(E!=null&&E.relative){S=this.getModel().getParent(S);E=this.getCellGeometry(S)}var w=this.view.getState(S);var b=w!=null?w.style:this.getCellStyle(S);if(mxUtils.getValue(b,"part",false)){var H=this.model.getParent(S);if(this.model.isVertex(H)){S=H}}C=this.duplicateCells([S],false)[0];this.fireEvent(new mxEventObject(mxEvent.CELLS_ADDED,"cells",[C]));var E=this.getCellGeometry(C);if(E!=null){E.x=a.x-E.width/2;E.y=a.y-E.height/2}if(y){this.addCells([C],c,null,null,null,true);c=null}}var L=null;if(this.layoutManager!=null){L=this.layoutManager.getLayout(this.model.getParent(t))}var T=mxEvent.isControlDown(n)&&v||c==null&&L!=null&&L.constructor==mxStackLayout?null:this.insertEdge(this.model.getParent(t),null,"",t,C,this.createCurrentEdgeStyle());if(T!=null&&this.connectionHandler.insertBeforeSource){var G=null;var A=t;while(A.parent!=null&&A.geometry!=null&&A.geometry.relative&&A.parent!=T.parent){A=this.model.getParent(A)}if(A!=null&&A.parent!=null&&A.parent==T.parent){var G=A.parent.getIndex(A);this.model.add(A.parent,T,G)}}if(c==null&&C!=null&&L!=null&&t.parent!=null&&L.constructor==mxStackLayout&&e==mxConstants.DIRECTION_WEST){var G=t.parent.getIndex(t);this.model.add(t.parent,C,G)}if(T!=null){x.push(T)}if(c==null&&C!=null){x.push(C)}if(C==null&&T!=null){T.geometry.setTerminalPoint(a,false)}if(T!=null){this.fireEvent(new mxEventObject("cellsInserted","cells",[T]))}}finally{this.model.endUpdate()}return x};Graph.prototype.getIndexableText=function(){var t=document.createElement("div");var e=[];var i="";for(var n in this.model.cells){var r=this.model.cells[n];if(this.model.isVertex(r)||this.model.isEdge(r)){if(this.isHtmlLabel(r)){t.innerHTML=this.getLabel(r);i=mxUtils.extractTextWithWhitespace([t])}else{i=this.getLabel(r)}i=mxUtils.trim(i.replace(/[\x00-\x1F\x7F-\x9F]|\s+/g," "));if(i.length>0){e.push(i)}}}return e.join(" ")};Graph.prototype.convertValueToString=function(t,e){if(t.value!=null&&typeof t.value=="object"){if(this.isReplacePlaceholders(t)&&t.getAttribute("placeholder")!=null){var i=t.getAttribute("placeholder");var n=t;var r=null;while(r==null&&n!=null){if(n.value!=null&&typeof n.value=="object"){r=n.hasAttribute(i)?n.getAttribute(i)!=null?n.getAttribute(i):"":null}n=this.model.getParent(n)}return r||""}else{return t.value.getAttribute("label")||""}}return mxGraph.prototype.convertValueToString.apply(this,arguments)};Graph.prototype.getLinksForState=function(t){if(t!=null&&t.text!=null&&t.text.node!=null){return t.text.node.getElementsByTagName("a")}return null};Graph.prototype.getLinkForCell=function(t){if(t.value!=null&&typeof t.value=="object"){var e=t.value.getAttribute("link");if(e!=null&&e.toLowerCase().substring(0,11)==="javascript:"){e=e.substring(11)}return e}return null};Graph.prototype.getCellStyle=function(t){var e=mxGraph.prototype.getCellStyle.apply(this,arguments);if(t!=null&&this.layoutManager!=null){var i=this.model.getParent(t);if(this.model.isVertex(i)&&this.isCellCollapsed(t)){var n=this.layoutManager.getLayout(i);if(n!=null&&n.constructor==mxStackLayout){e[mxConstants.STYLE_HORIZONTAL]=!n.horizontal}}}return e};Graph.prototype.updateAlternateBounds=function(t,e,i){if(t!=null&&e!=null&&this.layoutManager!=null&&e.alternateBounds!=null){var n=this.layoutManager.getLayout(this.model.getParent(t));if(n!=null&&n.constructor==mxStackLayout){if(n.horizontal){e.alternateBounds.height=0}else{e.alternateBounds.width=0}}}mxGraph.prototype.updateAlternateBounds.apply(this,arguments)};Graph.prototype.isMoveCellsEvent=function(t,e){return mxEvent.isShiftDown(t)||mxUtils.getValue(e.style,"moveCells","0")=="1"};Graph.prototype.foldCells=function(t,e,i,n,r){e=e!=null?e:false;if(i==null){i=this.getFoldableCells(this.getSelectionCells(),t)}if(i!=null){this.model.beginUpdate();try{mxGraph.prototype.foldCells.apply(this,arguments);if(this.layoutManager!=null){for(var l=0;l<i.length;l++){var a=this.view.getState(i[l]);var s=this.getCellGeometry(i[l]);if(a!=null&&s!=null){var o=Math.round(s.width-a.width/this.view.scale);var h=Math.round(s.height-a.height/this.view.scale);if(h!=0||o!=0){var u=this.model.getParent(i[l]);var p=this.layoutManager.getLayout(u);if(p==null){if(r!=null&&this.isMoveCellsEvent(r,a)){this.moveSiblings(a,u,o,h)}}else if((r==null||!mxEvent.isAltDown(r))&&p.constructor==mxStackLayout&&!p.resizeLast){this.resizeParentStacks(u,p,o,h)}}}}}}finally{this.model.endUpdate()}if(this.isEnabled()){this.setSelectionCells(i)}}};Graph.prototype.moveSiblings=function(t,e,i,n){this.model.beginUpdate();try{var r=this.getCellsBeyond(t.x,t.y,e,true,true);for(var l=0;l<r.length;l++){if(r[l]!=t.cell){var a=this.view.getState(r[l]);var s=this.getCellGeometry(r[l]);if(a!=null&&s!=null){s=s.clone();s.translate(Math.round(i*Math.max(0,Math.min(1,(a.x-t.x)/t.width))),Math.round(n*Math.max(0,Math.min(1,(a.y-t.y)/t.height))));this.model.setGeometry(r[l],s)}}}}finally{this.model.endUpdate()}};Graph.prototype.resizeParentStacks=function(t,e,i,n){if(this.layoutManager!=null&&e!=null&&e.constructor==mxStackLayout&&!e.resizeLast){this.model.beginUpdate();try{var r=e.horizontal;while(t!=null&&e!=null&&e.constructor==mxStackLayout&&e.horizontal==r&&!e.resizeLast){var l=this.getCellGeometry(t);var a=this.view.getState(t);if(a!=null&&l!=null){l=l.clone();if(e.horizontal){l.width+=i+Math.min(0,a.width/this.view.scale-l.width)}else{l.height+=n+Math.min(0,a.height/this.view.scale-l.height)}this.model.setGeometry(t,l)}t=this.model.getParent(t);e=this.layoutManager.getLayout(t)}}finally{this.model.endUpdate()}}};Graph.prototype.isContainer=function(t){var e=this.view.getState(t);var i=e!=null?e.style:this.getCellStyle(t);if(this.isSwimlane(t)){return i["container"]!="0"}else{return i["container"]=="1"}};Graph.prototype.isCellConnectable=function(t){var e=this.view.getState(t);var i=e!=null?e.style:this.getCellStyle(t);return i!=null&&i["connectable"]!=null?i["connectable"]!="0":mxGraph.prototype.isCellConnectable.apply(this,arguments)};Graph.prototype.isLabelMovable=function(t){var e=this.view.getState(t);var i=e!=null?e.style:this.getCellStyle(t);return i!=null&&i["movableLabel"]!=null?i["movableLabel"]!="0":mxGraph.prototype.isLabelMovable.apply(this,arguments)};Graph.prototype.selectAll=function(t){t=t||this.getDefaultParent();if(!this.isCellLocked(t)){mxGraph.prototype.selectAll.apply(this,arguments)}};Graph.prototype.selectCells=function(t,e,i){i=i||this.getDefaultParent();if(!this.isCellLocked(i)){mxGraph.prototype.selectCells.apply(this,arguments)}};Graph.prototype.getSwimlaneAt=function(t,e,i){i=i||this.getDefaultParent();if(!this.isCellLocked(i)){return mxGraph.prototype.getSwimlaneAt.apply(this,arguments)}return null};Graph.prototype.isCellFoldable=function(t){var e=this.view.getState(t);var i=e!=null?e.style:this.getCellStyle(t);return this.foldingEnabled&&(i["treeFolding"]=="1"||!this.isCellLocked(t)&&(this.isContainer(t)&&i["collapsible"]!="0"||!this.isContainer(t)&&i["collapsible"]=="1"))};Graph.prototype.reset=function(){if(this.isEditing()){this.stopEditing(true)}this.escape();if(!this.isSelectionEmpty()){this.clearSelection()}};Graph.prototype.zoom=function(t,e){t=Math.max(.01,Math.min(this.view.scale*t,160))/this.view.scale;mxGraph.prototype.zoom.apply(this,arguments)};Graph.prototype.zoomIn=function(){if(this.view.scale<.15){this.zoom((this.view.scale+.01)/this.view.scale)}else{this.zoom(Math.round(this.view.scale*this.zoomFactor*20)/20/this.view.scale)}};Graph.prototype.zoomOut=function(){if(this.view.scale<=.15){this.zoom((this.view.scale-.01)/this.view.scale)}else{this.zoom(Math.round(this.view.scale*(1/this.zoomFactor)*20)/20/this.view.scale)}};Graph.prototype.getTooltipForCell=function(t){var e="";if(mxUtils.isNode(t.value)){var i=t.value.getAttribute("tooltip");if(i!=null){if(i!=null&&this.isReplacePlaceholders(t)){i=this.replacePlaceholders(t,i)}e=this.sanitizeHtml(i)}else{var n=this.builtInProperties;var r=t.value.attributes;var l=[];if(this.isEnabled()){n.push("link")}for(var a=0;a<r.length;a++){if(mxUtils.indexOf(n,r[a].nodeName)<0&&r[a].nodeValue.length>0){l.push({name:r[a].nodeName,value:r[a].nodeValue})}}l.sort(function(t,e){if(t.name<e.name){return-1}else if(t.name>e.name){return 1}else{return 0}});for(var a=0;a<l.length;a++){if(l[a].name!="link"||!this.isCustomLink(l[a].value)){e+=(l[a].name!="link"?"<b>"+l[a].name+":</b> ":"")+mxUtils.htmlEntities(l[a].value)+"\n"}}if(e.length>0){e=e.substring(0,e.length-1);if(mxClient.IS_SVG){e='<div style="max-width:360px;">'+e+"</div>"}}}}return e};Graph.prototype.stringToBytes=function(t){return Graph.stringToBytes(t)};Graph.prototype.bytesToString=function(t){return Graph.bytesToString(t)};Graph.prototype.compressNode=function(t){return Graph.compressNode(t)};Graph.prototype.compress=function(t,e){return Graph.compress(t,e)};Graph.prototype.decompress=function(t,e){return Graph.decompress(t,e)};Graph.prototype.zapGremlins=function(t){return Graph.zapGremlins(t)};HoverIcons=function(t){this.graph=t;this.init()};HoverIcons.prototype.arrowSpacing=2;HoverIcons.prototype.updateDelay=500;HoverIcons.prototype.activationDelay=140;HoverIcons.prototype.currentState=null;HoverIcons.prototype.activeArrow=null;HoverIcons.prototype.inactiveOpacity=15;HoverIcons.prototype.cssCursor="copy";HoverIcons.prototype.checkCollisions=true;HoverIcons.prototype.arrowFill="#29b6f2";HoverIcons.prototype.triangleUp=!mxClient.IS_SVG?new mxImage(IMAGE_PATH+"/triangle-up.png",26,14):Graph.createSvgImage(18,28,'<path d="m 6 26 L 12 26 L 12 12 L 18 12 L 9 1 L 1 12 L 6 12 z" '+'stroke="#fff" fill="'+HoverIcons.prototype.arrowFill+'"/>');HoverIcons.prototype.triangleRight=!mxClient.IS_SVG?new mxImage(IMAGE_PATH+"/triangle-right.png",14,26):Graph.createSvgImage(26,18,'<path d="m 1 6 L 14 6 L 14 1 L 26 9 L 14 18 L 14 12 L 1 12 z" '+'stroke="#fff" fill="'+HoverIcons.prototype.arrowFill+'"/>');HoverIcons.prototype.triangleDown=!mxClient.IS_SVG?new mxImage(IMAGE_PATH+"/triangle-down.png",26,14):Graph.createSvgImage(18,26,'<path d="m 6 1 L 6 14 L 1 14 L 9 26 L 18 14 L 12 14 L 12 1 z" '+'stroke="#fff" fill="'+HoverIcons.prototype.arrowFill+'"/>');HoverIcons.prototype.triangleLeft=!mxClient.IS_SVG?new mxImage(IMAGE_PATH+"/triangle-left.png",14,26):Graph.createSvgImage(28,18,'<path d="m 1 9 L 12 1 L 12 6 L 26 6 L 26 12 L 12 12 L 12 18 z" '+'stroke="#fff" fill="'+HoverIcons.prototype.arrowFill+'"/>');HoverIcons.prototype.roundDrop=!mxClient.IS_SVG?new mxImage(IMAGE_PATH+"/round-drop.png",26,26):Graph.createSvgImage(26,26,'<circle cx="13" cy="13" r="12" '+'stroke="#fff" fill="'+HoverIcons.prototype.arrowFill+'"/>');HoverIcons.prototype.refreshTarget=new mxImage(mxClient.IS_SVG?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAYAAACoPemuAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBNYWNpbnRvc2giIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NDQxNERDRTU1QjY1MTFFNDkzNTRFQTVEMTdGMTdBQjciIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NDQxNERDRTY1QjY1MTFFNDkzNTRFQTVEMTdGMTdBQjciPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo0NDE0RENFMzVCNjUxMUU0OTM1NEVBNUQxN0YxN0FCNyIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo0NDE0RENFNDVCNjUxMUU0OTM1NEVBNUQxN0YxN0FCNyIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PsvuX50AAANaSURBVHja7FjRZ1tRGD9ZJ1NCyIQSwrivI4Q8hCpjlFDyFEoYfSp9Ko1QWnmo0If+BSXkIfo0QirTMUpeGo2EPfWllFYjZMLKLDJn53d3biU337m5J223bPbxk5t7v+/c3/2+73znO8fDOWezKM/YjMpz68Lj8ejY+QTeCCwLxOS9qPxtyN+6wAeBTwJ31CCO0cJDjXBGBN4LfIepSwykTUT1bgpuib0SONIgo8KRHOtRiCFcvUcgZeGrHPNBxLIyFPyRgTGz0xLbegJCdmzpElue5KlAIMDX19d5uVzm5+fnfDAYmMA17uEZdOx2Yvb/sHlu2S0xwymn5ufneTab5b1ej08S6EAXNrDd2dnhiUTim21MvMtwQ6yiIrWwsMDPzs64rsBmf3/fvM7n89TYlUnEllSkQqEQv7q64g+Vk5MTVXosORErU0Zer5f0FEIlw2N6MxwO82QyaXql2+2SxDqdjopYWUUsqEp45IldqtWq6UWVh/1+P7+8vCTJ4QMUJSRIEXuneoH96w8PDyeWAnhSJfCqwm6NIlaklFdXV0cGhRcQ2mlJQXK5nMq2YPEZbnteU1U2lUqN/D84OGD9fl+5fgnSrFarsUwmw0qlEru4uBjTicViTk3Cr27HSnxR+Doyz0ZE1CAWiUTusbu7y9rttlZv5fP5WDQavYfIMba4uEipfhF8XtqJoZXx/uH+sC/4vPg7OljZZQbsCmLtYzc3N6zRaJhotVrmfx0xDINtbm6athYUeXpHdbBNaqZUKpWxWXV7e2vex+xaWVnhc3NzjrPUXgexyCt0m67LBV7uJMITjqRE4o8tZeg8FPpFitgapYxiOC0poFgsji1jKNo6BZZckrAGUtJsNk1vqAihCBcKhTE7hNWhqw2qFnGy5UFOUYJVIJ1OjzSE+BCEilon0URavRmBqnbbQ00AXbm+vnZc9O1tj72OnQoc2+cwygRkb2+P1et17ZoEm3g87lRmjgWZ00kbXkNuse6/Bu2wlegIxfb2tuvWGroO4bO2c4bbzUh60mxDXm1sbJhhxkQYnhS4h2fUZoRAWnf7lv8N27f8P7Xhnekjgpk+VKGOoQbsiY+hhhtF3YO7twIJ+ULvUGv+GQ2fQEvWxI/THNx5/p/BaspPAQYAqStgiSQwCDoAAAAASUVORK5CYII=":IMAGE_PATH+"/refresh.png",38,38);HoverIcons.prototype.tolerance=mxClient.IS_TOUCH?6:0;HoverIcons.prototype.init=function(){this.arrowUp=this.createArrow(this.triangleUp,mxResources.get("plusTooltip"));this.arrowRight=this.createArrow(this.triangleRight,mxResources.get("plusTooltip"));this.arrowDown=this.createArrow(this.triangleDown,mxResources.get("plusTooltip"));this.arrowLeft=this.createArrow(this.triangleLeft,mxResources.get("plusTooltip"));this.elts=[this.arrowUp,this.arrowRight,this.arrowDown,this.arrowLeft];this.resetHandler=mxUtils.bind(this,function(){this.reset()});this.repaintHandler=mxUtils.bind(this,function(){this.repaint()});this.graph.selectionModel.addListener(mxEvent.CHANGE,this.resetHandler);this.graph.model.addListener(mxEvent.CHANGE,this.repaintHandler);this.graph.view.addListener(mxEvent.SCALE_AND_TRANSLATE,this.repaintHandler);this.graph.view.addListener(mxEvent.TRANSLATE,this.repaintHandler);this.graph.view.addListener(mxEvent.SCALE,this.repaintHandler);this.graph.view.addListener(mxEvent.DOWN,this.repaintHandler);this.graph.view.addListener(mxEvent.UP,this.repaintHandler);this.graph.addListener(mxEvent.ROOT,this.repaintHandler);this.graph.addListener(mxEvent.ESCAPE,this.resetHandler);mxEvent.addListener(this.graph.container,"scroll",this.resetHandler);this.graph.addListener(mxEvent.ESCAPE,mxUtils.bind(this,function(){this.mouseDownPoint=null}));mxEvent.addListener(this.graph.container,"mouseleave",mxUtils.bind(this,function(t){if(t.relatedTarget!=null&&mxEvent.getSource(t)==this.graph.container){this.setDisplay("none")}}));this.graph.addListener(mxEvent.START_EDITING,mxUtils.bind(this,function(t){this.reset()}));var e=this.graph.click;this.graph.click=mxUtils.bind(this,function(t){e.apply(this.graph,arguments);if(this.currentState!=null&&!this.graph.isCellSelected(this.currentState.cell)&&mxEvent.isTouchEvent(t.getEvent())&&!this.graph.model.isVertex(t.getCell())){this.reset()}});var r=false;this.graph.addMouseListener({mouseDown:mxUtils.bind(this,function(t,e){r=false;var i=e.getEvent();if(this.isResetEvent(i)){this.reset()}else if(!this.isActive()){var n=this.getState(e.getState());if(n!=null||!mxEvent.isTouchEvent(i)){this.update(n)}}this.setDisplay("none")}),mouseMove:mxUtils.bind(this,function(t,e){var i=e.getEvent();if(this.isResetEvent(i)){this.reset()}else if(!this.graph.isMouseDown&&!mxEvent.isTouchEvent(i)){this.update(this.getState(e.getState()),e.getGraphX(),e.getGraphY())}if(this.graph.connectionHandler!=null&&this.graph.connectionHandler.shape!=null){r=true}}),mouseUp:mxUtils.bind(this,function(t,e){var i=e.getEvent();var n=mxUtils.convertPoint(this.graph.container,mxEvent.getClientX(i),mxEvent.getClientY(i));if(this.isResetEvent(i)){this.reset()}else if(this.isActive()&&!r&&this.mouseDownPoint!=null){this.click(this.currentState,this.getDirection(),e)}else if(this.isActive()){if(this.graph.getSelectionCount()!=1||!this.graph.model.isEdge(this.graph.getSelectionCell())){this.update(this.getState(this.graph.view.getState(this.graph.getCellAt(e.getGraphX(),e.getGraphY()))))}else{this.reset()}}else if(mxEvent.isTouchEvent(i)||this.bbox!=null&&mxUtils.contains(this.bbox,e.getGraphX(),e.getGraphY())){this.setDisplay("");this.repaint()}else if(!mxEvent.isTouchEvent(i)){this.reset()}r=false;this.resetActiveArrow()})})};HoverIcons.prototype.isResetEvent=function(t,e){return mxEvent.isAltDown(t)||this.activeArrow==null&&mxEvent.isShiftDown(t)||mxEvent.isMetaDown(t)||mxEvent.isPopupTrigger(t)&&!mxEvent.isControlDown(t)};HoverIcons.prototype.createArrow=function(t,e){var i=null;if(mxClient.IS_IE&&!mxClient.IS_SVG){if(mxClient.IS_IE6&&document.compatMode!="CSS1Compat"){i=document.createElement(mxClient.VML_PREFIX+":image");i.setAttribute("src",t.src);i.style.borderStyle="none"}else{i=document.createElement("div");i.style.backgroundImage="url("+t.src+")";i.style.backgroundPosition="center";i.style.backgroundRepeat="no-repeat"}i.style.width=t.width+4+"px";i.style.height=t.height+4+"px";i.style.display=mxClient.IS_QUIRKS?"inline":"inline-block"}else{i=mxUtils.createImage(t.src);i.style.width=t.width+"px";i.style.height=t.height+"px";i.style.padding=this.tolerance+"px"}if(e!=null){i.setAttribute("title",e)}i.style.position="absolute";i.style.cursor=this.cssCursor;mxEvent.addGestureListeners(i,mxUtils.bind(this,function(t){if(this.currentState!=null&&!this.isResetEvent(t)){this.mouseDownPoint=mxUtils.convertPoint(this.graph.container,mxEvent.getClientX(t),mxEvent.getClientY(t));this.drag(t,this.mouseDownPoint.x,this.mouseDownPoint.y);this.activeArrow=i;this.setDisplay("none");mxEvent.consume(t)}}));mxEvent.redirectMouseEvents(i,this.graph,this.currentState);mxEvent.addListener(i,"mouseenter",mxUtils.bind(this,function(t){if(mxEvent.isMouseEvent(t)){if(this.activeArrow!=null&&this.activeArrow!=i){mxUtils.setOpacity(this.activeArrow,this.inactiveOpacity)}this.graph.connectionHandler.constraintHandler.reset();mxUtils.setOpacity(i,100);this.activeArrow=i}}));mxEvent.addListener(i,"mouseleave",mxUtils.bind(this,function(t){if(!this.graph.isMouseDown){this.resetActiveArrow()}}));return i};HoverIcons.prototype.resetActiveArrow=function(){if(this.activeArrow!=null){mxUtils.setOpacity(this.activeArrow,this.inactiveOpacity);this.activeArrow=null}};HoverIcons.prototype.getDirection=function(){var t=mxConstants.DIRECTION_EAST;if(this.activeArrow==this.arrowUp){t=mxConstants.DIRECTION_NORTH}else if(this.activeArrow==this.arrowDown){t=mxConstants.DIRECTION_SOUTH}else if(this.activeArrow==this.arrowLeft){t=mxConstants.DIRECTION_WEST}return t};HoverIcons.prototype.visitNodes=function(t){for(var e=0;e<this.elts.length;e++){if(this.elts[e]!=null){t(this.elts[e])}}};HoverIcons.prototype.removeNodes=function(){this.visitNodes(function(t){if(t.parentNode!=null){t.parentNode.removeChild(t)}})};HoverIcons.prototype.setDisplay=function(e){this.visitNodes(function(t){t.style.display=e})};HoverIcons.prototype.isActive=function(){return this.activeArrow!=null&&this.currentState!=null};HoverIcons.prototype.drag=function(t,e,i){this.graph.popupMenuHandler.hideMenu();this.graph.stopEditing(false);if(this.currentState!=null){this.graph.connectionHandler.start(this.currentState,e,i);this.graph.isMouseTrigger=mxEvent.isMouseEvent(t);this.graph.isMouseDown=true;var n=this.graph.selectionCellsHandler.getHandler(this.currentState.cell);if(n!=null){n.setHandlesVisible(false)}var r=this.graph.connectionHandler.edgeState;if(t!=null&&mxEvent.isShiftDown(t)&&mxEvent.isControlDown(t)&&r!=null&&mxUtils.getValue(r.style,mxConstants.STYLE_EDGE,null)==="orthogonalEdgeStyle"){var l=this.getDirection();r.cell.style=mxUtils.setStyle(r.cell.style,"sourcePortConstraint",l);r.style["sourcePortConstraint"]=l}}};HoverIcons.prototype.getStateAt=function(t,e,i){return this.graph.view.getState(this.graph.getCellAt(e,i))};HoverIcons.prototype.click=function(t,e,i){var n=i.getEvent();var r=i.getGraphX();var l=i.getGraphY();var a=this.getStateAt(t,r,l);if(a!=null&&this.graph.model.isEdge(a.cell)&&!mxEvent.isControlDown(n)&&(a.getVisibleTerminalState(true)==t||a.getVisibleTerminalState(false)==t)){this.graph.setSelectionCell(a.cell);this.reset()}else if(t!=null){this.graph.selectCellsForConnectVertex(this.graph.connectVertex(t.cell,e,this.graph.defaultEdgeLength,n),n,this)}i.consume()};HoverIcons.prototype.reset=function(t){t=t==null?true:t;if(t&&this.updateThread!=null){window.clearTimeout(this.updateThread)}this.mouseDownPoint=null;this.currentState=null;this.activeArrow=null;this.removeNodes();this.bbox=null};HoverIcons.prototype.repaint=function(){this.bbox=null;if(this.currentState!=null){this.currentState=this.getState(this.currentState);if(this.currentState!=null&&this.graph.model.isVertex(this.currentState.cell)&&this.graph.isCellConnectable(this.currentState.cell)){var t=mxRectangle.fromRectangle(this.currentState);if(this.currentState.shape!=null&&this.currentState.shape.boundingBox!=null){t=mxRectangle.fromRectangle(this.currentState.shape.boundingBox)}t.grow(this.graph.tolerance);t.grow(this.arrowSpacing);var e=this.graph.selectionCellsHandler.getHandler(this.currentState.cell);var r=null;if(e!=null){t.x-=e.horizontalOffset/2;t.y-=e.verticalOffset/2;t.width+=e.horizontalOffset;t.height+=e.verticalOffset;if(e.rotationShape!=null&&e.rotationShape.node!=null&&e.rotationShape.node.style.visibility!="hidden"&&e.rotationShape.node.style.display!="none"&&e.rotationShape.boundingBox!=null){r=e.rotationShape.boundingBox}}var i=mxUtils.bind(this,function(t,e,i){if(r!=null){var n=new mxRectangle(e,i,t.clientWidth,t.clientHeight);if(mxUtils.intersects(n,r)){if(t==this.arrowUp){i-=n.y+n.height-r.y}else if(t==this.arrowRight){e+=r.x+r.width-n.x}else if(t==this.arrowDown){i+=r.y+r.height-n.y}else if(t==this.arrowLeft){e-=n.x+n.width-r.x}}}t.style.left=e+"px";t.style.top=i+"px";mxUtils.setOpacity(t,this.inactiveOpacity)});i(this.arrowUp,Math.round(this.currentState.getCenterX()-this.triangleUp.width/2-this.tolerance),Math.round(t.y-this.triangleUp.height-this.tolerance));i(this.arrowRight,Math.round(t.x+t.width-this.tolerance),Math.round(this.currentState.getCenterY()-this.triangleRight.height/2-this.tolerance));i(this.arrowDown,parseInt(this.arrowUp.style.left),Math.round(t.y+t.height-this.tolerance));i(this.arrowLeft,Math.round(t.x-this.triangleLeft.width-this.tolerance),parseInt(this.arrowRight.style.top));if(this.checkCollisions){var n=this.graph.getCellAt(t.x+t.width+this.triangleRight.width/2,this.currentState.getCenterY());var l=this.graph.getCellAt(t.x-this.triangleLeft.width/2,this.currentState.getCenterY());var a=this.graph.getCellAt(this.currentState.getCenterX(),t.y-this.triangleUp.height/2);var s=this.graph.getCellAt(this.currentState.getCenterX(),t.y+t.height+this.triangleDown.height/2);if(n!=null&&n==l&&l==a&&a==s){n=null;l=null;a=null;s=null}var o=this.graph.getCellGeometry(this.currentState.cell);var h=mxUtils.bind(this,function(t,e){var i=this.graph.model.isVertex(t)&&this.graph.getCellGeometry(t);if(t!=null&&!this.graph.model.isAncestor(t,this.currentState.cell)&&!this.graph.isSwimlane(t)&&(i==null||o==null||i.height<3*o.height&&i.width<3*o.width)){e.style.visibility="hidden"}else{e.style.visibility="visible"}});h(n,this.arrowRight);h(l,this.arrowLeft);h(a,this.arrowUp);h(s,this.arrowDown)}else{this.arrowLeft.style.visibility="visible";this.arrowRight.style.visibility="visible";this.arrowUp.style.visibility="visible";this.arrowDown.style.visibility="visible"}if(this.graph.tooltipHandler.isEnabled()){this.arrowLeft.setAttribute("title",mxResources.get("plusTooltip"));this.arrowRight.setAttribute("title",mxResources.get("plusTooltip"));this.arrowUp.setAttribute("title",mxResources.get("plusTooltip"));this.arrowDown.setAttribute("title",mxResources.get("plusTooltip"))}else{this.arrowLeft.removeAttribute("title");this.arrowRight.removeAttribute("title");this.arrowUp.removeAttribute("title");this.arrowDown.removeAttribute("title")}}else{this.reset()}if(this.currentState!=null){this.bbox=this.computeBoundingBox();if(this.bbox!=null){this.bbox.grow(10)}}}};HoverIcons.prototype.computeBoundingBox=function(){var i=!this.graph.model.isEdge(this.currentState.cell)?mxRectangle.fromRectangle(this.currentState):null;this.visitNodes(function(t){if(t.parentNode!=null){var e=new mxRectangle(t.offsetLeft,t.offsetTop,t.offsetWidth,t.offsetHeight);if(i==null){i=e}else{i.add(e)}}});return i};HoverIcons.prototype.getState=function(t){if(t!=null){var e=t.cell;if(!this.graph.getModel().contains(e)){t=null}else{if(this.graph.getModel().isVertex(e)&&!this.graph.isCellConnectable(e)){var i=this.graph.getModel().getParent(e);if(this.graph.getModel().isVertex(i)&&this.graph.isCellConnectable(i)){e=i}}if(this.graph.isCellLocked(e)||this.graph.model.isEdge(e)){e=null}t=this.graph.view.getState(e);if(t!=null&&t.style==null){t=null}}}return t};HoverIcons.prototype.update=function(t,e,i){if(!this.graph.connectionArrowsEnabled||t!=null&&mxUtils.getValue(t.style,"allowArrows","1")=="0"){this.reset()}else{if(t!=null&&t.cell.geometry!=null&&t.cell.geometry.relative&&this.graph.model.isEdge(t.cell.parent)){t=null}var n=null;if(this.prev!=t||this.isActive()){this.startTime=(new Date).getTime();this.prev=t;n=0;if(this.updateThread!=null){window.clearTimeout(this.updateThread)}if(t!=null){this.updateThread=window.setTimeout(mxUtils.bind(this,function(){if(!this.isActive()&&!this.graph.isMouseDown&&!this.graph.panningHandler.isActive()){this.prev=t;this.update(t,e,i)}}),this.updateDelay+10)}}else if(this.startTime!=null){n=(new Date).getTime()-this.startTime}this.setDisplay("");if(this.currentState!=null&&this.currentState!=t&&n<this.activationDelay&&this.bbox!=null&&!mxUtils.contains(this.bbox,e,i)){this.reset(false)}else if(this.currentState!=null||n>this.activationDelay){if(this.currentState!=t&&(n>this.updateDelay&&t!=null||this.bbox==null||e==null||i==null||!mxUtils.contains(this.bbox,e,i))){if(t!=null&&this.graph.isEnabled()){this.removeNodes();this.setCurrentState(t);this.repaint();if(this.graph.connectionHandler.constraintHandler.currentFocus!=t){this.graph.connectionHandler.constraintHandler.reset()}}else{this.reset()}}}}};HoverIcons.prototype.setCurrentState=function(t){if(t.style["portConstraint"]!="eastwest"){this.graph.container.appendChild(this.arrowUp);this.graph.container.appendChild(this.arrowDown)}this.graph.container.appendChild(this.arrowRight);this.graph.container.appendChild(this.arrowLeft);this.currentState=t};(function(){var t=mxGraphView.prototype.resetValidationState;mxGraphView.prototype.resetValidationState=function(){t.apply(this,arguments);this.validEdges=[]};var a=mxGraphView.prototype.validateCellState;mxGraphView.prototype.validateCellState=function(t,e){e=e!=null?e:true;var i=this.getState(t);if(i!=null&&e&&this.graph.model.isEdge(i.cell)&&i.style!=null&&i.style[mxConstants.STYLE_CURVED]!=1&&!i.invalid&&this.updateLineJumps(i)){this.graph.cellRenderer.redraw(i,false,this.isRendering())}i=a.apply(this,arguments);if(i&&i.cell&&i.cell.customDom){if(i.cell.customDom.isClone||!i.cell.customDom.id){if(!i.cell.customDom.id){var n=i.view.graph.renderConfig&&i.view.graph.renderConfig.customDomFns.idCreat;if(typeof n==="function"){i.cell.customDom.id=n()}}var r=i.view.graph.renderConfig&&i.view.graph.renderConfig.customDomFns.dropFn;if(typeof r==="function"){if(!i.cell.customDom.dropFn){i.cell.customDom.dropFn=r}i.cell.customDom.dropFn({w:i.width,h:i.height,x:i.x,y:i.y,id:i.cell.id},i.cell.customDom.data,i.cell.customDom.customFo)}i.cell.customDom.isClone=false;mxUtils.recursiveSetChildren([i.cell],function(t){if(t.customDom){if(typeof n==="function"){if(!t.customDom.id){t.customDom.id=n()}}if(typeof r==="function"){if(!t.customDom.dropFn){t.customDom.dropFn=r}}t.customDom.isClone=false}})}else{var l=i.view.graph.renderConfig&&i.view.graph.renderConfig.customDomFns.updateFn;if(typeof l==="function"){if(!i.cell.customDom.updateFn){i.cell.customDom.updateFn=l;mxUtils.recursiveSetChildren([i.cell],function(t){if(t.customDom){t.customDom.updateFn=l}})}i.cell.customDom.updateFn({w:i.width,h:i.height,x:i.x,y:i.y,id:i.cell.id},i.cell)}}}else if(i&&i.cell&&i.cell.sceneComponent){if(i.cell.sceneComponent.isClone){var r=i.cell.sceneComponent.dropFn;if(typeof r==="function"){r(i.cell.sceneComponent.configChange)}i.cell.sceneComponent.isClone=false;mxUtils.recursiveSetChildren([i.cell],function(t){if(t.sceneComponent){t.sceneComponent.isClone=false}})}}if(i!=null&&e&&this.graph.model.isEdge(i.cell)&&i.style!=null&&i.style[mxConstants.STYLE_CURVED]!=1){this.validEdges.push(i)}return i};var i=mxCellRenderer.prototype.isShapeInvalid;mxCellRenderer.prototype.isShapeInvalid=function(t,e){return i.apply(this,arguments)||t.routedPoints!=null&&e.routedPoints!=null&&!mxUtils.equalPoints(e.routedPoints,t.routedPoints)};var e=mxGraphView.prototype.updateCellState;mxGraphView.prototype.updateCellState=function(t){e.apply(this,arguments);if(this.graph.model.isEdge(t.cell)&&t.style[mxConstants.STYLE_CURVED]!=1){this.updateLineJumps(t)}};mxGraphView.prototype.updateLineJumps=function(l){var t=l.absolutePoints;if(Graph.lineJumpsEnabled){var e=l.routedPoints!=null;var a=null;if(t!=null&&this.validEdges!=null&&mxUtils.getValue(l.style,"jumpStyle","none")!=="none"){var i=.5*this.scale;e=false;a=[];function n(t,e,i){var n=new mxPoint(e,i);n.type=t;a.push(n);var r=l.routedPoints!=null?l.routedPoints[a.length-1]:null;return r==null||r.type!=t||r.x!=e||r.y!=i}for(var r=0;r<t.length-1;r++){var s=t[r+1];var o=t[r];var h=[];var u=t[r+2];while(r<t.length-2&&mxUtils.ptSegDistSq(o.x,o.y,u.x,u.y,s.x,s.y)<1*this.scale*this.scale){s=u;r++;u=t[r+2]}e=n(0,o.x,o.y)||e;for(var p=0;p<this.validEdges.length;p++){var c=this.validEdges[p];var g=c.absolutePoints;if(g!=null&&mxUtils.intersects(l,c)&&c.style["noJump"]!="1"){for(var d=0;d<g.length-1;d++){var m=g[d+1];var v=g[d];u=g[d+2];while(d<g.length-2&&mxUtils.ptSegDistSq(v.x,v.y,u.x,u.y,m.x,m.y)<1*this.scale*this.scale){m=u;d++;u=g[d+2]}var f=mxUtils.intersection(o.x,o.y,s.x,s.y,v.x,v.y,m.x,m.y);if(f!=null&&(Math.abs(f.x-o.x)>i||Math.abs(f.y-o.y)>i)&&(Math.abs(f.x-s.x)>i||Math.abs(f.y-s.y)>i)&&(Math.abs(f.x-v.x)>i||Math.abs(f.y-v.y)>i)&&(Math.abs(f.x-m.x)>i||Math.abs(f.y-m.y)>i)){var x=f.x-o.x;var y=f.y-o.y;var C={distSq:x*x+y*y,x:f.x,y:f.y};for(var S=0;S<h.length;S++){if(h[S].distSq>C.distSq){h.splice(S,0,C);C=null;break}}if(C!=null&&(h.length==0||h[h.length-1].x!==C.x||h[h.length-1].y!==C.y)){h.push(C)}}}}}for(var d=0;d<h.length;d++){e=n(1,h[d].x,h[d].y)||e}}var f=t[t.length-1];e=n(0,f.x,f.y)||e}l.routedPoints=a;return e}else{return false}};var E=mxConnector.prototype.paintLine;mxConnector.prototype.paintLine=function(t,e,i){this.routedPoints=this.state!=null?this.state.routedPoints:null;if(this.outline||this.state==null||this.style==null||this.state.routedPoints==null||this.state.routedPoints.length==0){E.apply(this,arguments)}else{var n=mxUtils.getValue(this.style,mxConstants.STYLE_ARCSIZE,mxConstants.LINE_ARCSIZE)/2;var r=(parseInt(mxUtils.getValue(this.style,"jumpSize",Graph.defaultJumpSize))-2)/2+this.strokewidth;var l=mxUtils.getValue(this.style,"jumpStyle","none");var a=true;var s=null;var o=null;var h=[];var u=null;t.begin();for(var p=0;p<this.state.routedPoints.length;p++){var c=this.state.routedPoints[p];var g=new mxPoint(c.x/this.scale,c.y/this.scale);if(p==0){g=e[0]}else if(p==this.state.routedPoints.length-1){g=e[e.length-1]}var d=false;if(s!=null&&c.type==1){var m=this.state.routedPoints[p+1];var v=m.x/this.scale-g.x;var f=m.y/this.scale-g.y;var x=v*v+f*f;if(u==null){u=new mxPoint(g.x-s.x,g.y-s.y);o=Math.sqrt(u.x*u.x+u.y*u.y);if(o>0){u.x=u.x*r/o;u.y=u.y*r/o}else{u=null}}if(x>r*r&&o>0){var v=s.x-g.x;var f=s.y-g.y;var x=v*v+f*f;if(x>r*r){var y=new mxPoint(g.x-u.x,g.y-u.y);var C=new mxPoint(g.x+u.x,g.y+u.y);h.push(y);this.addPoints(t,h,i,n,false,null,a);var S=Math.round(u.x)<0||Math.round(u.x)==0&&Math.round(u.y)<=0?1:-1;a=false;if(l=="sharp"){t.lineTo(y.x-u.y*S,y.y+u.x*S);t.lineTo(C.x-u.y*S,C.y+u.x*S);t.lineTo(C.x,C.y)}else if(l=="arc"){S*=1.3;t.curveTo(y.x-u.y*S,y.y+u.x*S,C.x-u.y*S,C.y+u.x*S,C.x,C.y)}else{t.moveTo(C.x,C.y);a=true}h=[C];d=true}}}else{u=null}if(!d){h.push(g);s=g}}this.addPoints(t,h,i,n,false,null,a);t.stroke()}};var c=mxGraphView.prototype.updateFloatingTerminalPoint;mxGraphView.prototype.updateFloatingTerminalPoint=function(t,e,i,n){if(e!=null&&t!=null&&(e.style["snapToPoint"]=="1"||t.style["snapToPoint"]=="1")){e=this.getTerminalPort(t,e,n);var r=this.getNextPoint(t,i,n);var l=this.graph.isOrthogonal(t);var a=mxUtils.toRadians(Number(e.style[mxConstants.STYLE_ROTATION]||"0"));var s=new mxPoint(e.getCenterX(),e.getCenterY());if(a!=0){var o=Math.cos(-a);var h=Math.sin(-a);r=mxUtils.getRotatedPoint(r,o,h,s)}var u=parseFloat(t.style[mxConstants.STYLE_PERIMETER_SPACING]||0);u+=parseFloat(t.style[n?mxConstants.STYLE_SOURCE_PERIMETER_SPACING:mxConstants.STYLE_TARGET_PERIMETER_SPACING]||0);var p=this.getPerimeterPoint(e,r,a==0&&l,u);if(a!=0){var o=Math.cos(a);var h=Math.sin(a);p=mxUtils.getRotatedPoint(p,o,h,s)}t.setAbsoluteTerminalPoint(this.snapToAnchorPoint(t,e,i,n,p),n)}else{c.apply(this,arguments)}};mxGraphView.prototype.snapToAnchorPoint=function(t,e,i,n,r){if(e!=null&&t!=null){var l=this.graph.getAllConnectionConstraints(e);var a=null;var s=null;if(l!=null){for(var o=0;o<l.length;o++){var h=this.graph.getConnectionPoint(e,l[o]);if(h!=null){var u=(h.x-r.x)*(h.x-r.x)+(h.y-r.y)*(h.y-r.y);if(s==null||u<s){a=h;s=u}}}}if(a!=null){r=a}}return r};var l=mxStencil.prototype.evaluateTextAttribute;mxStencil.prototype.evaluateTextAttribute=function(t,e,i){var n=l.apply(this,arguments);var r=t.getAttribute("placeholders");if(r=="1"&&i.state!=null){n=i.state.view.graph.replacePlaceholders(i.state.cell,n)}return n};var r=mxCellRenderer.prototype.createShape;mxCellRenderer.prototype.createShape=function(t){if(t.style!=null&&typeof pako!=="undefined"){var e=mxUtils.getValue(t.style,mxConstants.STYLE_SHAPE,null);if(e!=null&&typeof e==="string"&&e.substring(0,8)=="stencil("){try{var i=e.substring(8,e.length-1);var n=mxUtils.parseXml(Graph.decompress(i));return new mxShape(new mxStencil(n.documentElement))}catch(t){if(window.console!=null){console.log("Error in shape: "+t)}}}}return r.apply(this,arguments)}})();mxStencilRegistry.libraries={};mxStencilRegistry.dynamicLoading=true;mxStencilRegistry.allowEval=true;mxStencilRegistry.packages=[];mxStencilRegistry.getStencil=function(t){var e=mxStencilRegistry.stencils[t];if(e==null&&mxCellRenderer.defaultShapes[t]==null&&mxStencilRegistry.dynamicLoading){var i=mxStencilRegistry.getBasenameForStencil(t);if(i!=null){var n=mxStencilRegistry.libraries[i];if(n!=null){if(mxStencilRegistry.packages[i]==null){for(var r=0;r<n.length;r++){var l=n[r];if(l.toLowerCase().substring(l.length-4,l.length)==".xml"){mxStencilRegistry.loadStencilSet(l,null)}else if(l.toLowerCase().substring(l.length-3,l.length)==".js"){try{if(mxStencilRegistry.allowEval){var a=mxUtils.load(l);if(a!=null&&a.getStatus()>=200&&a.getStatus()<=299){eval.call(window,a.getText())}}}catch(t){if(window.console!=null){console.log("error in getStencil:",l,t)}}}else{}}mxStencilRegistry.packages[i]=1}}else{i=i.replace("_-_","_");mxStencilRegistry.loadStencilSet(STENCIL_PATH+"/"+i+".xml",null)}e=mxStencilRegistry.stencils[t]}}return e};mxStencilRegistry.getBasenameForStencil=function(t){var e=null;if(t!=null&&typeof t==="string"){var i=t.split(".");if(i.length>0&&i[0]=="mxgraph"){e=i[1];for(var n=2;n<i.length-1;n++){e+="/"+i[n]}}}return e};mxStencilRegistry.loadStencilSet=function(e,i,t,n){t=t!=null?t:false;var r=mxStencilRegistry.packages[e];if(t||r==null){var l=false;if(r==null){try{if(n){mxStencilRegistry.loadStencil(e,mxUtils.bind(this,function(t){if(t!=null&&t.documentElement!=null){mxStencilRegistry.packages[e]=t;l=true;mxStencilRegistry.parseStencilSet(t.documentElement,i,l)}}));return}else{r=mxStencilRegistry.loadStencil(e);mxStencilRegistry.packages[e]=r;l=true}}catch(t){if(window.console!=null){console.log("error in loadStencilSet:",e,t)}}}if(r!=null&&r.documentElement!=null){mxStencilRegistry.parseStencilSet(r.documentElement,i,l)}}};mxStencilRegistry.loadStencil=function(t,e){if(e!=null){var i=mxUtils.get(t,mxUtils.bind(this,function(t){e(t.getStatus()>=200&&t.getStatus()<=299?t.getXml():null)}))}else{return mxUtils.load(t).getXml()}};mxStencilRegistry.parseStencilSets=function(t){for(var e=0;e<t.length;e++){mxStencilRegistry.parseStencilSet(mxUtils.parseXml(t[e]).documentElement)}};mxStencilRegistry.parseStencilSet=function(t,e,i){if(t.nodeName=="stencils"){var n=t.firstChild;while(n!=null){if(n.nodeName=="shapes"){mxStencilRegistry.parseStencilSet(n,e,i)}n=n.nextSibling}}else{i=i!=null?i:true;var r=t.firstChild;var l="";var a=t.getAttribute("name");if(a!=null){l=a+"."}while(r!=null){if(r.nodeType==mxConstants.NODETYPE_ELEMENT){a=r.getAttribute("name");if(a!=null){l=l.toLowerCase();var s=a.replace(/ /g,"_");if(i){mxStencilRegistry.addStencil(l+s.toLowerCase(),new mxStencil(r))}if(e!=null){var o=r.getAttribute("w");var h=r.getAttribute("h");o=o==null?80:parseInt(o,10);h=h==null?80:parseInt(h,10);e(l,s,a,o,h)}}}r=r.nextSibling}}};if(typeof mxVertexHandler!="undefined"){(function(){mxConstants.HANDLE_FILLCOLOR="#29b6f2";mxConstants.HANDLE_STROKECOLOR="#0088cf";mxConstants.VERTEX_SELECTION_COLOR="#00a8ff";mxConstants.OUTLINE_COLOR="#00a8ff";mxConstants.OUTLINE_HANDLE_FILLCOLOR="#99ccff";mxConstants.OUTLINE_HANDLE_STROKECOLOR="#00a8ff";mxConstants.CONNECT_HANDLE_FILLCOLOR="#cee7ff";mxConstants.EDGE_SELECTION_COLOR="#00a8ff";mxConstants.DEFAULT_VALID_COLOR="#00a8ff";mxConstants.LABEL_HANDLE_FILLCOLOR="#cee7ff";mxConstants.GUIDE_COLOR="#0088cf";mxConstants.HIGHLIGHT_OPACITY=30;mxConstants.HIGHLIGHT_SIZE=5;mxEdgeHandler.prototype.snapToTerminals=true;mxGraphHandler.prototype.guidesEnabled=true;mxGraphHandler.prototype.removeEmptyParents=true;mxRubberband.prototype.fadeOut=true;mxGuide.prototype.isEnabledForEvent=function(t){return!mxEvent.isAltDown(t)};var e=mxConnectionHandler.prototype.isCreateTarget;mxConnectionHandler.prototype.isCreateTarget=function(t){return mxEvent.isControlDown(t)||e.apply(this,arguments)};mxConstraintHandler.prototype.createHighlightShape=function(){var t=new mxEllipse(null,this.highlightColor,this.highlightColor,0);t.opacity=mxConstants.HIGHLIGHT_OPACITY;return t};mxConnectionHandler.prototype.livePreview=true;mxConnectionHandler.prototype.cursor="crosshair";mxConnectionHandler.prototype.createEdgeState=function(t){var e=this.graph.createCurrentEdgeStyle();var i=this.graph.createEdge(null,null,null,null,null,e);var n=new mxCellState(this.graph.view,i,this.graph.getCellStyle(i));for(var r in this.graph.currentEdgeStyle){n.style[r]=this.graph.currentEdgeStyle[r]}return n};var i=mxConnectionHandler.prototype.createShape;mxConnectionHandler.prototype.createShape=function(){var t=i.apply(this,arguments);t.isDashed=this.graph.currentEdgeStyle[mxConstants.STYLE_DASHED]=="1";return t};mxConnectionHandler.prototype.updatePreview=function(t){};var n=mxConnectionHandler.prototype.createMarker;mxConnectionHandler.prototype.createMarker=function(){var t=n.apply(this,arguments);var i=t.getCell;t.getCell=mxUtils.bind(this,function(t){var e=i.apply(this,arguments);this.error=null;return e});return t};Graph.prototype.defaultVertexStyle={};Graph.prototype.defaultEdgeStyle={edgeStyle:"orthogonalEdgeStyle",rounded:"0",jettySize:"auto",orthogonalLoop:"1"};Graph.prototype.createCurrentEdgeStyle=function(){var t="edgeStyle="+(this.currentEdgeStyle["edgeStyle"]||"none")+";";if(this.currentEdgeStyle["shape"]!=null){t+="shape="+this.currentEdgeStyle["shape"]+";"}if(this.currentEdgeStyle["curved"]!=null){t+="curved="+this.currentEdgeStyle["curved"]+";"}if(this.currentEdgeStyle["rounded"]!=null){t+="rounded="+this.currentEdgeStyle["rounded"]+";"}if(this.currentEdgeStyle["comic"]!=null){t+="comic="+this.currentEdgeStyle["comic"]+";"}if(this.currentEdgeStyle["jumpStyle"]!=null){t+="jumpStyle="+this.currentEdgeStyle["jumpStyle"]+";"}if(this.currentEdgeStyle["jumpSize"]!=null){t+="jumpSize="+this.currentEdgeStyle["jumpSize"]+";"}if(this.currentEdgeStyle["orthogonalLoop"]!=null){t+="orthogonalLoop="+this.currentEdgeStyle["orthogonalLoop"]+";"}else if(Graph.prototype.defaultEdgeStyle["orthogonalLoop"]!=null){t+="orthogonalLoop="+Graph.prototype.defaultEdgeStyle["orthogonalLoop"]+";"}if(this.currentEdgeStyle["jettySize"]!=null){t+="jettySize="+this.currentEdgeStyle["jettySize"]+";"}else if(Graph.prototype.defaultEdgeStyle["jettySize"]!=null){t+="jettySize="+Graph.prototype.defaultEdgeStyle["jettySize"]+";"}if(this.currentEdgeStyle["edgeStyle"]=="elbowEdgeStyle"&&this.currentEdgeStyle["elbow"]!=null){t+="elbow="+this.currentEdgeStyle["elbow"]+";"}if(this.currentEdgeStyle["html"]!=null){t+="html="+this.currentEdgeStyle["html"]+";"}else{t+="html=1;"}return t};Graph.prototype.getPagePadding=function(){return new mxPoint(0,0)};Graph.prototype.loadStylesheet=function(){var t=this.themes!=null?this.themes[this.defaultThemeName]:!mxStyleRegistry.dynamicLoading?null:mxUtils.load(STYLE_PATH+"/default.xml").getDocumentElement();if(t!=null){var e=new mxCodec(t.ownerDocument);e.decode(t,this.getStylesheet())}};Graph.prototype.createCellLookup=function(t,e){e=e!=null?e:new Object;for(var i=0;i<t.length;i++){var n=t[i];e[mxObjectIdentity.get(n)]=n.getId();var r=this.model.getChildCount(n);for(var l=0;l<r;l++){this.createCellLookup([this.model.getChildAt(n,l)],e)}}return e};Graph.prototype.createCellMapping=function(t,e,i){i=i!=null?i:new Object;for(var n in t){var r=e[n];if(i[r]==null){i[r]=t[n].getId()||""}}return i};Graph.prototype.importGraphModel=function(t,e,i,n){e=e!=null?e:0;i=i!=null?i:0;var r=new mxCodec(t.ownerDocument);var l=new mxGraphModel;r.decode(t,l);var a=[];var s=new Object;var o=new Object;var h=l.getChildren(this.cloneCell(l.root,this.isCloneInvalidEdges(),s));if(h!=null){var u=this.createCellLookup([l.root]);h=h.slice();this.model.beginUpdate();try{if(h.length==1&&!this.isCellLocked(this.getDefaultParent())){a=this.moveCells(l.getChildren(h[0]),e,i,false,this.getDefaultParent());o[l.getChildAt(l.root,0).getId()]=this.getDefaultParent().getId()}else{for(var p=0;p<h.length;p++){a=a.concat(this.model.getChildren(this.moveCells([h[p]],e,i,false,this.model.getRoot())[0]))}}this.createCellMapping(s,u,o);this.updateCustomLinks(o,a);if(n){if(this.isGridEnabled()){e=this.snap(e);i=this.snap(i)}var c=this.getBoundingBoxFromGeometry(a,true);if(c!=null){this.moveCells(a,e-c.x,i-c.y)}}}finally{this.model.endUpdate()}}return a};Graph.prototype.encodeCells=function(t){var e=new Object;var i=this.cloneCells(t,null,e);var n=new mxDictionary;for(var r=0;r<t.length;r++){n.put(t[r],true)}for(var r=0;r<i.length;r++){var l=this.view.getState(t[r]);if(l!=null){var a=this.getCellGeometry(i[r]);if(a!=null&&a.relative&&!this.model.isEdge(t[r])&&!n.get(this.model.getParent(t[r]))){a.relative=false;a.x=l.x/l.view.scale-l.view.translate.x;a.y=l.y/l.view.scale-l.view.translate.y}}}var s=new mxCodec;var o=new mxGraphModel;var h=o.getChildAt(o.getRoot(),0);for(var r=0;r<i.length;r++){o.add(h,i[r])}this.updateCustomLinks(this.createCellMapping(e,this.createCellLookup(t)),i);return s.encode(o)};var o=Graph.prototype.moveCells;Graph.prototype.moveCells=function(t,e,i,n,r,l,a){a=a!=null?a:new Object;var s=o.apply(this,arguments);if(n){this.updateCustomLinks(this.createCellMapping(a,this.createCellLookup(t)),s)}return s};Graph.prototype.updateCustomLinks=function(t,e){for(var i=0;i<e.length;i++){if(e[i]!=null){this.updateCustomLinksForCell(t,e[i])}}};Graph.prototype.updateCustomLinksForCell=function(t,e){};Graph.prototype.getAllConnectionConstraints=function(t,e){if(t!=null){var i=mxUtils.getValue(t.style,"points",null);if(i!=null){var n=[];try{var r=JSON.parse(i);for(var l=0;l<r.length;l++){var a=r[l];n.push(new mxConnectionConstraint(new mxPoint(a[0],a[1]),a.length>2?a[2]!="0":true,null,a.length>3?a[3]:0,a.length>4?a[4]:0))}}catch(t){}return n}else if(t.shape!=null&&t.shape.bounds!=null){var s=t.shape.direction;var o=t.shape.bounds;var h=t.shape.scale;var u=o.width/h;var p=o.height/h;if(s==mxConstants.DIRECTION_NORTH||s==mxConstants.DIRECTION_SOUTH){var a=u;u=p;p=a}i=t.shape.getConstraints(t.style,u,p);if(i!=null){return i}else if(t.shape.stencil!=null&&t.shape.stencil.constraints!=null){return t.shape.stencil.constraints}else if(t.shape.constraints!=null){return t.shape.constraints}}}return null};Graph.prototype.flipEdge=function(t){if(t!=null){var e=this.view.getState(t);var i=e!=null?e.style:this.getCellStyle(t);if(i!=null){var n=mxUtils.getValue(i,mxConstants.STYLE_ELBOW,mxConstants.ELBOW_HORIZONTAL);var r=n==mxConstants.ELBOW_HORIZONTAL?mxConstants.ELBOW_VERTICAL:mxConstants.ELBOW_HORIZONTAL;this.setCellStyles(mxConstants.STYLE_ELBOW,r,[t])}}};Graph.prototype.isValidRoot=function(t){var e=this.model.getChildCount(t);var i=0;for(var n=0;n<e;n++){var r=this.model.getChildAt(t,n);if(this.model.isVertex(r)){var l=this.getCellGeometry(r);if(l!=null&&!l.relative){i++}}}return i>0||this.isContainer(t)};Graph.prototype.isValidDropTarget=function(t){var e=this.view.getState(t);var i=e!=null?e.style:this.getCellStyle(t);return mxUtils.getValue(i,"part","0")!="1"&&(this.isContainer(t)||mxGraph.prototype.isValidDropTarget.apply(this,arguments)&&mxUtils.getValue(i,"dropTarget","1")!="0")};Graph.prototype.createGroupCell=function(){var t=mxGraph.prototype.createGroupCell.apply(this,arguments);t.setStyle("group");return t};Graph.prototype.isExtendParentsOnAdd=function(t){var e=mxGraph.prototype.isExtendParentsOnAdd.apply(this,arguments);if(e&&t!=null&&this.layoutManager!=null){var i=this.model.getParent(t);if(i!=null){var n=this.layoutManager.getLayout(i);if(n!=null&&n.constructor==mxStackLayout){e=false}}}return e};Graph.prototype.getPreferredSizeForCell=function(t){var e=mxGraph.prototype.getPreferredSizeForCell.apply(this,arguments);if(e!=null){e.width+=10;e.height+=4;if(this.gridEnabled){e.width=this.snap(e.width);e.height=this.snap(e.height)}}return e};Graph.prototype.turnShapes=function(t){var e=this.getModel();var i=[];e.beginUpdate();try{for(var n=0;n<t.length;n++){var r=t[n];if(e.isEdge(r)){var l=e.getTerminal(r,true);var a=e.getTerminal(r,false);e.setTerminal(r,a,true);e.setTerminal(r,l,false);var s=e.getGeometry(r);if(s!=null){s=s.clone();if(s.points!=null){s.points.reverse()}var o=s.getTerminalPoint(true);var h=s.getTerminalPoint(false);s.setTerminalPoint(o,false);s.setTerminalPoint(h,true);e.setGeometry(r,s);var u=this.view.getState(r);var p=this.view.getState(l);var c=this.view.getState(a);if(u!=null){var g=p!=null?this.getConnectionConstraint(u,p,true):null;var d=c!=null?this.getConnectionConstraint(u,c,false):null;this.setConnectionConstraint(r,l,true,d);this.setConnectionConstraint(r,a,false,g)}i.push(r)}}else if(e.isVertex(r)){var s=this.getCellGeometry(r);if(s!=null){s=s.clone();s.x+=s.width/2-s.height/2;s.y+=s.height/2-s.width/2;var m=s.width;s.width=s.height;s.height=m;e.setGeometry(r,s);var v=this.view.getState(r);if(v!=null){var f=v.style[mxConstants.STYLE_DIRECTION]||"east";if(f=="east"){f="south"}else if(f=="south"){f="west"}else if(f=="west"){f="north"}else if(f=="north"){f="east"}this.setCellStyles(mxConstants.STYLE_DIRECTION,f,[r])}i.push(r)}}}}finally{e.endUpdate()}return i};Graph.prototype.stencilHasPlaceholders=function(t){if(t!=null&&t.fgNode!=null){var e=t.fgNode.firstChild;while(e!=null){if(e.nodeName=="text"&&e.getAttribute("placeholders")=="1"){return true}e=e.nextSibling}}return false};Graph.prototype.processChange=function(t){mxGraph.prototype.processChange.apply(this,arguments);if(t instanceof mxValueChange&&t.cell!=null&&t.cell.value!=null&&typeof t.cell.value=="object"){var e=this.model.getDescendants(t.cell);if(e.length>0){for(var i=0;i<e.length;i++){var n=this.view.getState(e[i]);if(n!=null&&n.shape!=null&&n.shape.stencil!=null&&this.stencilHasPlaceholders(n.shape.stencil)){this.removeStateForCell(e[i])}else if(this.isReplacePlaceholders(e[i])){this.view.invalidate(e[i],false,false)}}}}};Graph.prototype.replaceElement=function(t,e){var i=t.ownerDocument.createElement(e!=null?e:"span");var n=Array.prototype.slice.call(t.attributes);while(attr=n.pop()){i.setAttribute(attr.nodeName,attr.nodeValue)}i.innerHTML=t.innerHTML;t.parentNode.replaceChild(i,t)};Graph.prototype.processElements=function(t,e){if(t!=null){var i=t.getElementsByTagName("*");for(var n=0;n<i.length;n++){e(i[n])}}};Graph.prototype.updateLabelElements=function(t,e,i){t=t!=null?t:this.getSelectionCells();var n=document.createElement("div");for(var r=0;r<t.length;r++){if(this.isHtmlLabel(t[r])){var l=this.convertValueToString(t[r]);if(l!=null&&l.length>0){n.innerHTML=l;var a=n.getElementsByTagName(i!=null?i:"*");for(var s=0;s<a.length;s++){e(a[s])}if(n.innerHTML!=l){this.cellLabelChanged(t[r],n.innerHTML)}}}}};Graph.prototype.cellLabelChanged=function(t,e,i){e=Graph.zapGremlins(e);this.model.beginUpdate();try{if(t.value!=null&&typeof t.value=="object"){if(this.isReplacePlaceholders(t)&&t.getAttribute("placeholder")!=null){var n=t.getAttribute("placeholder");var r=t;while(r!=null){if(r==this.model.getRoot()||r.value!=null&&typeof r.value=="object"&&r.hasAttribute(n)){this.setAttributeForCell(r,n,e);break}r=this.model.getParent(r)}}var l=t.value.cloneNode(true);l.setAttribute("label",e);e=l}mxGraph.prototype.cellLabelChanged.apply(this,arguments)}finally{this.model.endUpdate()}};Graph.prototype.cellsRemoved=function(t){if(t!=null){var e=new mxDictionary;for(var i=0;i<t.length;i++){e.put(t[i],true)}var n=[];for(var i=0;i<t.length;i++){var r=this.model.getParent(t[i]);if(r!=null&&!e.get(r)){e.put(r,true);n.push(r)}}for(var i=0;i<n.length;i++){var l=this.view.getState(n[i]);if(l!=null&&(this.model.isEdge(l.cell)||this.model.isVertex(l.cell))&&this.isCellDeletable(l.cell)&&this.isTransparentState(l)){var a=true;for(var s=0;s<this.model.getChildCount(l.cell)&&a;s++){if(!e.get(this.model.getChildAt(l.cell,s))){a=false}}if(a){t.push(l.cell)}}}}mxGraph.prototype.cellsRemoved.apply(this,arguments)};Graph.prototype.removeCellsAfterUngroup=function(t){var e=[];for(var i=0;i<t.length;i++){if(this.isCellDeletable(t[i])&&this.isTransparentState(this.view.getState(t[i]))){e.push(t[i])}}t=e;mxGraph.prototype.removeCellsAfterUngroup.apply(this,arguments)};Graph.prototype.setLinkForCell=function(t,e){this.setAttributeForCell(t,"link",e)};Graph.prototype.setTooltipForCell=function(t,e){this.setAttributeForCell(t,"tooltip",e)};Graph.prototype.getAttributeForCell=function(t,e,i){return t.value!=null&&typeof t.value==="object"?t.value.getAttribute(e)||i:i};Graph.prototype.setAttributeForCell=function(t,e,i){var n=null;if(t.value!=null&&typeof t.value=="object"){n=t.value.cloneNode(true)}else{var r=mxUtils.createXmlDocument();n=r.createElement("UserObject");n.setAttribute("label",t.value||"")}if(i!=null){n.setAttribute(e,i)}else{n.removeAttribute(e)}this.model.setValue(t,n)};Graph.prototype.getDropTarget=function(t,e,i,n){var r=this.getModel();if(mxEvent.isAltDown(e)){return null}for(var l=0;l<t.length;l++){if(this.model.isEdge(this.model.getParent(t[l]))){return null}}return mxGraph.prototype.getDropTarget.apply(this,arguments)};Graph.prototype.click=function(t){mxGraph.prototype.click.call(this,t);this.firstClickState=t.getState();this.firstClickSource=t.getSource()};Graph.prototype.dblClick=function(t,e){if(this.isEnabled()){var i=mxUtils.convertPoint(this.container,mxEvent.getClientX(t),mxEvent.getClientY(t));if(t!=null&&!this.model.isVertex(e)){var n=this.model.isEdge(e)?this.view.getState(e):null;var r=mxEvent.getSource(t);if(this.firstClickState==n&&this.firstClickSource==r&&(n==null||n.text==null||n.text.node==null||n.text.boundingBox==null||!mxUtils.contains(n.text.boundingBox,i.x,i.y)&&!mxUtils.isAncestorNode(n.text.node,mxEvent.getSource(t)))&&(n==null&&!this.isCellLocked(this.getDefaultParent())||n!=null&&!this.isCellLocked(n.cell))&&(n!=null||mxClient.IS_VML&&r==this.view.getCanvas()||mxClient.IS_SVG&&r==this.view.getCanvas().ownerSVGElement)){if(this.isFrozen()){return}e=this.addText(i.x,i.y,n)}}mxGraph.prototype.dblClick.call(this,t,e)}};Graph.prototype.getInsertPoint=function(){var t=this.getGridSize();var e=this.container.scrollLeft/this.view.scale-this.view.translate.x;var i=this.container.scrollTop/this.view.scale-this.view.translate.y;if(this.pageVisible){var n=this.getPageLayout();var r=this.getPageSize();e=Math.max(e,n.x*r.width);i=Math.max(i,n.y*r.height)}return new mxPoint(this.snap(e+t),this.snap(i+t))};Graph.prototype.getFreeInsertPoint=function(){var t=this.view;var e=this.getGraphBounds();var i=this.getInsertPoint();var n=this.snap(Math.round(Math.max(i.x,e.x/t.scale-t.translate.x+(e.width==0?2*this.gridSize:0))));var r=this.snap(Math.round(Math.max(i.y,(e.y+e.height)/t.scale-t.translate.y+2*this.gridSize)));return new mxPoint(n,r)};Graph.prototype.isMouseInsertPoint=function(){return false};Graph.prototype.addText=function(t,e,i){var n=new mxCell;n.value="Text";n.style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];";n.geometry=new mxGeometry(0,0,0,0);n.vertex=true;if(i!=null){n.style+="labelBackgroundColor=#ffffff;";n.geometry.relative=true;n.connectable=false;var r=this.view.getRelativePoint(i,t,e);n.geometry.x=Math.round(r.x*1e4)/1e4;n.geometry.y=Math.round(r.y);n.geometry.offset=new mxPoint(0,0);r=this.view.getPoint(i,n.geometry);var l=this.view.scale;n.geometry.offset=new mxPoint(Math.round((t-r.x)/l),Math.round((e-r.y)/l))}else{n.style+="autosize=1;";var a=this.view.translate;n.geometry.width=40;n.geometry.height=20;n.geometry.x=Math.round(t/this.view.scale)-a.x;n.geometry.y=Math.round(e/this.view.scale)-a.y}this.getModel().beginUpdate();try{this.addCells([n],i!=null?i.cell:null);this.fireEvent(new mxEventObject("textInserted","cells",[n]));this.autoSizeCell(n)}finally{this.getModel().endUpdate()}return n};Graph.prototype.addClickHandler=function(t,s,o){var e=mxUtils.bind(this,function(){var t=this.container.getElementsByTagName("a");if(t!=null){for(var e=0;e<t.length;e++){var i=this.getAbsoluteUrl(t[e].getAttribute("href"));if(i!=null){t[e].setAttribute("rel",this.linkRelation);t[e].setAttribute("href",i);if(s!=null){mxEvent.addGestureListeners(t[e],null,null,s)}}}}});this.model.addListener(mxEvent.CHANGE,e);e();var i=this.container.style.cursor;var h=this.getTolerance();var u=this;var n={currentState:null,currentLink:null,highlight:t!=null&&t!=""&&t!=mxConstants.NONE?new mxCellHighlight(u,t,4):null,startX:0,startY:0,scrollLeft:0,scrollTop:0,updateCurrentState:function(t){var e=t.sourceState;if(e==null||u.getLinkForCell(e.cell)==null){var i=u.getCellAt(t.getGraphX(),t.getGraphY(),null,null,null,function(t,e,i){return u.getLinkForCell(t.cell)==null});e=u.view.getState(i)}if(e!=this.currentState){if(this.currentState!=null){this.clear()}this.currentState=e;if(this.currentState!=null){this.activate(this.currentState)}}},mouseDown:function(t,e){this.startX=e.getGraphX();this.startY=e.getGraphY();this.scrollLeft=u.container.scrollLeft;this.scrollTop=u.container.scrollTop;if(this.currentLink==null&&u.container.style.overflow=="auto"){u.container.style.cursor="move"}this.updateCurrentState(e)},mouseMove:function(t,e){if(u.isMouseDown){if(this.currentLink!=null){var i=Math.abs(this.startX-e.getGraphX());var n=Math.abs(this.startY-e.getGraphY());if(i>h||n>h){this.clear()}}}else{var r=e.getSource();while(r!=null&&r.nodeName.toLowerCase()!="a"){r=r.parentNode}if(r!=null){this.clear()}else{if(u.tooltipHandler!=null&&this.currentLink!=null&&this.currentState!=null){u.tooltipHandler.reset(e,true,this.currentState)}if(this.currentState!=null&&(e.getState()==this.currentState||e.sourceState==null)&&u.intersects(this.currentState,e.getGraphX(),e.getGraphY())){return}this.updateCurrentState(e)}}},mouseUp:function(t,e){if(!mxUtils||!mxEvent){return}var i=e.getSource();var n=e.getEvent();var r=i;while(r!=null&&r.nodeName.toLowerCase()!="a"){r=r.parentNode}if(r==null&&Math.abs(this.scrollLeft-u.container.scrollLeft)<h&&Math.abs(this.scrollTop-u.container.scrollTop)<h&&(e.sourceState==null||!e.isSource(e.sourceState.control))&&((mxEvent.isLeftMouseButton(n)||mxEvent.isMiddleMouseButton(n))&&!mxEvent.isPopupTrigger(n)||mxEvent.isTouchEvent(n))){if(this.currentLink!=null){var l=u.isBlankLink(this.currentLink);if((this.currentLink.substring(0,5)==="data:"||!l)&&s!=null){s(n,this.currentLink)}if(!mxEvent.isConsumed(n)){var a=mxEvent.isMiddleMouseButton(n)?"_blank":l?u.linkTarget:"_top";u.openLink(this.currentLink,a);e.consume()}}else if(o!=null&&!e.isConsumed()&&Math.abs(this.scrollLeft-u.container.scrollLeft)<h&&Math.abs(this.scrollTop-u.container.scrollTop)<h&&Math.abs(this.startX-e.getGraphX())<h&&Math.abs(this.startY-e.getGraphY())<h){o(e.getEvent())}}this.clear()},activate:function(t){this.currentLink=u.getAbsoluteUrl(u.getLinkForCell(t.cell));if(this.currentLink!=null){u.container.style.cursor="pointer";if(this.highlight!=null){this.highlight.highlight(t)}}},clear:function(){if(u.container!=null){u.container.style.cursor=i}this.currentState=null;this.currentLink=null;if(this.highlight!=null){this.highlight.hide()}if(u.tooltipHandler!=null){u.tooltipHandler.hide()}}};u.click=function(t){};u.addMouseListener(n);mxEvent.addListener(document,"mouseleave",function(t){n.clear()})};Graph.prototype.duplicateCells=function(t,e){t=t!=null?t:this.getSelectionCells();e=e!=null?e:true;t=this.model.getTopmostCells(t);var i=this.getModel();var n=this.gridSize;var r=[];i.beginUpdate();try{var l=this.cloneCells(t,false,null,true);for(var a=0;a<t.length;a++){var s=i.getParent(t[a]);var o=this.moveCells([l[a]],n,n,false)[0];r.push(o);if(e){i.add(s,l[a])}else{var h=s.getIndex(t[a]);i.add(s,l[a],h+1)}}}finally{i.endUpdate()}return r};Graph.prototype.insertImage=function(t,e,i){if(t!=null&&this.cellEditor.textarea!=null){var n=this.cellEditor.textarea.getElementsByTagName("img");var r=[];for(var l=0;l<n.length;l++){r.push(n[l])}document.execCommand("insertimage",false,t);var a=this.cellEditor.textarea.getElementsByTagName("img");if(a.length==r.length+1){for(var l=a.length-1;l>=0;l--){if(l==0||a[l]!=r[l-1]){a[l].setAttribute("width",e);a[l].setAttribute("height",i);break}}}}};Graph.prototype.insertLink=function(t){if(this.cellEditor.textarea!=null){if(t.length==0){document.execCommand("unlink",false)}else if(mxClient.IS_FF){var e=this.cellEditor.textarea.getElementsByTagName("a");var i=[];for(var n=0;n<e.length;n++){i.push(e[n])}document.execCommand("createlink",false,mxUtils.trim(t));var r=this.cellEditor.textarea.getElementsByTagName("a");if(r.length==i.length+1){for(var n=r.length-1;n>=0;n--){if(r[n]!=i[n-1]){var e=r[n].getElementsByTagName("a");while(e.length>0){var l=e[0].parentNode;while(e[0].firstChild!=null){l.insertBefore(e[0].firstChild,e[0])}l.removeChild(e[0])}break}}}}else{document.execCommand("createlink",false,mxUtils.trim(t))}}};Graph.prototype.isCellResizable=function(t){var e=mxGraph.prototype.isCellResizable.apply(this,arguments);var i=this.view.getState(t);var n=i!=null?i.style:this.getCellStyle(t);return e||mxUtils.getValue(n,mxConstants.STYLE_RESIZABLE,"1")!="0"&&n[mxConstants.STYLE_WHITE_SPACE]=="wrap"};Graph.prototype.distributeCells=function(i,t){if(t==null){t=this.getSelectionCells()}if(t!=null&&t.length>1){var e=[];var n=null;var r=null;for(var l=0;l<t.length;l++){if(this.getModel().isVertex(t[l])){var a=this.view.getState(t[l]);if(a!=null){var s=i?a.getCenterX():a.getCenterY();n=n!=null?Math.max(n,s):s;r=r!=null?Math.min(r,s):s;e.push(a)}}}if(e.length>2){e.sort(function(t,e){return i?t.x-e.x:t.y-e.y});var o=this.view.translate;var h=this.view.scale;r=r/h-(i?o.x:o.y);n=n/h-(i?o.x:o.y);this.getModel().beginUpdate();try{var u=(n-r)/(e.length-1);var p=r;for(var l=1;l<e.length-1;l++){var c=this.view.getState(this.model.getParent(e[l].cell));var g=this.getCellGeometry(e[l].cell);p+=u;if(g!=null&&c!=null){g=g.clone();if(i){g.x=Math.round(p-g.width/2)-c.origin.x}else{g.y=Math.round(p-g.height/2)-c.origin.y}this.getModel().setGeometry(e[l].cell,g)}}}finally{this.getModel().endUpdate()}}}return t};Graph.prototype.isCloneEvent=function(t){return mxClient.IS_MAC&&mxEvent.isMetaDown(t)||mxEvent.isControlDown(t)};Graph.prototype.createSvgImageExport=function(){var t=new mxImageExport;t.getLinkForCellState=mxUtils.bind(this,function(t,e){return this.getLinkForCell(t.cell)});return t};Graph.prototype.getSvg=function(t,e,i,n,r,l,a,s,o,h){var u=this.useCssTransforms;if(u){this.useCssTransforms=false;this.view.revalidate();this.sizeDidChange()}try{e=e!=null?e:1;i=i!=null?i:0;r=r!=null?r:true;l=l!=null?l:true;a=a!=null?a:true;var p=l||n?this.getGraphBounds():this.getBoundingBox(this.getSelectionCells());if(p==null){throw Error(mxResources.get("drawingEmpty"))}var c=this.view.scale;var g=mxUtils.createXmlDocument();var d=g.createElementNS!=null?g.createElementNS(mxConstants.NS_SVG,"svg"):g.createElement("svg");if(t!=null){if(d.style!=null){if(typeof t==="string"){d.style.backgroundColor=t}else{d.style.backgroundColor=t.color;if(t&&t.imageCss){for(key in t.imageCss){if(Object.prototype.hasOwnProperty.call(d.style,key)){if("backgroundImageAttr"===key){return}if("backgroundSize"===key){var m=t.imageCss[key].split(",");var v="";var f=t.imageCss["backgroundImageAttr"];var x=["auto","repeat-x","repeat-y","repeat"];m.forEach(function(t,e){if(f[e]&&x.indexOf(t)>-1){var i=f[e].width;v+=i/p.width*100+"%"}else{v+=t}if(e<m.length-1){v+=","}});d.style[key]=v}else{d.style[key]=t.imageCss[key]}}}}}}else{if(typeof t==="string"){d.setAttribute("style","background-color:"+t)}else{d.setAttribute("style","background-color:"+t.color)}}}if(g.createElementNS==null){d.setAttribute("xmlns",mxConstants.NS_SVG);d.setAttribute("xmlns:xlink",mxConstants.NS_XLINK)}else{d.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:xlink",mxConstants.NS_XLINK)}var y=e/c;var C=Math.max(1,Math.ceil(p.width*y)+2*i)+(h?5:0);var S=Math.max(1,Math.ceil(p.height*y)+2*i)+(h?5:0);d.setAttribute("version","1.1");d.setAttribute("width",C+"px");d.setAttribute("height",S+"px");d.setAttribute("viewBox",(r?"-0.5 -0.5":"0 0")+" "+C+" "+S);g.appendChild(d);var E=g.createElementNS!=null?g.createElementNS(mxConstants.NS_SVG,"g"):g.createElement("g");d.appendChild(E);var w=this.createSvgCanvas(E);w.foOffset=r?-.5:0;w.textOffset=r?-.5:0;w.imageOffset=r?-.5:0;w.translate(Math.floor((i/e-p.x)/c),Math.floor((i/e-p.y)/c));var b=document.createElement("div");var H=w.getAlternateText;w.getAlternateText=function(t,e,i,n,r,l,a,s,o,h,u,p,c){if(l!=null&&this.state.fontSize>0){try{if(mxUtils.isNode(l)){l=l.innerText}else{b.innerHTML=l;l=mxUtils.extractTextWithWhitespace(b.childNodes)}var g=Math.ceil(2*n/this.state.fontSize);var d=[];var m=0;var v=0;while((g==0||m<g)&&v<l.length){var f=l.charCodeAt(v);if(f==10||f==13){if(m>0){break}}else{d.push(l.charAt(v));if(f<255){m++}}v++}if(d.length<l.length&&l.length-d.length>1){l=mxUtils.trim(d.join(""))+"..."}return l}catch(t){return H.apply(this,arguments)}}else{return H.apply(this,arguments)}};var L=this.backgroundImage;if(L!=null){var T=c/e;var G=this.view.translate;var A=new mxRectangle(G.x*T,G.y*T,L.width*T,L.height*T);if(mxUtils.intersects(p,A)){w.image(G.x,G.y,L.width,L.height,L.src,true)}}w.scale(y);w.textEnabled=a;s=s!=null?s:this.createSvgImageExport();var I=s.drawCellState;var M=s.getLinkForCellState;s.getLinkForCellState=function(t,e){var i=M.apply(this,arguments);return i!=null&&!t.view.graph.isCustomLink(i)?i:null};s.drawCellState=function(t,e){var i=t.view.graph;var n=i.isCellSelected(t.cell);var r=i.model.getParent(t.cell);while(!l&&!n&&r!=null){n=i.isCellSelected(r);r=i.model.getParent(r)}if(l||n){I.apply(this,arguments)}};s.drawState(this.getView().getState(this.model.root),w);this.updateSvgLinks(d,o,true);return d}finally{if(u){this.useCssTransforms=true;this.view.revalidate();this.sizeDidChange()}}};Graph.prototype.updateSvgLinks=function(t,e,i){var n=t.getElementsByTagName("a");for(var r=0;r<n.length;r++){var l=n[r].getAttribute("href");if(l==null){l=n[r].getAttribute("xlink:href")}if(l!=null){if(e!=null&&/^https?:\/\//.test(l)){n[r].setAttribute("target",e)}else if(i&&this.isCustomLink(l)){n[r].setAttribute("href","javascript:void(0);")}}}};Graph.prototype.createSvgCanvas=function(t){var e=new mxSvgCanvas2D(t);e.pointerEvents=true;return e};Graph.prototype.getSelectedElement=function(){var t=null;if(window.getSelection){var e=window.getSelection();if(e.getRangeAt&&e.rangeCount){var i=e.getRangeAt(0);t=i.commonAncestorContainer}}else if(document.selection){t=document.selection.createRange().parentElement()}return t};Graph.prototype.getParentByName=function(t,e,i){while(t!=null){if(t.nodeName==e){return t}if(t==i){return null}t=t.parentNode}return t};Graph.prototype.getParentByNames=function(t,e,i){while(t!=null){if(mxUtils.indexOf(e,t.nodeName)>=0){return t}if(t==i){return null}t=t.parentNode}return t};Graph.prototype.selectNode=function(t){var e=null;if(window.getSelection){e=window.getSelection();if(e.getRangeAt&&e.rangeCount){var i=document.createRange();i.selectNode(t);e.removeAllRanges();e.addRange(i)}}else if((e=document.selection)&&e.type!="Control"){var n=e.createRange();n.collapse(true);var i=e.createRange();i.setEndPoint("StartToStart",n);i.select()}};Graph.prototype.insertRow=function(t,e){var i=t.tBodies[0];var n=i.rows[0].cells;var r=0;for(var l=0;l<n.length;l++){var a=n[l].getAttribute("colspan");r+=a!=null?parseInt(a):1}var s=i.insertRow(e);for(var l=0;l<r;l++){mxUtils.br(s.insertCell(-1))}return s.cells[0]};Graph.prototype.deleteRow=function(t,e){t.tBodies[0].deleteRow(e)};Graph.prototype.insertColumn=function(t,e){var i=t.tHead;if(i!=null){for(var n=0;n<i.rows.length;n++){var r=document.createElement("th");i.rows[n].appendChild(r);mxUtils.br(r)}}var l=t.tBodies[0];for(var a=0;a<l.rows.length;a++){var s=l.rows[a].insertCell(e);mxUtils.br(s)}return l.rows[0].cells[e>=0?e:l.rows[0].cells.length-1]};Graph.prototype.deleteColumn=function(t,e){if(e>=0){var i=t.tBodies[0];var n=i.rows;for(var r=0;r<n.length;r++){if(n[r].cells.length>e){n[r].deleteCell(e)}}}};Graph.prototype.pasteHtmlAtCaret=function(t){var e,i;if(window.getSelection){e=window.getSelection();if(e.getRangeAt&&e.rangeCount){i=e.getRangeAt(0);i.deleteContents();var n=document.createElement("div");n.innerHTML=t;var r=document.createDocumentFragment(),l;while(l=n.firstChild){lastNode=r.appendChild(l)}i.insertNode(r)}}else if((e=document.selection)&&e.type!="Control"){e.createRange().pasteHTML(t)}};Graph.prototype.createLinkForHint=function(e,t){e=e!=null?e:"javascript:void(0);";if(t==null||t.length==0){if(this.isCustomLink(e)){t=this.getLinkTitle(e)}else{t=e}}function i(t,e){if(t.length>e){t=t.substring(0,Math.round(e/2))+"..."+t.substring(t.length-Math.round(e/4))}return t}var n=document.createElement("a");n.setAttribute("rel",this.linkRelation);n.setAttribute("href",this.getAbsoluteUrl(e));n.setAttribute("title",i(this.isCustomLink(e)?this.getLinkTitle(e):e,80));if(this.linkTarget!=null){n.setAttribute("target",this.linkTarget)}mxUtils.write(n,i(t,40));if(this.isCustomLink(e)){mxEvent.addListener(n,"click",mxUtils.bind(this,function(t){this.customLinkClicked(e);mxEvent.consume(t)}))}return n};Graph.prototype.initTouch=function(){this.connectionHandler.marker.isEnabled=function(){return this.graph.connectionHandler.first!=null};this.addListener(mxEvent.START_EDITING,function(t,e){this.popupMenuHandler.hideMenu()});var i=this.updateMouseEvent;this.updateMouseEvent=function(t){t=i.apply(this,arguments);if(mxEvent.isTouchEvent(t.getEvent())&&t.getState()==null){var e=this.getCellAt(t.graphX,t.graphY);if(e!=null&&this.isSwimlane(e)&&this.hitsSwimlaneContent(e,t.graphX,t.graphY)){e=null}else{t.state=this.view.getState(e);if(t.state!=null&&t.state.shape!=null){this.container.style.cursor=t.state.shape.node.style.cursor}}}if(t.getState()==null&&this.isEnabled()){this.container.style.cursor="default"}return t};var n=false;var r=false;var l=false;var a=this.fireMouseEvent;this.fireMouseEvent=function(t,e,i){if(t==mxEvent.MOUSE_DOWN){e=this.updateMouseEvent(e);n=this.isCellSelected(e.getCell());r=this.isSelectionEmpty();l=this.popupMenuHandler.isMenuShowing()}a.apply(this,arguments)};this.popupMenuHandler.mouseUp=mxUtils.bind(this,function(t,e){this.popupMenuHandler.popupTrigger=!this.isEditing()&&this.isEnabled()&&(e.getState()==null||!e.isSource(e.getState().control))&&(this.popupMenuHandler.popupTrigger||!l&&!mxEvent.isMouseEvent(e.getEvent())&&(r&&e.getCell()==null&&this.isSelectionEmpty()||n&&this.isCellSelected(e.getCell())));mxPopupMenuHandler.prototype.mouseUp.apply(this.popupMenuHandler,arguments)})};mxCellEditor.prototype.isContentEditing=function(){var t=this.graph.view.getState(this.editingCell);return t!=null&&t.style["html"]==1};mxCellEditor.prototype.isTableSelected=function(){return this.graph.getParentByName(this.graph.getSelectedElement(),"TABLE",this.textarea)!=null};mxCellEditor.prototype.alignText=function(t,e){var i=e!=null&&mxEvent.isShiftDown(e);if(i||window.getSelection!=null&&window.getSelection().containsNode!=null){var n=true;this.graph.processElements(this.textarea,function(t){if(i||window.getSelection().containsNode(t,true)){t.removeAttribute("align");t.style.textAlign=null}else{n=false}});if(n){this.graph.cellEditor.setAlign(t)}}document.execCommand("justify"+t.toLowerCase(),false,null)};mxCellEditor.prototype.saveSelection=function(){if(window.getSelection){var t=window.getSelection();if(t.getRangeAt&&t.rangeCount){var e=[];for(var i=0,n=t.rangeCount;i<n;++i){e.push(t.getRangeAt(i))}return e}}else if(document.selection&&document.selection.createRange){return document.selection.createRange()}return null};mxCellEditor.prototype.restoreSelection=function(t){try{if(t){if(window.getSelection){sel=window.getSelection();sel.removeAllRanges();for(var e=0,i=t.length;e<i;++e){sel.addRange(t[e])}}else if(document.selection&&t.select){t.select()}}}catch(t){}};var r=mxCellRenderer.prototype.initializeLabel;mxCellRenderer.prototype.initializeLabel=function(t){if(t.text!=null){t.text.replaceLinefeeds=mxUtils.getValue(t.style,"nl2Br","1")!="0"}r.apply(this,arguments)};var l=mxConstraintHandler.prototype.update;mxConstraintHandler.prototype.update=function(t,e){if(this.isKeepFocusEvent(t)||!mxEvent.isAltDown(t.getEvent())){l.apply(this,arguments)}else{this.reset()}};mxGuide.prototype.createGuideShape=function(t){var e=new mxPolyline([],mxConstants.GUIDE_COLOR,mxConstants.GUIDE_STROKEWIDTH);return e};mxCellEditor.prototype.escapeCancelsEditing=false;var a=mxCellEditor.prototype.startEditing;mxCellEditor.prototype.startEditing=function(t,e){a.apply(this,arguments);var i=this.graph.view.getState(t);if(i!=null&&i.style["html"]==1){this.textarea.className="mxCellEditor geContentEditable"}else{this.textarea.className="mxCellEditor mxPlainTextEditor"}this.codeViewMode=false;this.switchSelectionState=null;this.graph.setSelectionCell(t);var n=this.graph.getModel().getParent(t);var r=this.graph.getCellGeometry(t);if(this.graph.getModel().isEdge(n)&&r!=null&&r.relative||this.graph.getModel().isEdge(t)){if(mxClient.IS_QUIRKS){this.textarea.style.border="gray dotted 1px"}else if(mxClient.IS_IE||mxClient.IS_IE11||mxClient.IS_FF&&mxClient.IS_WIN){this.textarea.style.outline="gray dotted 1px"}else{this.textarea.style.outline=""}}else if(mxClient.IS_QUIRKS){this.textarea.style.outline="none";this.textarea.style.border=""}};var s=mxCellEditor.prototype.installListeners;mxCellEditor.prototype.installListeners=function(t){s.apply(this,arguments);function n(t,e){e.originalNode=t;t=t.firstChild;var i=e.firstChild;while(t!=null&&i!=null){n(t,i);t=t.nextSibling;i=i.nextSibling}return e}function r(t,e){if(t!=null){if(e.originalNode!=t){l(t)}else{t=t.firstChild;e=e.firstChild;while(t!=null){var i=t.nextSibling;if(e==null){l(t)}else{r(t,e);e=e.nextSibling}t=i}}}}function l(t){var e=t.firstChild;while(e!=null){var i=e.nextSibling;l(e);e=i}if((t.nodeType!=1||t.nodeName!=="BR"&&t.firstChild==null)&&(t.nodeType!=3||mxUtils.trim(mxUtils.getTextContent(t)).length==0)){t.parentNode.removeChild(t)}else{if(t.nodeType==3){mxUtils.setTextContent(t,mxUtils.getTextContent(t).replace(/\n|\r/g,""))}if(t.nodeType==1){t.removeAttribute("style");t.removeAttribute("class");t.removeAttribute("width");t.removeAttribute("cellpadding");t.removeAttribute("cellspacing");t.removeAttribute("border")}}}if(!mxClient.IS_QUIRKS&&document.documentMode!==7&&document.documentMode!==8){mxEvent.addListener(this.textarea,"paste",mxUtils.bind(this,function(t){var e=n(this.textarea,this.textarea.cloneNode(true));window.setTimeout(mxUtils.bind(this,function(){if(this.textarea!=null&&(this.textarea.innerHTML.indexOf("<o:OfficeDocumentSettings>")>=0||this.textarea.innerHTML.indexOf("\x3c!--[if !mso]>")>=0)){r(this.textarea,e)}}),0)}))}};mxCellEditor.prototype.toggleViewMode=function(){var t=this.graph.view.getState(this.editingCell);if(t!=null){var e=t!=null&&mxUtils.getValue(t.style,"nl2Br","1")!="0";var i=this.saveSelection();if(!this.codeViewMode){if(this.clearOnChange&&this.textarea.innerHTML==this.getEmptyLabelText()){this.clearOnChange=false;this.textarea.innerHTML=""}var n=mxUtils.htmlEntities(this.textarea.innerHTML);if(!mxClient.IS_QUIRKS&&document.documentMode!=8){n=mxUtils.replaceTrailingNewlines(n,"<div><br></div>")}n=this.graph.sanitizeHtml(e?n.replace(/\n/g,"").replace(/&lt;br\s*.?&gt;/g,"<br>"):n,true);this.textarea.className="mxCellEditor mxPlainTextEditor";var r=mxConstants.DEFAULT_FONTSIZE;this.textarea.style.lineHeight=mxConstants.ABSOLUTE_LINE_HEIGHT?Math.round(r*mxConstants.LINE_HEIGHT)+"px":mxConstants.LINE_HEIGHT;this.textarea.style.fontSize=Math.round(r)+"px";this.textarea.style.textDecoration="";this.textarea.style.fontWeight="normal";this.textarea.style.fontStyle="";this.textarea.style.fontFamily=mxConstants.DEFAULT_FONTFAMILY;this.textarea.style.textAlign="left";this.textarea.style.padding="2px";if(this.textarea.innerHTML!=n){this.textarea.innerHTML=n}this.codeViewMode=true}else{var n=mxUtils.extractTextWithWhitespace(this.textarea.childNodes);if(n.length>0&&n.charAt(n.length-1)=="\n"){n=n.substring(0,n.length-1)}n=this.graph.sanitizeHtml(e?n.replace(/\n/g,"<br/>"):n,true);this.textarea.className="mxCellEditor geContentEditable";var r=mxUtils.getValue(t.style,mxConstants.STYLE_FONTSIZE,mxConstants.DEFAULT_FONTSIZE);var l=mxUtils.getValue(t.style,mxConstants.STYLE_FONTFAMILY,mxConstants.DEFAULT_FONTFAMILY);var a=mxUtils.getValue(t.style,mxConstants.STYLE_ALIGN,mxConstants.ALIGN_LEFT);var s=(mxUtils.getValue(t.style,mxConstants.STYLE_FONTSTYLE,0)&mxConstants.FONT_BOLD)==mxConstants.FONT_BOLD;var o=(mxUtils.getValue(t.style,mxConstants.STYLE_FONTSTYLE,0)&mxConstants.FONT_ITALIC)==mxConstants.FONT_ITALIC;var h=[];if((mxUtils.getValue(t.style,mxConstants.STYLE_FONTSTYLE,0)&mxConstants.FONT_UNDERLINE)==mxConstants.FONT_UNDERLINE){h.push("underline")}if((mxUtils.getValue(t.style,mxConstants.STYLE_FONTSTYLE,0)&mxConstants.FONT_STRIKETHROUGH)==mxConstants.FONT_STRIKETHROUGH){h.push("line-through")}this.textarea.style.lineHeight=mxConstants.ABSOLUTE_LINE_HEIGHT?Math.round(r*mxConstants.LINE_HEIGHT)+"px":mxConstants.LINE_HEIGHT;this.textarea.style.fontSize=Math.round(r)+"px";this.textarea.style.textDecoration=h.join(" ");this.textarea.style.fontWeight=s?"bold":"normal";this.textarea.style.fontStyle=o?"italic":"";this.textarea.style.fontFamily=l;this.textarea.style.textAlign=a;this.textarea.style.padding="0px";if(this.textarea.innerHTML!=n){this.textarea.innerHTML=n;if(this.textarea.innerHTML.length==0){this.textarea.innerHTML=this.getEmptyLabelText();this.clearOnChange=this.textarea.innerHTML.length>0}}this.codeViewMode=false}this.textarea.focus();if(this.switchSelectionState!=null){this.restoreSelection(this.switchSelectionState)}this.switchSelectionState=i;this.resize()}};var h=mxCellEditor.prototype.resize;mxCellEditor.prototype.resize=function(t,e){if(this.textarea!=null){var t=this.graph.getView().getState(this.editingCell);if(this.codeViewMode&&t!=null){var i=t.view.scale;this.bounds=mxRectangle.fromRectangle(t);if(this.bounds.width==0&&this.bounds.height==0){this.bounds.width=160*i;this.bounds.height=60*i;var n=t.text!=null?t.text.margin:null;if(n==null){n=mxUtils.getAlignmentAsPoint(mxUtils.getValue(t.style,mxConstants.STYLE_ALIGN,mxConstants.ALIGN_CENTER),mxUtils.getValue(t.style,mxConstants.STYLE_VERTICAL_ALIGN,mxConstants.ALIGN_MIDDLE))}this.bounds.x+=n.x*this.bounds.width;this.bounds.y+=n.y*this.bounds.height}this.textarea.style.width=Math.round((this.bounds.width-4)/i)+"px";this.textarea.style.height=Math.round((this.bounds.height-4)/i)+"px";this.textarea.style.overflow="auto";if(this.textarea.clientHeight<this.textarea.offsetHeight){this.textarea.style.height=Math.round(this.bounds.height/i)+(this.textarea.offsetHeight-this.textarea.clientHeight)+"px";this.bounds.height=parseInt(this.textarea.style.height)*i}if(this.textarea.clientWidth<this.textarea.offsetWidth){this.textarea.style.width=Math.round(this.bounds.width/i)+(this.textarea.offsetWidth-this.textarea.clientWidth)+"px";this.bounds.width=parseInt(this.textarea.style.width)*i}this.textarea.style.left=Math.round(this.bounds.x)+"px";this.textarea.style.top=Math.round(this.bounds.y)+"px";if(mxClient.IS_VML){this.textarea.style.zoom=i}else{mxUtils.setPrefixedStyle(this.textarea.style,"transform","scale("+i+","+i+")")}}else{this.textarea.style.height="";this.textarea.style.overflow="";h.apply(this,arguments)}}};mxCellEditorGetInitialValue=mxCellEditor.prototype.getInitialValue;mxCellEditor.prototype.getInitialValue=function(t,e,i){if(mxUtils.getValue(t.style,"html","0")=="0"){return mxCellEditorGetInitialValue.apply(this,arguments)}else{var n=this.graph.getEditingValue(t.cell,e,i);if(mxUtils.getValue(t.style,"nl2Br","1")=="1"){n=n.replace(/\n/g,"<br/>")}n=this.graph.sanitizeHtml(n,true);return n}};mxCellEditorGetCurrentValue=mxCellEditor.prototype.getCurrentValue;mxCellEditor.prototype.getCurrentValue=function(t){if(mxUtils.getValue(t.style,"html","0")=="0"){return mxCellEditorGetCurrentValue.apply(this,arguments)}else{var e=this.graph.sanitizeHtml(this.textarea.innerHTML,true);if(mxUtils.getValue(t.style,"nl2Br","1")=="1"){e=e.replace(/\r\n/g,"<br/>").replace(/\n/g,"<br/>")}else{e=e.replace(/\r\n/g,"").replace(/\n/g,"")}return e}};var u=mxCellEditor.prototype.stopEditing;mxCellEditor.prototype.stopEditing=function(t){if(this.codeViewMode){this.toggleViewMode()}u.apply(this,arguments);this.focusContainer()};mxCellEditor.prototype.focusContainer=function(){try{this.graph.container.focus()}catch(t){}};var p=mxCellEditor.prototype.applyValue;mxCellEditor.prototype.applyValue=function(t,e){this.graph.getModel().beginUpdate();try{p.apply(this,arguments);if(e==""&&this.graph.isCellDeletable(t.cell)&&this.graph.model.getChildCount(t.cell)==0&&this.graph.isTransparentState(t)){this.graph.removeCells([t.cell],false)}}finally{this.graph.getModel().endUpdate()}};mxCellEditor.prototype.getBackgroundColor=function(t){var e=mxUtils.getValue(t.style,mxConstants.STYLE_LABEL_BACKGROUNDCOLOR,null);if((e==null||e==mxConstants.NONE)&&t.cell.geometry!=null&&t.cell.geometry.width>0&&(mxUtils.getValue(t.style,mxConstants.STYLE_ROTATION,0)!=0||mxUtils.getValue(t.style,mxConstants.STYLE_HORIZONTAL,1)==0)){e=mxUtils.getValue(t.style,mxConstants.STYLE_FILLCOLOR,null)}if(e==mxConstants.NONE){e=null}return e};mxCellEditor.prototype.getMinimumSize=function(t){var e=this.graph.getView().scale;return new mxRectangle(0,0,t.text==null?30:t.text.size*e+20,30)};var c=mxGraphHandler.prototype.moveCells;mxGraphHandler.prototype.moveCells=function(t,e,i,n,r,l){if(mxEvent.isAltDown(l)){r=null}c.apply(this,arguments)};function g(){var t=document.createElement("div");t.className="geHint";t.style.whiteSpace="nowrap";t.style.position="absolute";return t}function d(t,e){switch(e){case mxConstants.POINTS:return t;case mxConstants.MILLIMETERS:return(t/mxConstants.PIXELS_PER_MM).toFixed(1);case mxConstants.INCHES:return(t/mxConstants.PIXELS_PER_INCH).toFixed(2)}}mxGraphView.prototype.formatUnitText=function(t){return t?d(t,this.unit):t};mxGraphHandler.prototype.updateHint=function(t){if(this.pBounds!=null&&(this.shape!=null||this.livePreviewActive)){if(this.hint==null){this.hint=g();this.graph.container.appendChild(this.hint)}var e=this.graph.view.translate;var i=this.graph.view.scale;var n=this.roundLength((this.bounds.x+this.currentDx)/i-e.x);var r=this.roundLength((this.bounds.y+this.currentDy)/i-e.y);var l=this.graph.view.unit;this.hint.innerHTML=d(n,l)+", "+d(r,l);this.hint.style.left=this.pBounds.x+this.currentDx+Math.round((this.pBounds.width-this.hint.clientWidth)/2)+"px";this.hint.style.top=this.pBounds.y+this.currentDy+this.pBounds.height+Editor.hintOffset+"px"}};mxGraphHandler.prototype.removeHint=function(){if(this.hint!=null){this.hint.parentNode.removeChild(this.hint);this.hint=null}};mxVertexHandler.prototype.rotationHandleVSpacing=-12;mxVertexHandler.prototype.getRotationHandlePosition=function(){var t=this.getHandlePadding();return new mxPoint(this.bounds.x+this.bounds.width-this.rotationHandleVSpacing+t.x/2,this.bounds.y+this.rotationHandleVSpacing-t.y/2)};mxVertexHandler.prototype.isRecursiveResize=function(t,e){return!this.graph.isSwimlane(t.cell)&&this.graph.model.getChildCount(t.cell)>0&&!mxEvent.isControlDown(e.getEvent())&&!this.graph.isCellCollapsed(t.cell)&&mxUtils.getValue(t.style,"recursiveResize","1")=="1"&&mxUtils.getValue(t.style,"childLayout",null)==null};mxVertexHandler.prototype.isCenteredEvent=function(t,e){return!(!this.graph.isSwimlane(t.cell)&&this.graph.model.getChildCount(t.cell)>0&&!this.graph.isCellCollapsed(t.cell)&&mxUtils.getValue(t.style,"recursiveResize","1")=="1"&&mxUtils.getValue(t.style,"childLayout",null)==null)&&mxEvent.isControlDown(e.getEvent())||mxEvent.isMetaDown(e.getEvent())};var m=mxVertexHandler.prototype.getHandlePadding;mxVertexHandler.prototype.getHandlePadding=function(){var t=new mxPoint(0,0);var e=this.tolerance;if(this.graph.cellEditor.getEditingCell()==this.state.cell&&this.sizers!=null&&this.sizers.length>0&&this.sizers[0]!=null){e/=2;t.x=this.sizers[0].bounds.width+e;t.y=this.sizers[0].bounds.height+e}else{t=m.apply(this,arguments)}return t};mxVertexHandler.prototype.updateHint=function(t){if(this.index!=mxEvent.LABEL_HANDLE){if(this.hint==null){this.hint=g();this.state.view.graph.container.appendChild(this.hint)}if(this.index==mxEvent.ROTATION_HANDLE){this.hint.innerHTML=this.currentAlpha+"&deg;"}else{var e=this.state.view.scale;var i=this.state.view.unit;this.hint.innerHTML=d(this.roundLength(this.bounds.width/e),i)+" x "+d(this.roundLength(this.bounds.height/e),i)}var n=this.currentAlpha!=null?this.currentAlpha:this.state.style[mxConstants.STYLE_ROTATION]||"0";var r=mxUtils.getBoundingBox(this.bounds,n);if(r==null){r=this.bounds}this.hint.style.left=r.x+Math.round((r.width-this.hint.clientWidth)/2)+"px";this.hint.style.top=r.y+r.height+Editor.hintOffset+"px";if(this.linkHint!=null){this.linkHint.style.display="none"}}};mxVertexHandler.prototype.removeHint=function(){mxGraphHandler.prototype.removeHint.apply(this,arguments);if(this.linkHint!=null){this.linkHint.style.display=""}};var v=mxEdgeHandler.prototype.mouseMove;mxEdgeHandler.prototype.mouseMove=function(t,e){v.apply(this,arguments);if(this.graph.graphHandler!=null&&this.graph.graphHandler.first!=null&&this.linkHint!=null&&this.linkHint.style.display!="none"){this.linkHint.style.display="none"}};var f=mxEdgeHandler.prototype.mouseUp;mxEdgeHandler.prototype.mouseUp=function(t,e){f.apply(this,arguments);if(this.linkHint!=null&&this.linkHint.style.display=="none"){this.linkHint.style.display=""}};mxEdgeHandler.prototype.updateHint=function(t,e){if(this.hint==null){this.hint=g();this.state.view.graph.container.appendChild(this.hint)}var i=this.graph.view.translate;var n=this.graph.view.scale;var r=this.roundLength(e.x/n-i.x);var l=this.roundLength(e.y/n-i.y);var a=this.graph.view.unit;this.hint.innerHTML=d(r,a)+", "+d(l,a);this.hint.style.visibility="visible";if(this.isSource||this.isTarget){if(this.constraintHandler.currentConstraint!=null&&this.constraintHandler.currentFocus!=null){var s=this.constraintHandler.currentConstraint.point;this.hint.innerHTML="["+Math.round(s.x*100)+"%, "+Math.round(s.y*100)+"%]"}else if(this.marker.hasValidState()){this.hint.style.visibility="hidden"}}this.hint.style.left=Math.round(t.getGraphX()-this.hint.clientWidth/2)+"px";this.hint.style.top=Math.max(t.getGraphY(),e.y)+Editor.hintOffset+"px";if(this.linkHint!=null){this.linkHint.style.display="none"}};mxEdgeHandler.prototype.removeHint=mxVertexHandler.prototype.removeHint;HoverIcons.prototype.mainHandle=!mxClient.IS_SVG?new mxImage(IMAGE_PATH+"/handle-main.png",17,17):Graph.createSvgImage(18,18,'<circle cx="9" cy="9" r="5" stroke="#fff" fill="'+HoverIcons.prototype.arrowFill+'" stroke-width="1"/>');HoverIcons.prototype.secondaryHandle=!mxClient.IS_SVG?new mxImage(IMAGE_PATH+"/handle-secondary.png",17,17):Graph.createSvgImage(16,16,'<path d="m 8 3 L 13 8 L 8 13 L 3 8 z" stroke="#fff" fill="#fca000"/>');HoverIcons.prototype.fixedHandle=!mxClient.IS_SVG?new mxImage(IMAGE_PATH+"/handle-fixed.png",17,17):Graph.createSvgImage(18,18,'<circle cx="9" cy="9" r="5" stroke="#fff" fill="'+HoverIcons.prototype.arrowFill+'" stroke-width="1"/><path d="m 7 7 L 11 11 M 7 11 L 11 7" stroke="#fff"/>');HoverIcons.prototype.terminalHandle=!mxClient.IS_SVG?new mxImage(IMAGE_PATH+"/handle-terminal.png",17,17):Graph.createSvgImage(18,18,'<circle cx="9" cy="9" r="5" stroke="#fff" fill="'+HoverIcons.prototype.arrowFill+'" stroke-width="1"/><circle cx="9" cy="9" r="2" stroke="#fff" fill="transparent"/>');HoverIcons.prototype.rotationHandle=!mxClient.IS_SVG?new mxImage(IMAGE_PATH+"/handle-rotate.png",16,16):Graph.createSvgImage(16,16,'<path stroke="'+HoverIcons.prototype.arrowFill+'" fill="'+HoverIcons.prototype.arrowFill+'" d="M15.55 5.55L11 1v3.07C7.06 4.56 4 7.92 4 12s3.05 7.44 7 7.93v-2.02c-2.84-.48-5-2.94-5-5.91s2.16-5.43 5-5.91V10l4.55-4.45zM19.93 11c-.17-1.39-.72-2.73-1.62-3.89l-1.42 1.42c.54.75.88 1.6 1.02 2.47h2.02zM13 17.9v2.02c1.39-.17 2.74-.71 3.9-1.61l-1.44-1.44c-.75.54-1.59.89-2.46 1.03zm3.89-2.42l1.42 1.41c.9-1.16 1.45-2.5 1.62-3.89h-2.02c-.14.87-.48 1.72-1.02 2.48z"/>',24,24);if(mxClient.IS_SVG){mxConstraintHandler.prototype.pointImage=Graph.createSvgImage(5,5,'<path d="m 0 0 L 5 5 M 0 5 L 5 0" stroke="'+HoverIcons.prototype.arrowFill+'"/>')}mxVertexHandler.prototype.handleImage=HoverIcons.prototype.mainHandle;mxVertexHandler.prototype.secondaryHandleImage=HoverIcons.prototype.secondaryHandle;mxEdgeHandler.prototype.handleImage=HoverIcons.prototype.mainHandle;mxEdgeHandler.prototype.terminalHandleImage=HoverIcons.prototype.terminalHandle;mxEdgeHandler.prototype.fixedHandleImage=HoverIcons.prototype.fixedHandle;mxEdgeHandler.prototype.labelHandleImage=HoverIcons.prototype.secondaryHandle;mxOutline.prototype.sizerImage=HoverIcons.prototype.mainHandle;if(window.Sidebar!=null){Sidebar.prototype.triangleUp=HoverIcons.prototype.triangleUp;Sidebar.prototype.triangleRight=HoverIcons.prototype.triangleRight;Sidebar.prototype.triangleDown=HoverIcons.prototype.triangleDown;Sidebar.prototype.triangleLeft=HoverIcons.prototype.triangleLeft;Sidebar.prototype.refreshTarget=HoverIcons.prototype.refreshTarget;Sidebar.prototype.roundDrop=HoverIcons.prototype.roundDrop}if(!mxClient.IS_SVG){(new Image).src=HoverIcons.prototype.mainHandle.src;(new Image).src=HoverIcons.prototype.fixedHandle.src;(new Image).src=HoverIcons.prototype.terminalHandle.src;(new Image).src=HoverIcons.prototype.secondaryHandle.src;(new Image).src=HoverIcons.prototype.rotationHandle.src;(new Image).src=HoverIcons.prototype.triangleUp.src;(new Image).src=HoverIcons.prototype.triangleRight.src;(new Image).src=HoverIcons.prototype.triangleDown.src;(new Image).src=HoverIcons.prototype.triangleLeft.src;(new Image).src=HoverIcons.prototype.refreshTarget.src;(new Image).src=HoverIcons.prototype.roundDrop.src}mxVertexHandler.prototype.rotationEnabled=true;mxVertexHandler.prototype.manageSizers=true;mxVertexHandler.prototype.livePreview=true;mxGraphHandler.prototype.maxLivePreview=16;mxRubberband.prototype.defaultOpacity=30;mxConnectionHandler.prototype.outlineConnect=true;mxCellHighlight.prototype.keepOnTop=true;mxVertexHandler.prototype.parentHighlightEnabled=true;mxEdgeHandler.prototype.parentHighlightEnabled=true;mxEdgeHandler.prototype.dblClickRemoveEnabled=true;mxEdgeHandler.prototype.straightRemoveEnabled=true;mxEdgeHandler.prototype.virtualBendsEnabled=true;mxEdgeHandler.prototype.mergeRemoveEnabled=true;mxEdgeHandler.prototype.manageLabelHandle=true;mxEdgeHandler.prototype.outlineConnect=true;mxEdgeHandler.prototype.isAddVirtualBendEvent=function(t){return!mxEvent.isShiftDown(t.getEvent())};mxEdgeHandler.prototype.isCustomHandleEvent=function(t){return!mxEvent.isShiftDown(t.getEvent())};if(Graph.touchStyle){if(mxClient.IS_TOUCH||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0){mxShape.prototype.svgStrokeTolerance=18;mxVertexHandler.prototype.tolerance=12;mxEdgeHandler.prototype.tolerance=12;Graph.prototype.tolerance=12;mxVertexHandler.prototype.rotationHandleVSpacing=-16;mxConstraintHandler.prototype.getTolerance=function(t){return mxEvent.isMouseEvent(t.getEvent())?4:this.graph.getTolerance()}}mxPanningHandler.prototype.isPanningTrigger=function(t){var e=t.getEvent();return t.getState()==null&&!mxEvent.isMouseEvent(e)||mxEvent.isPopupTrigger(e)&&(t.getState()==null||mxEvent.isControlDown(e)||mxEvent.isShiftDown(e))};var x=mxGraphHandler.prototype.mouseDown;mxGraphHandler.prototype.mouseDown=function(t,e){x.apply(this,arguments);if(mxEvent.isTouchEvent(e.getEvent())&&this.graph.isCellSelected(e.getCell())&&this.graph.getSelectionCount()>1){this.delayedSelection=false}}}else{mxPanningHandler.prototype.isPanningTrigger=function(t){var e=t.getEvent();return mxEvent.isLeftMouseButton(e)&&(this.useLeftButtonForPanning&&t.getState()==null||mxEvent.isControlDown(e)&&!mxEvent.isShiftDown(e))||this.usePopupTrigger&&mxEvent.isPopupTrigger(e)}}mxRubberband.prototype.isSpaceEvent=function(t){return this.graph.isEnabled()&&!this.graph.isCellLocked(this.graph.getDefaultParent())&&mxEvent.isControlDown(t.getEvent())&&mxEvent.isShiftDown(t.getEvent())};mxRubberband.prototype.mouseUp=function(t,e){var i=this.div!=null&&this.div.style.display!="none";var n=null;var r=null;var l=null;var a=null;if(this.first!=null&&this.currentX!=null&&this.currentY!=null){n=this.first.x;r=this.first.y;l=(this.currentX-n)/this.graph.view.scale;a=(this.currentY-r)/this.graph.view.scale;if(!mxEvent.isAltDown(e.getEvent())){l=this.graph.snap(l);a=this.graph.snap(a);if(!this.graph.isGridEnabled()){if(Math.abs(l)<this.graph.tolerance){l=0}if(Math.abs(a)<this.graph.tolerance){a=0}}}}this.reset();if(i){if(mxEvent.isAltDown(e.getEvent())&&this.graph.isToggleEvent(e.getEvent())){var s=new mxRectangle(this.x,this.y,this.width,this.height);var o=this.graph.getCells(s.x,s.y,s.width,s.height);this.graph.removeSelectionCells(o)}else if(this.isSpaceEvent(e)){this.graph.model.beginUpdate();try{var o=this.graph.getCellsBeyond(n,r,this.graph.getDefaultParent(),true,true);for(var h=0;h<o.length;h++){if(this.graph.isCellMovable(o[h])){var u=this.graph.view.getState(o[h]);var p=this.graph.getCellGeometry(o[h]);if(u!=null&&p!=null){p=p.clone();p.translate(l,a);this.graph.model.setGeometry(o[h],p)}}}}finally{this.graph.model.endUpdate()}}else{var s=new mxRectangle(this.x,this.y,this.width,this.height);this.graph.selectRegion(s,e.getEvent())}e.consume()}};mxRubberband.prototype.mouseMove=function(t,e){if(!mxUtils||!mxEvent){return}if(!e.isConsumed()&&this.first!=null){var i=mxUtils.getScrollOrigin(this.graph.container);var n=mxUtils.getOffset(this.graph.container);i.x-=n.x;i.y-=n.y;var r=e.getX()+i.x;var l=e.getY()+i.y;var a=this.first.x-r;var s=this.first.y-l;var o=this.graph.tolerance;if(this.div!=null||Math.abs(a)>o||Math.abs(s)>o){if(this.div==null){this.div=this.createShape()}mxUtils.clearSelection();this.update(r,l);if(this.isSpaceEvent(e)){var h=this.x+this.width;var u=this.y+this.height;var p=this.graph.view.scale;if(!mxEvent.isAltDown(e.getEvent())){this.width=this.graph.snap(this.width/p)*p;this.height=this.graph.snap(this.height/p)*p;if(!this.graph.isGridEnabled()){if(this.width<this.graph.tolerance){this.width=0}if(this.height<this.graph.tolerance){this.height=0}}if(this.x<this.first.x){this.x=h-this.width}if(this.y<this.first.y){this.y=u-this.height}}this.div.style.borderStyle="dashed";this.div.style.backgroundColor="white";this.div.style.left=this.x+"px";this.div.style.top=this.y+"px";this.div.style.width=Math.max(0,this.width)+"px";this.div.style.height=this.graph.container.clientHeight+"px";this.div.style.borderWidth=this.width<=0?"0px 1px 0px 0px":"0px 1px 0px 1px";if(this.secondDiv==null){this.secondDiv=this.div.cloneNode(true);this.div.parentNode.appendChild(this.secondDiv)}this.secondDiv.style.left=this.x+"px";this.secondDiv.style.top=this.y+"px";this.secondDiv.style.width=this.graph.container.clientWidth+"px";this.secondDiv.style.height=Math.max(0,this.height)+"px";this.secondDiv.style.borderWidth=this.height<=0?"1px 0px 0px 0px":"1px 0px 1px 0px"}else{this.div.style.backgroundColor="";this.div.style.borderWidth="";this.div.style.borderStyle="";if(this.secondDiv!=null){this.secondDiv.parentNode.removeChild(this.secondDiv);this.secondDiv=null}}e.consume()}}};var t=mxRubberband.prototype.reset;mxRubberband.prototype.reset=function(){if(this.secondDiv!=null){this.secondDiv.parentNode.removeChild(this.secondDiv);this.secondDiv=null}t.apply(this,arguments)};var y=(new Date).getTime();var C=0;var S=mxEdgeHandler.prototype.updatePreviewState;mxEdgeHandler.prototype.updatePreviewState=function(t,e,i,n){S.apply(this,arguments);if(i!=this.currentTerminalState){y=(new Date).getTime();C=0}else{C=(new Date).getTime()-y}this.currentTerminalState=i};var E=mxEdgeHandler.prototype.isOutlineConnectEvent;mxEdgeHandler.prototype.isOutlineConnectEvent=function(t){return this.currentTerminalState!=null&&t.getState()==this.currentTerminalState&&C>2e3||(this.currentTerminalState==null||mxUtils.getValue(this.currentTerminalState.style,"outlineConnect","1")!="0")&&E.apply(this,arguments)};mxVertexHandler.prototype.isCustomHandleEvent=function(t){return!mxEvent.isShiftDown(t.getEvent())};mxEdgeHandler.prototype.createHandleShape=function(t,e){var i=t!=null&&t==0;var n=this.state.getVisibleTerminalState(i);var r=t!=null&&(t==0||t>=this.state.absolutePoints.length-1||this.constructor==mxElbowEdgeHandler&&t==2)?this.graph.getConnectionConstraint(this.state,n,i):null;var l=r!=null?this.graph.getConnectionPoint(this.state.getVisibleTerminalState(i),r):null;var a=l!=null?this.fixedHandleImage:r!=null&&n!=null?this.terminalHandleImage:this.handleImage;if(a!=null){var s=new mxImageShape(new mxRectangle(0,0,a.width,a.height),a.src);s.preserveImageAspect=false;return s}else{var o=mxConstants.HANDLE_SIZE;if(this.preferHtml){o-=1}return new mxRectangleShape(new mxRectangle(0,0,o,o),mxConstants.HANDLE_FILLCOLOR,mxConstants.HANDLE_STROKECOLOR)}};var w=mxVertexHandler.prototype.createSizerShape;mxVertexHandler.prototype.createSizerShape=function(t,e,i){this.handleImage=e==mxEvent.ROTATION_HANDLE?HoverIcons.prototype.rotationHandle:e==mxEvent.LABEL_HANDLE?this.secondaryHandleImage:this.handleImage;return w.apply(this,arguments)};var b=mxGraphHandler.prototype.getBoundingBox;mxGraphHandler.prototype.getBoundingBox=function(t){if(t!=null&&t.length==1){var e=this.graph.getModel();var i=e.getParent(t[0]);var n=this.graph.getCellGeometry(t[0]);if(e.isEdge(i)&&n!=null&&n.relative){var r=this.graph.view.getState(t[0]);if(r!=null&&r.width<2&&r.height<2&&r.text!=null&&r.text.boundingBox!=null){return mxRectangle.fromRectangle(r.text.boundingBox)}}}return b.apply(this,arguments)};var H=mxGraphHandler.prototype.getGuideStates;mxGraphHandler.prototype.getGuideStates=function(){var t=H.apply(this,arguments);var e=[];for(var i=0;i<t.length;i++){if(mxUtils.getValue(t[i].style,"part","0")!="1"){e.push(t[i])}}return e};var L=mxVertexHandler.prototype.getSelectionBounds;mxVertexHandler.prototype.getSelectionBounds=function(t){var e=this.graph.getModel();var i=e.getParent(t.cell);var n=this.graph.getCellGeometry(t.cell);if(e.isEdge(i)&&n!=null&&n.relative&&t.width<2&&t.height<2&&t.text!=null&&t.text.boundingBox!=null){var r=t.text.unrotatedBoundingBox||t.text.boundingBox;return new mxRectangle(Math.round(r.x),Math.round(r.y),Math.round(r.width),Math.round(r.height))}else{return L.apply(this,arguments)}};var T=mxVertexHandler.prototype.mouseDown;mxVertexHandler.prototype.mouseDown=function(t,e){var i=this.graph.getModel();var n=i.getParent(this.state.cell);var r=this.graph.getCellGeometry(this.state.cell);var l=this.getHandleForEvent(e);if(l==mxEvent.ROTATION_HANDLE||!i.isEdge(n)||r==null||!r.relative||this.state==null||this.state.width>=2||this.state.height>=2){T.apply(this,arguments)}};mxVertexHandler.prototype.isRotationHandleVisible=function(){return this.graph.isEnabled()&&this.rotationEnabled&&this.graph.isCellRotatable(this.state.cell)&&(mxGraphHandler.prototype.maxCells<=0||this.graph.getSelectionCount()<mxGraphHandler.prototype.maxCells)};mxVertexHandler.prototype.rotateClick=function(){var t=mxUtils.getValue(this.state.style,mxConstants.STYLE_STROKECOLOR,mxConstants.NONE);var e=mxUtils.getValue(this.state.style,mxConstants.STYLE_FILLCOLOR,mxConstants.NONE);if(this.state.view.graph.model.isVertex(this.state.cell)&&t==mxConstants.NONE&&e==mxConstants.NONE){var i=mxUtils.mod(mxUtils.getValue(this.state.style,mxConstants.STYLE_ROTATION,0)+90,360);this.state.view.graph.setCellStyles(mxConstants.STYLE_ROTATION,i,[this.state.cell])}else{this.state.view.graph.turnShapes([this.state.cell])}};var G=mxVertexHandler.prototype.mouseMove;mxVertexHandler.prototype.mouseMove=function(t,e){G.apply(this,arguments);if(this.graph.graphHandler.first!=null){if(this.rotationShape!=null&&this.rotationShape.node!=null){this.rotationShape.node.style.display="none"}if(this.linkHint!=null&&this.linkHint.style.display!="none"){this.linkHint.style.display="none"}}};var A=mxVertexHandler.prototype.mouseUp;mxVertexHandler.prototype.mouseUp=function(t,e){A.apply(this,arguments);if(this.rotationShape!=null&&this.rotationShape.node!=null){this.rotationShape.node.style.display=this.graph.getSelectionCount()==1?"":"none"}if(this.linkHint!=null&&this.linkHint.style.display=="none"){this.linkHint.style.display=""}};var I=mxVertexHandler.prototype.init;mxVertexHandler.prototype.init=function(){I.apply(this,arguments);var t=false;if(this.rotationShape!=null){this.rotationShape.node.setAttribute("title",mxResources.get("rotateTooltip"))}var i=mxUtils.bind(this,function(){if(this.specialHandle!=null){this.specialHandle.node.style.display=this.graph.isEnabled()&&this.graph.getSelectionCount()<this.graph.graphHandler.maxCells?"":"none"}this.redrawHandles()});this.changeHandler=mxUtils.bind(this,function(t,e){this.updateLinkHint(this.graph.getLinkForCell(this.state.cell),typeof this.graph.getLinkForCellState==="function"?this.graph.getLinksForState(this.state):null);i()});this.graph.getSelectionModel().addListener(mxEvent.CHANGE,this.changeHandler);this.graph.getModel().addListener(mxEvent.CHANGE,this.changeHandler);this.editingHandler=mxUtils.bind(this,function(t,e){this.redrawHandles()});this.graph.addListener(mxEvent.EDITING_STOPPED,this.editingHandler);var e=this.graph.getLinkForCell(this.state.cell);var n=typeof this.graph.getLinksForState==="function"?this.graph.getLinksForState(this.state):null;this.updateLinkHint(e,n);if(e!=null||n!=null&&n.length>0){t=true}if(t){this.redrawHandles()}};mxVertexHandler.prototype.updateLinkHint=function(t,e){try{if(t==null&&(e==null||e.length==0)||this.graph.getSelectionCount()>1){if(this.linkHint!=null){this.linkHint.parentNode.removeChild(this.linkHint);this.linkHint=null}}else if(t!=null||e!=null&&e.length>0){if(this.linkHint==null){this.linkHint=g();this.linkHint.style.padding="6px 8px 6px 8px";this.linkHint.style.opacity="1";this.linkHint.style.filter="";this.graph.container.appendChild(this.linkHint)}this.linkHint.innerHTML="";if(t!=null){this.linkHint.appendChild(this.graph.createLinkForHint(t));if(this.graph.isEnabled()&&typeof this.graph.editLink==="function"){var i=document.createElement("img");i.setAttribute("src",Editor.editImage);i.setAttribute("title",mxResources.get("editLink"));i.setAttribute("width","11");i.setAttribute("height","11");i.style.marginLeft="10px";i.style.marginBottom="-1px";i.style.cursor="pointer";this.linkHint.appendChild(i);mxEvent.addListener(i,"click",mxUtils.bind(this,function(t){this.graph.setSelectionCell(this.state.cell);this.graph.editLink();mxEvent.consume(t)}));var n=document.createElement("img");n.setAttribute("src",Dialog.prototype.clearImage);n.setAttribute("title",mxResources.get("removeIt",[mxResources.get("link")]));n.setAttribute("width","13");n.setAttribute("height","10");n.style.marginLeft="4px";n.style.marginBottom="-1px";n.style.cursor="pointer";this.linkHint.appendChild(n);mxEvent.addListener(n,"click",mxUtils.bind(this,function(t){this.graph.setLinkForCell(this.state.cell,null);mxEvent.consume(t)}))}}if(e!=null){for(var r=0;r<e.length;r++){var l=document.createElement("div");l.style.marginTop=t!=null||r>0?"6px":"0px";l.appendChild(this.graph.createLinkForHint(e[r].getAttribute("href"),mxUtils.getTextContent(e[r])));this.linkHint.appendChild(l)}}}}catch(t){}};mxEdgeHandler.prototype.updateLinkHint=mxVertexHandler.prototype.updateLinkHint;var M=mxEdgeHandler.prototype.init;mxEdgeHandler.prototype.init=function(){M.apply(this,arguments);this.constraintHandler.isEnabled=mxUtils.bind(this,function(){return this.state.view.graph.connectionHandler.isEnabled()});var i=mxUtils.bind(this,function(){if(this.linkHint!=null){this.linkHint.style.display=this.graph.getSelectionCount()==1?"":"none"}if(this.labelShape!=null){this.labelShape.node.style.display=this.graph.isEnabled()&&this.graph.getSelectionCount()<this.graph.graphHandler.maxCells?"":"none"}});this.changeHandler=mxUtils.bind(this,function(t,e){this.updateLinkHint(this.graph.getLinkForCell(this.state.cell),typeof this.graph.getLinksForState==="function"?this.graph.getLinksForState(this.state):null);i();this.redrawHandles()});this.graph.getSelectionModel().addListener(mxEvent.CHANGE,this.changeHandler);this.graph.getModel().addListener(mxEvent.CHANGE,this.changeHandler);var t=this.graph.getLinkForCell(this.state.cell);var e=typeof this.graph.getLinksForState==="function"?this.graph.getLinksForState(this.state):null;if(t!=null||e!=null&&e.length>0){this.updateLinkHint(t,e);this.redrawHandles()}};var U=mxConnectionHandler.prototype.init;mxConnectionHandler.prototype.init=function(){U.apply(this,arguments);this.constraintHandler.isEnabled=mxUtils.bind(this,function(){return this.graph.connectionHandler.isEnabled()})};var R=mxVertexHandler.prototype.redrawHandles;mxVertexHandler.prototype.redrawHandles=function(){if(this.rotationShape!=null&&this.rotationShape.node!=null){this.rotationShape.node.style.display=this.graph.getSelectionCount()==1&&(this.index==null||this.index==mxEvent.ROTATION_HANDLE)?"":"none"}R.apply(this);if(this.state!=null&&this.linkHint!=null){var t=new mxPoint(this.state.getCenterX(),this.state.getCenterY());var e=new mxRectangle(this.state.x,this.state.y-22,this.state.width+24,this.state.height+22);var i=mxUtils.getBoundingBox(e,this.state.style[mxConstants.STYLE_ROTATION]||"0",t);var n=i!=null?mxUtils.getBoundingBox(this.state,this.state.style[mxConstants.STYLE_ROTATION]||"0"):this.state;var r=this.state.text!=null?this.state.text.boundingBox:null;if(i==null){i=this.state}var l=i.y+i.height;if(r!=null){l=Math.max(l,r.y+r.height)}this.linkHint.style.left=Math.max(0,Math.round(n.x+(n.width-this.linkHint.clientWidth)/2))+"px";this.linkHint.style.top=Math.round(l+this.verticalOffset/2+Editor.hintOffset)+"px"}};var D=mxVertexHandler.prototype.destroy;mxVertexHandler.prototype.destroy=function(){D.apply(this,arguments);if(this.linkHint!=null){this.linkHint.parentNode.removeChild(this.linkHint);this.linkHint=null}if(this.changeHandler!=null){this.graph.getSelectionModel().removeListener(this.changeHandler);this.graph.getModel().removeListener(this.changeHandler);this.changeHandler=null}if(this.editingHandler!=null){this.graph.removeListener(this.editingHandler);this.editingHandler=null}};var k=mxEdgeHandler.prototype.redrawHandles;mxEdgeHandler.prototype.redrawHandles=function(){if(this.marker!=null){k.apply(this);if(this.state!=null&&this.linkHint!=null){var t=this.state;if(this.state.text!=null&&this.state.text.bounds!=null){t=new mxRectangle(t.x,t.y,t.width,t.height);t.add(this.state.text.bounds)}this.linkHint.style.left=Math.max(0,Math.round(t.x+(t.width-this.linkHint.clientWidth)/2))+"px";this.linkHint.style.top=Math.round(t.y+t.height+Editor.hintOffset)+"px"}}};var P=mxEdgeHandler.prototype.reset;mxEdgeHandler.prototype.reset=function(){P.apply(this,arguments);if(this.linkHint!=null){this.linkHint.style.visibility=""}};var N=mxEdgeHandler.prototype.destroy;mxEdgeHandler.prototype.destroy=function(){N.apply(this,arguments);if(this.linkHint!=null){this.linkHint.parentNode.removeChild(this.linkHint);this.linkHint=null}if(this.changeHandler!=null){this.graph.getModel().removeListener(this.changeHandler);this.graph.getSelectionModel().removeListener(this.changeHandler);this.changeHandler=null}}})()}EditorUi.prototype.svgBrokenImage=Graph.createSvgImage(10,10,'<rect x="0" y="0" width="10" height="10" stroke="#000" fill="transparent"/><path d="m 0 0 L 10 10 L 0 10 L 10 0" stroke="#000" fill="transparent"/>');