function Menubar(e,t){this.editorUi=e,this.container=t}function Menu(e,t){mxEventSource.call(this),this.funct=e,this.enabled=null==t||t}(Menus=function(e){this.editorUi=e,this.menus=new Object,this.init(),mxClient.IS_SVG||((new Image).src=this.checkmarkImage)}).prototype.defaultFont="Helvetica",Menus.prototype.defaultFontSize="12",Menus.prototype.defaultMenuItems=["file","edit","view","arrange","extras","help"],Menus.prototype.defaultFonts=["Helvetica","Verdana","Times New Roman","Garamond","Comic Sans MS","Courier New","Georgia","Lucida Console","Tahoma"],Menus.prototype.init=function(){var o=this.editorUi.editor.graph,e=mxUtils.bind(o,o.isEnabled);this.customFonts=[],this.customFontSizes=[],this.put("fontFamily",new Menu(mxUtils.bind(this,function(t,n){for(var e=mxUtils.bind(this,function(e){this.styleChange(t,e,[mxConstants.STYLE_FONTFAMILY],[e],null,n,function(){document.execCommand("fontname",!1,e)},function(){o.updateLabelElements(o.getSelectionCells(),function(e){e.removeAttribute("face"),e.style.fontFamily=null,"PRE"==e.nodeName&&o.replaceElement(e,"div")})}).firstChild.nextSibling.style.fontFamily=e}),i=0;i<this.defaultFonts.length;i++)e(this.defaultFonts[i]);if(t.addSeparator(n),0<this.customFonts.length){for(i=0;i<this.customFonts.length;i++)e(this.customFonts[i]);t.addSeparator(n),t.addItem(mxResources.get("reset"),null,mxUtils.bind(this,function(){this.customFonts=[],this.editorUi.fireEvent(new mxEventObject("customFontsChanged"))}),n),t.addSeparator(n)}this.promptChange(t,mxResources.get("custom")+"...","",mxConstants.DEFAULT_FONTFAMILY,mxConstants.STYLE_FONTFAMILY,n,!0,mxUtils.bind(this,function(e){mxUtils.indexOf(this.customFonts,e)<0&&(this.customFonts.push(e),this.editorUi.fireEvent(new mxEventObject("customFontsChanged")))}))}))),this.put("formatBlock",new Menu(mxUtils.bind(this,function(n,i){function e(e,t){return n.addItem(e,null,mxUtils.bind(this,function(){null!=o.cellEditor.textarea&&(o.cellEditor.textarea.focus(),document.execCommand("formatBlock",!1,"<"+t+">"))}),i)}e(mxResources.get("normal"),"p"),e("","h1").firstChild.nextSibling.innerHTML='<h1 style="margin:0px;">'+mxResources.get("heading")+" 1</h1>",e("","h2").firstChild.nextSibling.innerHTML='<h2 style="margin:0px;">'+mxResources.get("heading")+" 2</h2>",e("","h3").firstChild.nextSibling.innerHTML='<h3 style="margin:0px;">'+mxResources.get("heading")+" 3</h3>",e("","h4").firstChild.nextSibling.innerHTML='<h4 style="margin:0px;">'+mxResources.get("heading")+" 4</h4>",e("","h5").firstChild.nextSibling.innerHTML='<h5 style="margin:0px;">'+mxResources.get("heading")+" 5</h5>",e("","h6").firstChild.nextSibling.innerHTML='<h6 style="margin:0px;">'+mxResources.get("heading")+" 6</h6>",e("","pre").firstChild.nextSibling.innerHTML='<pre style="margin:0px;">'+mxResources.get("formatted")+"</pre>",e("","blockquote").firstChild.nextSibling.innerHTML='<blockquote style="margin-top:0px;margin-bottom:0px;">'+mxResources.get("blockquote")+"</blockquote>"}))),this.put("fontSize",new Menu(mxUtils.bind(this,function(e,t){for(var n=[6,8,9,10,11,12,14,18,24,36,48,72],i=mxUtils.bind(this,function(n){this.styleChange(e,n,[mxConstants.STYLE_FONTSIZE],[n],null,t,function(){if(null!=o.cellEditor.textarea){document.execCommand("fontSize",!1,"3");for(var e=o.cellEditor.textarea.getElementsByTagName("font"),t=0;t<e.length;t++)if("3"==e[t].getAttribute("size")){e[t].removeAttribute("size"),e[t].style.fontSize=n+"px";break}}})}),l=0;l<n.length;l++)i(n[l]);if(e.addSeparator(t),0<this.customFontSizes.length){for(l=0;l<this.customFontSizes.length;l++)i(this.customFontSizes[l]);e.addSeparator(t),e.addItem(mxResources.get("reset"),null,mxUtils.bind(this,function(){this.customFontSizes=[]}),t),e.addSeparator(t)}this.promptChange(e,mxResources.get("custom")+"...","(pt)","12",mxConstants.STYLE_FONTSIZE,t,!0,mxUtils.bind(this,function(e){this.customFontSizes.push(e)}))}))),this.put("direction",new Menu(mxUtils.bind(this,function(e,t){e.addItem(mxResources.get("flipH"),null,function(){o.toggleCellStyles(mxConstants.STYLE_FLIPH,!1)},t),e.addItem(mxResources.get("flipV"),null,function(){o.toggleCellStyles(mxConstants.STYLE_FLIPV,!1)},t),this.addMenuItems(e,["-","rotation"],t)}))),this.put("align",new Menu(mxUtils.bind(this,function(e,t){e.addItem(mxResources.get("leftAlign"),null,function(){o.alignCells(mxConstants.ALIGN_LEFT)},t),e.addItem(mxResources.get("center"),null,function(){o.alignCells(mxConstants.ALIGN_CENTER)},t),e.addItem(mxResources.get("rightAlign"),null,function(){o.alignCells(mxConstants.ALIGN_RIGHT)},t),e.addSeparator(t),e.addItem(mxResources.get("topAlign"),null,function(){o.alignCells(mxConstants.ALIGN_TOP)},t),e.addItem(mxResources.get("middle"),null,function(){o.alignCells(mxConstants.ALIGN_MIDDLE)},t),e.addItem(mxResources.get("bottomAlign"),null,function(){o.alignCells(mxConstants.ALIGN_BOTTOM)},t)}))),this.put("distribute",new Menu(mxUtils.bind(this,function(e,t){e.addItem(mxResources.get("horizontal"),null,function(){o.distributeCells(!0)},t),e.addItem(mxResources.get("vertical"),null,function(){o.distributeCells(!1)},t)}))),this.put("layout",new Menu(mxUtils.bind(this,function(e,t){var i=mxUtils.bind(this,function(e,t){e=new FilenameDialog(this.editorUi,e,mxResources.get("apply"),function(e){t(parseFloat(e))},mxResources.get("spacing"));this.editorUi.showDialog(e.container,300,80,!0,!0),e.init()});e.addItem(mxResources.get("horizontalFlow"),null,mxUtils.bind(this,function(){var t=new mxHierarchicalLayout(o,mxConstants.DIRECTION_WEST);this.editorUi.executeLayout(function(){var e=o.getSelectionCells();t.execute(o.getDefaultParent(),0==e.length?null:e)},!0)}),t),e.addItem(mxResources.get("verticalFlow"),null,mxUtils.bind(this,function(){var t=new mxHierarchicalLayout(o,mxConstants.DIRECTION_NORTH);this.editorUi.executeLayout(function(){var e=o.getSelectionCells();t.execute(o.getDefaultParent(),0==e.length?null:e)},!0)}),t),e.addSeparator(t),e.addItem(mxResources.get("horizontalTree"),null,mxUtils.bind(this,function(){var t,n=o.getSelectionCell(),e=null;null==n||0==o.getModel().getChildCount(n)?0==o.getModel().getEdgeCount(n)&&(e=o.findTreeRoots(o.getDefaultParent())):e=o.findTreeRoots(n),null!=(n=null!=e&&0<e.length?e[0]:n)&&((t=new mxCompactTreeLayout(o,!0)).edgeRouting=!1,t.levelDistance=30,i(t.levelDistance,mxUtils.bind(this,function(e){t.levelDistance=e,this.editorUi.executeLayout(function(){t.execute(o.getDefaultParent(),n)},!0)})))}),t),e.addItem(mxResources.get("verticalTree"),null,mxUtils.bind(this,function(){var t,n=o.getSelectionCell(),e=null;null==n||0==o.getModel().getChildCount(n)?0==o.getModel().getEdgeCount(n)&&(e=o.findTreeRoots(o.getDefaultParent())):e=o.findTreeRoots(n),null!=(n=null!=e&&0<e.length?e[0]:n)&&((t=new mxCompactTreeLayout(o,!1)).edgeRouting=!1,t.levelDistance=30,i(t.levelDistance,mxUtils.bind(this,function(e){t.levelDistance=e,this.editorUi.executeLayout(function(){t.execute(o.getDefaultParent(),n)},!0)})))}),t),e.addItem(mxResources.get("radialTree"),null,mxUtils.bind(this,function(){var t,n=o.getSelectionCell(),e=null;null==n||0==o.getModel().getChildCount(n)?0==o.getModel().getEdgeCount(n)&&(e=o.findTreeRoots(o.getDefaultParent())):e=o.findTreeRoots(n),null!=(n=null!=e&&0<e.length?e[0]:n)&&((t=new mxRadialTreeLayout(o,!1)).levelDistance=80,t.autoRadius=!0,i(t.levelDistance,mxUtils.bind(this,function(e){t.levelDistance=e,this.editorUi.executeLayout(function(){t.execute(o.getDefaultParent(),n),o.isSelectionEmpty()||(n=o.getModel().getParent(n),o.getModel().isVertex(n)&&o.updateGroupBounds([n],2*o.gridSize,!0))},!0)})))}),t),e.addSeparator(t),e.addItem(mxResources.get("organic"),null,mxUtils.bind(this,function(){var t=new mxFastOrganicLayout(o);i(t.forceConstant,mxUtils.bind(this,function(e){t.forceConstant=e,this.editorUi.executeLayout(function(){var e=o.getSelectionCell();null!=e&&0!=o.getModel().getChildCount(e)||(e=o.getDefaultParent()),t.execute(e),o.getModel().isVertex(e)&&o.updateGroupBounds([e],2*o.gridSize,!0)},!0)}))}),t),e.addItem(mxResources.get("circle"),null,mxUtils.bind(this,function(){var t=new mxCircleLayout(o);this.editorUi.executeLayout(function(){var e=o.getSelectionCell();null!=e&&0!=o.getModel().getChildCount(e)||(e=o.getDefaultParent()),t.execute(e),o.getModel().isVertex(e)&&o.updateGroupBounds([e],2*o.gridSize,!0)},!0)}),t)}))),this.put("navigation",new Menu(mxUtils.bind(this,function(e,t){this.addMenuItems(e,["home","-","exitGroup","enterGroup","-","expand","collapse","-","collapsible"],t)}))),this.put("arrange",new Menu(mxUtils.bind(this,function(e,t){this.addMenuItems(e,["toFront","toBack","-"],t),this.addSubmenu("direction",e,t),this.addMenuItems(e,["turn","-"],t),this.addSubmenu("align",e,t),this.addSubmenu("distribute",e,t),e.addSeparator(t),this.addSubmenu("navigation",e,t),this.addSubmenu("insert",e,t),this.addSubmenu("layout",e,t),this.addMenuItems(e,["-","group","ungroup","removeFromGroup","-","clearWaypoints","autosize"],t)}))).isEnabled=e,this.put("insert",new Menu(mxUtils.bind(this,function(e,t){this.addMenuItems(e,["insertLink","insertImage"],t)}))),this.put("view",new Menu(mxUtils.bind(this,function(e,t){this.addMenuItems(e,(null!=this.editorUi.format?["formatPanel"]:[]).concat(["outline","layers","-","pageView","pageScale","-","scrollbars","tooltips","-","grid","guides","-","connectionArrows","connectionPoints","-","resetView","zoomIn","zoomOut"],t))}))),this.put("viewPanels",new Menu(mxUtils.bind(this,function(e,t){null!=this.editorUi.format&&this.addMenuItems(e,["formatPanel"],t),this.addMenuItems(e,["outline","layers"],t)}))),this.put("viewZoom",new Menu(mxUtils.bind(this,function(t,n){this.addMenuItems(t,["resetView","-"],n);for(var e=[.25,.5,.75,1,1.25,1.5,2,3,4],i=0;i<e.length;i++)!function(e){t.addItem(100*e+"%",null,function(){o.zoomTo(e)},n)}(e[i]);this.addMenuItems(t,["-","fitWindow","fitPageWidth","fitPage","fitTwoPages","-","customZoom"],n)}))),this.put("file",new Menu(mxUtils.bind(this,function(e,t){this.addMenuItems(e,["new","open","-","save","saveAs","-","import","export","-","pageSetup","print"],t)}))),this.put("edit",new Menu(mxUtils.bind(this,function(e,t){this.addMenuItems(e,["undo","redo","-","cut","copy","paste","delete","-","duplicate","-","editData","editTooltip","editStyle","-","edit","-","editLink","openLink","-","selectVertices","selectEdges","selectAll","selectNone","-","lockUnlock"])}))),this.put("extras",new Menu(mxUtils.bind(this,function(e,t){this.addMenuItems(e,["copyConnect","collapseExpand","-","editDiagram"])}))),this.put("help",new Menu(mxUtils.bind(this,function(e,t){this.addMenuItems(e,["help","-","about"])})))},Menus.prototype.put=function(e,t){return this.menus[e]=t},Menus.prototype.get=function(e){return this.menus[e]},Menus.prototype.addSubmenu=function(e,t,n,i){var l=this.get(e);null!=l&&(l=l.isEnabled(),(t.showDisabled||l)&&(i=t.addItem(i||mxResources.get(e),null,null,n,null,l),this.addMenu(e,t,i)))},Menus.prototype.addMenu=function(e,t,n){var i=this.get(e);null!=i&&(t.showDisabled||i.isEnabled())&&this.get(e).execute(t,n)},Menus.prototype.addInsertTableItem=function(e){var g=this.editorUi.editor.graph;var e=e.addItem("",null,mxUtils.bind(this,function(e){e=g.getParentByName(mxEvent.getSource(e),"TD");if(null!=e&&null!=g.cellEditor.textarea){for(var t=g.getParentByName(e,"TR"),n=g.cellEditor.textarea.getElementsByTagName("table"),i=[],l=0;l<n.length;l++)i.push(n[l]);g.container.focus(),g.pasteHtmlAtCaret(function(e,t){for(var n=["<table>"],i=0;i<e;i++){n.push("<tr>");for(var l=0;l<t;l++)n.push("<td><br></td>");n.push("</tr>")}return n.push("</table>"),n.join("")}(t.sectionRowIndex+1,e.cellIndex+1));var o=g.cellEditor.textarea.getElementsByTagName("table");if(o.length==i.length+1)for(l=o.length-1;0<=l;l--)if(0==l||o[l]!=i[l-1]){g.selectNode(o[l].rows[0].cells[0]);break}}})),p='<img src="'+mxClient.imageBasePath+'/transparent.gif" width="16" height="16"/>';e.firstChild.innerHTML="";var f=function(e,t){var n=document.createElement("table");n.setAttribute("border","1"),n.style.borderCollapse="collapse",mxClient.IS_QUIRKS||n.setAttribute("cellPadding","8");for(var i=0;i<e;i++)for(var l=n.insertRow(i),o=0;o<t;o++){var s=l.insertCell(-1);mxClient.IS_QUIRKS&&(s.innerHTML=p)}return n}(5,5),x=(e.firstChild.appendChild(f),document.createElement("div"));x.style.padding="4px",x.style.fontSize=Menus.prototype.defaultFontSize+"px",x.innerHTML="1x1",e.firstChild.appendChild(x),mxEvent.addListener(f,"mouseover",function(e){var t=g.getParentByName(mxEvent.getSource(e),"TD");if(null!=t){for(var n=g.getParentByName(t,"TR"),i=f,l=Math.min(20,n.sectionRowIndex+2),o=Math.min(20,t.cellIndex+2),s=i.rows.length;s<l;s++)for(var r=i.insertRow(s),a=0;a<i.rows[0].cells.length;a++){var u=r.insertCell(-1);mxClient.IS_QUIRKS&&(u.innerHTML=p)}for(s=0;s<i.rows.length;s++)for(a=(r=i.rows[s]).cells.length;a<o;a++){u=r.insertCell(-1);mxClient.IS_QUIRKS&&(u.innerHTML=p)}x.innerHTML=t.cellIndex+1+"x"+(n.sectionRowIndex+1);for(var d=0;d<f.rows.length;d++)for(var c=f.rows[d],m=0;m<c.cells.length;m++){var h=c.cells[m];d<=n.sectionRowIndex&&m<=t.cellIndex?h.style.backgroundColor="blue":h.style.backgroundColor="white"}mxEvent.consume(e)}})},Menus.prototype.edgeStyleChange=function(e,t,r,a,n,i,u){return e.addItem(t,null,mxUtils.bind(this,function(){var e=this.editorUi.editor.graph;e.stopEditing(!1),e.getModel().beginUpdate();try{for(var t=e.getSelectionCells(),n=[],i=0;i<t.length;i++){var l,o=t[i];if(e.getModel().isEdge(o)){!u||null!=(l=e.getCellGeometry(o))&&((l=l.clone()).points=null,e.getModel().setGeometry(o,l));for(var s=0;s<r.length;s++)e.setCellStyles(r[s],a[s],[o]);n.push(o)}}this.editorUi.fireEvent(new mxEventObject("styleChanged","keys",r,"values",a,"cells",n))}finally{e.getModel().endUpdate()}}),i,n)},Menus.prototype.styleChange=function(e,t,n,i,l,o,s,r){var a=this.createStyleChangeFunction(n,i);return e.addItem(t,null,mxUtils.bind(this,function(){var e=this.editorUi.editor.graph;null!=s&&e.cellEditor.isContentEditing()?s():a(r)}),o,l)},Menus.prototype.createStyleChangeFunction=function(l,o){return mxUtils.bind(this,function(e){var t=this.editorUi.editor.graph;t.stopEditing(!1),t.getModel().beginUpdate();try{for(var n=t.getSelectionCells(),i=0;i<l.length;i++)if(t.setCellStyles(l[i],o[i],n),l[i]==mxConstants.STYLE_ALIGN&&t.updateLabelElements(n,function(e){e.removeAttribute("align"),e.style.textAlign=null}),l[i]==mxConstants.STYLE_FONTFAMILY)for(i=0;i<n.length;i++)0==t.model.getChildCount(n[i])&&t.autoSizeCell(n[i],!1);null!=e&&e(),this.editorUi.fireEvent(new mxEventObject("styleChanged","keys",l,"values",o,"cells",n))}finally{t.getModel().endUpdate()}})},Menus.prototype.promptChange=function(e,t,i,l,o,n,s,r,a){return e.addItem(t,null,mxUtils.bind(this,function(){var t=this.editorUi.editor.graph,e=l,n=t.getView().getState(t.getSelectionCell()),n=(null!=n&&(e=n.style[o]||e),new FilenameDialog(this.editorUi,e,mxResources.get("apply"),mxUtils.bind(this,function(e){if(null!=e&&0<e.length){t.getModel().beginUpdate();try{t.stopEditing(!1),t.setCellStyles(o,e)}finally{t.getModel().endUpdate()}null!=r&&r(e)}}),mxResources.get("enterValue")+(0<i.length?" "+i:"")));this.editorUi.showDialog(n.container,300,80,!0,!0),n.init()}),n,a,s)},Menus.prototype.pickColor=function(e,t,n){var i,l,o=this.editorUi.editor.graph,s=226+17*(Math.ceil(ColorDialog.prototype.presetColors.length/12)+Math.ceil(ColorDialog.prototype.defaultColors.length/12));null!=t&&o.cellEditor.isContentEditing()?(i=o.cellEditor.saveSelection(),n=new ColorDialog(this.editorUi,n||"000000",mxUtils.bind(this,function(e){o.cellEditor.restoreSelection(i),document.execCommand(t,!1,e!=mxConstants.NONE?e:"transparent")}),function(){o.cellEditor.restoreSelection(i)}),this.editorUi.showDialog(n.container,230,s,!0,!0),n.init()):(null==this.colorDialog&&(this.colorDialog=new ColorDialog(this.editorUi)),this.colorDialog.currentColorKey=e,(n="none")==(n=null!=(l=o.getView().getState(o.getSelectionCell()))?l.style[e]||n:n)?(this.colorDialog.picker.fromString(n="ffffff"),this.colorDialog.colorInput.value="none"):this.colorDialog.picker.fromString(n),this.editorUi.showDialog(this.colorDialog.container,230,s,!0,!0),this.colorDialog.init())},Menus.prototype.toggleStyle=function(e,t){var n=this.editorUi.editor.graph,t=n.toggleCellStyles(e,t);this.editorUi.fireEvent(new mxEventObject("styleChanged","keys",[e],"values",[t],"cells",n.getSelectionCells()))},Menus.prototype.addMenuItem=function(e,t,n,i,l,o){var s=this.editorUi.actions.get(t);return null!=s&&(e.showDisabled||s.isEnabled())&&s.visible?(t=e.addItem(o||s.label,null,function(){s.funct(i)},n,l,s.isEnabled()),s.toggleAction&&s.isSelected()&&e.addCheckmark(t,Editor.checkmarkImage),this.addShortcut(t,s),t):null},Menus.prototype.addShortcut=function(e,t){var n;null!=t.shortcut&&(e=e.firstChild.nextSibling.nextSibling,(n=document.createElement("span")).style.color="gray",mxUtils.write(n,t.shortcut),e.appendChild(n))},Menus.prototype.addMenuItems=function(e,t,n,i,l){for(var o=0;o<t.length;o++)"-"==t[o]?e.addSeparator(n):this.addMenuItem(e,t[o],n,i,null!=l?l[o]:null)},Menus.prototype.createPopupMenu=function(e,t,n){var i,l,o,s,r,a,u=this.editorUi.editor.graph;e.smartSeparators=!0,u.isSelectionEmpty()?this.addMenuItems(e,["undo","redo","pasteHere"],null,n):this.addMenuItems(e,["delete","-","cut","copy","-","duplicate"],null,n),u.isSelectionEmpty()?this.addMenuItems(e,["-","selectVertices","selectEdges","selectAll","-","clearDefaultStyle"],null,n):(1==u.getSelectionCount()&&this.addMenuItems(e,["setAsDefaultStyle"],null,n),e.addSeparator(),t=u.getSelectionCell(),null!=(i=u.view.getState(t))&&(a=!1,this.addMenuItems(e,["toFront","toBack","-"],null,n),u.getModel().isEdge(t)&&"entityRelationEdgeStyle"!=mxUtils.getValue(i.style,mxConstants.STYLE_EDGE,null)&&"arrow"!=mxUtils.getValue(i.style,mxConstants.STYLE_SHAPE,null)&&(o=!1,(l=u.selectionCellsHandler.getHandler(t))instanceof mxEdgeHandler&&null!=l.bends&&2<l.bends.length&&(s=l.getHandleForEvent(u.updateMouseEvent(new mxMouseEvent(n))),(r=this.editorUi.actions.get("removeWaypoint")).handler=l,o=0<(r.index=s)&&s<l.bends.length-1),e.addSeparator(),this.addMenuItem(e,"turn",null,n,null,mxResources.get("reverse")),this.addMenuItems(e,[o?"removeWaypoint":"addWaypoint"],null,n),a=null!=(r=u.getModel().getGeometry(t))&&null!=r.points&&0<r.points.length),1==u.getSelectionCount()&&(a||u.getModel().isVertex(t)&&0<u.getModel().getEdgeCount(t))&&this.addMenuItems(e,["clearWaypoints"],null,n),1<u.getSelectionCount()?(e.addSeparator(),this.addMenuItems(e,["group"],null,n)):1==u.getSelectionCount()&&!u.getModel().isEdge(t)&&!u.isSwimlane(t)&&0<u.getModel().getChildCount(t)&&(e.addSeparator(),this.addMenuItems(e,["ungroup"],null,n)),1==u.getSelectionCount()&&(e.addSeparator(),this.addMenuItems(e,["editData","editLink"],null,n),u.getModel().isVertex(t)&&null!=mxUtils.getValue(i.style,mxConstants.STYLE_IMAGE,null)&&(e.addSeparator(),this.addMenuItem(e,"image",null,n).firstChild.nextSibling.innerHTML=mxResources.get("editImage")+"..."))))},Menus.prototype.createMenubar=function(e){for(var n=new Menubar(this.editorUi,e),i=this.defaultMenuItems,l=0;l<i.length;l++)mxUtils.bind(this,function(e){var t=n.addMenu(mxResources.get(i[l]),mxUtils.bind(this,function(){e.funct.apply(this,arguments)}));this.menuCreated(e,t)})(this.get(i[l]));return n},Menus.prototype.menuCreated=function(e,t,n){null!=t&&(n=null!=n?n:"geItem",e.addListener("stateChanged",function(){t.enabled=e.enabled,e.enabled?(t.className=n,8==document.documentMode&&(t.style.color="")):(t.className=n+" mxDisabled",8==document.documentMode&&(t.style.color="#c3c3c3"))}))},Menubar.prototype.hideMenu=function(){this.editorUi.hideCurrentMenu()},Menubar.prototype.addMenu=function(e,t,n){var i=document.createElement("a");return i.className="geItem",mxUtils.write(i,e),this.addMenuHandler(i,t),null!=n?this.container.insertBefore(i,n):this.container.appendChild(i),i},Menubar.prototype.addMenuHandler=function(i,l){var o,t;null!=l&&(o=!0,t=mxUtils.bind(this,function(e){var t,n;(o&&null==i.enabled||i.enabled)&&(this.editorUi.editor.graph.popupMenuHandler.hideMenu(),(t=new mxPopupMenu(l)).div.className+=" geMenubarMenu",t.smartSeparators=!0,t.showDisabled=!0,t.autoExpand=!0,t.hideMenu=mxUtils.bind(this,function(){mxPopupMenu.prototype.hideMenu.apply(t,arguments),this.editorUi.resetCurrentMenu(),t.destroy()}),n=mxUtils.getOffset(i),t.popup(n.x,n.y+i.offsetHeight,null,e),this.editorUi.setCurrentMenu(t,i)),mxEvent.consume(e)}),mxEvent.addListener(i,"mousemove",mxUtils.bind(this,function(e){null!=this.editorUi.currentMenu&&this.editorUi.currentMenuElt!=i&&(this.editorUi.hideCurrentMenu(),t(e))})),mxEvent.addListener(i,mxClient.IS_POINTER?"pointerdown":"mousedown",mxUtils.bind(this,function(e){o=this.currentElt!=i,e.preventDefault()})),mxEvent.addListener(i,"click",mxUtils.bind(this,function(e){t(e),o=!0})))},Menubar.prototype.destroy=function(){},mxUtils.extend(Menu,mxEventSource),Menu.prototype.isEnabled=function(){return this.enabled},Menu.prototype.setEnabled=function(e){this.enabled!=e&&(this.enabled=e,this.fireEvent(new mxEventObject("stateChanged")))},Menu.prototype.execute=function(e,t){this.funct(e,t)},EditorUi.prototype.createMenus=function(){return new Menus(this)};