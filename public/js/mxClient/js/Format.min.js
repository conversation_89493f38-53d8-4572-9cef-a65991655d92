(Format=function(t,e,n){this.editorUi=t,this.container=e,this.isCustom=n}).prototype.labelIndex=0,Format.prototype.currentIndex=0,Format.prototype.showCloseButton=!0,Format.prototype.inactiveTabBackgroundColor="#f1f3f4",Format.prototype.roundableShapes=["label","rectangle","internalStorage","corner","parallelogram","swimlane","triangle","trapezoid","ext","step","tee","process","link","rhombus","offPageConnector","loopLimit","hexagon","manualInput","curlyBracket","singleArrow","callout","doubleArrow","flexArrow","card","umlLifeline"],Format.prototype.init=function(){var t=this.editorUi.editor,e=t.graph;this.update=mxUtils.bind(this,function(t,e){this.clearSelectionState(),this.refresh()}),e.getSelectionModel().addListener(mxEvent.CHANGE,this.update),e.addListener(mxEvent.EDITING_STARTED,this.update),e.addListener(mxEvent.EDITING_STOPPED,this.update),e.getModel().addListener(mxEvent.CHANGE,this.update),e.addListener(mxEvent.ROOT,mxUtils.bind(this,function(){this.refresh()})),t.addListener("autosaveChanged",mxUtils.bind(this,function(){this.refresh()})),this.refresh()},Format.prototype.clearSelectionState=function(){this.selectionState=null},Format.prototype.getSelectionState=function(){return null==this.selectionState&&(this.selectionState=this.createSelectionState()),this.selectionState},Format.prototype.createSelectionState=function(){for(var t=this.editorUi.editor.graph.getSelectionCells(),e=this.initSelectionState(),n=0;n<t.length;n++)this.updateSelectionStateForCell(e,t[n],t);return e},Format.prototype.initSelectionState=function(){return{vertices:[],edges:[],x:null,y:null,width:null,height:null,style:{},containsImage:!1,containsLabel:!1,fill:!0,glass:!0,rounded:!0,comic:!0,autoSize:!1,image:!0,shadow:!0,lineJumps:!0,aspect:!1}},Format.prototype.updateSelectionStateForCell=function(t,e,n){var i,s=this.editorUi.editor.graph,l=(s.getModel().isVertex(e)?(t.vertices.push(e),null!=(i=s.getCellGeometry(e))&&(0<i.width?null==t.width?t.width=i.width:t.width!=i.width&&(t.width=""):t.containsLabel=!0,0<i.height?null==t.height?t.height=i.height:t.height!=i.height&&(t.height=""):t.containsLabel=!0,i.relative&&null==i.offset||(a=(i.relative?i.offset:i).x,i=(i.relative?i.offset:i).y,null==t.x?t.x=a:t.x!=a&&(t.x=""),null==t.y?t.y=i:t.y!=i&&(t.y="")))):s.getModel().isEdge(e)&&t.edges.push(e),s.view.getState(e));if(null!=l){t.autoSize=t.autoSize||this.isAutoSizeState(l),t.glass=t.glass&&this.isGlassState(l),t.rounded=t.rounded&&this.isRoundedState(l),t.lineJumps=t.lineJumps&&this.isLineJumpState(l),t.comic=t.comic&&this.isComicState(l),t.image=t.image&&this.isImageState(l),t.shadow=t.shadow&&this.isShadowState(l),t.fill=t.fill&&this.isFillState(l);var o,a=mxUtils.getValue(l.style,mxConstants.STYLE_SHAPE,null);for(o in t.containsImage=t.containsImage||"image"==a,l.style){var r=l.style[o];null!=r&&(null==t.style[o]?t.style[o]=r:t.style[o]!=r&&(t.style[o]=""))}}},Format.prototype.isFillState=function(t){return t.view.graph.model.isVertex(t.cell)||"arrow"==mxUtils.getValue(t.style,mxConstants.STYLE_SHAPE,null)||"filledEdge"==mxUtils.getValue(t.style,mxConstants.STYLE_SHAPE,null)||"flexArrow"==mxUtils.getValue(t.style,mxConstants.STYLE_SHAPE,null)},Format.prototype.isGlassState=function(t){t=mxUtils.getValue(t.style,mxConstants.STYLE_SHAPE,null);return"label"==t||"rectangle"==t||"internalStorage"==t||"ext"==t||"umlLifeline"==t||"swimlane"==t||"process"==t},Format.prototype.isRoundedState=function(t){return void 0!==t.sceneComponent_rounded?t.sceneComponent_rounded:null!=t.shape?t.shape.isRoundable():0<=mxUtils.indexOf(this.roundableShapes,mxUtils.getValue(t.style,mxConstants.STYLE_SHAPE,null))},Format.prototype.isLineJumpState=function(t){var e=mxUtils.getValue(t.style,mxConstants.STYLE_SHAPE,null);return!mxUtils.getValue(t.style,mxConstants.STYLE_CURVED,!1)&&("connector"==e||"filledEdge"==e)},Format.prototype.isComicState=function(t){t=mxUtils.getValue(t.style,mxConstants.STYLE_SHAPE,null);return 0<=mxUtils.indexOf(["label","rectangle","internalStorage","corner","parallelogram","note","collate","swimlane","triangle","trapezoid","ext","step","tee","process","link","rhombus","offPageConnector","loopLimit","hexagon","manualInput","singleArrow","doubleArrow","flexArrow","filledEdge","card","umlLifeline","connector","folder","component","sortShape","cross","umlFrame","cube","isoCube","isoRectangle","partialRectangle"],t)},Format.prototype.isAutoSizeState=function(t){return"1"==mxUtils.getValue(t.style,mxConstants.STYLE_AUTOSIZE,null)},Format.prototype.isImageState=function(t){t=mxUtils.getValue(t.style,mxConstants.STYLE_SHAPE,null);return"label"==t||"image"==t},Format.prototype.isShadowState=function(t){return"image"!=mxUtils.getValue(t.style,mxConstants.STYLE_SHAPE,null)},Format.prototype.clear=function(){if(this.container.innerHTML="",null!=this.panels)for(var t=0;t<this.panels.length;t++)this.panels[t].destroy();this.panels=[]},Format.prototype.refresh=function(){var t,e,s,l,o,n,i,a,r,d,u;"0px"!=this.container.style.width&&(this.clear(),i=(t=this.editorUi).editor.graph,(e=document.createElement("div")).style.whiteSpace="nowrap",e.style.color="rgb(112, 112, 112)",e.style.textAlign="left",e.style.cursor="default",(d=document.createElement("div")).className="geFormatSection",d.style.textAlign="center",d.style.fontWeight="bold",d.style.paddingTop="8px",d.style.fontSize="13px",d.style.borderWidth="0px 0px 1px 1px",d.style.borderStyle="solid",d.style.display=mxClient.IS_QUIRKS?"inline":"inline-block",d.style.height=mxClient.IS_QUIRKS?"34px":"25px",d.style.overflow="hidden",d.style.width="100%",this.container.appendChild(e),mxEvent.addListener(d,mxClient.IS_POINTER?"pointerdown":"mousedown",mxUtils.bind(this,function(t){t.preventDefault()})),i.isSelectionEmpty()?(mxUtils.write(d,mxResources.get("diagram")),d.style.borderLeftWidth="0px",this.showCloseButton&&((n=document.createElement("img")).setAttribute("border","0"),n.setAttribute("src",Dialog.prototype.closeImage),n.setAttribute("title",mxResources.get("hide")),n.style.position="absolute",n.style.display="block",n.style.right="0px",n.style.top="8px",n.style.cursor="pointer",n.style.marginTop="1px",n.style.marginRight="17px",n.style.border="1px solid transparent",n.style.padding="1px",n.style.opacity=.5,this.isCustom&&(n.style.display="none"),d.appendChild(n),mxEvent.addListener(n,"click",function(){t.actions.get("formatPanel").funct()})),e.appendChild(d),this.panels.push(new DiagramFormatPanel(this,t,e))):i.isEditing()?(mxUtils.write(d,mxResources.get("text")),e.appendChild(d),this.panels.push(new TextFormatPanel(this,t,e))):(s=this.getSelectionState().containsLabel,o=l=null,n=mxUtils.bind(this,function(e,n,i){var t=mxUtils.bind(this,function(t){l!=e&&(s?this.labelIndex=i:this.currentIndex=i,null!=l&&(l.style.backgroundColor=this.inactiveTabBackgroundColor,l.style.borderBottomWidth="1px"),(l=e).style.backgroundColor="",l.style.borderBottomWidth="0px",o!=n&&(null!=o&&(o.style.display="none"),(o=n).style.display=""))});mxEvent.addListener(e,"click",t),mxEvent.addListener(e,mxClient.IS_POINTER?"pointerdown":"mousedown",mxUtils.bind(this,function(t){t.preventDefault()})),i==(s?this.labelIndex:this.currentIndex)&&t()}),i=0,d.style.backgroundColor=this.inactiveTabBackgroundColor,d.style.borderLeftWidth="1px",d.style.cursor="pointer",d.style.width=s?"50%":"33.3%",d.style.width=s?"50%":"33.3%",r=(a=d.cloneNode(!1)).cloneNode(!1),a.style.backgroundColor=this.inactiveTabBackgroundColor,r.style.backgroundColor=this.inactiveTabBackgroundColor,s?a.style.borderLeftWidth="0px":(d.style.borderLeftWidth="0px",mxUtils.write(d,mxResources.get("style")),e.appendChild(d),(u=e.cloneNode(!1)).style.display="none",this.panels.push(new StyleFormatPanel(this,t,u)),this.container.appendChild(u),n(d,u,i++)),mxUtils.write(a,mxResources.get("text")),e.appendChild(a),(d=e.cloneNode(!1)).style.display="none",this.panels.push(new TextFormatPanel(this,t,d)),this.container.appendChild(d),mxUtils.write(r,mxResources.get("arrange")),e.appendChild(r),(u=e.cloneNode(!1)).style.display="none",this.panels.push(new ArrangePanel(this,t,u)),this.container.appendChild(u),n(a,d,i++),n(r,u,i++)))},(BaseFormatPanel=function(t,e,n){this.format=t,this.editorUi=e,this.container=n,this.listeners=[]}).prototype.buttonBackgroundColor="white",BaseFormatPanel.prototype.getSelectionState=function(){for(var t=this.editorUi.editor.graph,e=t.getSelectionCells(),n=null,i=0;i<e.length;i++){var s=t.view.getState(e[i]);if(null!=s){s=mxUtils.getValue(s.style,mxConstants.STYLE_SHAPE,null);if(null!=s)if(null==n)n=s;else if(n!=s)return null}}return n},BaseFormatPanel.prototype.installInputHandler=function(l,o,a,r,d,u,m,p){u=null!=u?u:"",p=null!=p&&p;var c=this.editorUi,g=c.editor.graph,h=(r=null!=r?r:1,d=null!=d?d:999,null),x=!1,t=mxUtils.bind(this,function(t){var e=l.value,n=(g&&g.sceneComponent&&(e=t,"rotation"===o?(e=360*t,g.sceneComponentLock=!0):"perimeterSpacing"===o&&(e=10*t)),(p?parseFloat:parseInt)(e));if(isNaN(n)||o!=mxConstants.STYLE_ROTATION||(n=mxUtils.mod(Math.round(100*n),36e3)/100),n=Math.min(d,Math.max(r,isNaN(n)?a:n)),!g.cellEditor.isContentEditing()||!m||g&&g.sceneComponent&&"fontSize"===o){if(n!=mxUtils.getValue(this.format.getSelectionState().style,o,a)){g.isEditing()&&g.stopEditing(!0),g.getModel().beginUpdate();try{var i=g.getSelectionCells();g.setCellStyles(o,n,i),o==mxConstants.STYLE_FONTSIZE&&g.updateLabelElements(g.getSelectionCells(),function(t){t.style.fontSize=n+"px",t.removeAttribute("size")});for(var s=0;s<i.length;s++)0==g.model.getChildCount(i[s])&&g.autoSizeCell(i[s],!1);c.fireEvent(new mxEventObject("styleChanged","keys",[o],"values",[n],"cells",i))}finally{g.getModel().endUpdate(),g.sceneComponentLock=!1}}}else x||(x=!0,null!=h&&(g.cellEditor.restoreSelection(h),h=null),m(n),l.value=n+u,x=!1);l.value=n+u,mxEvent.consume(t)});return m&&g.cellEditor.isContentEditing()&&(mxEvent.addListener(l,"mousedown",function(){document.activeElement==g.cellEditor.textarea&&(h=g.cellEditor.saveSelection())}),mxEvent.addListener(l,"touchstart",function(){document.activeElement==g.cellEditor.textarea&&(h=g.cellEditor.saveSelection())})),mxEvent.addListener(l,"change",t),mxEvent.addListener(l,"blur",t),t},BaseFormatPanel.prototype.createPanel=function(){var t=document.createElement("div");return t.className="geFormatSection",t.style.padding="12px 0px 12px 18px",t},BaseFormatPanel.prototype.createTitle=function(t){var e=document.createElement("div");return e.style.padding="0px 0px 6px 0px",e.style.whiteSpace="nowrap",e.style.overflow="hidden",e.style.width="200px",e.style.fontWeight="bold",mxUtils.write(e,t),e},BaseFormatPanel.prototype.createStepper=function(n,i,s,t,e,l,o){s=null!=s?s:1,t=null!=t?t:8,mxClient.IS_QUIRKS?t-=2:(mxClient.IS_MT||8<=document.documentMode)&&(t+=1);var a,r=document.createElement("div"),d=(mxUtils.setPrefixedStyle(r.style,"borderRadius","3px"),r.style.border="1px solid rgb(192, 192, 192)",r.style.position="absolute",document.createElement("div")),u=(d.style.borderBottom="1px solid rgb(192, 192, 192)",d.style.position="relative",d.style.height=t+"px",d.style.width="10px",d.className="geBtnUp",r.appendChild(d),d.cloneNode(!1));return u.style.border="none",u.style.height=t+"px",u.className="geBtnDown",r.appendChild(u),mxEvent.addListener(u,"click",function(t){""==n.value&&(n.value=l||"2");var e=(o?parseFloat:parseInt)(n.value);isNaN(e)||(n.value=e-s,null!=i&&i(t)),mxEvent.consume(t)}),mxEvent.addListener(d,"click",function(t){""==n.value&&(n.value=l||"0");var e=(o?parseFloat:parseInt)(n.value);isNaN(e)||(n.value=e+s,null!=i&&i(t)),mxEvent.consume(t)}),e&&(a=null,mxEvent.addGestureListeners(r,function(t){!mxClient.IS_QUIRKS&&8!=document.documentMode||(a=document.selection.createRange()),mxEvent.consume(t)},null,function(t){if(null!=a){try{a.select()}catch(t){}a=null,mxEvent.consume(t)}})),r},BaseFormatPanel.prototype.createOption=function(t,e,n,i,s){var l=document.createElement("div"),o=(l.style.padding="6px 0px 1px 0px",l.style.whiteSpace="nowrap",l.style.overflow="hidden",l.style.width="200px",l.style.height=mxClient.IS_QUIRKS?"27px":"18px",document.createElement("input")),a=(o.setAttribute("type","checkbox"),o.style.margin="0px 6px 0px 0px",l.appendChild(o),document.createElement("span")),r=(mxUtils.write(a,t),l.appendChild(a),!1),d=e(),u=(this.editorUi&&this.editorUi.editor&&this.editorUi.editor.graph&&this.editorUi.editor.graph,mxUtils.bind(this,function(t){r||(r=!0,t?(o.setAttribute("checked","checked"),o.defaultChecked=!0,o.checked=!0):(o.removeAttribute("checked"),o.defaultChecked=!1,o.checked=!1),d!=t&&(d=t,e()!=d&&n(d)),r=!1)}));return mxEvent.addListener(l,"click",function(t){"disabled"!=o.getAttribute("disabled")&&((t=mxEvent.getSource(t))!=l&&t!=a||(o.checked=!o.checked),u(o.checked))}),u(d),null!=i&&(i.install(u),this.listeners.push(i)),l},BaseFormatPanel.prototype.createCellOption=function(t,n,i,s,l,o,a,r){s=null!=s?"null"==s?null:s:"1",l=null!=l?"null"==l?null:l:"0";var d=this.editorUi,u=d.editor.graph;return this.createOption(t,function(){var t=u.view.getState(u.getSelectionCell());return null!=t?mxUtils.getValue(t.style,n,i)!=l:null},function(t){if(r&&u.stopEditing(),null!=a)a.funct();else{u.getModel().beginUpdate();try{var e=t?s:l;"rounded"===n&&u.sceneComponent&&(e=t),u.setCellStyles(n,e,u.getSelectionCells()),null!=o&&o(u.getSelectionCells(),e),d.fireEvent(new mxEventObject("styleChanged","keys",[n],"values",[e],"cells",u.getSelectionCells()))}finally{u.getModel().endUpdate()}}},{install:function(e){this.listener=function(){var t=u.view.getState(u.getSelectionCell());null!=t&&e(mxUtils.getValue(t.style,n,i)!=l)},u.getModel().addListener(mxEvent.CHANGE,this.listener)},destroy:function(){u.getModel().removeListener(this.listener)}},n)},BaseFormatPanel.prototype.createColorOption=function(t,i,s,l,e,o,a,n){function r(t,e,n){c||(c=!0,t=/(^#?[a-zA-Z0-9]*$)/.test(t)?t:l,g.innerHTML='<div style="width:'+(mxClient.IS_QUIRKS?"30":"36")+"px;height:12px;margin:3px;border:1px solid black;background-color:"+mxUtils.htmlEntities(null!=t&&t!=mxConstants.NONE?t:l)+';"></div>',!mxClient.IS_QUIRKS&&8!=document.documentMode||(g.firstChild.style.margin="0px"),null!=t&&t!=mxConstants.NONE?(u.setAttribute("checked","checked"),u.defaultChecked=!0,u.checked=!0):(u.removeAttribute("checked"),u.defaultChecked=!1,u.checked=!1),g.style.display=u.checked||a?"":"none",null!=o&&o(t),e||(p=t,(n||a||i()!=p&&"var(--primary)"!==i())&&s(p)),c=!1)}var d=document.createElement("div"),u=(d.style.padding="6px 0px 1px 0px",d.style.whiteSpace="nowrap",d.style.overflow="hidden",d.style.width="200px",d.style.height=mxClient.IS_QUIRKS?"27px":"18px",document.createElement("input")),m=(u.setAttribute("type","checkbox"),u.style.margin="0px 6px 0px 0px",a||d.appendChild(u),document.createElement("span")),p=(mxUtils.write(m,t),d.appendChild(m),i()),c=!1,g=null;return(g=mxUtils.button("",mxUtils.bind(this,function(t){this.editorUi.pickColor(p,function(t){r(t,null,!0)}),mxEvent.consume(t)}))).style.position="absolute",g.style.marginTop="-4px",g.style.right=mxClient.IS_QUIRKS?"0px":"20px",g.style.height="22px",g.className="geColorBtn",g.style.display=u.checked||a?"":"none",d.appendChild(g),mxEvent.addListener(d,"click",function(t){t=mxEvent.getSource(t);t!=u&&"INPUT"==t.nodeName||(t!=u&&(u.checked=!u.checked),u.checked||null==p||p==mxConstants.NONE||l==mxConstants.NONE||(l=p),r(u.checked?l:mxConstants.NONE))}),r(p,!0),null!=e&&(e.install(r),this.listeners.push(e)),d},BaseFormatPanel.prototype.createCellColorOption=function(t,n,e,i,s){var l=this.editorUi,o=l.editor.graph;return this.createColorOption(t,function(){var t=o.view.getState(o.getSelectionCell());return null!=t?mxUtils.getValue(t.style,n,null):null},function(t){o.getModel().beginUpdate();try{null!=s&&s(t),o.setCellStyles(n,t,o.getSelectionCells()),l.fireEvent(new mxEventObject("styleChanged","keys",[n],"values",[t],"cells",o.getSelectionCells()))}finally{o.getModel().endUpdate()}},e||mxConstants.NONE,{install:function(e){this.listener=function(){var t=o.view.getState(o.getSelectionCell());null!=t&&e(mxUtils.getValue(t.style,n,null))},o.getModel().addListener(mxEvent.CHANGE,this.listener)},destroy:function(){o.getModel().removeListener(this.listener)}},i,void 0,{graph:o,key:n})},BaseFormatPanel.prototype.addArrow=function(t,e){e=null!=e?e:10;var n=document.createElement("div"),i=(n.style.display=mxClient.IS_QUIRKS?"inline":"inline-block",n.style.padding="6px",n.style.paddingRight="4px",10-e),i=(2==i?n.style.paddingTop="6px":0<i?n.style.paddingTop=6-i+"px":n.style.marginTop="-2px",n.style.height=e+"px",n.style.borderLeft="1px solid #a0a0a0",n.innerHTML='<img border="0" src="'+(mxClient.IS_SVG?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAHBJREFUeNpidHB2ZyAGsACxDRBPIKCuA6TwCBB/h2rABu4A8SYmKCcXiP/iUFgAxL9gCi8A8SwsirZCMQMTkmANEH9E4v+CmsaArvAdyNFI/FlQ92EoBIE+qCRIUz168DBgsU4OqhinQpgHMABAgAEALY4XLIsJ20oAAAAASUVORK5CYII=":IMAGE_PATH+"/dropdown.png")+'" style="margin-bottom:4px;">',mxUtils.setOpacity(n,70),t.getElementsByTagName("div")[0]);return null!=i&&(i.style.paddingRight="6px",i.style.marginLeft="4px",i.style.marginTop="-1px",i.style.display=mxClient.IS_QUIRKS?"inline":"inline-block",mxUtils.setOpacity(i,60)),mxUtils.setOpacity(t,100),t.style.border="1px solid #a0a0a0",t.style.backgroundColor=this.buttonBackgroundColor,t.style.backgroundImage="none",t.style.width="auto",t.className+=" geColorBtn",mxUtils.setPrefixedStyle(t.style,"borderRadius","3px"),t.appendChild(n),i};var index=0;BaseFormatPanel.prototype.addUnitInput=function(t,e,n,i,s,l,o,a,r){o=null!=o?o:0;var d=document.createElement("input"),i=(d.style.position="absolute",d.style.textAlign="right",d.style.marginTop="-2px",d.style.right=n+12+"px",d.style.width=i+"px",d.setAttribute("index",index),t.appendChild(d),this.createStepper(d,s,l,null,a,null,r));return i.style.marginTop=o-2+"px",i.style.right=n+"px",t.appendChild(i),d},BaseFormatPanel.prototype.createRelativeOption=function(t,i,e,s,n){e=null!=e?e:44;var l,o=this.editorUi.editor.graph,a=this.createPanel(),t=(a.style.paddingTop="10px",a.style.paddingBottom="10px",mxUtils.write(a,t),a.style.fontWeight="bold",mxUtils.bind(this,function(t){var e,n;null!=s?s(r):(e=parseInt(o.sceneComponent?100*t:r.value),e=Math.min(100,Math.max(0,isNaN(e)?100:e)),null!=(n=o.view.getState(o.getSelectionCell()))&&e!=mxUtils.getValue(n.style,i,100)&&(o.setCellStyles(i,e=100==e?null:e,o.getSelectionCells()),this.editorUi.fireEvent(new mxEventObject("styleChanged","keys",[i],"values",[e],"cells",o.getSelectionCells()))),o.sceneComponent||(r.value=(null!=e?e:"100")+" %")),o.sceneComponent||mxEvent.consume(t)})),r=this.addUnitInput(a,"%",20,e,t,10,-15,null!=s);return null!=i&&(l=mxUtils.bind(this,function(t,e,n){if(n||r!=document.activeElement)return n=this.format.getSelectionState(),n=parseInt(mxUtils.getValue(n.style,i,100)),r.value=isNaN(n)?"":n+" %",n}),mxEvent.addListener(r,"keydown",function(t){13==t.keyCode?(o.container.focus(),mxEvent.consume(t)):27==t.keyCode&&(l(null,null,!0),o.container.focus(),mxEvent.consume(t))}),o.getModel().addListener(mxEvent.CHANGE,l),this.listeners.push({destroy:function(){o.getModel().removeListener(l)}}),l()),mxEvent.addListener(r,"blur",t),mxEvent.addListener(r,"change",t),null!=n&&n(r),a},BaseFormatPanel.prototype.addLabel=function(t,e,n,i){i=null!=i?i:61;var s=document.createElement("div");mxUtils.write(s,e),s.style.position="absolute",s.style.right=n+"px",s.style.width=i+"px",s.style.marginTop="6px",s.style.textAlign="center",t.appendChild(s)},BaseFormatPanel.prototype.addKeyHandler=function(t,e){mxEvent.addListener(t,"keydown",mxUtils.bind(this,function(t){13==t.keyCode?(this.editorUi.editor.graph.container.focus(),mxEvent.consume(t)):27==t.keyCode&&(null!=e&&e(null,null,!0),this.editorUi.editor.graph.container.focus(),mxEvent.consume(t))}))},BaseFormatPanel.prototype.styleButtons=function(t){for(var e=0;e<t.length;e++)mxUtils.setPrefixedStyle(t[e].style,"borderRadius","3px"),mxUtils.setOpacity(t[e],100),t[e].style.border="1px solid #a0a0a0",t[e].style.padding="4px",t[e].style.paddingTop="3px",t[e].style.paddingRight="1px",t[e].style.margin="1px",t[e].style.width="24px",t[e].style.height="20px",t[e].className+=" geColorBtn"},BaseFormatPanel.prototype.destroy=function(){if(null!=this.listeners){for(var t=0;t<this.listeners.length;t++)this.listeners[t].destroy();this.listeners=null}},ArrangePanel=function(t,e,n){BaseFormatPanel.call(this,t,e,n),this.init()},mxUtils.extend(ArrangePanel,BaseFormatPanel),ArrangePanel.prototype.init=function(){this.editorUi.editor.graph;var t=this.format.getSelectionState();this.container.appendChild(this.addLayerOps(this.createPanel())),this.addGeometry(this.container),this.addEdgeGeometry(this.container),t.containsLabel&&0!=t.edges.length||this.container.appendChild(this.addAngle(this.createPanel())),t.containsLabel||0!=t.edges.length||this.container.appendChild(this.addFlip(this.createPanel())),1<t.vertices.length&&(this.container.appendChild(this.addAlign(this.createPanel())),this.container.appendChild(this.addDistribute(this.createPanel()))),this.container.appendChild(this.addGroupOps(this.createPanel())),t.containsLabel&&((t=document.createElement("div")).style.width="100%",t.style.marginTop="0px",t.style.fontWeight="bold",t.style.padding="10px 0 0 18px",mxUtils.write(t,mxResources.get("style")),this.container.appendChild(t),new StyleFormatPanel(this.format,this.editorUi,this.container))},ArrangePanel.prototype.addLayerOps=function(t){var e=this.editorUi,n=mxUtils.button(mxResources.get("toFront"),function(t){e.actions.get("toFront").funct()});return n.setAttribute("title",mxResources.get("toFront")+" ("+this.editorUi.actions.get("toFront").shortcut+")"),n.style.width="100px",n.style.marginRight="2px",t.appendChild(n),(n=mxUtils.button(mxResources.get("toBack"),function(t){e.actions.get("toBack").funct()})).setAttribute("title",mxResources.get("toBack")+" ("+this.editorUi.actions.get("toBack").shortcut+")"),n.style.width="100px",t.appendChild(n),t},ArrangePanel.prototype.addGroupOps=function(t){var e=this.editorUi,n=e.editor.graph,i=n.getSelectionCell(),s=this.format.getSelectionState(),l=0,o=null;return t.style.paddingTop="8px",t.style.paddingBottom="6px",1<n.getSelectionCount()?((o=mxUtils.button(mxResources.get("group"),function(t){e.actions.get("group").funct()})).setAttribute("title",mxResources.get("group")+" ("+this.editorUi.actions.get("group").shortcut+")"),o.style.width="202px",o.style.marginBottom="2px",t.appendChild(o),l++):1==n.getSelectionCount()&&!n.getModel().isEdge(i)&&!n.isSwimlane(i)&&0<n.getModel().getChildCount(i)&&((o=mxUtils.button(mxResources.get("ungroup"),function(t){e.actions.get("ungroup").funct()})).setAttribute("title",mxResources.get("ungroup")+" ("+this.editorUi.actions.get("ungroup").shortcut+")"),o.style.width="202px",o.style.marginBottom="2px",t.appendChild(o),l++),0<s.vertices.length&&(0<l&&(mxUtils.br(t),l=0),(o=mxUtils.button(mxResources.get("copySize"),function(t){e.actions.get("copySize").funct()})).setAttribute("title",mxResources.get("copySize")+" ("+this.editorUi.actions.get("copySize").shortcut+")"),o.style.width="202px",o.style.marginBottom="2px",t.appendChild(o),l++,null!=e.copiedSize&&((s=mxUtils.button(mxResources.get("pasteSize"),function(t){e.actions.get("pasteSize").funct()})).setAttribute("title",mxResources.get("pasteSize")+" ("+this.editorUi.actions.get("pasteSize").shortcut+")"),t.appendChild(s),l++,o.style.width="100px",o.style.marginBottom="2px",s.style.width="100px",s.style.marginBottom="2px")),1==n.getSelectionCount()&&n.getModel().isVertex(i)&&n.getModel().isVertex(n.getModel().getParent(i))?(0<l&&mxUtils.br(t),(o=mxUtils.button(mxResources.get("removeFromGroup"),function(t){e.actions.get("removeFromGroup").funct()})).setAttribute("title",mxResources.get("removeFromGroup")),o.style.width="202px",o.style.marginBottom="2px",t.appendChild(o),l++):0<n.getSelectionCount()&&(0<l&&mxUtils.br(t),(o=mxUtils.button(mxResources.get("clearWaypoints"),mxUtils.bind(this,function(t){this.editorUi.actions.get("clearWaypoints").funct()}))).setAttribute("title",mxResources.get("clearWaypoints")+" ("+this.editorUi.actions.get("clearWaypoints").shortcut+")"),o.style.width="202px",o.style.marginBottom="2px",t.appendChild(o),l++),1==n.getSelectionCount()&&(0<l&&mxUtils.br(t),(o=mxUtils.button(mxResources.get("editData"),mxUtils.bind(this,function(t){this.editorUi.actions.get("editData").funct()}))).setAttribute("title",mxResources.get("editData")+" ("+this.editorUi.actions.get("editData").shortcut+")"),o.style.width="100px",o.style.marginBottom="2px",t.appendChild(o),l++,(o=mxUtils.button(mxResources.get("editLink"),mxUtils.bind(this,function(t){this.editorUi.actions.get("editLink").funct()}))).setAttribute("title",mxResources.get("editLink")),o.style.width="100px",o.style.marginLeft="2px",o.style.marginBottom="2px",t.appendChild(o),l++),0==l&&(t.style.display="none"),t},ArrangePanel.prototype.addAlign=function(t){var e=this.editorUi.editor.graph,n=(t.style.paddingTop="6px",t.style.paddingBottom="12px",t.appendChild(this.createTitle(mxResources.get("align"))),document.createElement("div")),i=(n.style.position="relative",n.style.paddingLeft="0px",n.style.borderWidth="0px",n.className="geToolbarContainer",mxClient.IS_QUIRKS&&(t.style.height="60px"),this.editorUi.toolbar.addButton("geSprite-alignleft",mxResources.get("left"),function(){e.alignCells(mxConstants.ALIGN_LEFT)},n)),s=this.editorUi.toolbar.addButton("geSprite-aligncenter",mxResources.get("center"),function(){e.alignCells(mxConstants.ALIGN_CENTER)},n),l=this.editorUi.toolbar.addButton("geSprite-alignright",mxResources.get("right"),function(){e.alignCells(mxConstants.ALIGN_RIGHT)},n),o=this.editorUi.toolbar.addButton("geSprite-aligntop",mxResources.get("top"),function(){e.alignCells(mxConstants.ALIGN_TOP)},n),a=this.editorUi.toolbar.addButton("geSprite-alignmiddle",mxResources.get("middle"),function(){e.alignCells(mxConstants.ALIGN_MIDDLE)},n),r=this.editorUi.toolbar.addButton("geSprite-alignbottom",mxResources.get("bottom"),function(){e.alignCells(mxConstants.ALIGN_BOTTOM)},n);return this.styleButtons([i,s,l,o,a,r]),l.style.marginRight="6px",t.appendChild(n),t},ArrangePanel.prototype.addFlip=function(t){var e=this.editorUi.editor.graph,n=(t.style.paddingTop="6px",t.style.paddingBottom="10px",document.createElement("div")),n=(n.style.marginTop="2px",n.style.marginBottom="8px",n.style.fontWeight="bold",mxUtils.write(n,mxResources.get("flip")),t.appendChild(n),mxUtils.button(mxResources.get("horizontal"),function(t){e.toggleCellStyles(mxConstants.STYLE_FLIPH,!1)}));return n.setAttribute("title",mxResources.get("horizontal")),n.style.width="100px",n.style.marginRight="2px",t.appendChild(n),(n=mxUtils.button(mxResources.get("vertical"),function(t){e.toggleCellStyles(mxConstants.STYLE_FLIPV,!1)})).setAttribute("title",mxResources.get("vertical")),n.style.width="100px",t.appendChild(n),t},ArrangePanel.prototype.addDistribute=function(t){var e=this.editorUi.editor.graph,n=(t.style.paddingTop="6px",t.style.paddingBottom="12px",t.appendChild(this.createTitle(mxResources.get("distribute"))),mxUtils.button(mxResources.get("horizontal"),function(t){e.distributeCells(!0)}));return n.setAttribute("title",mxResources.get("horizontal")),n.style.width="100px",n.style.marginRight="2px",t.appendChild(n),(n=mxUtils.button(mxResources.get("vertical"),function(t){e.distributeCells(!1)})).setAttribute("title",mxResources.get("vertical")),n.style.width="100px",t.appendChild(n),t},ArrangePanel.prototype.addAngle=function(t){var e,n=this.editorUi,i=n.editor.graph,s=this.format.getSelectionState(),l=(t.style.paddingBottom="8px",document.createElement("div")),o=(l.style.position="absolute",l.style.width="70px",l.style.marginTop="0px",l.style.fontWeight="bold",null),a=null,r=null;return 0==s.edges.length?(mxUtils.write(l,mxResources.get("angle")),t.appendChild(l),o=this.addUnitInput(t,"°",20,44,function(){a.apply(this,arguments)}),mxUtils.br(t),t.style.paddingTop="10px"):t.style.paddingTop="8px",s.containsLabel||(l=mxResources.get("reverse"),0<s.vertices.length&&0<s.edges.length?l=mxResources.get("turn")+" / "+l:0<s.vertices.length&&(l=mxResources.get("turn")),(r=mxUtils.button(l,function(t){n.actions.get("turn").funct()})).setAttribute("title",l+" ("+this.editorUi.actions.get("turn").shortcut+")"),r.style.width="202px",t.appendChild(r),null!=o&&(r.style.marginTop="8px")),null!=o&&(e=mxUtils.bind(this,function(t,e,n){if(n||document.activeElement!=o)return s=this.format.getSelectionState(),n=parseFloat(mxUtils.getValue(s.style,mxConstants.STYLE_ROTATION,0)),o.value=isNaN(n)?"":n+"°",n}),a=this.installInputHandler(o,mxConstants.STYLE_ROTATION,0,0,360,"°",null,!0),this.addKeyHandler(o,e),i.getModel().addListener(mxEvent.CHANGE,e),this.listeners.push({destroy:function(){i.getModel().removeListener(e)}}),e()),t},BaseFormatPanel.prototype.getUnit=function(){switch(this.editorUi.editor.graph.view.unit){case mxConstants.POINTS:return"pt";case mxConstants.INCHES:return'"';case mxConstants.MILLIMETERS:return"mm"}},BaseFormatPanel.prototype.inUnit=function(t){return this.editorUi.editor.graph.view.formatUnitText(t)},BaseFormatPanel.prototype.fromUnit=function(t){switch(this.editorUi.editor.graph.view.unit){case mxConstants.POINTS:return t;case mxConstants.INCHES:return t*mxConstants.PIXELS_PER_INCH;case mxConstants.MILLIMETERS:return t*mxConstants.PIXELS_PER_MM}},BaseFormatPanel.prototype.isFloatUnit=function(){return this.editorUi.editor.graph.view.unit!=mxConstants.POINTS},BaseFormatPanel.prototype.getUnitStep=function(){switch(this.editorUi.editor.graph.view.unit){case mxConstants.POINTS:return 1;case mxConstants.INCHES:return.1;case mxConstants.MILLIMETERS:return.5}},ArrangePanel.prototype.addGeometry=function(t){var i,e,n,s,l,o=this,a=this.editorUi,r=a.editor.graph,d=(this.format.getSelectionState(),this.createPanel()),u=(d.style.paddingBottom="8px",document.createElement("div")),m=(u.style.position="absolute",u.style.width="50px",u.style.marginTop="0px",u.style.fontWeight="bold",mxUtils.write(u,mxResources.get("size")),d.appendChild(u),this.addUnitInput(d,this.getUnit(),84,44,function(){e.apply(this,arguments)},this.getUnitStep(),null,null,this.isFloatUnit())),p=this.addUnitInput(d,this.getUnit(),20,44,function(){n.apply(this,arguments)},this.getUnitStep(),null,null,this.isFloatUnit()),c=document.createElement("div"),g=(c.className="geSprite geSprite-fit",c.setAttribute("title",mxResources.get("autosize")+" ("+this.editorUi.actions.get("autosize").shortcut+")"),c.style.position="relative",c.style.cursor="pointer",c.style.marginTop="-3px",c.style.border="0px",c.style.left="52px",mxUtils.setOpacity(c,50),mxEvent.addListener(c,"mouseenter",function(){mxUtils.setOpacity(c,100)}),mxEvent.addListener(c,"mouseleave",function(){mxUtils.setOpacity(c,50)}),mxEvent.addListener(c,"click",function(){a.actions.get("autosize").funct()}),d.appendChild(c),this.addLabel(d,mxResources.get("width"),84),this.addLabel(d,mxResources.get("height"),20),mxUtils.br(d),document.createElement("div")),h=(g.style.paddingTop="8px",g.style.paddingRight="20px",g.style.whiteSpace="nowrap",g.style.textAlign="right",this.createCellOption(mxResources.get("constrainProportions"),mxConstants.STYLE_ASPECT,null,"fixed","null")),x=(h.style.width="100%",g.appendChild(h),d.appendChild(g),h.getElementsByTagName("input")[0]),y=(this.addKeyHandler(m,void 0),this.addKeyHandler(p,void 0),e=this.addGeometryHandler(m,function(t,e){0<t.width&&(e=Math.max(1,o.fromUnit(e)),x.checked&&(t.height=Math.round(t.height*e*100/t.width)/100),t.width=e)},r&&r.sceneComponent&&r.sceneComponent.configChange),n=this.addGeometryHandler(p,function(t,e){0<t.height&&(e=Math.max(1,o.fromUnit(e)),x.checked&&(t.width=Math.round(t.width*e*100/t.height)/100),t.height=e)},r&&r.sceneComponent&&r.sceneComponent.configChange),t.appendChild(d),this.createPanel());y.style.paddingBottom="30px";(u=document.createElement("div")).style.position="absolute",u.style.width="70px",u.style.marginTop="0px",u.style.fontWeight="bold",mxUtils.write(u,mxResources.get("position")),y.appendChild(u);var S=this.addUnitInput(y,this.getUnit(),84,44,function(){s.apply(this,arguments)},this.getUnitStep(),null,null,this.isFloatUnit()),C=this.addUnitInput(y,this.getUnit(),20,44,function(){l.apply(this,arguments)},this.getUnitStep(),null,null,this.isFloatUnit()),E=(mxUtils.br(y),this.addLabel(y,mxResources.get("left"),84),this.addLabel(y,mxResources.get("top"),20),mxUtils.bind(this,function(t,e,n){return(i=this.format.getSelectionState()).containsLabel||i.vertices.length!=r.getSelectionCount()||null==i.width||null==i.height?d.style.display="none":(d.style.display="",!n&&document.activeElement==m||(m.value=this.inUnit(i.width)+(""==i.width?"":" "+this.getUnit())),!n&&document.activeElement==p||(p.value=this.inUnit(i.height)+(""==i.height?"":" "+this.getUnit()))),i.vertices.length==r.getSelectionCount()&&null!=i.x&&null!=i.y?(y.style.display="",!n&&document.activeElement==S||(S.value=this.inUnit(i.x)+(""==i.x?"":" "+this.getUnit())),!n&&document.activeElement==C||(C.value=this.inUnit(i.y)+(""==i.y?"":" "+this.getUnit()))):y.style.display="none",{width:m.value,height:p.value}}));this.addKeyHandler(S,E),this.addKeyHandler(C,E),r.getModel().addListener(mxEvent.CHANGE,E),this.listeners.push({destroy:function(){r.getModel().removeListener(E)}}),E(),s=this.addGeometryHandler(S,function(t,e){e=o.fromUnit(e),t.relative?t.offset.x=e:t.x=e}),l=this.addGeometryHandler(C,function(t,e){e=o.fromUnit(e),t.relative?t.offset.y=e:t.y=e}),t.appendChild(y)},ArrangePanel.prototype.addGeometryHandler=function(l,o,a){var r=this.editorUi.editor.graph,d=null,u=this;function t(t){if(""!=l.value||a){var e=parseFloat(a?t:l.value);if(isNaN(e))l.value=d+" "+u.getUnit();else if(e!=d){r.getModel().beginUpdate();try{for(var n,i=r.getSelectionCells(),s=0;s<i.length;s++)!r.getModel().isVertex(i[s])||null!=(n=r.getCellGeometry(i[s]))&&(n=n.clone(),o(n,e),r.getModel().setGeometry(i[s],n))}finally{r.getModel().endUpdate()}d=e,l.value=e+" "+u.getUnit()}}mxEvent.consume(t)}return mxEvent.addListener(l,"blur",t),mxEvent.addListener(l,"change",t),mxEvent.addListener(l,"focus",function(){d=l.value}),t},ArrangePanel.prototype.addEdgeGeometryHandler=function(l,o){var a=this.editorUi.editor.graph,r=null;function t(t){if(""!=l.value){var e=parseFloat(l.value);if(isNaN(e))l.value=r+" pt";else if(e!=r){a.getModel().beginUpdate();try{for(var n,i=a.getSelectionCells(),s=0;s<i.length;s++)!a.getModel().isEdge(i[s])||null!=(n=a.getCellGeometry(i[s]))&&(n=n.clone(),o(n,e),a.getModel().setGeometry(i[s],n))}finally{a.getModel().endUpdate()}r=e,l.value=e+" pt"}}mxEvent.consume(t)}return mxEvent.addListener(l,"blur",t),mxEvent.addListener(l,"change",t),mxEvent.addListener(l,"focus",function(){r=l.value}),t},ArrangePanel.prototype.addEdgeGeometry=function(t){var e,n=this.editorUi,s=n.editor.graph,l=this.format.getSelectionState(),o=this.createPanel(),a=((i=document.createElement("div")).style.position="absolute",i.style.width="70px",i.style.marginTop="0px",i.style.fontWeight="bold",mxUtils.write(i,mxResources.get("width")),o.appendChild(i),this.addUnitInput(o,"pt",20,44,function(){e.apply(this,arguments)}));function e(t){var e=parseInt(a.value);(e=Math.min(999,Math.max(1,isNaN(e)?1:e)))!=mxUtils.getValue(l.style,"width",mxCellRenderer.defaultShapes.flexArrow.prototype.defaultWidth)&&(s.setCellStyles("width",e,s.getSelectionCells()),n.fireEvent(new mxEventObject("styleChanged","keys",["width"],"values",[e],"cells",s.getSelectionCells()))),a.value=e+" pt",mxEvent.consume(t)}mxUtils.br(o),this.addKeyHandler(a,g),mxEvent.addListener(a,"blur",e),mxEvent.addListener(a,"change",e),t.appendChild(o);var r=this.createPanel();r.style.paddingBottom="30px";(i=document.createElement("div")).style.position="absolute",i.style.width="70px",i.style.marginTop="0px",i.style.fontWeight="bold",mxUtils.write(i,"Start"),r.appendChild(i);var d=this.addUnitInput(r,"pt",84,44,function(){h.apply(this,arguments)}),u=this.addUnitInput(r,"pt",20,44,function(){x.apply(this,arguments)}),m=(mxUtils.br(r),this.addLabel(r,mxResources.get("left"),84),this.addLabel(r,mxResources.get("top"),20),t.appendChild(r),this.addKeyHandler(d,g),this.addKeyHandler(u,g),this.createPanel());m.style.paddingBottom="30px";(i=document.createElement("div")).style.position="absolute",i.style.width="70px",i.style.marginTop="0px",i.style.fontWeight="bold",mxUtils.write(i,"End"),m.appendChild(i);var i,p=this.addUnitInput(m,"pt",84,44,function(){y.apply(this,arguments)}),c=this.addUnitInput(m,"pt",20,44,function(){S.apply(this,arguments)}),g=(mxUtils.br(m),this.addLabel(m,mxResources.get("left"),84),this.addLabel(m,mxResources.get("top"),20),t.appendChild(m),this.addKeyHandler(p,g),this.addKeyHandler(c,g),mxUtils.bind(this,function(t,e,n){l=this.format.getSelectionState();var i=s.getSelectionCell();"link"==l.style.shape||"flexArrow"==l.style.shape?(o.style.display="",!n&&document.activeElement==a||(n=mxUtils.getValue(l.style,"width",mxCellRenderer.defaultShapes.flexArrow.prototype.defaultWidth),a.value=n+" pt")):o.style.display="none",1==s.getSelectionCount()&&s.model.isEdge(i)?(null!=(n=s.model.getGeometry(i)).sourcePoint&&null==s.model.getTerminal(i,!0)?(d.value=n.sourcePoint.x,u.value=n.sourcePoint.y):r.style.display="none",null!=n.targetPoint&&null==s.model.getTerminal(i,!1)?(p.value=n.targetPoint.x,c.value=n.targetPoint.y):m.style.display="none"):(r.style.display="none",m.style.display="none")})),h=this.addEdgeGeometryHandler(d,function(t,e){t.sourcePoint.x=e}),x=this.addEdgeGeometryHandler(u,function(t,e){t.sourcePoint.y=e}),y=this.addEdgeGeometryHandler(p,function(t,e){t.targetPoint.x=e}),S=this.addEdgeGeometryHandler(c,function(t,e){t.targetPoint.y=e});s.getModel().addListener(mxEvent.CHANGE,g),this.listeners.push({destroy:function(){s.getModel().removeListener(g)}}),g()},TextFormatPanel=function(t,e,n){BaseFormatPanel.call(this,t,e,n),this.init()},mxUtils.extend(TextFormatPanel,BaseFormatPanel),TextFormatPanel.prototype.init=function(){this.container.style.borderBottom="none",this.addFont(this.container)},TextFormatPanel.prototype.addFont=function(t){function e(t){return function(){return t()}}for(var F,Y,g,P,h,x,y,n=this.editorUi,S=n.editor.graph,C=this.format.getSelectionState(),i=this.createTitle(mxResources.get("font")),i=(i.style.paddingLeft="18px",i.style.paddingTop="10px",i.style.paddingBottom="6px",t.appendChild(i),this.createPanel()),s=(i.style.paddingTop="2px",i.style.paddingBottom="2px",i.style.position="relative",i.style.marginLeft="-2px",i.style.borderWidth="0px",i.className="geToolbarContainer",mxClient.IS_QUIRKS&&(i.style.display="block"),S.cellEditor.isContentEditing()&&(l=i.cloneNode(),(s=this.editorUi.toolbar.addMenu(mxResources.get("style"),mxResources.get("style"),!0,"formatBlock",l,null,!0)).style.color="rgb(112, 112, 112)",s.style.whiteSpace="nowrap",s.style.overflow="hidden",s.style.margin="0px",this.addArrow(s),s.style.width="192px",s.style.height="15px",s.getElementsByTagName("div")[0].style.cssFloat="right",t.appendChild(l)),t.appendChild(i),this.createPanel()),E=(s.style.marginTop="8px",s.style.borderTop="1px solid #c0c0c0",s.style.paddingTop="6px",s.style.paddingBottom="6px",this.editorUi.toolbar.addMenu("Helvetica",mxResources.get("fontFamily"),!0,"fontFamily",i,null,!0)),l=(E.style.color="rgb(112, 112, 112)",E.style.whiteSpace="nowrap",E.style.overflow="hidden",E.style.margin="0px",this.addArrow(E),E.style.width="192px",E.style.height="15px",i.cloneNode(!1)),f=(l.style.marginLeft="-3px",this.editorUi.toolbar.addItems(["bold","italic","underline"],l,!0)),B=(f[0].setAttribute("title",mxResources.get("bold")+" ("+this.editorUi.actions.get("bold").shortcut+")"),f[1].setAttribute("title",mxResources.get("italic")+" ("+this.editorUi.actions.get("italic").shortcut+")"),f[2].setAttribute("title",mxResources.get("underline")+" ("+this.editorUi.actions.get("underline").shortcut+")"),this.editorUi.toolbar.addItems(["vertical"],l,!0)[0]),o=(mxClient.IS_QUIRKS&&mxUtils.br(t),t.appendChild(l),this.styleButtons(f),this.styleButtons([B]),i.cloneNode(!1)),T=(o.style.marginLeft="-3px",o.style.paddingBottom="0px",this.editorUi.toolbar.addButton("geSprite-left",mxResources.get("left"),S.cellEditor.isContentEditing()?function(t){S.cellEditor.alignText(mxConstants.ALIGN_LEFT,t)}:e(this.editorUi.menus.createStyleChangeFunction([mxConstants.STYLE_ALIGN],[mxConstants.ALIGN_LEFT])),o)),L=this.editorUi.toolbar.addButton("geSprite-center",mxResources.get("center"),S.cellEditor.isContentEditing()?function(t){S.cellEditor.alignText(mxConstants.ALIGN_CENTER,t)}:e(this.editorUi.menus.createStyleChangeFunction([mxConstants.STYLE_ALIGN],[mxConstants.ALIGN_CENTER])),o),R=this.editorUi.toolbar.addButton("geSprite-right",mxResources.get("right"),S.cellEditor.isContentEditing()?function(t){S.cellEditor.alignText(mxConstants.ALIGN_RIGHT,t)}:e(this.editorUi.menus.createStyleChangeFunction([mxConstants.STYLE_ALIGN],[mxConstants.ALIGN_RIGHT])),o),G=(this.styleButtons([T,L,R]),S.cellEditor.isContentEditing()&&(d=this.editorUi.toolbar.addButton("geSprite-removeformat",mxResources.get("strikethrough"),function(){document.execCommand("strikeThrough",!1,null)},l),this.styleButtons([d]),d.firstChild.style.background="url(data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)",d.firstChild.style.backgroundPosition="2px 2px",d.firstChild.style.backgroundSize="18px 18px",this.styleButtons([d])),this.editorUi.toolbar.addButton("geSprite-top",mxResources.get("top"),e(this.editorUi.menus.createStyleChangeFunction([mxConstants.STYLE_VERTICAL_ALIGN],[mxConstants.ALIGN_TOP])),o)),H=this.editorUi.toolbar.addButton("geSprite-middle",mxResources.get("middle"),e(this.editorUi.menus.createStyleChangeFunction([mxConstants.STYLE_VERTICAL_ALIGN],[mxConstants.ALIGN_MIDDLE])),o),k=this.editorUi.toolbar.addButton("geSprite-bottom",mxResources.get("bottom"),e(this.editorUi.menus.createStyleChangeFunction([mxConstants.STYLE_VERTICAL_ALIGN],[mxConstants.ALIGN_BOTTOM])),o),o=(this.styleButtons([G,H,k]),mxClient.IS_QUIRKS&&mxUtils.br(t),t.appendChild(o),S.cellEditor.isContentEditing()?(G.style.display="none",H.style.display="none",k.style.display="none",B.style.display="none",(g=this.editorUi.toolbar.addButton("geSprite-justifyfull",mxResources.get("block"),function(){1==g.style.opacity&&document.execCommand("justifyfull",!1,null)},o)).style.marginRight="9px",g.style.opacity=1,this.styleButtons([g,F=this.editorUi.toolbar.addButton("geSprite-subscript",mxResources.get("subscript")+" ("+Editor.ctrlKey+"+,)",function(){document.execCommand("subscript",!1,null)},o),Y=this.editorUi.toolbar.addButton("geSprite-superscript",mxResources.get("superscript")+" ("+Editor.ctrlKey+"+.)",function(){document.execCommand("superscript",!1,null)},o)]),F.style.marginLeft="9px",(d=o.cloneNode(!1)).style.paddingTop="4px",p=[this.editorUi.toolbar.addButton("geSprite-orderedlist",mxResources.get("numberedList"),function(){document.execCommand("insertorderedlist",!1,null)},d),this.editorUi.toolbar.addButton("geSprite-unorderedlist",mxResources.get("bulletedList"),function(){document.execCommand("insertunorderedlist",!1,null)},d),this.editorUi.toolbar.addButton("geSprite-outdent",mxResources.get("decreaseIndent"),function(){document.execCommand("outdent",!1,null)},d),this.editorUi.toolbar.addButton("geSprite-indent",mxResources.get("increaseIndent"),function(){document.execCommand("indent",!1,null)},d),this.editorUi.toolbar.addButton("geSprite-removeformat",mxResources.get("removeFormat"),function(){document.execCommand("removeformat",!1,null)},d),this.editorUi.toolbar.addButton("geSprite-code",mxResources.get("html"),function(){S.cellEditor.toggleViewMode()},d)],this.styleButtons(p),p[p.length-2].style.marginLeft="9px",mxClient.IS_QUIRKS&&(mxUtils.br(t),d.style.height="40"),t.appendChild(d)):(f[2].style.marginRight="9px",R.style.marginRight="9px"),i.cloneNode(!1)),a=(o.style.marginLeft="0px",o.style.paddingTop="8px",o.style.paddingBottom="4px",o.style.fontWeight="normal",mxUtils.write(o,mxResources.get("position")),document.createElement("select")),W=(a.style.position="absolute",a.style.right="20px",a.style.width="97px",a.style.marginTop="-2px",["topLeft","top","topRight","left","center","right","bottomLeft","bottom","bottomRight"]),j={topLeft:[mxConstants.ALIGN_LEFT,mxConstants.ALIGN_TOP,mxConstants.ALIGN_RIGHT,mxConstants.ALIGN_BOTTOM],top:[mxConstants.ALIGN_CENTER,mxConstants.ALIGN_TOP,mxConstants.ALIGN_CENTER,mxConstants.ALIGN_BOTTOM],topRight:[mxConstants.ALIGN_RIGHT,mxConstants.ALIGN_TOP,mxConstants.ALIGN_LEFT,mxConstants.ALIGN_BOTTOM],left:[mxConstants.ALIGN_LEFT,mxConstants.ALIGN_MIDDLE,mxConstants.ALIGN_RIGHT,mxConstants.ALIGN_MIDDLE],center:[mxConstants.ALIGN_CENTER,mxConstants.ALIGN_MIDDLE,mxConstants.ALIGN_CENTER,mxConstants.ALIGN_MIDDLE],right:[mxConstants.ALIGN_RIGHT,mxConstants.ALIGN_MIDDLE,mxConstants.ALIGN_LEFT,mxConstants.ALIGN_MIDDLE],bottomLeft:[mxConstants.ALIGN_LEFT,mxConstants.ALIGN_BOTTOM,mxConstants.ALIGN_RIGHT,mxConstants.ALIGN_TOP],bottom:[mxConstants.ALIGN_CENTER,mxConstants.ALIGN_BOTTOM,mxConstants.ALIGN_CENTER,mxConstants.ALIGN_TOP],bottomRight:[mxConstants.ALIGN_RIGHT,mxConstants.ALIGN_BOTTOM,mxConstants.ALIGN_LEFT,mxConstants.ALIGN_TOP]},r=0;r<W.length;r++){var z=document.createElement("option");z.setAttribute("value",W[r]),mxUtils.write(z,mxResources.get(W[r])),a.appendChild(z)}o.appendChild(a);for(var d=i.cloneNode(!1),u=(d.style.marginLeft="0px",d.style.paddingTop="4px",d.style.paddingBottom="4px",d.style.fontWeight="normal",mxUtils.write(d,mxResources.get("writingDirection")),document.createElement("select")),V=(u.style.position="absolute",u.style.right="20px",u.style.width="97px",u.style.marginTop="-2px",["automatic","leftToRight","rightToLeft"]),K={automatic:null,leftToRight:mxConstants.TEXT_DIRECTION_LTR,rightToLeft:mxConstants.TEXT_DIRECTION_RTL},r=0;r<V.length;r++){var Q=document.createElement("option");Q.setAttribute("value",V[r]),mxUtils.write(Q,mxResources.get(V[r])),u.appendChild(Q)}d.appendChild(u),S.isEditing()||(t.appendChild(o),mxEvent.addListener(a,"change",function(t){S.getModel().beginUpdate();try{var e=j[a.value];null!=e&&(S.setCellStyles(mxConstants.STYLE_LABEL_POSITION,e[0],S.getSelectionCells()),S.setCellStyles(mxConstants.STYLE_VERTICAL_LABEL_POSITION,e[1],S.getSelectionCells()),S.setCellStyles(mxConstants.STYLE_ALIGN,e[2],S.getSelectionCells()),S.setCellStyles(mxConstants.STYLE_VERTICAL_ALIGN,e[3],S.getSelectionCells()))}finally{S.getModel().endUpdate()}mxEvent.consume(t)}),t.appendChild(d),mxEvent.addListener(u,"change",function(t){S.setCellStyles(mxConstants.STYLE_TEXT_DIRECTION,K[u.value],S.getSelectionCells()),mxEvent.consume(t)}));var b=document.createElement("input"),A=(b.style.textAlign="right",b.style.marginTop="4px",mxClient.IS_QUIRKS||(b.style.position="absolute",b.style.right="32px"),b.style.width="46px",b.style.height=mxClient.IS_QUIRKS?"21px":"17px",l.appendChild(b),null),o=this.installInputHandler(b,mxConstants.STYLE_FONTSIZE,Menus.prototype.defaultFontSize,1,999," pt",function(n){if(!window.getSelection||mxClient.IS_IE||mxClient.IS_IE11){if(window.getSelection||document.selection){var t=null;if(document.selection?t=document.selection.createRange().parentElement():0<(i=window.getSelection()).rangeCount&&(t=i.getRangeAt(0).commonAncestorContainer),null!=t&&function(t,e){for(;null!=e;){if(e===t)return 1;e=e.parentNode}}(S.cellEditor.textarea,t)){A=n,document.execCommand("fontSize",!1,"4");for(s=S.cellEditor.textarea.getElementsByTagName("font"),l=0;l<s.length;l++)if("4"==s[l].getAttribute("size")){s[l].removeAttribute("size"),s[l].style.fontSize=A+"px",window.setTimeout(function(){b.value=A+" pt",A=null},0);break}}}}else{var i,t=0<(i=window.getSelection()).rangeCount?i.getRangeAt(0).commonAncestorContainer:S.cellEditor.textarea;function e(t,e){null!=S.cellEditor.textarea&&t!=S.cellEditor.textarea&&S.cellEditor.textarea.contains(t)&&(e||i.containsNode(t,!0))&&("FONT"==t.nodeName?(t.removeAttribute("size"),t.style.fontSize=n+"px"):mxUtils.getCurrentStyle(t).fontSize!=n+"px"&&(mxUtils.getCurrentStyle(t.parentNode).fontSize!=n+"px"?t.style.fontSize=n+"px":t.style.fontSize=""))}if(t!=S.cellEditor.textarea&&t.nodeType==mxConstants.NODETYPE_ELEMENT||document.execCommand("fontSize",!1,"1"),null!=(t=t!=S.cellEditor.textarea?t.parentNode:t)&&t.nodeType==mxConstants.NODETYPE_ELEMENT){var s=t.getElementsByTagName("*");e(t);for(var l=0;l<s.length;l++)e(s[l])}b.value=n+" pt"}},!0),d=this.createStepper(b,o,1,10,!0,Menus.prototype.defaultFontSize);d.style.display=b.style.display,d.style.marginTop="4px",mxClient.IS_QUIRKS||(d.style.right="20px"),l.appendChild(d);E.getElementsByTagName("div")[0].style.cssFloat="right";var m,U,p,I=null,Z="#ffffff",v=null,X="#000000",c=S.cellEditor.isContentEditing()?this.createColorOption(mxResources.get("backgroundColor"),function(){return Z},function(t){document.execCommand("backcolor",!1,t!=mxConstants.NONE?t:"transparent")},"#ffffff",{install:function(t){I=t},destroy:function(){I=null}},null,!0,{graph:S,key:"backcolor"}):this.createCellColorOption(mxResources.get("backgroundColor"),mxConstants.STYLE_LABEL_BACKGROUNDCOLOR,"#ffffff",null,function(t){S.updateLabelElements(S.getSelectionCells(),function(t){t.style.backgroundColor=null})}),J=(c.style.fontWeight="bold",this.createCellColorOption(mxResources.get("borderColor"),mxConstants.STYLE_LABEL_BORDERCOLOR,"#000000")),o=(J.style.fontWeight="bold",S.cellEditor.isContentEditing()?this.createColorOption(mxResources.get("fontColor"),function(){return X},function(t){if(mxClient.IS_FF){for(var e=S.cellEditor.textarea.getElementsByTagName("font"),n=[],i=0;i<e.length;i++)n.push({node:e[i],color:e[i].getAttribute("color")});document.execCommand("forecolor",!1,t!=mxConstants.NONE?t:"transparent");for(var s=S.cellEditor.textarea.getElementsByTagName("font"),i=0;i<s.length;i++)if(i>=n.length||s[i]!=n[i].node||s[i]==n[i].node&&s[i].getAttribute("color")!=n[i].color){var l=s[i].firstChild;if(null!=l&&"A"==l.nodeName&&null==l.nextSibling&&null!=l.firstChild){for(e=(s[i].parentNode.insertBefore(l,s[i]),l.firstChild);null!=e;){var o=e.nextSibling;s[i].appendChild(e),e=o}l.appendChild(s[i])}break}}else document.execCommand("forecolor",!1,t!=mxConstants.NONE?t:"transparent")},"#000000",{install:function(t){v=t},destroy:function(){v=null}},null,!0,{graph:S,key:"forecolor"}):this.createCellColorOption(mxResources.get("fontColor"),mxConstants.STYLE_FONTCOLOR,"#000000",function(t){null==t||t==mxConstants.NONE?c.style.display="none":c.style.display="",J.style.display=c.style.display},function(t){null==t||t==mxConstants.NONE?S.setCellStyles(mxConstants.STYLE_NOLABEL,"1",S.getSelectionCells()):S.setCellStyles(mxConstants.STYLE_NOLABEL,null,S.getSelectionCells()),S.updateLabelElements(S.getSelectionCells(),function(t){t.removeAttribute("color"),t.style.color=null})})),l=(o.style.fontWeight="bold",s.appendChild(o),s.appendChild(c),S.cellEditor.isContentEditing()||s.appendChild(J),t.appendChild(s),this.createPanel()),d=(l.style.paddingTop="2px",l.style.paddingBottom="4px",this.createCellOption(mxResources.get("wordWrap"),mxConstants.STYLE_WHITE_SPACE,null,"wrap","null",null,null,!0)),o=(d.style.fontWeight="bold",C.containsLabel||C.autoSize||0!=C.edges.length||l.appendChild(d),this.createCellOption(mxResources.get("formattedText"),"html","0",null,null,null,n.actions.get("formattedText"))),s=(o.style.fontWeight="bold",l.appendChild(o),this.createPanel()),d=(s.style.paddingTop="10px",s.style.paddingBottom="28px",s.style.fontWeight="normal",document.createElement("div")),N=(d.style.position="absolute",d.style.width="70px",d.style.marginTop="0px",d.style.fontWeight="bold",mxUtils.write(d,mxResources.get("spacing")),s.appendChild(d),this.addUnitInput(s,"pt",91,44,function(){et.apply(this,arguments)})),_=this.addUnitInput(s,"pt",20,44,function(){tt.apply(this,arguments)}),O=(mxUtils.br(s),this.addLabel(s,mxResources.get("top"),91),this.addLabel(s,mxResources.get("global"),20),mxUtils.br(s),mxUtils.br(s),this.addUnitInput(s,"pt",162,44,function(){st.apply(this,arguments)})),w=this.addUnitInput(s,"pt",91,44,function(){it.apply(this,arguments)}),q=this.addUnitInput(s,"pt",20,44,function(){nt.apply(this,arguments)});function M(t,e){mxClient.IS_IE&&(mxClient.IS_QUIRKS||document.documentMode<10)?t.style.filter=e?"progid:DXImageTransform.Microsoft.Gradient(StartColorStr='#c5ecff', EndColorStr='#87d4fb', GradientType=0)":"":t.style.backgroundImage=e?"linear-gradient(#c5ecff 0px,#87d4fb 100%)":""}mxUtils.br(s),this.addLabel(s,mxResources.get("left"),162),this.addLabel(s,mxResources.get("bottom"),91),this.addLabel(s,mxResources.get("right"),20),S.cellEditor.isContentEditing()?(U=m=null,t.appendChild(this.createRelativeOption(mxResources.get("lineheight"),null,null,function(t){var e=""==t.value?120:parseInt(t.value),e=Math.max(0,isNaN(e)?120:e);null!=m&&(S.cellEditor.restoreSelection(m),m=null);for(var n=S.getSelectedElement();null!=n&&n.nodeType!=mxConstants.NODETYPE_ELEMENT;)n=n.parentNode;null!=n&&n==S.cellEditor.textarea&&null!=S.cellEditor.textarea.firstChild&&("P"!=S.cellEditor.textarea.firstChild.nodeName&&(S.cellEditor.textarea.innerHTML="<p>"+S.cellEditor.textarea.innerHTML+"</p>"),n=S.cellEditor.textarea.firstChild),null!=n&&null!=S.cellEditor.textarea&&n!=S.cellEditor.textarea&&S.cellEditor.textarea.contains(n)&&(n.style.lineHeight=e+"%"),t.value=e+" %"},function(t){U=t,mxEvent.addListener(t,"mousedown",function(){document.activeElement==S.cellEditor.textarea&&(m=S.cellEditor.saveSelection())}),mxEvent.addListener(t,"touchstart",function(){document.activeElement==S.cellEditor.textarea&&(m=S.cellEditor.saveSelection())}),t.value="120 %"})),(o=i.cloneNode(!1)).style.paddingLeft="0px",d=this.editorUi.toolbar.addItems(["link","image"],o,!0),p=[this.editorUi.toolbar.addButton("geSprite-horizontalrule",mxResources.get("insertHorizontalRule"),function(){document.execCommand("inserthorizontalrule",!1)},o),this.editorUi.toolbar.addMenuFunctionInContainer(o,"geSprite-table",mxResources.get("table"),!1,mxUtils.bind(this,function(t){this.editorUi.menus.addInsertTableItem(t)}))],this.styleButtons(d),this.styleButtons(p),(d=this.createPanel()).style.paddingTop="10px",d.style.paddingBottom="10px",d.appendChild(this.createTitle(mxResources.get("insert"))),d.appendChild(o),t.appendChild(d),mxClient.IS_QUIRKS&&(d.style.height="70"),(o=i.cloneNode(!1)).style.paddingLeft="0px",p=[this.editorUi.toolbar.addButton("geSprite-insertcolumnbefore",mxResources.get("insertColumnBefore"),mxUtils.bind(this,function(){try{null!=h&&S.insertColumn(h,null!=x?x.cellIndex:0)}catch(t){this.editorUi.handleError(t)}}),o),this.editorUi.toolbar.addButton("geSprite-insertcolumnafter",mxResources.get("insertColumnAfter"),mxUtils.bind(this,function(){try{null!=h&&S.insertColumn(h,null!=x?x.cellIndex+1:-1)}catch(t){this.editorUi.handleError(t)}}),o),this.editorUi.toolbar.addButton("geSprite-deletecolumn",mxResources.get("deleteColumn"),mxUtils.bind(this,function(){try{null!=h&&null!=x&&S.deleteColumn(h,x.cellIndex)}catch(t){this.editorUi.handleError(t)}}),o),this.editorUi.toolbar.addButton("geSprite-insertrowbefore",mxResources.get("insertRowBefore"),mxUtils.bind(this,function(){try{null!=h&&null!=y&&S.insertRow(h,y.sectionRowIndex)}catch(t){this.editorUi.handleError(t)}}),o),this.editorUi.toolbar.addButton("geSprite-insertrowafter",mxResources.get("insertRowAfter"),mxUtils.bind(this,function(){try{null!=h&&null!=y&&S.insertRow(h,y.sectionRowIndex+1)}catch(t){this.editorUi.handleError(t)}}),o),this.editorUi.toolbar.addButton("geSprite-deleterow",mxResources.get("deleteRow"),mxUtils.bind(this,function(){try{null!=h&&null!=y&&S.deleteRow(h,y.sectionRowIndex)}catch(t){this.editorUi.handleError(t)}}),o)],this.styleButtons(p),p[2].style.marginRight="9px",(d=this.createPanel()).style.paddingTop="10px",d.style.paddingBottom="10px",d.appendChild(this.createTitle(mxResources.get("table"))),d.appendChild(o),mxClient.IS_QUIRKS&&(mxUtils.br(t),d.style.height="70"),(o=i.cloneNode(!1)).style.paddingLeft="0px",p=[this.editorUi.toolbar.addButton("geSprite-strokecolor",mxResources.get("borderColor"),mxUtils.bind(this,function(n){var t;null!=h&&(t=h.style.borderColor.replace(/\brgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/g,function(t,e,n,i){return"#"+("0"+Number(e).toString(16)).substr(-2)+("0"+Number(n).toString(16)).substr(-2)+("0"+Number(i).toString(16)).substr(-2)}),this.editorUi.pickColor(t,function(t){var e=null==x||null!=n&&mxEvent.isShiftDown(n)?h:x;S.processElements(e,function(t){t.style.border=null}),null==t||t==mxConstants.NONE?(e.removeAttribute("border"),e.style.border="",e.style.borderCollapse=""):(e.setAttribute("border","1"),e.style.border="1px solid "+t,e.style.borderCollapse="collapse")}))}),o),this.editorUi.toolbar.addButton("geSprite-fillcolor",mxResources.get("backgroundColor"),mxUtils.bind(this,function(n){var t;null!=h&&(t=h.style.backgroundColor.replace(/\brgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/g,function(t,e,n,i){return"#"+("0"+Number(e).toString(16)).substr(-2)+("0"+Number(n).toString(16)).substr(-2)+("0"+Number(i).toString(16)).substr(-2)}),this.editorUi.pickColor(t,function(t){var e=null==x||null!=n&&mxEvent.isShiftDown(n)?h:x;S.processElements(e,function(t){t.style.backgroundColor=null}),null==t||t==mxConstants.NONE?e.style.backgroundColor="":e.style.backgroundColor=t}))}),o),this.editorUi.toolbar.addButton("geSprite-fit",mxResources.get("spacing"),function(){var t;null!=h&&(t=h.getAttribute("cellPadding")||0,t=new FilenameDialog(n,t,mxResources.get("apply"),mxUtils.bind(this,function(t){null!=t&&0<t.length?h.setAttribute("cellPadding",t):h.removeAttribute("cellPadding")}),mxResources.get("spacing")),n.showDialog(t.container,300,80,!0,!0),t.init())},o),this.editorUi.toolbar.addButton("geSprite-left",mxResources.get("left"),function(){null!=h&&h.setAttribute("align","left")},o),this.editorUi.toolbar.addButton("geSprite-center",mxResources.get("center"),function(){null!=h&&h.setAttribute("align","center")},o),this.editorUi.toolbar.addButton("geSprite-right",mxResources.get("right"),function(){null!=h&&h.setAttribute("align","right")},o)],this.styleButtons(p),p[2].style.marginRight="9px",mxClient.IS_QUIRKS&&(mxUtils.br(d),mxUtils.br(d)),d.appendChild(o),t.appendChild(d),P=d):(t.appendChild(l),t.appendChild(this.createRelativeOption(mxResources.get("opacity"),mxConstants.STYLE_TEXT_OPACITY)),t.appendChild(s));var $,D=mxUtils.bind(this,function(t,e,n){C=this.format.getSelectionState();var i,s=mxUtils.getValue(C.style,mxConstants.STYLE_FONTSTYLE,0),s=(M(f[0],(s&mxConstants.FONT_BOLD)==mxConstants.FONT_BOLD),M(f[1],(s&mxConstants.FONT_ITALIC)==mxConstants.FONT_ITALIC),M(f[2],(s&mxConstants.FONT_UNDERLINE)==mxConstants.FONT_UNDERLINE),E.firstChild.nodeValue=mxUtils.getValue(C.style,mxConstants.STYLE_FONTFAMILY,Menus.prototype.defaultFont),M(B,"0"==mxUtils.getValue(C.style,mxConstants.STYLE_HORIZONTAL,"1")),!n&&document.activeElement==b||(i=parseFloat(mxUtils.getValue(C.style,mxConstants.STYLE_FONTSIZE,Menus.prototype.defaultFontSize)),b.value=isNaN(i)?"":i+" pt"),mxUtils.getValue(C.style,mxConstants.STYLE_ALIGN,mxConstants.ALIGN_CENTER)),s=(M(T,s==mxConstants.ALIGN_LEFT),M(L,s==mxConstants.ALIGN_CENTER),M(R,s==mxConstants.ALIGN_RIGHT),mxUtils.getValue(C.style,mxConstants.STYLE_VERTICAL_ALIGN,mxConstants.ALIGN_MIDDLE)),s=(M(G,s==mxConstants.ALIGN_TOP),M(H,s==mxConstants.ALIGN_MIDDLE),M(k,s==mxConstants.ALIGN_BOTTOM),mxUtils.getValue(C.style,mxConstants.STYLE_LABEL_POSITION,mxConstants.ALIGN_CENTER)),l=mxUtils.getValue(C.style,mxConstants.STYLE_VERTICAL_LABEL_POSITION,mxConstants.ALIGN_MIDDLE),l=(s==mxConstants.ALIGN_LEFT&&l==mxConstants.ALIGN_TOP?a.value="topLeft":s==mxConstants.ALIGN_CENTER&&l==mxConstants.ALIGN_TOP?a.value="top":s==mxConstants.ALIGN_RIGHT&&l==mxConstants.ALIGN_TOP?a.value="topRight":s==mxConstants.ALIGN_LEFT&&l==mxConstants.ALIGN_BOTTOM?a.value="bottomLeft":s==mxConstants.ALIGN_CENTER&&l==mxConstants.ALIGN_BOTTOM?a.value="bottom":s==mxConstants.ALIGN_RIGHT&&l==mxConstants.ALIGN_BOTTOM?a.value="bottomRight":s==mxConstants.ALIGN_LEFT?a.value="left":s==mxConstants.ALIGN_RIGHT?a.value="right":a.value="center",mxUtils.getValue(C.style,mxConstants.STYLE_TEXT_DIRECTION,mxConstants.DEFAULT_TEXT_DIRECTION));l==mxConstants.TEXT_DIRECTION_RTL?u.value="rightToLeft":l==mxConstants.TEXT_DIRECTION_LTR?u.value="leftToRight":l==mxConstants.TEXT_DIRECTION_AUTO&&(u.value="automatic"),!n&&document.activeElement==_||(i=parseFloat(mxUtils.getValue(C.style,mxConstants.STYLE_SPACING,2)),_.value=isNaN(i)?"":i+" pt"),!n&&document.activeElement==N||(i=parseFloat(mxUtils.getValue(C.style,mxConstants.STYLE_SPACING_TOP,0)),N.value=isNaN(i)?"":i+" pt"),!n&&document.activeElement==q||(i=parseFloat(mxUtils.getValue(C.style,mxConstants.STYLE_SPACING_RIGHT,0)),q.value=isNaN(i)?"":i+" pt"),!n&&document.activeElement==w||(i=parseFloat(mxUtils.getValue(C.style,mxConstants.STYLE_SPACING_BOTTOM,0)),w.value=isNaN(i)?"":i+" pt"),!n&&document.activeElement==O||(i=parseFloat(mxUtils.getValue(C.style,mxConstants.STYLE_SPACING_LEFT,0)),O.value=isNaN(i)?"":i+" pt")}),tt=this.installInputHandler(_,mxConstants.STYLE_SPACING,2,-999,999," pt"),et=this.installInputHandler(N,mxConstants.STYLE_SPACING_TOP,0,-999,999," pt"),nt=this.installInputHandler(q,mxConstants.STYLE_SPACING_RIGHT,0,-999,999," pt"),it=this.installInputHandler(w,mxConstants.STYLE_SPACING_BOTTOM,0,-999,999," pt"),st=this.installInputHandler(O,mxConstants.STYLE_SPACING_LEFT,0,-999,999," pt");return this.addKeyHandler(b,D),this.addKeyHandler(_,D),this.addKeyHandler(N,D),this.addKeyHandler(q,D),this.addKeyHandler(w,D),this.addKeyHandler(O,D),S.getModel().addListener(mxEvent.CHANGE,D),this.listeners.push({destroy:function(){S.getModel().removeListener(D)}}),D(),S.cellEditor.isContentEditing()&&($=!1,i=function(){$||($=!0,window.setTimeout(function(){for(var n=S.getSelectedElement();null!=n&&n.nodeType!=mxConstants.NODETYPE_ELEMENT;)n=n.parentNode;if(null!=n){function t(t,e,n){return null!=n.style&&null!=e?(e=e.lineHeight,null!=n.style.lineHeight&&"%"==n.style.lineHeight.substring(n.style.lineHeight.length-1)?parseInt(n.style.lineHeight)/100:"px"==e.substring(e.length-2)?parseFloat(e)/t:parseInt(e)):""}function e(t){t=null!=t?t.fontSize:null;return null!=t&&"px"==t.substring(t.length-2)?parseFloat(t):mxConstants.DEFAULT_FONTSIZE}n==S.cellEditor.textarea&&1==S.cellEditor.textarea.children.length&&S.cellEditor.textarea.firstChild.nodeType==mxConstants.NODETYPE_ELEMENT&&(n=S.cellEditor.textarea.firstChild);var i,s,l=mxUtils.getCurrentStyle(n),o=e(l),a=t(o,l,n),r=n.getElementsByTagName("*");if(0<r.length&&window.getSelection&&!mxClient.IS_IE&&!mxClient.IS_IE11)for(var d,u=window.getSelection(),m=0;m<r.length;m++)u.containsNode(r[m],!0)&&(temp=mxUtils.getCurrentStyle(r[m]),(d=t(o=Math.max(e(temp),o),temp,r[m]))==a&&!isNaN(d)||(a=""));function p(t){if(null!=S.getParentByName(n,t,S.cellEditor.textarea))return!0;for(var e=n;null!=e&&1==e.childNodes.length;)if((e=e.childNodes[0]).nodeName==t)return!0;return!1}function c(t,e){if(null!=t&&null!=e){if(t==e)return!0;if(t.length>e.length+1)return t.substring(t.length-e.length-1,t.length)=="-"+e}return!1}null!=l&&(M(f[0],"bold"==l.fontWeight||400<l.fontWeight||p("B")||p("STRONG")),M(f[1],"italic"==l.fontStyle||p("I")||p("EM")),M(f[2],p("U")),M(Y,p("SUP")),M(F,p("SUB")),S.cellEditor.isTableSelected()?(M(g,c(l.textAlign,"justify")),M(T,c(l.textAlign,"left")),M(L,c(l.textAlign,"center")),M(R,c(l.textAlign,"right"))):(s=S.cellEditor.align||mxUtils.getValue(C.style,mxConstants.STYLE_ALIGN,mxConstants.ALIGN_CENTER),c(l.textAlign,"justify")?(M(g,c(l.textAlign,"justify")),M(T,!1),M(L,!1),M(R,!1)):(M(g,!1),M(T,s==mxConstants.ALIGN_LEFT),M(L,s==mxConstants.ALIGN_CENTER),M(R,s==mxConstants.ALIGN_RIGHT))),h=S.getParentByName(n,"TABLE",S.cellEditor.textarea),y=null==h?null:S.getParentByName(n,"TR",h),x=null==h?null:S.getParentByNames(n,["TD","TH"],h),P.style.display=null!=h?"":"none",document.activeElement!=b&&("FONT"==n.nodeName&&"4"==n.getAttribute("size")&&null!=A?(n.removeAttribute("size"),n.style.fontSize=A+" pt",A=null):b.value=isNaN(o)?"":o+" pt",d=parseFloat(a),isNaN(d)?U.value="100 %":U.value=Math.round(100*d)+" %"),s=l.color.replace(/\brgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/g,function(t,e,n,i){return"#"+("0"+Number(e).toString(16)).substr(-2)+("0"+Number(n).toString(16)).substr(-2)+("0"+Number(i).toString(16)).substr(-2)}),i=l.backgroundColor.replace(/\brgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/g,function(t,e,n,i){return"#"+("0"+Number(e).toString(16)).substr(-2)+("0"+Number(n).toString(16)).substr(-2)+("0"+Number(i).toString(16)).substr(-2)}),null!=v&&(X="#"==s.charAt(0)?s:"#000000",v(X,!0)),null!=I&&(Z="#"==i.charAt(0)?i:null,I(Z,!0)),null!=E.firstChild&&('"'==(s='"'==(s="'"==(s="'"==(s=l.fontFamily).charAt(0)?s.substring(1):s).charAt(s.length-1)?s.substring(0,s.length-1):s).charAt(0)?s.substring(1):s).charAt(s.length-1)&&(s=s.substring(0,s.length-1)),E.firstChild.nodeValue=s))}$=!1},0))},(mxClient.IS_FF||mxClient.IS_EDGE||mxClient.IS_IE||mxClient.IS_IE11)&&mxEvent.addListener(S.cellEditor.textarea,"DOMSubtreeModified",i),mxEvent.addListener(S.cellEditor.textarea,"input",i),mxEvent.addListener(S.cellEditor.textarea,"touchend",i),mxEvent.addListener(S.cellEditor.textarea,"mouseup",i),mxEvent.addListener(S.cellEditor.textarea,"keyup",i),this.listeners.push({destroy:function(){}}),i()),t},StyleFormatPanel=function(t,e,n){BaseFormatPanel.call(this,t,e,n),this.init()},mxUtils.extend(StyleFormatPanel,BaseFormatPanel),StyleFormatPanel.prototype.defaultStrokeColor="black",StyleFormatPanel.prototype.init=function(){this.editorUi.editor.graph;var t=this.format.getSelectionState(),t=(t.containsLabel||(t.containsImage&&1==t.vertices.length&&"image"==t.style.shape&&null!=t.style.image&&"data:image/svg+xml;"==t.style.image.substring(0,19)&&this.container.appendChild(this.addSvgStyles(this.createPanel())),t.containsImage&&"image"!=t.style.shape||this.container.appendChild(this.addFill(this.createPanel())),this.container.appendChild(this.addStroke(this.createPanel())),this.container.appendChild(this.addLineJumps(this.createPanel())),(t=this.createRelativeOption(mxResources.get("opacity"),mxConstants.STYLE_OPACITY,41)).style.paddingTop="8px",t.style.paddingBottom="8px",this.container.appendChild(t),this.container.appendChild(this.addEffects(this.createPanel()))),this.addEditOps(this.createPanel()));null!=t.firstChild&&mxUtils.br(t),this.container.appendChild(this.addStyleOps(t))},StyleFormatPanel.prototype.getCssRules=function(t){var e=document.implementation.createHTMLDocument(""),n=document.createElement("style");return mxUtils.setTextContent(n,t),e.body.appendChild(n),n.sheet.cssRules},StyleFormatPanel.prototype.addSvgStyles=function(t){this.editorUi.editor.graph;var e=this.format.getSelectionState();t.style.paddingTop="6px",t.style.paddingBottom="6px",t.style.fontWeight="bold",t.style.display="none";try{var n=e.style.editableCssRules;if(null!=n){var i=new RegExp(n),s=e.style.image.substring(e.style.image.indexOf(",")+1),l=window.atob?atob(s):Base64.decode(s,!0),o=mxUtils.parseXml(l);if(null!=o)for(var a=o.getElementsByTagName("style"),r=0;r<a.length;r++)for(var d=this.getCssRules(mxUtils.getTextContent(a[r])),u=0;u<d.length;u++)this.addSvgRule(t,d[u],o,a[r],d,u,i)}}catch(t){}return t},StyleFormatPanel.prototype.addSvgRule=function(n,t,s,l,o,a,e){var r=this.editorUi.editor.graph;e.test(t.selectorText)&&((e=mxUtils.bind(this,function(e,i,t){""!=e.style[i]&&(t=this.createColorOption(t+" "+e.selectorText,function(){return(t=(t=e.style[i]).match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i))&&4===t.length?"#"+("0"+parseInt(t[1],10).toString(16)).slice(-2)+("0"+parseInt(t[2],10).toString(16)).slice(-2)+("0"+parseInt(t[3],10).toString(16)).slice(-2):"";var t},function(t){o[a].style[i]=t;for(var e="",n=0;n<o.length;n++)e+=o[n].cssText+" ";l.textContent=e;t=mxUtils.getXml(s.documentElement);r.setCellStyles(mxConstants.STYLE_IMAGE,"data:image/svg+xml,"+(window.btoa?btoa(t):Base64.encode(t,!0)),r.getSelectionCells())},"#ffffff",{install:function(t){},destroy:function(){}},void 0,void 0,{graph:r,key:"image"}),n.appendChild(t),n.style.display="")}))(t,"fill",mxResources.get("fill")),e(t,"stroke",mxResources.get("line")))},StyleFormatPanel.prototype.addEditOps=function(t){var e=this.format.getSelectionState(),n=null;return 1==this.editorUi.editor.graph.getSelectionCount()&&((n=mxUtils.button(mxResources.get("editStyle"),mxUtils.bind(this,function(t){this.editorUi.actions.get("editStyle").funct()}))).setAttribute("title",mxResources.get("editStyle")+" ("+this.editorUi.actions.get("editStyle").shortcut+")"),n.style.width="202px",n.style.marginBottom="2px",t.appendChild(n)),e.image&&((e=mxUtils.button(mxResources.get("editImage"),mxUtils.bind(this,function(t){this.editorUi.actions.get("image").funct()}))).setAttribute("title",mxResources.get("editImage")),e.style.marginBottom="2px",null==n?e.style.width="202px":(n.style.width="100px",e.style.width="100px",e.style.marginLeft="2px"),t.appendChild(e)),t},StyleFormatPanel.prototype.addFill=function(e){for(var n=this.editorUi.editor.graph,i=this.format.getSelectionState(),s=(e.style.paddingTop="6px",e.style.paddingBottom="6px",document.createElement("select")),l=(s.style.position="absolute",s.style.marginTop="-2px",s.style.right=mxClient.IS_QUIRKS?"52px":"72px",s.style.width="70px",mxEvent.addListener(s,"click",function(t){mxEvent.consume(t)}),this.createCellColorOption(mxResources.get("gradient"),mxConstants.STYLE_GRADIENTCOLOR,"#ffffff",function(t){null==t||t==mxConstants.NONE?s.style.display="none":s.style.display=""})),t="image"==i.style.shape?mxConstants.STYLE_IMAGE_BACKGROUND:mxConstants.STYLE_FILLCOLOR,o="image"==i.style.shape?mxResources.get("background"):mxResources.get("fill"),o=this.createCellColorOption(o,t,"#ffffff"),t=(o.style.fontWeight="bold",mxUtils.getValue(i.style,t,null)),a=(l.style.display=null!=t&&t!=mxConstants.NONE&&i.fill&&"image"!=i.style.shape?"":"none",[mxConstants.DIRECTION_NORTH,mxConstants.DIRECTION_EAST,mxConstants.DIRECTION_SOUTH,mxConstants.DIRECTION_WEST]),r=0;r<a.length;r++){var d=document.createElement("option");d.setAttribute("value",a[r]),mxUtils.write(d,mxResources.get(a[r])),s.appendChild(d)}l.appendChild(s);for(var u=mxUtils.bind(this,function(){i=this.format.getSelectionState();var t=mxUtils.getValue(i.style,mxConstants.STYLE_GRADIENT_DIRECTION,mxConstants.DIRECTION_SOUTH),t=(""==t&&(t=mxConstants.DIRECTION_SOUTH),s.value=t,e.style.display=i.fill?"":"none",mxUtils.getValue(i.style,mxConstants.STYLE_FILLCOLOR,null));!i.fill||i.containsImage||null==t||t==mxConstants.NONE||"filledEdge"==i.style.shape?l.style.display="none":l.style.display=""}),m=(n.getModel().addListener(mxEvent.CHANGE,u),this.listeners.push({destroy:function(){n.getModel().removeListener(u)}}),u(),mxEvent.addListener(s,"change",function(t){n.setCellStyles(mxConstants.STYLE_GRADIENT_DIRECTION,s.value,n.getSelectionCells()),mxEvent.consume(t)}),e.appendChild(o),e.appendChild(l),this.getCustomColors()),r=0;r<m.length;r++)e.appendChild(this.createCellColorOption(m[r].title,m[r].key,m[r].defaultValue));return e},StyleFormatPanel.prototype.getCustomColors=function(){var t=[];return"swimlane"==this.format.getSelectionState().style.shape&&t.push({title:mxResources.get("laneColor"),key:"swimlaneFillColor",defaultValue:"#ffffff"}),t},StyleFormatPanel.prototype.addStroke=function(t){for(var a=this.editorUi,r=a.editor.graph,d=this.format.getSelectionState(),e=(t.style.paddingTop="4px",t.style.paddingBottom="4px",t.style.whiteSpace="normal",document.createElement("div")),u=(e.style.fontWeight="bold",document.createElement("select")),n=(u.style.position="absolute",u.style.marginTop="-2px",u.style.right="72px",u.style.width="80px",["sharp","rounded","curved"]),i=0;i<n.length;i++){var s=document.createElement("option");s.setAttribute("value",n[i]),mxUtils.write(s,mxResources.get(n[i])),u.appendChild(s)}mxEvent.addListener(u,"change",function(t){r.getModel().beginUpdate();try{var e=[mxConstants.STYLE_ROUNDED,mxConstants.STYLE_CURVED],n=["0",null];"rounded"==u.value?n=["1",null]:"curved"==u.value&&(n=[null,"1"]);for(var i=0;i<e.length;i++)r.setCellStyles(e[i],n[i],r.getSelectionCells());a.fireEvent(new mxEventObject("styleChanged","keys",e,"values",n,"cells",r.getSelectionCells()))}finally{r.getModel().endUpdate()}mxEvent.consume(t)}),mxEvent.addListener(u,"click",function(t){mxEvent.consume(t)});var m="image"==d.style.shape?mxConstants.STYLE_IMAGE_BORDER:mxConstants.STYLE_STROKECOLOR,l="image"==d.style.shape?mxResources.get("border"):mxResources.get("line"),l=this.createCellColorOption(l,m,"#000000"),p=(l.appendChild(u),e.appendChild(l),e.cloneNode(!1)),o=(p.style.fontWeight="normal",p.style.whiteSpace="nowrap",p.style.position="relative",p.style.paddingLeft="16px",p.style.marginBottom="2px",p.style.marginTop="2px",p.className="geToolbarContainer",mxUtils.bind(this,function(t,e,n,i,s){t=this.editorUi.menus.styleChange(t,"",i,s,"geIcon",null),i=document.createElement("div");return i.style.width=e+"px",i.style.height="1px",i.style.borderBottom="1px "+n+" "+this.defaultStrokeColor,i.style.paddingTop="6px",t.firstChild.firstChild.style.padding="0px 4px 0px 4px",t.firstChild.firstChild.style.width=e+"px",t.firstChild.firstChild.appendChild(i),t})),l=this.editorUi.toolbar.addMenuFunctionInContainer(p,"geSprite-orthogonal",mxResources.get("pattern"),!1,mxUtils.bind(this,function(t){o(t,75,"solid",[mxConstants.STYLE_DASHED,mxConstants.STYLE_DASH_PATTERN],[null,null]).setAttribute("title",mxResources.get("solid")),o(t,75,"dashed",[mxConstants.STYLE_DASHED,mxConstants.STYLE_DASH_PATTERN],["1",null]).setAttribute("title",mxResources.get("dashed")),o(t,75,"dotted",[mxConstants.STYLE_DASHED,mxConstants.STYLE_DASH_PATTERN],["1","1 1"]).setAttribute("title",mxResources.get("dotted")+" (1)"),o(t,75,"dotted",[mxConstants.STYLE_DASHED,mxConstants.STYLE_DASH_PATTERN],["1","1 2"]).setAttribute("title",mxResources.get("dotted")+" (2)"),o(t,75,"dotted",[mxConstants.STYLE_DASHED,mxConstants.STYLE_DASH_PATTERN],["1","1 4"]).setAttribute("title",mxResources.get("dotted")+" (3)")})),c=p.cloneNode(!1),g=this.editorUi.toolbar.addMenuFunctionInContainer(c,"geSprite-connection",mxResources.get("connection"),!1,mxUtils.bind(this,function(t){this.editorUi.menus.styleChange(t,"",[mxConstants.STYLE_SHAPE,mxConstants.STYLE_STARTSIZE,mxConstants.STYLE_ENDSIZE,"width"],[null,null,null,null],"geIcon geSprite geSprite-connection",null,!0).setAttribute("title",mxResources.get("line")),this.editorUi.menus.styleChange(t,"",[mxConstants.STYLE_SHAPE,mxConstants.STYLE_STARTSIZE,mxConstants.STYLE_ENDSIZE,"width"],["link",null,null,null],"geIcon geSprite geSprite-linkedge",null,!0).setAttribute("title",mxResources.get("link")),this.editorUi.menus.styleChange(t,"",[mxConstants.STYLE_SHAPE,mxConstants.STYLE_STARTSIZE,mxConstants.STYLE_ENDSIZE,"width"],["flexArrow",null,null,null],"geIcon geSprite geSprite-arrow",null,!0).setAttribute("title",mxResources.get("arrow")),this.editorUi.menus.styleChange(t,"",[mxConstants.STYLE_SHAPE,mxConstants.STYLE_STARTSIZE,mxConstants.STYLE_ENDSIZE,"width"],["arrow",null,null,null],"geIcon geSprite geSprite-simplearrow",null,!0).setAttribute("title",mxResources.get("simpleArrow"))})),h=this.editorUi.toolbar.addMenuFunctionInContainer(c,"geSprite-orthogonal",mxResources.get("pattern"),!1,mxUtils.bind(this,function(t){o(t,33,"solid",[mxConstants.STYLE_DASHED,mxConstants.STYLE_DASH_PATTERN],[null,null]).setAttribute("title",mxResources.get("solid")),o(t,33,"dashed",[mxConstants.STYLE_DASHED,mxConstants.STYLE_DASH_PATTERN],["1",null]).setAttribute("title",mxResources.get("dashed")),o(t,33,"dotted",[mxConstants.STYLE_DASHED,mxConstants.STYLE_DASH_PATTERN],["1","1 1"]).setAttribute("title",mxResources.get("dotted")+" (1)"),o(t,33,"dotted",[mxConstants.STYLE_DASHED,mxConstants.STYLE_DASH_PATTERN],["1","1 2"]).setAttribute("title",mxResources.get("dotted")+" (2)"),o(t,33,"dotted",[mxConstants.STYLE_DASHED,mxConstants.STYLE_DASH_PATTERN],["1","1 4"]).setAttribute("title",mxResources.get("dotted")+" (3)")})),x=p.cloneNode(!1),y=document.createElement("input"),S=(y.style.textAlign="right",y.style.marginTop="2px",y.style.width="41px",y.setAttribute("title",mxResources.get("linewidth")),p.appendChild(y),y.cloneNode(!0));function C(t){var e=parseInt(y.value);(e=Math.min(999,Math.max(1,isNaN(e)?1:e)))!=mxUtils.getValue(d.style,mxConstants.STYLE_STROKEWIDTH,1)&&(r.setCellStyles(mxConstants.STYLE_STROKEWIDTH,e,r.getSelectionCells()),a.fireEvent(new mxEventObject("styleChanged","keys",[mxConstants.STYLE_STROKEWIDTH],"values",[e],"cells",r.getSelectionCells()))),y.value=e+" pt",mxEvent.consume(t)}function E(t){var e=parseInt(S.value);(e=Math.min(999,Math.max(1,isNaN(e)?1:e)))!=mxUtils.getValue(d.style,mxConstants.STYLE_STROKEWIDTH,1)&&(r.setCellStyles(mxConstants.STYLE_STROKEWIDTH,e,r.getSelectionCells()),a.fireEvent(new mxEventObject("styleChanged","keys",[mxConstants.STYLE_STROKEWIDTH],"values",[e],"cells",r.getSelectionCells()))),S.value=e+" pt",mxEvent.consume(t)}c.appendChild(S);var f=this.createStepper(y,C,1,9),T=(f.style.display=y.style.display,f.style.marginTop="2px",p.appendChild(f),this.createStepper(S,E,1,9)),L=(T.style.display=S.style.display,T.style.marginTop="2px",c.appendChild(T),mxClient.IS_QUIRKS?(y.style.height="17px",S.style.height="17px"):(y.style.position="absolute",y.style.right="32px",y.style.height="15px",f.style.right="20px",S.style.position="absolute",S.style.right="32px",S.style.height="15px",T.style.right="20px"),mxEvent.addListener(y,"blur",C),mxEvent.addListener(y,"change",C),mxEvent.addListener(S,"blur",E),mxEvent.addListener(S,"change",E),mxClient.IS_QUIRKS&&(mxUtils.br(x),mxUtils.br(x)),this.editorUi.toolbar.addMenuFunctionInContainer(x,"geSprite-orthogonal",mxResources.get("waypoints"),!1,mxUtils.bind(this,function(t){"arrow"!=d.style.shape&&(this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_EDGE,mxConstants.STYLE_CURVED,mxConstants.STYLE_NOEDGESTYLE],[null,null,null],"geIcon geSprite geSprite-straight",null,!0).setAttribute("title",mxResources.get("straight")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_EDGE,mxConstants.STYLE_CURVED,mxConstants.STYLE_NOEDGESTYLE],["orthogonalEdgeStyle",null,null],"geIcon geSprite geSprite-orthogonal",null,!0).setAttribute("title",mxResources.get("orthogonal")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_EDGE,mxConstants.STYLE_ELBOW,mxConstants.STYLE_CURVED,mxConstants.STYLE_NOEDGESTYLE],["elbowEdgeStyle",null,null,null],"geIcon geSprite geSprite-horizontalelbow",null,!0).setAttribute("title",mxResources.get("simple")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_EDGE,mxConstants.STYLE_ELBOW,mxConstants.STYLE_CURVED,mxConstants.STYLE_NOEDGESTYLE],["elbowEdgeStyle","vertical",null,null],"geIcon geSprite geSprite-verticalelbow",null,!0).setAttribute("title",mxResources.get("simple")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_EDGE,mxConstants.STYLE_ELBOW,mxConstants.STYLE_CURVED,mxConstants.STYLE_NOEDGESTYLE],["isometricEdgeStyle",null,null,null],"geIcon geSprite geSprite-horizontalisometric",null,!0).setAttribute("title",mxResources.get("isometric")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_EDGE,mxConstants.STYLE_ELBOW,mxConstants.STYLE_CURVED,mxConstants.STYLE_NOEDGESTYLE],["isometricEdgeStyle","vertical",null,null],"geIcon geSprite geSprite-verticalisometric",null,!0).setAttribute("title",mxResources.get("isometric")),"connector"==d.style.shape&&this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_EDGE,mxConstants.STYLE_CURVED,mxConstants.STYLE_NOEDGESTYLE],["orthogonalEdgeStyle","1",null],"geIcon geSprite geSprite-curved",null,!0).setAttribute("title",mxResources.get("curved")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_EDGE,mxConstants.STYLE_CURVED,mxConstants.STYLE_NOEDGESTYLE],["entityRelationEdgeStyle",null,null],"geIcon geSprite geSprite-entity",null,!0).setAttribute("title",mxResources.get("entityRelation")))}))),R=this.editorUi.toolbar.addMenuFunctionInContainer(x,"geSprite-startclassic",mxResources.get("linestart"),!1,mxUtils.bind(this,function(t){var e;"connector"!=d.style.shape&&"flexArrow"!=d.style.shape&&"filledEdge"!=d.style.shape||((e=this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.NONE,0],"geIcon",null,!1)).setAttribute("title",mxResources.get("none")),e.firstChild.firstChild.innerHTML='<font style="font-size:10px;">'+mxUtils.htmlEntities(mxResources.get("none"))+"</font>","connector"==d.style.shape||"filledEdge"==d.style.shape?(this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_CLASSIC,1],"geIcon geSprite geSprite-startclassic",null,!1).setAttribute("title",mxResources.get("classic")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_CLASSIC_THIN,1],"geIcon geSprite geSprite-startclassicthin",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_OPEN,0],"geIcon geSprite geSprite-startopen",null,!1).setAttribute("title",mxResources.get("openArrow")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_OPEN_THIN,0],"geIcon geSprite geSprite-startopenthin",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],["openAsync",0],"geIcon geSprite geSprite-startopenasync",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_BLOCK,1],"geIcon geSprite geSprite-startblock",null,!1).setAttribute("title",mxResources.get("block")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_BLOCK_THIN,1],"geIcon geSprite geSprite-startblockthin",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],["async",1],"geIcon geSprite geSprite-startasync",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_OVAL,1],"geIcon geSprite geSprite-startoval",null,!1).setAttribute("title",mxResources.get("oval")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_DIAMOND,1],"geIcon geSprite geSprite-startdiamond",null,!1).setAttribute("title",mxResources.get("diamond")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_DIAMOND_THIN,1],"geIcon geSprite geSprite-startthindiamond",null,!1).setAttribute("title",mxResources.get("diamondThin")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_CLASSIC,0],"geIcon geSprite geSprite-startclassictrans",null,!1).setAttribute("title",mxResources.get("classic")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_CLASSIC_THIN,0],"geIcon geSprite geSprite-startclassicthintrans",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_BLOCK,0],"geIcon geSprite geSprite-startblocktrans",null,!1).setAttribute("title",mxResources.get("block")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_BLOCK_THIN,0],"geIcon geSprite geSprite-startblockthintrans",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],["async",0],"geIcon geSprite geSprite-startasynctrans",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_OVAL,0],"geIcon geSprite geSprite-startovaltrans",null,!1).setAttribute("title",mxResources.get("oval")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_DIAMOND,0],"geIcon geSprite geSprite-startdiamondtrans",null,!1).setAttribute("title",mxResources.get("diamond")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],[mxConstants.ARROW_DIAMOND_THIN,0],"geIcon geSprite geSprite-startthindiamondtrans",null,!1).setAttribute("title",mxResources.get("diamondThin")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],["dash",0],"geIcon geSprite geSprite-startdash",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],["cross",0],"geIcon geSprite geSprite-startcross",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],["circlePlus",0],"geIcon geSprite geSprite-startcircleplus",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],["circle",1],"geIcon geSprite geSprite-startcircle",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],["ERone",0],"geIcon geSprite geSprite-starterone",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],["ERmandOne",0],"geIcon geSprite geSprite-starteronetoone",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],["ERmany",0],"geIcon geSprite geSprite-startermany",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],["ERoneToMany",0],"geIcon geSprite geSprite-starteronetomany",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],["ERzeroToOne",1],"geIcon geSprite geSprite-starteroneopt",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW,"startFill"],["ERzeroToMany",1],"geIcon geSprite geSprite-startermanyopt",null,!1)):this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_STARTARROW],[mxConstants.ARROW_BLOCK],"geIcon geSprite geSprite-startblocktrans",null,!1).setAttribute("title",mxResources.get("block")))})),b=this.editorUi.toolbar.addMenuFunctionInContainer(x,"geSprite-endclassic",mxResources.get("lineend"),!1,mxUtils.bind(this,function(t){var e;"connector"!=d.style.shape&&"flexArrow"!=d.style.shape&&"filledEdge"!=d.style.shape||((e=this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.NONE,0],"geIcon",null,!1)).setAttribute("title",mxResources.get("none")),e.firstChild.firstChild.innerHTML='<font style="font-size:10px;">'+mxUtils.htmlEntities(mxResources.get("none"))+"</font>","connector"==d.style.shape||"filledEdge"==d.style.shape?(this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_CLASSIC,1],"geIcon geSprite geSprite-endclassic",null,!1).setAttribute("title",mxResources.get("classic")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_CLASSIC_THIN,1],"geIcon geSprite geSprite-endclassicthin",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_OPEN,0],"geIcon geSprite geSprite-endopen",null,!1).setAttribute("title",mxResources.get("openArrow")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_OPEN_THIN,0],"geIcon geSprite geSprite-endopenthin",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],["openAsync",0],"geIcon geSprite geSprite-endopenasync",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_BLOCK,1],"geIcon geSprite geSprite-endblock",null,!1).setAttribute("title",mxResources.get("block")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_BLOCK_THIN,1],"geIcon geSprite geSprite-endblockthin",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],["async",1],"geIcon geSprite geSprite-endasync",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_OVAL,1],"geIcon geSprite geSprite-endoval",null,!1).setAttribute("title",mxResources.get("oval")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_DIAMOND,1],"geIcon geSprite geSprite-enddiamond",null,!1).setAttribute("title",mxResources.get("diamond")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_DIAMOND_THIN,1],"geIcon geSprite geSprite-endthindiamond",null,!1).setAttribute("title",mxResources.get("diamondThin")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_CLASSIC,0],"geIcon geSprite geSprite-endclassictrans",null,!1).setAttribute("title",mxResources.get("classic")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_CLASSIC_THIN,0],"geIcon geSprite geSprite-endclassicthintrans",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_BLOCK,0],"geIcon geSprite geSprite-endblocktrans",null,!1).setAttribute("title",mxResources.get("block")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_BLOCK_THIN,0],"geIcon geSprite geSprite-endblockthintrans",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],["async",0],"geIcon geSprite geSprite-endasynctrans",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_OVAL,0],"geIcon geSprite geSprite-endovaltrans",null,!1).setAttribute("title",mxResources.get("oval")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_DIAMOND,0],"geIcon geSprite geSprite-enddiamondtrans",null,!1).setAttribute("title",mxResources.get("diamond")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],[mxConstants.ARROW_DIAMOND_THIN,0],"geIcon geSprite geSprite-endthindiamondtrans",null,!1).setAttribute("title",mxResources.get("diamondThin")),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],["dash",0],"geIcon geSprite geSprite-enddash",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],["cross",0],"geIcon geSprite geSprite-endcross",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],["circlePlus",0],"geIcon geSprite geSprite-endcircleplus",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],["circle",1],"geIcon geSprite geSprite-endcircle",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],["ERone",0],"geIcon geSprite geSprite-enderone",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],["ERmandOne",0],"geIcon geSprite geSprite-enderonetoone",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],["ERmany",0],"geIcon geSprite geSprite-endermany",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],["ERoneToMany",0],"geIcon geSprite geSprite-enderonetomany",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],["ERzeroToOne",1],"geIcon geSprite geSprite-enderoneopt",null,!1),this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW,"endFill"],["ERzeroToMany",1],"geIcon geSprite geSprite-endermanyopt",null,!1)):this.editorUi.menus.edgeStyleChange(t,"",[mxConstants.STYLE_ENDARROW],[mxConstants.ARROW_BLOCK],"geIcon geSprite geSprite-endblocktrans",null,!1).setAttribute("title",mxResources.get("block")))})),f=(this.addArrow(g,8),this.addArrow(L),this.addArrow(R),this.addArrow(b),this.addArrow(l,9)),T=(f.className="geIcon",f.style.width="84px",this.addArrow(h,9)),A=(T.className="geIcon",T.style.width="22px",document.createElement("div")),U=(A.style.width="85px",A.style.height="1px",A.style.borderBottom="1px solid "+this.defaultStrokeColor,A.style.marginBottom="9px",f.appendChild(A),document.createElement("div")),f=(U.style.width="23px",U.style.height="1px",U.style.borderBottom="1px solid "+this.defaultStrokeColor,U.style.marginBottom="9px",T.appendChild(U),l.style.height="15px",h.style.height="15px",g.style.height="15px",L.style.height="17px",R.style.marginLeft="3px",R.style.height="17px",b.style.marginLeft="3px",b.style.height="17px",t.appendChild(e),t.appendChild(c),t.appendChild(p),p.cloneNode(!1)),T=(f.style.paddingBottom="6px",f.style.paddingTop="4px",f.style.fontWeight="normal",document.createElement("div")),I=(T.style.position="absolute",T.style.marginLeft="3px",T.style.marginBottom="12px",T.style.marginTop="2px",T.style.fontWeight="normal",T.style.width="76px",mxUtils.write(T,mxResources.get("lineend")),f.appendChild(T),this.addUnitInput(f,"pt",74,33,function(){Y.apply(this,arguments)})),v=this.addUnitInput(f,"pt",20,33,function(){F.apply(this,arguments)}),l=(mxUtils.br(f),document.createElement("div")),N=(l.style.height="8px",f.appendChild(l),T=T.cloneNode(!1),mxUtils.write(T,mxResources.get("linestart")),f.appendChild(T),this.addUnitInput(f,"pt",74,33,function(){D.apply(this,arguments)})),_=this.addUnitInput(f,"pt",20,33,function(){M.apply(this,arguments)}),h=(mxUtils.br(f),this.addLabel(f,mxResources.get("spacing"),74,50),this.addLabel(f,mxResources.get("size"),20,50),mxUtils.br(f),e.cloneNode(!1));h.style.fontWeight="normal",h.style.position="relative",h.style.paddingLeft="16px",h.style.marginBottom="2px",h.style.marginTop="6px",h.style.borderWidth="0px",h.style.paddingBottom="18px";(T=document.createElement("div")).style.position="absolute",T.style.marginLeft="3px",T.style.marginBottom="12px",T.style.marginTop="1px",T.style.fontWeight="normal",T.style.width="120px",mxUtils.write(T,mxResources.get("perimeter")),h.appendChild(T);var O=this.addUnitInput(h,"pt",20,41,function(){P.apply(this,arguments)}),w=(d.edges.length==r.getSelectionCount()?(t.appendChild(x),mxClient.IS_QUIRKS&&(mxUtils.br(t),mxUtils.br(t)),t.appendChild(f)):d.vertices.length==r.getSelectionCount()&&(mxClient.IS_QUIRKS&&mxUtils.br(t),t.appendChild(h)),mxUtils.bind(this,function(t,e,n){d=this.format.getSelectionState();mxUtils.getValue(d.style,m,null);!n&&document.activeElement==y||(o=parseInt(mxUtils.getValue(d.style,mxConstants.STYLE_STROKEWIDTH,1)),y.value=isNaN(o)?"":o+" pt"),!n&&document.activeElement==S||(o=parseInt(mxUtils.getValue(d.style,mxConstants.STYLE_STROKEWIDTH,1)),S.value=isNaN(o)?"":o+" pt"),u.style.visibility="connector"==d.style.shape||"filledEdge"==d.style.shape?"":"hidden","1"==mxUtils.getValue(d.style,mxConstants.STYLE_CURVED,null)?u.value="curved":"1"==mxUtils.getValue(d.style,mxConstants.STYLE_ROUNDED,null)&&(u.value="rounded"),"1"==mxUtils.getValue(d.style,mxConstants.STYLE_DASHED,null)?null==mxUtils.getValue(d.style,mxConstants.STYLE_DASH_PATTERN,null)?A.style.borderBottom="1px dashed "+this.defaultStrokeColor:A.style.borderBottom="1px dotted "+this.defaultStrokeColor:A.style.borderBottom="1px solid "+this.defaultStrokeColor,U.style.borderBottom=A.style.borderBottom;var i=L.getElementsByTagName("div")[0],s=mxUtils.getValue(d.style,mxConstants.STYLE_EDGE,null),i=("orthogonalEdgeStyle"==(s="1"==mxUtils.getValue(d.style,mxConstants.STYLE_NOEDGESTYLE,null)?null:s)&&"1"==mxUtils.getValue(d.style,mxConstants.STYLE_CURVED,null)?i.className="geSprite geSprite-curved":i.className="straight"==s||"none"==s||null==s?"geSprite geSprite-straight":"entityRelationEdgeStyle"==s?"geSprite geSprite-entity":"elbowEdgeStyle"==s?"geSprite "+("vertical"==mxUtils.getValue(d.style,mxConstants.STYLE_ELBOW,null)?"geSprite-verticalelbow":"geSprite-horizontalelbow"):"isometricEdgeStyle"==s?"geSprite "+("vertical"==mxUtils.getValue(d.style,mxConstants.STYLE_ELBOW,null)?"geSprite-verticalisometric":"geSprite-horizontalisometric"):"geSprite geSprite-orthogonal",g.getElementsByTagName("div")[0]);function l(t,e,n,i){n=n.getElementsByTagName("div")[0];return n.className=a.getCssClassForMarker(i,d.style.shape,t,e),"geSprite geSprite-noarrow"==n.className&&(n.innerHTML=mxUtils.htmlEntities(mxResources.get("none")),n.style.backgroundImage="none",n.style.verticalAlign="top",n.style.marginTop="5px",n.style.fontSize="10px",n.style.filter="none",n.style.color=this.defaultStrokeColor,n.nextSibling.style.marginTop="0px"),n}"link"==d.style.shape?i.className="geSprite geSprite-linkedge":"flexArrow"==d.style.shape?i.className="geSprite geSprite-arrow":"arrow"==d.style.shape?i.className="geSprite geSprite-simplearrow":i.className="geSprite geSprite-connection",d.edges.length==r.getSelectionCount()?(c.style.display="",p.style.display="none"):(c.style.display="none",p.style.display="");var o,s=l(mxUtils.getValue(d.style,mxConstants.STYLE_STARTARROW,null),mxUtils.getValue(d.style,"startFill","1"),R,"start"),i=l(mxUtils.getValue(d.style,mxConstants.STYLE_ENDARROW,null),mxUtils.getValue(d.style,"endFill","1"),b,"end");"arrow"==d.style.shape?(s.className="geSprite geSprite-noarrow",i.className="geSprite geSprite-endblocktrans"):"link"==d.style.shape&&(s.className="geSprite geSprite-noarrow",i.className="geSprite geSprite-noarrow"),mxUtils.setOpacity(L,"arrow"==d.style.shape?30:100),"connector"!=d.style.shape&&"flexArrow"!=d.style.shape&&"filledEdge"!=d.style.shape?(mxUtils.setOpacity(R,30),mxUtils.setOpacity(b,30)):(mxUtils.setOpacity(R,100),mxUtils.setOpacity(b,100)),!n&&document.activeElement==_||(o=parseInt(mxUtils.getValue(d.style,mxConstants.STYLE_STARTSIZE,mxConstants.DEFAULT_MARKERSIZE)),_.value=isNaN(o)?"":o+" pt"),!n&&document.activeElement==N||(o=parseInt(mxUtils.getValue(d.style,mxConstants.STYLE_SOURCE_PERIMETER_SPACING,0)),N.value=isNaN(o)?"":o+" pt"),!n&&document.activeElement==v||(o=parseInt(mxUtils.getValue(d.style,mxConstants.STYLE_ENDSIZE,mxConstants.DEFAULT_MARKERSIZE)),v.value=isNaN(o)?"":o+" pt"),!n&&document.activeElement==N||(o=parseInt(mxUtils.getValue(d.style,mxConstants.STYLE_TARGET_PERIMETER_SPACING,0)),I.value=isNaN(o)?"":o+" pt"),!n&&document.activeElement==O||(o=parseInt(mxUtils.getValue(d.style,mxConstants.STYLE_PERIMETER_SPACING,0)),O.value=isNaN(o)?"":o+" pt")})),M=this.installInputHandler(_,mxConstants.STYLE_STARTSIZE,mxConstants.DEFAULT_MARKERSIZE,0,999," pt"),D=this.installInputHandler(N,mxConstants.STYLE_SOURCE_PERIMETER_SPACING,0,-999,999," pt"),F=this.installInputHandler(v,mxConstants.STYLE_ENDSIZE,mxConstants.DEFAULT_MARKERSIZE,0,999," pt"),Y=this.installInputHandler(I,mxConstants.STYLE_TARGET_PERIMETER_SPACING,0,-999,999," pt"),P=this.installInputHandler(O,mxConstants.STYLE_PERIMETER_SPACING,0,0,999," pt");return this.addKeyHandler(y,w),this.addKeyHandler(_,w),this.addKeyHandler(N,w),this.addKeyHandler(v,w),this.addKeyHandler(I,w),this.addKeyHandler(O,w),r.getModel().addListener(mxEvent.CHANGE,w),this.listeners.push({destroy:function(){r.getModel().removeListener(w)}}),w(),t},StyleFormatPanel.prototype.addLineJumps=function(t){var i=this.format.getSelectionState();if(Graph.lineJumpsEnabled&&0<i.edges.length&&0==i.vertices.length&&i.lineJumps){t.style.padding="8px 0px 24px 18px";for(var e=this.editorUi,n=e.editor.graph,s=document.createElement("div"),l=(s.style.position="absolute",s.style.fontWeight="bold",s.style.width="80px",mxUtils.write(s,mxResources.get("lineJumps")),t.appendChild(s),document.createElement("select")),o=(l.style.position="absolute",l.style.marginTop="-2px",l.style.right="76px",l.style.width="62px",["none","arc","gap","sharp"]),a=0;a<o.length;a++){var r=document.createElement("option");r.setAttribute("value",o[a]),mxUtils.write(r,mxResources.get(o[a])),l.appendChild(r)}mxEvent.addListener(l,"change",function(t){n.getModel().beginUpdate();try{n.setCellStyles("jumpStyle",l.value,n.getSelectionCells()),e.fireEvent(new mxEventObject("styleChanged","keys",["jumpStyle"],"values",[l.value],"cells",n.getSelectionCells()))}finally{n.getModel().endUpdate()}mxEvent.consume(t)}),mxEvent.addListener(l,"click",function(t){mxEvent.consume(t)}),t.appendChild(l);var d=this.addUnitInput(t,"pt",22,33,function(){u.apply(this,arguments)}),u=this.installInputHandler(d,"jumpSize",Graph.defaultJumpSize,0,999," pt"),m=mxUtils.bind(this,function(t,e,n){i=this.format.getSelectionState(),l.value=mxUtils.getValue(i.style,"jumpStyle","none"),!n&&document.activeElement==d||(n=parseInt(mxUtils.getValue(i.style,"jumpSize",Graph.defaultJumpSize)),d.value=isNaN(n)?"":n+" pt")});this.addKeyHandler(d,m),n.getModel().addListener(mxEvent.CHANGE,m),this.listeners.push({destroy:function(){n.getModel().removeListener(m)}}),m()}else t.style.display="none";return t},StyleFormatPanel.prototype.addEffects=function(i){var s,t=this.editorUi.editor.graph,e=(this.format.getSelectionState(),i.style.paddingTop="0px",i.style.paddingBottom="2px",document.createElement("table")),n=(mxClient.IS_QUIRKS&&(e.style.fontSize="1em"),e.style.width="100%",e.style.fontWeight="bold",e.style.paddingRight="20px",document.createElement("tbody")),l=document.createElement("tr"),o=(l.style.padding="0px",document.createElement("td")),a=(o.style.padding="0px",o.style.width="50%",o.setAttribute("valign","top"),o.cloneNode(!0)),r=(a.style.paddingLeft="8px",l.appendChild(o),l.appendChild(a),n.appendChild(l),e.appendChild(n),i.appendChild(e),o),d=0,u=mxUtils.bind(this,function(t,e,n){t=this.createCellOption(t,e,n);t.style.width="100%",r.appendChild(t),r=r==o?a:o,d++}),m=mxUtils.bind(this,function(t,e,n){s=this.format.getSelectionState(),o.innerHTML="",a.innerHTML="",r=o,(s.rounded||this.editorUi&&this.editorUi.editor&&this.editorUi.editor.graph&&this.editorUi.editor.graph.sceneComponent)&&u(mxResources.get("rounded"),mxConstants.STYLE_ROUNDED,0),"swimlane"==s.style.shape&&u(mxResources.get("divider"),"swimlaneLine",1),s.containsImage||u(mxResources.get("shadow"),mxConstants.STYLE_SHADOW,0),s.glass&&u(mxResources.get("glass"),mxConstants.STYLE_GLASS,0),s.comic&&u(mxResources.get("comic"),"comic",0),0==d&&(i.style.display="none")});return t.getModel().addListener(mxEvent.CHANGE,m),this.listeners.push({destroy:function(){t.getModel().removeListener(m)}}),m(),i},StyleFormatPanel.prototype.addStyleOps=function(t){t.style.paddingTop="10px",t.style.paddingBottom="10px";var e=mxUtils.button(mxResources.get("setAsDefaultStyle"),mxUtils.bind(this,function(t){this.editorUi.actions.get("setAsDefaultStyle").funct()}));return e.setAttribute("title",mxResources.get("setAsDefaultStyle")+" ("+this.editorUi.actions.get("setAsDefaultStyle").shortcut+")"),e.style.width="202px",t.appendChild(e),t},DiagramFormatPanel=function(t,e,n){BaseFormatPanel.call(this,t,e,n),this.init()},mxUtils.extend(DiagramFormatPanel,BaseFormatPanel),DiagramFormatPanel.showPageView=!0,DiagramFormatPanel.prototype.showBackgroundImageOption=!0,DiagramFormatPanel.prototype.init=function(){var t=this.editorUi.editor.graph;this.container.appendChild(this.addView(this.createPanel())),t.isEnabled()&&(this.container.appendChild(this.addOptions(this.createPanel())),this.container.appendChild(this.addPaperSize(this.createPanel())),this.container.appendChild(this.addStyleOps(this.createPanel())))},DiagramFormatPanel.prototype.addView=function(t){var e,n,i=this.editorUi,s=i.editor.graph;return t.appendChild(this.createTitle(mxResources.get("view"))),this.addGridOption(t),DiagramFormatPanel.showPageView&&t.appendChild(this.createOption(mxResources.get("pageView"),function(){return s.pageVisible},function(t){i.actions.get("pageView").funct()},{install:function(t){this.listener=function(){t(s.pageVisible)},i.addListener("pageViewChanged",this.listener)},destroy:function(){i.removeListener(this.listener)}})),s.isEnabled()&&(e=this.createColorOption(mxResources.get("background"),function(){return s.background},function(t){t=new ChangePageSetup(i,t);t.ignoreImage=!0,s.model.execute(t)},"#ffffff",{install:function(t){this.listener=function(){t(s.background)},i.addListener("backgroundColorChanged",this.listener)},destroy:function(){i.removeListener(this.listener)}},void 0,void 0,{graph:s,key:"PageSetup"}),this.showBackgroundImageOption&&((n=mxUtils.button(mxResources.get("image"),function(t){i.showBackgroundImageDialog(),mxEvent.consume(t)})).style.position="absolute",n.className="geColorBtn",n.style.marginTop="-4px",n.style.paddingBottom=11==document.documentMode||mxClient.IS_MT?"0px":"2px",n.style.height="22px",n.style.right=mxClient.IS_QUIRKS?"52px":"72px",n.style.width="56px",e.appendChild(n)),t.appendChild(e)),t},DiagramFormatPanel.prototype.addOptions=function(t){var e=this.editorUi,n=e.editor.graph;return t.appendChild(this.createTitle(mxResources.get("options"))),n.isEnabled()&&(t.appendChild(this.createOption(mxResources.get("connectionArrows"),function(){return n.connectionArrowsEnabled},function(t){e.actions.get("connectionArrows").funct()},{install:function(t){this.listener=function(){t(n.connectionArrowsEnabled)},e.addListener("connectionArrowsChanged",this.listener)},destroy:function(){e.removeListener(this.listener)}})),t.appendChild(this.createOption(mxResources.get("connectionPoints"),function(){return n.connectionHandler.isEnabled()},function(t){e.actions.get("connectionPoints").funct()},{install:function(t){this.listener=function(){t(n.connectionHandler.isEnabled())},e.addListener("connectionPointsChanged",this.listener)},destroy:function(){e.removeListener(this.listener)}})),t.appendChild(this.createOption(mxResources.get("guides"),function(){return n.graphHandler.guidesEnabled},function(t){e.actions.get("guides").funct()},{install:function(t){this.listener=function(){t(n.graphHandler.guidesEnabled)},e.addListener("guidesEnabledChanged",this.listener)},destroy:function(){e.removeListener(this.listener)}}))),t},DiagramFormatPanel.prototype.addGridOption=function(t){var n=this,i=this.editorUi,s=i.editor.graph,l=document.createElement("input"),o=(l.style.position="absolute",l.style.textAlign="right",l.style.width="38px",l.value=this.inUnit(s.getGridSize())+" "+this.getUnit(),this.createStepper(l,e,this.getUnitStep(),null,null,null,this.isFloatUnit()));function e(t){var e=(n.isFloatUnit()?parseFloat:parseInt)(l.value);(e=n.fromUnit(Math.max(n.inUnit(1),isNaN(e)?n.inUnit(10):e)))!=s.getGridSize()&&s.setGridSize(e),l.value=n.inUnit(e)+" "+n.getUnit(),mxEvent.consume(t)}l.style.display=s.isGridEnabled()?"":"none",o.style.display=l.style.display,mxEvent.addListener(l,"keydown",function(t){13==t.keyCode?(s.container.focus(),mxEvent.consume(t)):27==t.keyCode&&(l.value=s.getGridSize(),s.container.focus(),mxEvent.consume(t))}),mxEvent.addListener(l,"blur",e),mxEvent.addListener(l,"change",e);function a(t,e){l.value=n.inUnit(s.getGridSize())+" "+n.getUnit(),n.format.refresh()}var r;s.view.addListener("unitChanged",a),this.listeners.push({destroy:function(){s.view.removeListener(a)}}),mxClient.IS_SVG?(l.style.marginTop="-2px",l.style.right="84px",o.style.marginTop="-16px",o.style.right="72px",(r=this.createColorOption(mxResources.get("grid"),function(){var t=s.view.gridColor;return s.isGridEnabled()?t:null},function(t){var e=s.isGridEnabled();t==mxConstants.NONE?s.setGridEnabled(!1):(s.setGridEnabled(!0),i.setGridColor(t)),l.style.display=s.isGridEnabled()?"":"none",o.style.display=l.style.display,e!=s.isGridEnabled()&&i.fireEvent(new mxEventObject("gridEnabledChanged"))},"#e0e0e0",{install:function(t){this.listener=function(){t(s.isGridEnabled()?s.view.gridColor:null)},i.addListener("gridColorChanged",this.listener),i.addListener("gridEnabledChanged",this.listener)},destroy:function(){i.removeListener(this.listener)}},void 0,void 0,{graph:s,key:"color"})).appendChild(l),r.appendChild(o),t.appendChild(r)):(l.style.marginTop="2px",l.style.right="32px",o.style.marginTop="2px",o.style.right="20px",t.appendChild(l),t.appendChild(o),t.appendChild(this.createOption(mxResources.get("grid"),function(){return s.isGridEnabled()},function(t){s.setGridEnabled(t),s.isGridEnabled()&&(s.view.gridColor="#e0e0e0"),i.fireEvent(new mxEventObject("gridEnabledChanged"))},{install:function(t){this.listener=function(){l.style.display=s.isGridEnabled()?"":"none",o.style.display=l.style.display,t(s.isGridEnabled())},i.addListener("gridEnabledChanged",this.listener)},destroy:function(){i.removeListener(this.listener)}})))},DiagramFormatPanel.prototype.addDocumentProperties=function(t){this.editorUi.editor.graph;return t.appendChild(this.createTitle(mxResources.get("options"))),t},DiagramFormatPanel.prototype.addPaperSize=function(t){function e(){s.set(i.pageFormat)}var n=this.editorUi,i=n.editor.graph,s=(t.appendChild(this.createTitle(mxResources.get("paperSize"))),PageSetupDialog.addPageFormatPanel(t,"formatpanel",i.pageFormat,function(t){null!=i.pageFormat&&i.pageFormat.width==t.width&&i.pageFormat.height==t.height||((t=new ChangePageSetup(n,null,null,t)).ignoreColor=!0,t.ignoreImage=!0,i.model.execute(t))}));this.addKeyHandler(s.widthInput,function(){s.set(i.pageFormat)}),this.addKeyHandler(s.heightInput,function(){s.set(i.pageFormat)});return n.addListener("pageFormatChanged",e),this.listeners.push({destroy:function(){n.removeListener(e)}}),i.getModel().addListener(mxEvent.CHANGE,e),this.listeners.push({destroy:function(){i.getModel().removeListener(e)}}),t},DiagramFormatPanel.prototype.addStyleOps=function(t){var e=mxUtils.button(mxResources.get("editData"),mxUtils.bind(this,function(t){this.editorUi.actions.get("editData").funct()}));return e.setAttribute("title",mxResources.get("editData")+" ("+this.editorUi.actions.get("editData").shortcut+")"),e.style.width="202px",e.style.marginBottom="2px",t.appendChild(e),mxUtils.br(t),(e=mxUtils.button(mxResources.get("clearDefaultStyle"),mxUtils.bind(this,function(t){this.editorUi.actions.get("clearDefaultStyle").funct()}))).setAttribute("title",mxResources.get("clearDefaultStyle")+" ("+this.editorUi.actions.get("clearDefaultStyle").shortcut+")"),e.style.width="202px",t.appendChild(e),t},DiagramFormatPanel.prototype.destroy=function(){BaseFormatPanel.prototype.destroy.apply(this,arguments),this.gridEnabledListener&&(this.editorUi.removeListener(this.gridEnabledListener),this.gridEnabledListener=null)};