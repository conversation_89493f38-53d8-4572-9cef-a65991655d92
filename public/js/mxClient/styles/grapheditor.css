.geEditor {
  font-family: Helvetica, Arial, sans-serif;
  font-size: 14px;
  border: none;
  margin: 0px;
}
.geEditor input[type='text']::-ms-clear {
  display: none;
}
.geEditor input,
select,
textarea,
button {
  font-size: inherit;
}
.geEditor input {
  border-width: 2px;
}
.geEditor select {
  border-width: 1px;
}
.geEditor div.mxTooltip {
  background: whiteSmoke;
  border-color: lightGray;
  font-size: 11px;
  color: black;
  padding: 6px;
}
.geMenubarContainer .geItem,
.geToolbar .geButton,
.geToolbar .geLabel {
  cursor: pointer !important;
}
.scene-left-container .geTitle {
  cursor: default !important;
}
.geBackgroundPage {
  box-shadow: 0px 0px 2px 1px #d1d1d1;
}
.scene-left-container a,
.geMenubarContainer a,
.geToolbar a {
  color: #000000;
  text-decoration: none;
}
.scene-left-container {
  overflow: hidden;
  cursor: default;
}
.geMenubarContainer,
.geToolbarContainer,
.geFooterContainer,
.geHsplit,
.geVsplit {
  overflow: hidden;
  position: absolute;
  cursor: default;
}
.geDiagramContainer {
  overflow: hidden;
  position: absolute;
  cursor: default;
  /* width:100%;
  height:100%; */
}
.geFormatContainer {
  overflow-x: hidden !important;
  overflow-y: auto !important;
  font-size: 12px;
  border-left: 1px solid #dadce0;
}
.geSidebarFooter {
  border-top: 1px solid #dadce0;
}
.geFormatSection {
  border-bottom: 1px solid #dadce0;
  border-color: #dadce0;
}
.weapp-de-render-content .geDiagramContainer,
.scene-main-container .geDiagramContainer {
  background-color: #ffffff;
  font-size: 0px;
  outline: none;
}
.geMenubar,
.geToolbar {
  white-space: nowrap;
  display: block;
  width: 100%;
}
.geMenubarContainer .geItem,
.geToolbar .geButton,
.geToolbar .geLabel,
.geSidebar,
.scene-left-container .geTitle,
.geSidebar .geItem,
.mxPopupMenuItem {
  -webkit-transition: all 0.1s ease-in-out;
  -moz-transition: all 0.1s ease-in-out;
  -o-transition: all 0.1s ease-in-out;
  -ms-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
}
.geHint {
  background-color: #ffffff;
  border: 1px solid gray;
  padding: 4px 16px 4px 16px;
  border-radius: 3px;
  -webkit-box-shadow: 1px 1px 2px 0px #ddd;
  -moz-box-shadow: 1px 1px 2px 0px #ddd;
  box-shadow: 1px 1px 2px 0px #ddd;
  opacity: 0.8;
  filter: alpha(opacity=80);
  font-size: 9pt;
}
.geStatusAlert {
  white-space: nowrap;
  margin-top: -5px;
  font-size: 12px;
  padding: 4px 6px 4px 6px;
  background-color: #f2dede;
  border: 1px solid #ebccd1;
  color: #a94442 !important;
  border-radius: 3px;
}
.geStatusAlert:hover {
  background-color: #f1d8d8;
  border-color: #d6b2b8;
}
.geStatusMessage {
  white-space: nowrap;
  margin-top: -5px;
  padding: 4px 6px 4px 6px;
  font-size: 12px;
  background: -webkit-linear-gradient(top, #dff0d8 0, #c8e5bc 100%);
  background: -o-linear-gradient(top, #dff0d8 0, #c8e5bc 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(#dff0d8), to(#c8e5bc));
  background: linear-gradient(to bottom, #dff0d8 0, #c8e5bc 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffdff0d8', endColorstr='#ffc8e5bc', GradientType=0);
  background-repeat: repeat-x;
  border: 1px solid #b2dba1;
  border-radius: 3px;
  color: #3c763d !important;
}
.geAlert {
  position: absolute;
  white-space: nowrap;
  padding: 14px;
  background-color: #f2dede;
  border: 1px solid #ebccd1;
  color: #a94442;
  border-radius: 3px;
  -webkit-box-shadow: 2px 2px 3px 0px #ddd;
  -moz-box-shadow: 2px 2px 3px 0px #ddd;
  box-shadow: 2px 2px 3px 0px #ddd;
}
.geBtn,
.mxWindow .geBtn {
  background-image: none;
  background-color: #f5f5f5;
  border-radius: 2px;
  border: 1px solid #d8d8d8;
  color: #333;
  cursor: default;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.25px;
  height: 29px;
  line-height: 27px;
  margin: 0 0 0 8px;
  min-width: 72px;
  outline: 0;
  padding: 0 8px;
  cursor: pointer;
}
.geBtn:hover,
.geBtn:focus {
  -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
  border: 1px solid #c6c6c6;
  background-color: #f8f8f8;
  background-image: linear-gradient(#f8f8f8 0px, #f1f1f1 100%);
  color: #111;
}
.geBtn:active,
.geStatus:active {
  opacity: 0.7;
}
.geBtn:disabled {
  opacity: 0.5;
}
.geToolbarContainer > .geToolbar > div > a:active {
  opacity: 0.5;
}
.geBtnUp {
  background-image: url(data:image/gif;base64,R0lGODlhCgAGAJECAGZmZtXV1f///wAAACH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IE1hY2ludG9zaCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo0QzM3ODJERjg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo0QzM3ODJFMDg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjRDMzc4MkREODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjRDMzc4MkRFODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkEAQAAAgAsAAAAAAoABgAAAg6UjwiQBhGYglCKhXFLBQA7);
  _background-image: url(up.gif);
  background-position: center center;
  background-repeat: no-repeat;
}
.geBtnUp:active {
  background-color: #4d90fe;
  background-image: linear-gradient(#4d90fe 0px, #357ae8 100%);
}
.geBtnDown {
  background-image: url(data:image/gif;base64,R0lGODlhCgAGAJECANXV1WZmZv///wAAACH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IE1hY2ludG9zaCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo0QzM3ODJEQjg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo0QzM3ODJEQzg4NUQxMUU0OTFEQ0E2MzRGQzcwNUY3NCI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjRDMzc4MkQ5ODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjRDMzc4MkRBODg1RDExRTQ5MURDQTYzNEZDNzA1Rjc0Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkEAQAAAgAsAAAAAAoABgAAAg6UjxLLewEiCAnOZBzeBQA7);
  _background-image: url(down.gif);
  background-position: center center;
  background-repeat: no-repeat;
}
.geBtnDown:active {
  background-color: #4d90fe;
  background-image: linear-gradient(#4d90fe 0px, #357ae8 100%);
}
.geColorBtn {
  background-color: #f5f5f5;
  background-image: linear-gradient(#f5f5f5 0px, #e1e1e1 100%);
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.5);
  color: #333;
  cursor: default;
  margin: 0px;
  outline: 0;
  padding: 0px;
  cursor: pointer;
}
.geColorBtn:hover {
  -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.7);
}
.geColorBtn:active {
  background-color: #4d90fe;
  background-image: linear-gradient(#4d90fe 0px, #357ae8 100%);
  border: 1px solid #2f5bb7;
  color: #fff;
}
.geColorBtn:disabled {
  opacity: 0.5;
}
.gePrimaryBtn,
.mxWindow .gePrimaryBtn {
  background-color: #4d90fe;
  background-image: linear-gradient(#4d90fe 0px, #4787ed 100%);
  border: 1px solid #3079ed;
  color: #fff;
}
.gePrimaryBtn:hover,
.gePrimaryBtn:focus {
  background-color: #357ae8;
  background-image: linear-gradient(#4d90fe 0px, #357ae8 100%);
  border: 1px solid #2f5bb7;
  color: #fff;
}
.gePrimaryBtn:disabled {
  opacity: 0.5;
}
.geAlertLink {
  color: #843534;
  font-weight: 700;
  text-decoration: none;
}
.geActivePage {
  font-weight: bold;
  color: #188038 !important;
}
.geMenubarContainer,
.geToolbarContainer,
.geHsplit,
.geVsplit {
  background-color: #fbfbfb;
}
.geMenubar {
  padding: 0px 2px 0px 2px;
  vertical-align: middle;
}
.geMenubarContainer .geItem,
.geToolbar .geItem {
  padding: 6px 6px 6px 9px;
  cursor: default;
}
.geMenubarContainer .geItem:hover {
  background: #eee;
  border-radius: 2px;
}
.geMenubarContainer .geItem:active {
  background: #f8c382;
}
.geToolbarContainer .geButton:hover {
  opacity: 1;
  _filter: none !important;
  background: #eee;
  border-radius: 2px;
}
.geToolbarContainer .geButton:active,
.geToolbarContainer .geLabel:active {
  background: #f8c382;
}
.geToolbarContainer .geLabel:hover {
  background: #eee;
  border-radius: 2px;
}
.geToolbarButton {
  opacity: 0.6;
}
.geToolbarButton:active {
  opacity: 0.2;
}
.mxDisabled:hover {
  background: inherit !important;
}
.geMenubar a.geStatus {
  color: #888888;
  padding-left: 12px;
  display: inline-block;
  cursor: default !important;
}
.geMenubar a.geStatus:hover {
  background: transparent;
}
.geToolbarContainer {
  border-bottom: 1px solid #dadce0;
}
.scene-left-container .geToolbarContainer {
  background: transparent;
  border-bottom: none;
}
.scene-left-container button {
  text-overflow: ellipsis;
  overflow: hidden;
}
.geToolbar {
  padding: 1px 0px 0px 6px;
  border-top: 1px solid #dadce0;
  -webkit-box-shadow: inset 0 1px 0 0 #fff;
  -moz-box-shadow: inset 0 1px 0 0 #fff;
  box-shadow: inset 0 1px 0 0 #fff;
  padding-top: 5px;
}
.geToolbarContainer .geSeparator {
  float: left;
  width: 1px;
  height: 20px;
  background: #e5e5e5;
  margin-left: 6px;
  margin-right: 6px;
  margin-top: 4px;
}
.geToolbarContainer .geButton {
  float: left;
  width: 20px;
  height: 20px;
  padding: 0px 2px 4px 2px;
  margin: 2px;
  border: 1px solid transparent;
  cursor: pointer;
  opacity: 0.6;
  _filter: alpha(opacity=60);
}
div.mxWindow .geButton {
  margin: -1px 2px 2px 2px;
  padding: 1px 2px 2px 1px;
}
.geToolbarContainer .geLabel {
  float: left;
  margin: 2px;
  cursor: pointer;
  padding: 3px 5px 3px 5px;
  border: 1px solid transparent;
}
.geToolbarContainer .mxDisabled:hover {
  border: 1px solid transparent !important;
  opacity: 0.2 !important;
  filter: alpha(opacity=20) !important;
}
.geDiagramBackdrop {
  background-color: #f8f9fa;
}
.scene-left-container {
  background: #fbfbfb;
  overflow: hidden;
  /* position:absolute; */
  overflow: auto;
}
.geTabContainer {
  border-top: 1px solid #e5e5e5;
  background-color: #f1f3f4;
}
.geSidebar {
  border-bottom: 1px solid #e5e5e5;
  padding: 6px;
  padding-left: 10px;
  _padding: 2px;
  _padding-left: 6px;
  padding-bottom: 6px;
  overflow: hidden;
}
.scene-left-container .geTitle {
  display: block;
  font-size: 13px;
  border-bottom: 1px solid #e5e5e5;
  font-weight: 500;
  padding: 8px 0px 8px 14px;
  margin: 0px;
  cursor: default;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4em;
}
.scene-left-container .geTitle:hover {
  background: #eee;
  border-radius: 2px;
}
.scene-left-container .geTitle:active {
  background-color: #f8c382;
}
.scene-left-container .geDropTarget {
  border-radius: 10px;
  border: 2px dotted #b0b0b0;
  text-align: center;
  padding: 6px;
  margin: 6px;
  color: #a0a0a0;
  fontsize: 13px;
}
.geTitle img {
  opacity: 0.5;
  _filter: alpha(opacity=50);
}
.geTitle img:hover {
  opacity: 1;
  _filter: alpha(opacity=100);
}
.geTitle .geButton {
  border: 1px solid transparent;
  padding: 3px;
  border-radius: 2px;
}
.geTitle .geButton:hover {
  border: 1px solid gray;
}
.geSidebar .geItem {
  display: inline-block;
  background-repeat: no-repeat;
  background-position: 50% 50%;
  border-radius: 8px;
}
.geSidebar .geItem:hover {
  background-color: #e0e0e0;
}
.geItem {
  vertical-align: top;
  display: inline-block;
}
.geSidebarTooltip {
  position: absolute;
  background: #fbfbfb;
  overflow: hidden;
  box-shadow: 0 2px 6px 2px rgba(60, 64, 67, 0.15);
  border-radius: 6px;
  _filter: progid:DXImageTransform.Microsoft.DropShadow(OffX=2, OffY=2, Color='#d5d5d5', Positive='true');
}
.geFooterContainer {
  background: #e5e5e5;
  border-top: 1px solid #c0c0c0;
}
.geFooterContainer a {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  white-space: nowrap;
  font-size: 14px;
  color: #235695;
  font-weight: bold;
  text-decoration: none;
}
.geFooterContainer table {
  border-collapse: collapse;
  margin: 0 auto;
}
.geFooterContainer td {
  border-left: 1px solid #c0c0c0;
  border-right: 1px solid #c0c0c0;
}
.geFooterContainer td:hover {
  background-color: #b3b3b3;
}
.geHsplit {
  cursor: col-resize;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAHBAMAAADdS/HjAAAAGFBMVEUzMzP///9tbW1QUFCKiopBQUF8fHxfX1/IXlmXAAAAHUlEQVQImWMQEGAQFWUQFmYQF2cQEmIQE2MQEQEACy4BF67hpEwAAAAASUVORK5CYII=);
  _background-image: url('thumb_vertical.png');
  background-repeat: no-repeat;
  background-position: center center;
}
.geVsplit {
  font-size: 1pt;
  cursor: row-resize;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAEBAMAAACw6DhOAAAAGFBMVEUzMzP///9tbW1QUFCKiopBQUF8fHxfX1/IXlmXAAAAFElEQVQImWNgNVdzYBAUFBRggLMAEzYBy29kEPgAAAAASUVORK5CYII=);
  _background-image: url('thumb_horz.png');
  background-repeat: no-repeat;
  background-position: center center;
}
.geHsplit {
  border-left: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
}
.geVSplit {
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
}
.geHsplit:hover,
.geVsplit:hover {
  background-color: #e0e0e0;
}
.geDialog {
  position: absolute;
  background: white;
  line-height: 1em;
  overflow: hidden;
  padding: 30px;
  border: 1px solid #acacac;
  -webkit-box-shadow: 0px 0px 2px 2px #d5d5d5;
  -moz-box-shadow: 0px 0px 2px 2px #d5d5d5;
  box-shadow: 0px 0px 2px 2px #d5d5d5;
  _filter: progid:DXImageTransform.Microsoft.DropShadow(OffX=2, OffY=2, Color='#d5d5d5', Positive='true');
  z-index: 2;
}
.geTransDialog {
  position: absolute;
  overflow: hidden;
  padding: 30px;
  z-index: 2;
}
.geDialogClose {
  position: absolute;
  width: 9px;
  height: 9px;
  opacity: 0.5;
  cursor: pointer;
  _filter: alpha(opacity=50);
}
.geDialogClose:hover {
  opacity: 1;
}
.geDialogTitle {
  box-sizing: border-box;
  white-space: nowrap;
  background: rgb(229, 229, 229);
  border-bottom: 1px solid rgb(192, 192, 192);
  font-size: 15px;
  font-weight: bold;
  text-align: center;
  color: rgb(35, 86, 149);
}
.geDialogFooter {
  background: whiteSmoke;
  white-space: nowrap;
  text-align: right;
  box-sizing: border-box;
  border-top: 1px solid #e5e5e5;
  color: darkGray;
}
.geSprite {
  background: url('data:image/png;base64,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')
    no-repeat;
  _background: url('sprites.png') no-repeat top left;
  width: 21px;
  height: 21px;
}
.geBaseButton {
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #c0c0c0;
  cursor: pointer;
  background-color: #ececec;
  background-image: linear-gradient(#ececec 0%, #fcfcfc 100%);
}
.geBaseButton:hover {
  background: #ececec;
}
.geBigButton {
  color: #ffffff;
  border: none;
  padding: 4px 10px;
  font-size: 14px;
  white-space: nowrap;
  border-radius: 3px;
  background-color: #0052cc;
  currsor: pointer;
  transition: background-color 0.1s ease-out;
  overflow: hidden;
  text-overflow: ellipsis;
}
.geBigButton:hover {
  background-color: #0065ff;
}
.geBigButton:active {
  background-color: #0747a6;
}
html body .geBigStandardButton {
  color: #344563;
  background-color: rgba(9, 30, 66, 0.08);
}
html body .geBigStandardButton:hover {
  background-color: rgba(9, 30, 66, 0.13);
}
html body .geBigStandardButton:active {
  background-color: #f8c382;
  color: #600000;
}
@media print {
  div.geNoPrint {
    display: none !important;
  }
}
.geSprite-actualsize {
  background-position: 0 0;
}
.geSprite-bold {
  background-position: 0 -46px;
}
.geSprite-bottom {
  background-position: 0 -92px;
}
.geSprite-center {
  background-position: 0 -138px;
}
.geSprite-delete {
  background-position: 0 -184px;
}
.geSprite-fillcolor {
  background-position: 0 -229px;
}
.geSprite-fit {
  background-position: 0 -277px;
}
.geSprite-fontcolor {
  background-position: 0 -322px;
}
.geSprite-gradientcolor {
  background-position: 0 -368px;
}
.geSprite-image {
  background-position: 0 -414px;
}
.geSprite-italic {
  background-position: 0 -460px;
}
.geSprite-left {
  background-position: 0 -505px;
}
.geSprite-middle {
  background-position: 0 -552px;
}
.geSprite-print {
  background-position: 0 -598px;
}
.geSprite-redo {
  background-position: 0 -644px;
}
.geSprite-right {
  background-position: 0 -689px;
}
.geSprite-shadow {
  background-position: 0 -735px;
}
.geSprite-strokecolor {
  background-position: 0 -782px;
}
.geSprite-top {
  background-position: 0 -828px;
}
.geSprite-underline {
  background-position: 0 -874px;
}
.geSprite-undo {
  background-position: 0 -920px;
}
.geSprite-zoomin {
  background-position: 0 -966px;
}
.geSprite-zoomout {
  background-position: 0 -1012px;
}
.geSprite-arrow {
  background-position: 0 -1059px;
}
.geSprite-linkedge {
  background-position: 0 -1105px;
}
.geSprite-straight {
  background-position: 0 -1150px;
}
.geSprite-entity {
  background-position: 0 -1196px;
}
.geSprite-orthogonal {
  background-position: 0 -1242px;
}
.geSprite-curved {
  background-position: 0 -1288px;
}
.geSprite-noarrow {
  background-position: 0 -1334px;
}
.geSprite-endclassic {
  background-position: 0 -1380px;
}
.geSprite-endopen {
  background-position: 0 -1426px;
}
.geSprite-endblock {
  background-position: 0 -1472px;
}
.geSprite-endoval {
  background-position: 0 -1518px;
}
.geSprite-enddiamond {
  background-position: 0 -1564px;
}
.geSprite-endthindiamond {
  background-position: 0 -1610px;
}
.geSprite-endclassictrans {
  background-position: 0 -1656px;
}
.geSprite-endblocktrans {
  background-position: 0 -1702px;
}
.geSprite-endovaltrans {
  background-position: 0 -1748px;
}
.geSprite-enddiamondtrans {
  background-position: 0 -1794px;
}
.geSprite-endthindiamondtrans {
  background-position: 0 -1840px;
}
.geSprite-startclassic {
  background-position: 0 -1886px;
}
.geSprite-startopen {
  background-position: 0 -1932px;
}
.geSprite-startblock {
  background-position: 0 -1978px;
}
.geSprite-startoval {
  background-position: 0 -2024px;
}
.geSprite-startdiamond {
  background-position: 0 -2070px;
}
.geSprite-startthindiamond {
  background-position: 0 -2116px;
}
.geSprite-startclassictrans {
  background-position: 0 -2162px;
}
.geSprite-startblocktrans {
  background-position: 0 -2208px;
}
.geSprite-startovaltrans {
  background-position: 0 -2254px;
}
.geSprite-startdiamondtrans {
  background-position: 0 -2300px;
}
.geSprite-startthindiamondtrans {
  background-position: 0 -2346px;
}
.geSprite-globe {
  background-position: 0 -2392px;
}
.geSprite-orderedlist {
  background-position: 0 -2438px;
}
.geSprite-unorderedlist {
  background-position: 0 -2484px;
}
.geSprite-horizontalrule {
  background-position: 0 -2530px;
}
.geSprite-link {
  background-position: 0 -2576px;
}
.geSprite-indent {
  background-position: 0 -2622px;
}
.geSprite-outdent {
  background-position: 0 -2668px;
}
.geSprite-code {
  background-position: 0 -2714px;
}
.geSprite-fontbackground {
  background-position: 0 -2760px;
}
.geSprite-removeformat {
  background-position: 0 -2806px;
}
.geSprite-superscript {
  background-position: 0 -2852px;
}
.geSprite-subscript {
  background-position: 0 -2898px;
}
.geSprite-table {
  background-position: 0 -2944px;
}
.geSprite-deletecolumn {
  background-position: 0 -2990px;
}
.geSprite-deleterow {
  background-position: 0 -3036px;
}
.geSprite-insertcolumnafter {
  background-position: 0 -3082px;
}
.geSprite-insertcolumnbefore {
  background-position: 0 -3128px;
}
.geSprite-insertrowafter {
  background-position: 0 -3174px;
}
.geSprite-insertrowbefore {
  background-position: 0 -3220px;
}
.geSprite-grid {
  background-position: 0 -3272px;
}
.geSprite-guides {
  background-position: 0 -3324px;
}
.geSprite-dots {
  background-position: 0 -3370px;
}
.geSprite-alignleft {
  background-position: 0 -3416px;
}
.geSprite-alignright {
  background-position: 0 -3462px;
}
.geSprite-aligncenter {
  background-position: 0 -3508px;
}
.geSprite-aligntop {
  background-position: 0 -3554px;
}
.geSprite-alignbottom {
  background-position: 0 -3600px;
}
.geSprite-alignmiddle {
  background-position: 0 -3646px;
}
.geSprite-justifyfull {
  background-position: 0 -3692px;
}
.geSprite-formatpanel {
  background-position: 0 -3738px;
}
.geSprite-connection {
  background-position: 0 -3784px;
}
.geSprite-vertical {
  background-position: 0 -3830px;
}
.geSprite-simplearrow {
  background-position: 0 -3876px;
}
.geSprite-plus {
  background-position: 0 -3922px;
}
.geSprite-rounded {
  background-position: 0 -3968px;
}
.geSprite-toback {
  background-position: 0 -4014px;
}
.geSprite-tofront {
  background-position: 0 -4060px;
}
.geSprite-duplicate {
  background-position: 0 -4106px;
}
.geSprite-insert {
  background-position: 0 -4152px;
}
.geSprite-endblockthin {
  background-position: 0 -4201px;
}
.geSprite-endblockthintrans {
  background-position: 0 -4247px;
}
.geSprite-enderone {
  background-position: 0 -4293px;
}
.geSprite-enderonetoone {
  background-position: 0 -4339px;
}
.geSprite-enderonetomany {
  background-position: 0 -4385px;
}
.geSprite-endermany {
  background-position: 0 -4431px;
}
.geSprite-enderoneopt {
  background-position: 0 -4477px;
}
.geSprite-endermanyopt {
  background-position: 0 -4523px;
}
.geSprite-endclassicthin {
  background-position: 0 -4938px;
}
.geSprite-endclassicthintrans {
  background-position: 0 -4984px;
}
.geSprite-enddash {
  background-position: 0 -5029px;
}
.geSprite-endcircleplus {
  background-position: 0 -5075px;
}
.geSprite-endcircle {
  background-position: 0 -5121px;
}
.geSprite-endasync {
  background-position: 0 -5167px;
}
.geSprite-endasynctrans {
  background-position: 0 -5213px;
}
.geSprite-startblockthin {
  background-position: 0 -4569px;
}
.geSprite-startblockthintrans {
  background-position: 0 -4615px;
}
.geSprite-starterone {
  background-position: 0 -4661px;
}
.geSprite-starteronetoone {
  background-position: 0 -4707px;
}
.geSprite-starteronetomany {
  background-position: 0 -4753px;
}
.geSprite-startermany {
  background-position: 0 -4799px;
}
.geSprite-starteroneopt {
  background-position: 0 -4845px;
}
.geSprite-startermanyopt {
  background-position: 0 -4891px;
}
.geSprite-startclassicthin {
  background-position: 0 -5259px;
}
.geSprite-startclassicthintrans {
  background-position: 0 -5305px;
}
.geSprite-startdash {
  background-position: 0 -5351px;
}
.geSprite-startcircleplus {
  background-position: 0 -5397px;
}
.geSprite-startcircle {
  background-position: 0 -5443px;
}
.geSprite-startasync {
  background-position: 0 -5489px;
}
.geSprite-startasynctrans {
  background-position: 0 -5535px;
}
.geSprite-startcross {
  background-position: 0 -5581px;
}
.geSprite-startopenthin {
  background-position: 0 -5627px;
}
.geSprite-startopenasync {
  background-position: 0 -5673px;
}
.geSprite-endcross {
  background-position: 0 -5719px;
}
.geSprite-endopenthin {
  background-position: 0 -5765px;
}
.geSprite-endopenasync {
  background-position: 0 -5811px;
}
.geSprite-verticalelbow {
  background-position: 0 -5857px;
}
.geSprite-horizontalelbow {
  background-position: 0 -5903px;
}
.geSprite-horizontalisometric {
  background-position: 0 -5949px;
}
.geSprite-verticalisometric {
  background-position: 0 -5995px;
}
html div.mxRubberband {
  border-color: #0000dd;
  background: #99ccff;
  position: absolute;
}
td.mxPopupMenuIcon div {
  width: 16px;
  height: 16px;
}
.geEditor div.mxPopupMenu {
  box-shadow: 0 2px 6px 2px rgba(60, 64, 67, 0.15);
  background: white;
  border-radius: 4px;
  border: none;
  padding: 3px;
}
html table.mxPopupMenu {
  border-collapse: collapse;
  margin: 0px;
}
html td.mxPopupMenuItem {
  padding: 7px 30px 7px 30px;
  font-family: Helvetica Neue, Helvetica, Arial Unicode MS, Arial;
  font-size: 10pt;
}
html td.mxPopupMenuIcon {
  background-color: white;
  padding: 0px;
}
td.mxPopupMenuIcon .geIcon {
  padding: 2px;
  padding-bottom: 4px;
  margin: 2px;
  border: 1px solid transparent;
  opacity: 0.5;
  _width: 26px;
  _height: 26px;
}
td.mxPopupMenuIcon .geIcon:hover {
  border: 1px solid gray;
  border-radius: 2px;
  opacity: 1;
}
html tr.mxPopupMenuItemHover {
  background-color: #eeeeee;
  color: black;
}
table.mxPopupMenu hr {
  color: #cccccc;
  background-color: #cccccc;
  border: none;
  height: 1px;
}
table.mxPopupMenu tr {
  font-size: 4pt;
}
html td.mxWindowTitle {
  font-family: Helvetica Neue, Helvetica, Arial Unicode MS, Arial;
  text-align: left;
  font-size: 12px;
  color: rgb(112, 112, 112);
  padding: 4px;
}

.geMenubarContainer,
.geFormatContainer,
.geFooterContainer,
.geToolbarContainer,
.geSidebarContainer,
.mxPopupMenu,
.geHsplit {
  display: none;
}
.mxCellEditor {
  font-size: initial;
}
/* .scene-left-container {
  z-index: 100;
  left: 100px !important;
} */

/* 选择图形/链接 文字重叠效果 */
foreignObject.active > div > div {
  border: 1px solid #0270c1;
}
foreignObject.textNumber {
  /* visibility: hidden; */
}
foreignObject.textNumber > div > div {
  position: relative;
}
foreignObject.textNumber > div > div > div {
  background: #0670c1 !important;
  border-radius: 6px;
  color: #fff !important;
  height: 16px;
  line-height: 16px !important;
  padding: 0 2px;
  min-width: 22px;
  position: absolute;
}
div.textNumber > div {
  position: relative;
}
div.textNumber > div > div {
  background: #0670c1 !important;
  border-radius: 6px;
  color: #fff !important;
  height: 16px;
  line-height: 16px !important;
  padding: 0 2px;
  min-width: 22px;
  position: absolute;
  top: 4px;
}
/*  ------- 文本设置 文字溢出设置 ------- */
.mxText-overflow-auto, .mxText-overflow-scroll {
  margin: .67em 0;
}
.mxText-overflow-auto::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.mxText-overflow-auto:hover::-webkit-scrollbar-corner,
.mxText-overflow-auto:hover::-webkit-scrollbar-thumb {
  background-color: #7a7a7a;
  border-radius: 3px;
}
.mxText-overflow-scroll::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.mxText-overflow-scroll::-webkit-scrollbar-corner,
.mxText-overflow-scroll::-webkit-scrollbar-thumb {
  background-color: #7a7a7a;
  border-radius: 3px;
}
.mxText-overflow-hidden:hover .mxText-overflow-hidden-tip {
  visibility: visible;
}
.mxText-overflow-hidden-tip {
  position: absolute;
  visibility: hidden;
  max-width: 250px;
  font-size: 12px;
  text-align: left;
  opacity: 0.7;
  white-space: normal;
}
.mxText-overflow-hidden.must-hidden .mxText-overflow-hidden-tip {
  visibility: hidden;
}
.mxText-overflow-hidden.must-hidden:hover .mxText-overflow-hidden-tip {
  visibility: hidden;
}
.mxText-overflow-hidden-tip-content {
  background: #000000;
  color: #fff;
  padding: 8px 10px;
  border-radius: 3px;
}
.mxText-overflow-hidden-tip-content * {
  background: #000000 !important;
  color: #fff !important;
}
.mxText-overflow-hidden-tip-arrow {
  border-top: 6px solid #000000;
  border-bottom: 0;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  width: 10px;
  margin-left: 20px;
}
.mxText-overflow-hidden-tip.ie_11 .mxText-overflow-hidden-tip-content {
  display: inline-flex;
}
.mxText-overflow-auto.ie_11 {
  -ms-overflow-style: none;
}
.mxText-overflow-auto.ie_11:hover {
  -ms-overflow-style: auto;
}
