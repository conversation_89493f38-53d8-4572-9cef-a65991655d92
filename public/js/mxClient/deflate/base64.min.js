var Base64={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(r,t){var e,o,a,h,n,c,d="",C=0;for((t=null!=t&&t)||(r=Base64._utf8_encode(r));C<r.length;)a=(e=r.charCodeAt(C++))>>2,h=(3&e)<<4|(e=r.charCodeAt(C++))>>4,n=(15&e)<<2|(o=r.charCodeAt(C++))>>6,c=63&o,isNaN(e)?n=c=64:isNaN(o)&&(c=64),d=d+this._keyStr.charAt(a)+this._keyStr.charAt(h)+this._keyStr.charAt(n)+this._keyStr.charAt(c);return d},decode:function(r,t){t=null!=t&&t;var e,o,a,h,n,c,d="",C=0;for(r=r.replace(/[^A-Za-z0-9\+\/\=]/g,"");C<r.length;)a=this._keyStr.indexOf(r.charAt(C++)),e=(15&(h=this._keyStr.indexOf(r.charAt(C++))))<<4|(n=this._keyStr.indexOf(r.charAt(C++)))>>2,o=(3&n)<<6|(c=this._keyStr.indexOf(r.charAt(C++))),d+=String.fromCharCode(a<<2|h>>4),64!=n&&(d+=String.fromCharCode(e)),64!=c&&(d+=String.fromCharCode(o));return d=t?d:Base64._utf8_decode(d)},_utf8_encode:function(r){r=r.replace(/\r\n/g,"\n");for(var t="",e=0;e<r.length;e++){var o=r.charCodeAt(e);o<128?t+=String.fromCharCode(o):t=127<o&&o<2048?(t+=String.fromCharCode(o>>6|192))+String.fromCharCode(63&o|128):(t=(t+=String.fromCharCode(o>>12|224))+String.fromCharCode(o>>6&63|128))+String.fromCharCode(63&o|128)}return t},_utf8_decode:function(r){var t,e="",o=0;for(c1=c2=0;o<r.length;)(t=r.charCodeAt(o))<128?(e+=String.fromCharCode(t),o++):191<t&&t<224?(c2=r.charCodeAt(o+1),e+=String.fromCharCode((31&t)<<6|63&c2),o+=2):(c2=r.charCodeAt(o+1),c3=r.charCodeAt(o+2),e+=String.fromCharCode((15&t)<<12|(63&c2)<<6|63&c3),o+=3);return e}};