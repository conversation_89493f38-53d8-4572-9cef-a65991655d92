.excel-view {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  overflow: hidden;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.excel-view .gc-floatingobject-selected {
  border: 1px solid transparent;
}

.weapp-formbuilder-preview-content {
  display: block !important;
  height: 100% !important;
}

.weapp-form {
  height: 100% !important;
}

.weapp-form-wrapper {
  height: 100% !important;
  display: flex;
  flex-direction: column;
}

.weapp-form-view {
  flex: auto;
  overflow: hidden;
}

.weapp-form-view > div:last-child > div:last-child {
  height: 100% !important;
}

/* .excel-view .gc-floatingobject-container {
  overflow: unset;
} */

.excel-view .floatingnode {
  overflow: hidden;
  height: 100%;
}

.excel-view .floatingnode > div:last-child {
  height: 100%;
  padding: 0px !important;
}

/* .weapp-form-widget .weapp-form-field {
  padding: 0px;
} */
.excel-view .weapp-form-widget .weapp-form-widget-content {
  padding: 0px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.excel-view .weapp-form-input-wrapper,
.excel-view .weapp-form-textarea-wrapper {
  width: 100% !important;
  height: 100%;
}

.excel-view
  .weapp-form-widget
  .weapp-form-widget-content
  .weapp-form-input-wrapper
  > div:first-child,
.excel-view
  .weapp-form-widget
  .weapp-form-widget-content
  .weapp-form-textarea-wrapper
  > div:first-child {
  height: 100% !important;
}

.excel-view
  .weapp-form-widget
  .weapp-form-widget-content
  .weapp-form-textarea-wrapper
  > textarea:first-child {
  height: 100% !important;
}

.excel-view
  .weapp-form-widget
  .weapp-form-widget-content
  .weapp-form-input-wrapper
  > input:first-child {
  height: 100% !important;
}

.excel-view .floatingnode .weapp-form-widget .weapp-form-widget-error {
  transform: translateY(calc(100% + -22px));
  -webkit-transform: translateY(calc(100% + -22px));
}

.excel-view .floatingnode .weapp-form-widget .weapp-form-widget-required:after {
  height: 100%;
}

/* .excel-view
  .floatingnode
  .weapp-form-widget-content
  .ui-input.is-focus.ui-browser-associative-search-input,
.excel-view
  .floatingnode
  .weapp-form-widget-content
  .ui-input:focus.ui-browser-associative-search-input {
  border-color: rgb(82, 146, 247);
} */

/**
*兼容填报高度样式
*/
.ui-spin-nested-loading {
  height: 100% !important;
}
.ui-spin-container {
  height: 100% !important;
}

.weapp-ebdf-cardv-formcontent-excel {
  height: 100% !important;
}
