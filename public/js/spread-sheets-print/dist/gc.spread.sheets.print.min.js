/*!
 * 
 * Spread.Sheets Library 11.2.6
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
!function(a){"object"==typeof module&&"object"==typeof module.exports?module.exports=a(require("@weapp/spread-sheets")):"function"==typeof define&&define.amd?define(["@weapp/spread-sheets"],a):"object"==typeof exports?exports.Spread=a(require("@weapp/spread-sheets")):a(GC)}(function(a){a=a||{},a.Spread=a.Spread||{},a.Spread.Sheets=a.Spread.Sheets||{},a.Spread.Sheets.Print=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={exports:{},id:d,loaded:!1};return a[d].call(e.exports,e,e.exports,c),e.loaded=!0,e.exports}return c.m=a,c.c=b,c.p="/assets/",c(0)}([function(a,b,c){!function(){"use strict";a.exports=c(1)}()},function(a,b,c){!function(){"use strict";var b,d,e,f,g,h,i,j,k,l,m,n=c(2),o=n.Workbook,p=n.Worksheet,q=n.xJ,r=n.GC$,s=r.extend,t=r.each,u=n.oo,v=n.Rect,w=n.Ul,x=w.Ml,y={},z="firstPageNumber",A="margin",B="paperSize",C="pageBreak",D="",E="gc-printPage",F="2d",G="px",H="div",I="canvas",J=null,K=Math.min,L=Math.max,M=Math.floor,N=parseInt,O=parseFloat,P=JSON.stringify,Q=JSON.parse;function R(a){return"number"==typeof a&&a%1===0}s(p.prototype,{printInfo:function(a){var b=this,c;return 0===arguments.length?(b.e3||(b.e3=new T),c=b.e3):(b.e3=a,c=b),c},getRowPageBreak:function(a){return this.ITa.getPageBreak(!0,3,a)},setRowPageBreak:function(a,b){this.Vr(a,b,C,!0)},getColumnPageBreak:function(a){return this.ITa.getPageBreak(!1,3,a)},setColumnPageBreak:function(a,b){this.Vr(a,b,C,!1)}}),p.$n("print",{toJson:function(a){var b=this.e3;b&&(a.printInfo=b.toJSON())},fromJson:function(a){var b=a&&a.printInfo;b&&this.printInfo().fromJSON(b)}}),o.prototype.print=function(a){var b,c=this;if(!(U(a)||a===J||R(a)&&a>=0&&c.getSheetCount()>a))throw Error("Invalid  sheetIndex");c.f3||(c.f3=new Oa),b=c.f3,b.print(c,a)},o.$n("print",{dispose:function(){var a=this.f3;a&&a.dispose()}}),y.PrintVisibilityType={inherit:0,hide:1,show:2,showOnce:3},y.PrintCentering={none:0,horizontal:1,vertical:2,both:3},y.PrintPageOrientation={portrait:1,landscape:2},y.PrintPageOrder={auto:0,downThenOver:1,overThenDown:2},y.PaperKind={a2:66,a3:8,a3Extra:63,a3ExtraTransverse:68,a3Rotated:76,a3Transverse:67,a4:9,a4Extra:53,a4Plus:60,a4Rotated:77,a4Small:10,a4Transverse:55,a5:11,a5Extra:64,a5Rotated:78,a5Transverse:61,a6:70,a6Rotated:83,aPlus:57,b4:12,b4Envelope:33,b4JisRotated:79,b5:13,b5Envelope:34,b5Extra:65,b5JisRotated:80,b5Transverse:62,b6Envelope:35,b6Jis:88,b6JisRotated:89,bPlus:58,c3Envelope:29,c4Envelope:30,c5Envelope:28,c65Envelope:32,c6Envelope:31,cSheet:24,custom:0,dlEnvelope:27,dSheet:25,eSheet:26,executive:7,folio:14,germanLegalFanfold:41,germanStandardFanfold:40,inviteEnvelope:47,isoB4:42,italyEnvelope:36,japaneseDoublePostcard:69,japaneseDoublePostcardRotated:82,japaneseEnvelopeChouNumber3:73,japaneseEnvelopeChouNumber3Rotated:86,japaneseEnvelopeChouNumber4:74,japaneseEnvelopeChouNumber4Rotated:87,japaneseEnvelopeKakuNumber2:71,japaneseEnvelopeKakuNumber2Rotated:84,japaneseEnvelopeKakuNumber3:72,japaneseEnvelopeKakuNumber3Rotated:85,japaneseEnvelopeYouNumber4:91,japaneseEnvelopeYouNumber4Rotated:92,japanesePostcard:43,japanesePostcardRotated:81,ledger:4,legal:5,legalExtra:51,letter:1,letterExtra:50,letterExtraTransverse:56,letterPlus:59,letterRotated:75,letterSmall:2,letterTransverse:54,monarchEnvelope:37,note:18,number10Envelope:20,number11Envelope:21,number12Envelope:22,number14Envelope:23,number9Envelope:19,personalEnvelope:38,prc16K:93,prc16KRotated:106,prc32K:94,prc32KBig:95,prc32KBigRotated:108,prc32KRotated:107,prcEnvelopeNumber1:96,prcEnvelopeNumber10:105,prcEnvelopeNumber10Rotated:118,prcEnvelopeNumber1Rotated:109,prcEnvelopeNumber2:97,prcEnvelopeNumber2Rotated:110,prcEnvelopeNumber3:98,prcEnvelopeNumber3Rotated:111,prcEnvelopeNumber4:99,prcEnvelopeNumber4Rotated:112,prcEnvelopeNumber5:100,prcEnvelopeNumber5Rotated:113,prcEnvelopeNumber6:101,prcEnvelopeNumber6Rotated:114,prcEnvelopeNumber7:102,prcEnvelopeNumber7Rotated:115,prcEnvelopeNumber8:103,prcEnvelopeNumber8Rotated:116,prcEnvelopeNumber9:104,prcEnvelopeNumber9Rotated:117,quarto:15,standard10x11:45,standard10x14:16,standard11x17:17,standard12x11:90,standard15x11:46,standard9x11:44,statement:6,tabloid:3,tabloidExtra:52,usStandardFanfold:39};function S(a,b){var c=this,d=arguments.length;1===d?c.kind(a):2===d?(c.Vo=a,c._v=b,c.g3=0):c.kind(1)}s(S.prototype,{height:function(a){var b=this;return 0===arguments.length?b._v:(b._v!==a&&(b.g3=0),b._v=a,b)},width:function(a){var b=this;return 0===arguments.length?b.Vo:(b.Vo!==a&&(b.g3=0),b.Vo=a,b)},kind:function(a){var b,c=this;return 0===arguments.length?c.g3:(c.g3=a,b=c.getPageSize(a),c.Vo=b.width,c._v=b.height,c)},getPageSize:function(a){function b(a,b){return b?100*a:a/25.4*100}function c(a,c){return{width:b(a,!0),height:b(c,!0)}}function d(a,c){return{width:b(a,!1),height:b(c,!1)}}switch(a){case 0:return{width:0,height:0};case 1:return c(8.5,11);case 5:return c(8.5,14);case 9:return d(210,297);case 24:return c(17,22);case 25:return c(22,34);case 26:return c(34,44);case 2:return c(8.5,11);case 3:return c(11,17);case 4:return c(17,11);case 6:return c(5.5,8.5);case 7:return c(7.25,10.5);case 8:return d(297,420);case 10:return d(210,297);case 11:return d(148,210);case 12:return d(250,353);case 13:return d(176,250);case 14:return c(8.5,13);case 15:return d(215,275);case 16:return c(10,14);case 17:return c(11,17);case 18:return c(8.5,11);case 19:return c(3.875,8.875);case 20:return c(4.125,9.5);case 21:return c(4.5,10.375);case 22:return c(4.75,11);case 23:return c(5,11.5);case 27:return d(110,220);case 28:return d(162,229);case 29:return d(324,458);case 30:return d(229,324);case 31:return d(114,162);case 32:return d(114,229);case 33:return d(250,353);case 34:return d(176,250);case 35:return d(176,125);case 36:return d(110,230);case 37:return c(3.875,7.5);case 38:return c(3.625,6.5);case 39:return c(14.875,11);case 40:return c(8.5,12);case 41:return c(8.5,13);case 42:return d(250,353);case 43:return d(100,148);case 44:return c(9,11);case 45:return c(10,11);case 46:return c(15,11);case 47:return d(220,220);case 50:return c(9.275,12);case 51:return c(9.275,15);case 52:return c(11.69,18);case 53:return d(236,322);case 54:return c(8.275,11);case 55:return d(210,297);case 56:return c(9.275,12);case 57:return d(227,356);case 58:return d(305,487);case 59:return c(8.5,12.69);case 60:return d(210,330);case 61:return d(148,210);case 62:return d(182,257);case 63:return d(322,445);case 64:return d(174,235);case 65:return d(201,276);case 66:return d(420,594);case 67:return d(297,420);case 68:return d(322,445);case 69:return d(200,148);case 70:return d(105,148);case 71:return d(240,332);case 72:return d(216,277);case 73:return d(120,235);case 74:return d(90,205);case 75:return c(11,8.5);case 76:return d(420,297);case 77:return d(297,210);case 78:return d(210,148);case 79:return d(364,257);case 80:return d(257,182);case 81:return d(148,100);case 82:return d(148,200);case 83:return d(148,105);case 84:return d(332,240);case 85:return d(277,216);case 86:return d(235,120);case 87:return d(205,90);case 88:return d(128,182);case 89:return d(182,128);case 90:return c(12,11);case 91:return d(235,105);case 92:return d(105,235);case 93:return d(146,215);case 94:return d(97,151);case 95:return d(97,151);case 96:return d(102,165);case 97:return d(102,176);case 98:return d(125,176);case 99:return d(110,208);case 100:return d(110,220);case 101:return d(120,230);case 102:return d(160,230);case 103:return d(120,309);case 104:return d(229,324);case 105:return d(324,458);case 106:return d(146,215);case 107:return d(97,151);case 108:return d(97,151);case 109:return d(165,102);case 110:return d(176,102);case 111:return d(176,125);case 112:return d(208,110);case 113:return d(220,110);case 114:return d(230,120);case 115:return d(230,160);case 116:return d(309,120);case 117:return d(324,229);case 118:return d(458,324);default:return{width:0,height:0}}},toJSON:function(){return{width:this.Vo,height:this._v,kind:this.g3}},fromJSON:function(a){U(a.width)||(this.Vo=a.width),U(a.height)||(this._v=a.height),U(a.kind)||(this.g3=a.kind)}}),y.PaperSize=S,b='{"top":75,"bottom":75,"left":70,"right":70,"header":30,"footer":30}',d={bestFitRows:!1,bestFitColumns:!1,columnStart:-1,columnEnd:-1,rowStart:-1,rowEnd:-1,repeatColumnStart:-1,repeatColumnEnd:-1,repeatRowStart:-1,repeatRowEnd:-1,showBorder:!0,showGridLine:!1,showColumnHeader:0,showRowHeader:0,useMax:!0,centering:0,firstPageNumber:1,headerLeft:D,headerCenter:D,headerRight:D,footerLeft:D,footerCenter:D,footerRight:D,headerLeftImage:D,headerCenterImage:D,headerRightImage:D,footerLeftImage:D,footerCenterImage:D,footerRightImage:D,margin:Q(b),orientation:1,pageRange:D,pageOrder:0,blackAndWhite:!1,zoomFactor:1,fitPagesTall:-1,fitPagesWide:-1,paperSize:{},qualityFactor:2};function T(){var a=this;t(d,function(c,d){c===A?a["_"+c]=Q(b):c===B?a["_"+c]=new S:a["_"+c]=d})}e={toJSON:function(){var a,b,c;function e(a,b,c){var e=!1,f=d[a];return e=a===A?P(f)===P(b):a===B?850===b.width&&1100===b.height&&0===b.kind:a===z?1===b&&!c.fma:f===b}return a=this,b={},t(d,function(d){c=a["_"+d],e(d,c,a)||(d===B?b[d]=c.toJSON():b[d]=c)}),b},fromJSON:function(a){var b=this;t(d,function(c){var d=a[c];U(d)||(c===B?b["_"+c].fromJSON(d):(c===z&&(b.fma=!0),b["_"+c]=d))})}},t(d,function(a){e[a]=function(b){if(0===arguments.length)return this["_"+a];if("zoomFactor"===a)b<.1?b=.1:b>4&&(b=4);else if("qualityFactor"===a){var c=N(b,10);c<1?c=1:c>8&&(c=8),b=c}else a===z&&(this.fma=!0);return this["_"+a]=b,this}}),s(T.prototype,e),y.PrintInfo=T;function U(a){return void 0===a}function V(a,b){return a.createElement(b)}function W(a,b,c){var d=X(a,b.paperSize(),b.orientation(),!b.showBorder()),e=d.width,f=d.height,g=Y(b.margin()),h=g.left,i=g.top,j=g.right,k=g.bottom;c.paperSize={width:e,height:f},c.pageImageableArea=new v(h,i,e-h-j,f-i-k)}function X(a,b,c,d){var e,f=.96,g={},h=b.width()*f,i=b.height()*f;return 2===c?(g.width=i,g.height=N(h,10)):(g.width=h,g.height=N(i,10)),d&&(e=a.options.sheetAreaOffset,g.width-=e.left,g.height-=e.top),g}function Y(a){var b=.96,c={};return c.left=a.left*b,c.top=a.top*b,c.right=a.right*b,c.bottom=a.bottom*b,c.header=a.header*b,c.footer=a.footer*b,c}function Z(a){var b,c,d,e,f,g,h,i,j,k;if(!a)return[];for(b=[],c=a.split(","),d=0,e=c.length;d<e;d++)if(f=c[d].trim())if(g=f.indexOf("-"),g>=0){for(h=N(f.substr(0,g),10),i=N(f.substr(g+1),10),j=i>=h?1:-1,k=h;k!==i;k+=j)b.push(k);b.push(i)}else b.push(N(f,10));return b}function $(a,b){a.sort();var c=[],d,e,f;for(d=0,e=a.length;d<e;d++)a[d]-=1,(a[d]>=b||a[d]<0)&&c.push(d);for(f=c.length-1;f>=0;f--)a.splice(c[f],1)}function _(a){var b,c,d,e,f,g,h=a.ITa,i=h.getLastNonNullCol(),j=h.getLastNonNullRow(),k=a.tables&&a.tables.all();for(b=i;b>=0;b--){if(c=a.Iq(-1,b),ca(c))return b;if(k)for(d=0,e=k.length;d<e;d++)if(f=k[d].range(),b>=f.col&&b<=f.col+f.colCount-1)return b;for(g=0;g<=j;g++)if(ba(a,g,b))return b}return-1}function aa(a){var b,c,d,e,f,g,h=a.ITa,i=h.getLastNonNullCol(),j=h.getLastNonNullRow(),k=a.tables&&a.tables.all();for(b=j;b>=0;b--){if(c=a.Iq(b,-1),ca(c))return b;if(k)for(d=0,e=k.length;d<e;d++)if(f=k[d].range(),b>=f.row&&b<=f.row+f.rowCount-1)return b;for(g=0;g<=i;g++)if(ba(a,b,g))return b}return-1}function ba(a,b,c){var d=a.getValue(b,c),e,f;return d!==J&&!U(d)||(!!(e=a.getSparkline&&a.getSparkline(b,c))||(f=a.Iq(b,c),ca(f)))}function ca(a){return!(!a||!(a.backColor||a.backgroundImage||a.borderBottom||a.borderLeft||a.borderRight||a.borderTop||a.diagonalDown||a.diagonalUp))}function da(a,b){var c,d=[],e=a.pictures&&a.pictures.all();e&&(d=d.concat(e)),c=a.charts&&a.charts.all(),c&&(d=d.concat(c)),d.forEach(b)}function ea(a,b){var c=-1;return da(a,function(a){a.isVisible()&&a.canPrint()&&(c=L(c,b?a.endRow():a.endColumn()))}),c}function fa(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,r,s,t,u,w,x=a.ss,y=aa(a)+1,z=0;for(b=0,c=a.getColumnCount(2);b<c;b++)z+=a.Tl(b,2);for(d=0,e=a.getColumnCount();d<e;d++)z+=x._m(d);for(f=0,g=0,h=a.getRowCount(1);g<h;g++)f+=a.Sl(g,1);for(i=0;i<y;i++)f+=x.Sl(i);for(j=new v(0,0,z,f),a.Us(j),a.invalidateLayout(),k=-1,l=0;l<y;l++)for(m=a.Er(l),n=0;n<=2;n++){for(o=new q(a,m,n),p=o.nJ(l),r=0,s=p.length;r<s;r++)t=p[r],t.endColumn>k&&(k=t.endColumn);u=p.headingOverflowlayout,u&&u.endColumn>k&&(k=u.endColumn),w=p.trailingOverflowLayout,w&&w.endColumn>k&&(k=w.endColumn)}return k}function ga(a,b,c){var d=-1,e=-1;return ha(a,0,0,b,c,function(a){var b=a.col+a.colCount-1,c=a.row+a.rowCount-1;b>d&&(d=b),c>e&&(e=c)}),{colIndex:d,rowIndex:e}}function ha(a,b,c,d,e,f){var g,h,i,j=a;for(g=0,h=j.length;g<h;g++){if(b>=0||c>=0)for(;g<h&&!j[g].intersect(b,c,d,e);)g++;if(g<h&&f&&(i=f(j[g]),i===!1))break}}function ia(a){var b,c,d,e=-1,f=a.ITa.getSpans(J,1),g=a.getRowCount(1),h=a.getColumnCount(1);return e=L(e,ga(f,g,h).colIndex),b=a.ITa.getSpans(J,3),c=a.getRowCount(),d=a.getColumnCount(),e=L(e,ga(b,c,d).colIndex)}function ja(a){var b,c,d,e=-1,f=a.ITa.getSpans(J,2),g=a.getRowCount(2),h=a.getColumnCount(2);return e=L(e,ga(f,g,h).rowIndex),b=a.ITa.getSpans(J,3),c=a.getRowCount(),d=a.getColumnCount(),e=L(e,ga(b,c,d).rowIndex)}function ka(a,b){var c,d,e,f,g,h,i=b.columnEnd(),j=a.getColumnCount();return i===-1?(b.useMax()?(e=_(a),a.options.allowCellOverflow&&(f=fa(a),e=L(e,f)),g=ea(a,!1),d=L(e,g),h=ia(a),d=L(d,h)):d=j-1,c=d):c=i,c=K(c,j-1)}function la(a,b){var c,d,e,f,g,h=b.rowEnd(),i=a.getRowCount();return h===-1?(b.useMax()?(e=aa(a),f=ea(a,!0),d=L(e,f),g=ja(a),d=L(d,g)):d=i-1,c=d):c=h,c=K(c,i-1)}f={headerSize:0,contentSize:0,contentOffset:0,itemStart:-1,itemEnd:-1,repeatItemStart:-1,repeatItemEnd:-1};function ma(){var a=this;t(f,function(b,c){a["_"+b]=c})}g={},t(f,function(a){g[a]=function(b){return 0===arguments.length?this["_"+a]:(this["_"+a]=b,this)}}),s(ma.prototype,g),h={sheetIndex:-1,pageNumber:-1,columnPageIndex:-1,rowPageIndex:-1,pageNumberInSheet:-1,columnPageIndexInSheet:-1,rowPageIndexInSheet:-1,columnPage:J,rowPage:J,paperSize:J,pageImageableArea:J,workbookName:D,worksheetName:D};function na(){var a=this;t(h,function(b,c){"columnPage"===b||"rowPage"===b?c=new ma:b===B?c={width:0,height:0}:"pageImageableArea"===b&&(c=new v(0,0,0,0)),a["_"+b]=c})}i={getPageSize:function(){var a=this.columnPage(),b=this.rowPage();return{width:a.contentSize()+a.headerSize(),height:b.contentSize()+b.headerSize()}}},t(h,function(a){i[a]=function(b){return 0===arguments.length?this["_"+a]:(this["_"+a]=b,this)}}),s(na.prototype,i);function oa(a,b,c,d){var e=this;e.OC=a,e.kj=a.getSheet(b),e._sheetIndex=b,e.h3=e.kj.printInfo(),e._paperSize=c,e._pageImageableArea=d,e.i3={width:d.width,height:d.height},e.j3=0,e.l3=[],e.m3=[],e.n3=1,e.o3=1}j={paginate:function(){var a,b,c,d,e,f,g,h,i,j,k,l,m=this;if(!m.kj.visible()||m.q3()<=0||m.r3()<=0)return void(m.j3=0);if(a=m.h3,b=a.fitPagesTall(),c=a.fitPagesWide(),b===-1&&c===-1&&(d=a.zoomFactor(),m.horizontalZoomFactor(d),m.verticalZoomFactor(d)),m.$m={},m.an={},m.s3(),b>=1||c>=1){for(e=0,f=0,g=0,h=m.horizontalPageCount();g<h;g++)e+=m.l3[g].contentSize();for(i=0,j=m.verticalPageCount();i<j;i++)f+=m.m3[i].contentSize();k=m.verticalPageCount(),b<k&&b>=1&&m.t3(f,k),l=m.horizontalPageCount(),c<l&&c>=1&&m.u3(e,l),m.j3=m.verticalPageCount()*m.horizontalPageCount()}m.$m={},m.an={}},pageCount:function(){return this.j3},getPage:function(a){var b,c=this,d=0,e=0,f=c.h3.pageOrder(),g=c.verticalPageCount(),h=c.horizontalPageCount();return 1===f||0===f&&g>=h?(d=a%g,e=M(a/g)):(e=a%h,d=M(a/h)),b=new na,b.sheetIndex(c._sheetIndex),b.pageNumberInSheet(a),b.rowPageIndexInSheet(d),b.columnPageIndexInSheet(e),b.pageNumber(a),b.rowPageIndex(d),b.columnPageIndex(e),b.rowPage(c.m3[d]),b.columnPage(c.l3[e]),b.paperSize(c._paperSize),b.pageImageableArea(c._pageImageableArea),b.workbookName(c.kj.parent.name),b.worksheetName(c.kj.name()),b},verticalZoomFactor:function(a){return 0===arguments.length?this.o3:(this.o3=a,this.$m={},this)},horizontalZoomFactor:function(a){return 0===arguments.length?this.n3:(this.n3=a,this.an={},this)},horizontalPageCount:function(){return this.l3.length},verticalPageCount:function(){return this.m3.length},sheetIndex:function(){return this._sheetIndex},q3:function(){return this.i3.width},r3:function(){return this.i3.height},s3:function(){var a=this;a.v3(),a.w3(),a.j3=a.verticalPageCount()*a.horizontalPageCount()},u3:function(a,b){var c,d,e,f,g,h=this,i=h.h3.fitPagesWide(),j=1;for(i<b&&(j=i/b),c=i*h.q3()/a,d=c,e=15,f=(c-j)/e,g=0;g<=e&&(h.horizontalZoomFactor(d),h.w3(),h.horizontalPageCount()!==i);g++)d-=f},t3:function(a,b){var c,d,e,f,g,h=this,i=h.h3.fitPagesTall(),j=1;for(i<b&&(j=i/b),c=i*h.r3()/a,d=c,e=15,f=(c-j)/e,g=0;g<=e&&(h.verticalZoomFactor(d),h.v3(),h.verticalPageCount()!==i);g++)d-=f},w3:function(){var a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r=this,s=r.h3,t=r.kj,u=s.columnStart()===-1?0:s.columnStart(),v=ka(t,s);if(v!==-1){for(a=r.x3(),b=r.q3(),c=s.repeatColumnStart(),d=s.repeatColumnEnd(),e=s.showRowHeader(),f=[],g=u;g<=v;g++)t.getColumnPageBreak(g)&&f.push(g);for(r.l3=[],h=-1,i=pa(u,v,f),j=0,k=i.length;j<k-1;j+=2){if(l=i[j],m=i[j+1],n=[],2===e||0===e&&t.options.rowHeaderVisible)for(n=r.y3(l,m,c,d,3,b-a,h),o=0,p=n.length;o<p;o++)n[o].headerSize(a);else 3===e?(q=r.y3(l,m,c,d,3,b-a,h),q.length>0&&(q[0].headerSize(a),n.push(q[0]),q.length>1&&(q=r.y3(q[1].itemStart(),m,c,d,3,b,q[0].itemEnd()),n=n.concat(q)))):n=r.y3(l,m,c,d,3,b,h);r.l3=r.l3.concat(n),n.length>0&&(h=n[n.length-1].itemEnd())}}},v3:function(){var a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r=this,s=r.h3,t=r.kj,u=s.rowStart()===-1?0:s.rowStart(),v=la(t,s);if(v!==-1){for(a=r.z3(),b=r.r3(),c=s.repeatRowStart(),d=s.repeatRowEnd(),e=s.showColumnHeader(),f=[],g=u;g<=v;g++)t.getRowPageBreak(g)&&f.push(g);for(r.m3=[],h=-1,i=pa(u,v,f),j=0,k=i.length;j<k-1;j+=2){if(l=i[j],m=i[j+1],n=[],2===e||0===e&&t.options.colHeaderVisible)for(n=r.B3(l,m,c,d,3,b-a,h),o=0,p=n.length;o<p;o++)n[o].headerSize(a);else 3===e?(q=r.B3(l,m,c,d,3,b-a,h),q.length>0&&(q[0].headerSize(a),n.push(q[0]),q.length>1&&(q=r.B3(q[1].itemStart(),m,c,d,3,b,q[0].itemEnd()),n=n.concat(q)))):n=r.B3(l,m,c,d,3,b,h);r.m3=r.m3.concat(n),n.length>0&&(h=n[n.length-1].itemEnd())}}},x3:function(){var a,b=this,c=b.y3(0,b.kj.getColumnCount(2)-1,-1,-1,2,b.q3(),-1),d=0;for(a=0;a<c.length;a++)d+=c[a].contentSize();return d},z3:function(){var a,b=this,c=b.B3(0,b.kj.getRowCount(1)-1,-1,-1,1,b.r3(),-1),d=0;for(a=0;a<c.length;a++)d+=c[a].contentSize();return d},y3:function(a,b,c,d,e,f,g){var h,i,j,k,l,m,n,o,p=this,q=c<a,r=0,s=0,t=0,u=0,v=f,w=!1,x=J,y=[];for(a>b&&(j=a,a=b,b=j),k=a;k<=b;k++){if(q&&3===e&&(h=c,i=d,h!==-1&&h<=g)){for(i!==-1&&i>g&&(i=g-1),x===J&&(x=new ma),x.repeatItemStart(c),x.repeatItemEnd(d),l=h;l<=i;l++)m=p.C3(l,e),l<=g&&(u+=m);v-=u,q=!1}if(r=p.C3(k,e),r>v&&(w||k===a)){for(w=!1,n=0;r-n>=v;)x===J&&(x=new ma),x.repeatItemStart(c),x.repeatItemEnd(d),x.itemStart(k),x.itemEnd(k),x.contentSize(f),x.contentOffset(n),y.push(x),n+=v,x=J,s=0,q=!0;if(o=r-n,0===o){g=k;continue}if(g=k-1,k===b){x===J&&(x=new ma),x.repeatItemStart(c),x.repeatItemEnd(d),x.itemStart(k),x.itemEnd(k),x.contentSize(o+u),x.contentOffset(n),y.push(x);break}if(k++,!(k<=b))break;x===J&&(x=new ma),x.repeatItemStart(c),x.repeatItemEnd(d),s=o,x.contentOffset(n),r=p.C3(k,e)}s+=r,s>v||s===v?(s>v?(t=s-r+u,k-=1):t=s+u,x===J&&(x=new ma),g===-1?x.itemStart(a):x.itemStart(g+1),x.itemEnd(k),x.contentSize(t),y.push(x),x=J,s=0,u=0,v=f,q=!0,g=k,r>v&&(w=!0)):k===b&&(x===J&&(x=new ma),t=s+u,g===-1?x.itemStart(a):x.itemStart(g+1),x.itemEnd(k),x.contentSize(t),y.push(x),x=J,s=0,u=0,v=f,q=!0)}return y},B3:function(a,b,c,d,e,f,g){var h,i,j,k,l,m,n,o=this,p=c<a,q=0,r=0,s=0,t=0,u=f,v=d,w=!1,x=J,y=[];for(a>b&&(i=a,a=b,b=i),j=a;j<=b;j++){if(p&&3===e&&(h=c,v=d,h!==-1&&h<=g)){for(v!==-1&&v>g&&(v=g-1),x===J&&(x=new ma),x.repeatItemStart(h),x.repeatItemEnd(v),k=h;k<=v;k++)l=o.D3(k,e),k<=g&&(t+=l);u-=t,p=!1}if(q=o.D3(j,e),q>u&&(w||j===a)){for(w=!1,m=0;q-m>=u;)x===J&&(x=new ma),x.repeatItemStart(c),x.repeatItemEnd(v),x.itemStart(j),x.itemEnd(j),x.contentSize(f),x.contentOffset(m),y.push(x),m+=u,x=J,r=0,p=!0;if(n=q-m,0===n){g=j;continue}if(g=j-1,j===b){x===J&&(x=new ma),x.repeatItemStart(c),x.repeatItemEnd(d),x.itemStart(j),x.itemEnd(j),x.contentSize(n+t),x.contentOffset(m),y.push(x);break}if(j++,!(j<=b))break;x===J&&(x=new ma),x.repeatItemStart(c),x.repeatItemEnd(v),r=n,x.contentOffset(m),q=o.D3(j,e)}r+=q,r>u||r===u?(r>u?(s=r-q+t,j-=1):s=r+t,x===J&&(x=new ma),g===-1?x.itemStart(a):x.itemStart(g+1),x.itemEnd(j),x.contentSize(s),y.push(x),x=J,r=0,t=0,u=f,p=!0,g=j,q>u&&(w=!0)):j===b&&(x===J&&(x=new ma),s=r+t,g===-1?x.itemStart(a):x.itemStart(g+1),x.itemEnd(j),x.contentSize(s),y.push(x),x=J,r=0,t=0,u=f,p=!0)}return y},E3:function(a,b){var c,d,e,f=this,g=f.kj,h=g.defaults,i=g.getColumnVisible(a,b);return 3===b&&a<g.getColumnCount()&&g.columnOutlines&&(i=i&&!g.columnOutlines.isCollapsed(a)),c=0,i&&(c=g.getColumnWidth(a,b),f.h3.bestFitColumns()&&(d=2===b,e=w.xl(a,g,d?2:3,1),c=e<=0?d?h.rowHeaderColWidth:h.colWidth:e)),c},C3:function(a,b){var c,d=this,e=d.an[b];return e||(e=d.an[b]={}),c=e[a],U(c)&&(c=e[a]=d.E3(a,b)*d.n3),c},F3:function(a,b){var c,d,e,f,g=this,h=g.kj,i=h.defaults,j=i.rowHeight,k=i.colHeaderRowHeight,l=h.getRowVisible(a,b);return 3===b&&a<h.getRowCount()&&h.rowOutlines&&(l=l&&!(h.Ps&&h.Ps(a))&&!h.rowOutlines.isCollapsed(a)),c=0,l&&(c=h.getRowHeight(a,b),g.h3.bestFitRows()&&(d=1===b,e=w.Fl(a,h,d?1:3,1),e<=0?c=d?k:j:(f=e,d?f<k&&(f=k):f<j&&(f=j),c=f))),c},D3:function(a,b){var c,d=this,e=d.$m[b];return e||(e=d.$m[b]={}),c=e[a],U(c)&&(c=e[a]=d.F3(a,b)*d.o3),c}};function pa(a,b,c){var d=[],e,f,g;for(d.push(a),e=0,f=c.length;e<f;e++)g=c[e],g-1>=a&&(d.push(g-1),d.push(g));return d.push(b),d}s(oa.prototype,j);function qa(a){this.OC=a,this.j3=0;var b=[],c,d,e,f;for(c=0,d=a.getSheetCount();c<d;c++)e=a.getSheet(c),e.visible()&&(f={},W(e,e.printInfo(),f),b.push(new oa(a,c,f.paperSize,f.pageImageableArea)));this.G3=b}k={paginate:function(){var a=this.G3,b,c,d;for(b=0,c=a.length;b<c;b++)d=a[b],d.paginate(),this.j3+=d.pageCount()},pageCount:function(){return this.j3},getPage:function(a){var b,c,d,e,f={pageNumberInSheet:-1},g=this.getSheetPaginator(a,f),h=f.pageNumberInSheet,i=J;if(g&&(i=g.getPage(h),i.sheetIndex()>0))for(i.pageNumber(a),b=this.G3,c=0,d=b.length;c<d;c++)e=b[c],i.sheetIndex()<e.sheetIndex()&&(i.rowPageIndex(i.rowPageIndexInSheet()+e.verticalPageCount()),i.columnPageIndex(i.columnPageIndexInSheet()+e.horizontalPageCount()));return i},getSheetPaginator:function(a,b){var c,d,e,f=0,g=-1,h=this.G3;for(c=0,d=h.length;c<d;c++){if(e=h[c],g=a-f,g>=0&&g<e.pageCount())return b.pageNumberInSheet=g,e;f+=e.pageCount()}return J}},s(qa.prototype,k);function ra(a){this.Xva=a}ra.prototype.Yva=function(a,b){var c,d=document.createElement("div");d.style.width=b.width()+"px",d.style.height=b.height()+"px",a.appendChild(d),b.Nva(d,b.sheet().printInfo().qualityFactor(),!1),c=d.getElementsByTagName("canvas")[0],b.no(),b.Xs=c},ra.prototype.Zva=function(){var a=this,b=a.xo;b||(b=a.xo=document.createElement("div"),b.style.width="0px",b.style.height="0px",b.style.overflow="hidden",document.body.appendChild(b)),a.Xva.forEach(function(c){a.Yva(b,c)}),b.innerHTML=""},ra.prototype.no=function(){var a,b=this;b.Xva=J,a=b.xo,a&&(a.parentElement&&a.parentElement.removeChild(a),b.xo=J)};function sa(a){var b=a.contentDocument;return b.head||b.write("<head></head>"),b.body||b.write("<body></body>"),b}function ta(a,b){var c;return a instanceof oa?c=a:a instanceof qa&&(c=a.getSheetPaginator(b,{})),c}function ua(a,b,c,d){var e=V(a,H),f=e.style;return f.border="1px transparent solid",f.boxSizing="border-box",f.width=b+G,f.height=c+G,e.className=d,a.body.appendChild(e),e}function va(a,b,c,d,e,f){var g=V(a,H),h=g.style;return h.marginLeft=e+G,h.width=c+G,h.marginTop=f+G,h.height=d+G,b.appendChild(g),g}function wa(a,b,c,d){var e=V(a,I),f=r(e),g=e.style;return g.height=c+G,g.width=b+G,e.width=f.width()*d,e.height=f.height()*d,e.getContext(F).scale(d,d),e}function xa(a,b,c,d){var e=V(a,I),f=r(e),g=e.style;return g.height=c+G,g.width=b+G,e.width=f.width()*d,e.height=f.height()*d,e.getContext(F).scale(d,d),e}function ya(a,b,c,d){var e=V(a,H),f=e.style;return f.width=c+G,f.height=d+G,b.appendChild(e),e}function za(a,b,c,d,e,f,g,h,i,j){var k,l,m,n,o,p,q,s,t,u=0;if(f?u=1:(k=a.options.sheetAreaOffset,d+=k.left,e+=k.top),l=c.style,1!==g&&3!==g||(l.boxSizing="border-box",l.paddingLeft=(r(c).width()-d-2*u)/2+G),2!==g&&3!==g||(l.boxSizing="border-box",l.paddingTop=(r(c).height()-e-2*u)/2+G),m=V(b,H),n=m.style,n.width=d+G,n.height=e+G,n.border=u+"px black solid",o=V(b,I),p=o.style,p.margin=u+G,o.width=1/h*(d-2*u),o.height=1/i*(e-2*u),p.width=d-2*u+G,p.height=e-2*u+G,q=j,s=j,x.chrome)for(t=o.width*q*o.height*s;t<65792;)t*=2,s*=2;return o.$va=o.width,o._va=o.height,o.width*=q,o.height*=s,o.getContext(F).scale(q,s),c.appendChild(m),o}function Aa(a,b,c,d,e){var f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,E,F,H,I,J,K,L,M,N,P,Q,R,S,T,U,V,W,X=[];if(!b)return X;for(f="&",g="K",h="S",i="U",j='"',k="B",l="I",m="D",n="T",o="P",p="N",q="G",r="F",s="A",t=f.length,u=/&[0-9]+/,v=/&K[0-9A-Fa-f]{6}/,w=/&".+"/,x=D,y=!1,z=0,A=!1,B=!1,C=!1,E="black",F=D;b;)H=x,I=y,J=z,K=C,L=A,M=B,N=E,P=b.indexOf(f),P<0&&(P=b.length),F+=b.substr(0,P),Q=P+1<b.length?b.substr(P+1,1):D,Q=Q.toUpperCase(),R=-1,S=!1,T=!1,W=new Date,"1234567890".indexOf(Q)>-1?(U=u.exec(b.substr(P)),U&&U.length>0?(V=U[0],J=O(V.substr(1)),S=!0,R=P+V.length):R=P+t):Q===g?(U=v.exec(b.substr(P)),U&&U.length>0?(V=U[0],N="#"+V.substr(2,2)+V.substr(4,2)+V.substr(6,2),S=!0,R=P+V.length):R=P+t):Q===h?(I=!I,S=!0,R=P+t+h.length):Q===i?(K=!K,S=!0,R=P+t+i.length):Q===j?(U=w.exec(b.substr(P)),U&&U.length>0?(V=U[0],H=V.substr(2,V.length-3),S=!0,R=P+V.length):R=P+t):Q===k?(L=!L,S=!0,R=P+t+k.length):Q===l?(M=!M,S=!0,R=P+t+l.length):Q===f?(F+=f,S=!0,R=P+t+t):Q===m?(F+=W.getFullYear()+"/"+(W.getMonth()+1)+"/"+W.getDate(),S=!0,R=P+t+m.length):Q===n?(F+=W.getHours()+":"+W.getMinutes()+":"+W.getSeconds(),S=!0,R=P+t+n.length):Q===o?(F+=a,S=!0,R=P+t+o.length):Q===p?(F+=e,S=!0,R=P+t+p.length):Q===q?(c&&(S=!0,T=!0),R=P+t+q.length):Q===r?(F+=d.workbookName()||D,S=!0,R=P+t+r.length):Q===s?(F+=d.worksheetName()||D,S=!0,R=P+t+s.length):R=P+t,R>=b.length?b=D:(R<0&&(R=P+t),b=b.substr(R)),!F||!S&&b||(X.push({text:F,underline:C,strikethrough:y,fontFamily:x,fontSize:z>0?z+G:D,fontWeight:A?"bold":D,fontStyle:B?"italic":D,color:E}),F=D),T&&c&&X.push({image:c}),x=H,y=I,z=J,C=K,A=L,B=M,E=N;return X}function Ba(a,b,c,d,e,f,g,h,i){var j,k,l,m,n,o,p,q,r,t,u=[],v=[];for(j=0,k=b.length;j<k;j++)if(l=b[j],m=l.text)if(n=m.indexOf("\r\n")>=0,n||m.indexOf("\n")>=0){for(o=n?m.split("\r\n"):m.split("\n"),o[0]&&v.push(s({},l,{text:o[0]})),u.push(v),v=[],p=1,q=o.length;p<q-1;p++)o[p]&&u.push([s({},l,{text:o[p]})]);o[o.length-1]&&v.push(s({},l,{text:o[o.length-1]}))}else v.push(l);else v.push(l);for(v.length>0&&u.push(v),r=0,t=u.length;r<t;r++)Ca(a,u[r],c,d+r*f/t,e,f/t,g,h,i)}function Ca(a,b,c,d,e,f,g,h,i){var j,k,l,m,n,o,p,q,r,s,t,u,v,x,y,z,A,B=[],C=[],D=[],E=K(e,f)-2,F=c;for(1===g?F=c+e/2:2===g&&(F=c+e),j=E,k=0,l=b.length;k<l;k++)o=b[k],p=o.image,q=o.text,r=o.fontFamily,s=o.fontSize,t=o.fontWeight,u=o.fontStyle,p?1===g?F-=E/2:2===g&&(F-=E):q&&(v=Ea(r,s,t,u),B[k]=v,w.lZa(a,v),n=O(s||"13.3px"),j<n&&(j=n),C[k]=n,m=a.measureText(q).width,D[k]=m,1===g?F-=m/2:2===g&&(F-=m));for(x=d+j/2,1===h?x=d+f/2:2===h&&(x=d+f-j/2),a.save(),a.textAlign="left",a.textBaseline="middle",k=0,l=b.length;k<l;k++)o=b[k],p=o.image,q=o.text,y=o.underline,z=o.strikethrough,A=o.color,p?(Da(a,i,p,F,x-E/2,E,E),F+=E):q&&(a.beginPath(),a.font=B[k],a.fillStyle=A,a.fillText(q,F,x),n=C[k],m=D[k],(y||z)&&(a.beginPath(),a.moveTo(F,z?x:x+n/2),a.lineTo(F+m,z?x:x+n/2),a.stroke()),F+=m);a.restore()}function Da(a,b,c,d,e,f,g){if(b.ko(c)){var h=b.lo(c);try{a.drawImage(h,0,0,h.width,h.height,d,e,f,g)}catch(a){}}else b.fo(c)}function Ea(a,b,c,d){var e,f,g,h;return a||(a="Arial"),b||(b="13.3px"),e=D,f="normal",g=" ",h="400",d!==f&&(e=d),c!==f&&c!==h&&(e+=(e?g:D)+c),e+=(e?g:D)+b,e+=g+a}function Fa(a,b){var c,d,e=a.getImageData(b.x,b.y,b.width,b.height),f=e.data;for(c=0;c<f.length-4;c+=4)d=M((30*f[c]+59*f[c+1]+11*f[c+2]+50)/100),f[c]=d,f[c+1]=d,f[c+2]=d;a.putImageData(e,b.x,b.y)}function Ga(a,b,c,d,e,f){var g=a.ss,h=a.zoom(),i=a.it(e),j=a.jt(d),k=i[0],l=i[i.length-1],m=j[0],n=j[j.length-1];da(a,function(a){var d,e,o,p,q,r,s,t,u,v,w,x,y,z;if(a.isVisible()&&a.canPrint()){if(d=0,e=0,o=a.width()*h,p=a.height()*h,q=a.startColumn(),r=i.findCol(q),r)d=r.x;else if(q<k.col)for(d=k.x,s=k.col-1;s>=q;s--)d-=g._m(s);else for(d=l.x,t=l.col+1;t<=q;t++)d+=g._m(t);if(d+=a.startColumnOffset()*h,u=a.startRow(),v=j.findRow(u),v)e=v.y;else if(u<m.row)for(e=m.y,w=m.row-1;w>=u;w--)e-=g.Sl(w);else for(e=n.y,x=n.row+1;x<=u;x++)e+=g.Sl(x);if(e+=a.startRowOffset()*h,c&&c.intersect(d,e,o,p)){if(b.save(),b.rect(c.x,c.y,c.width,c.height),b.clip(),b.beginPath(),"1"===a.typeName)y=a.src(),y&&Da(b,f,y,d,e,o,p);else if("2"===a.typeName&&(z=a.Xs))try{b.drawImage(z,0,0,z.width,z.height,d,e,o,p)}catch(a){}b.restore()}}})}function Ha(a,b){var c=[];return da(a,function(d){d.startColumn()<=b&&b<=d.endColumn()&&(c.push({type:"startColumnOffset",floatingObject:d,offset:d.startColumnOffset()}),d.startColumnOffset(d.startColumnOffset()-a.$r(b)))}),c}function Ia(a,b){var c=[];return da(a,function(d){d.startRow()<=b&&b<=d.endRow()&&(c.push({type:"startRowOffset",floatingObject:d,offset:d.startRowOffset()}),d.startRowOffset(d.startRowOffset()-a.Yr(b)))}),c}function Ja(a,b,c,d){a.wu().execute({cmd:"autoFitColumn",sheetName:a.name(),columns:b,rowHeader:c,autoFitType:d})}function Ka(a,b,c,d){a.wu().execute({cmd:"autoFitRow",sheetName:a.name(),rows:b,columnHeader:c,autoFitType:d})}function La(a,b,c,d){var e=b.toDataURL(),f=a.createElement("img");return d&&(f.style.margin=b.style.margin),f.style.width=b.style.width,f.style.height=b.style.height,f.src=e,c.ko(e)||c.fo(e),f}function Ma(a,b){var c,d,e,f,g,h,i,j=[],k=1,l=-1;for(c=0,d=a.pageCount();c<d;c++)e=a.getPage(c),f=e.sheetIndex(),l!==f&&(g=b.getSheet(f),h=g.printInfo(),i=h.firstPageNumber(),(1!==i||h.fma)&&(k=i),l=f),j[c]=k++;return j}function Na(a,b){this.xn={},this.OC=a,this.H3=b}l={initBuild:function(){var a=this,b=sa(a.I3());b.open(),b.write("<head><style>body{margin:0} .gc-printPage{page-break-after: always} .grayscale{filter: grayscale(100%)}</style></head><body></body>"),b.close()},initContainer:function(a,b){var c=this,d=sa(c.I3()),e=a.width,f=a.height,g=b.left,h=b.right,i=b.top,j=b.bottom,k=b.header,l=b.footer,m=e-g-h,n=f-K(k,i)-K(l,j),o=ua(d,e,f,E);c.xo=o,c.c5=va(d,o,m,n,g,K(k,i))},processHeader:function(a,b,c,d,e,f,g,h){var i=this,j=i.c5,k=h.qualityFactor,l=sa(i.I3()),m=wa(l,a,b,k);i.Pma(m.getContext(F),a,b,c,d,e,f,g),j.appendChild(k<=2?m:La(l,m,f))},Pma:function(a,b,c,d,e,f,g,h){var i=Aa(h,d.headerLeft(),d.headerLeftImage(),e,f),j,k;Ba(a,i,0,0,b/3,c,0,0,g),j=Aa(h,d.headerCenter(),d.headerCenterImage(),e,f),Ba(a,j,b/3,0,b/3,c,1,0,g),k=Aa(h,d.headerRight(),d.headerRightImage(),e,f),Ba(a,k,b/3*2,0,b/3,c,2,0,g)},processBody:function(a,b,c,d,e){var f,g=this,h=g.c5,i=e.qualityFactor,j=e.contentWidth,k=e.bodyContentHeight,l=e.showBorder,m=e.centering,n=sa(g.I3()),o=ya(n,h,j,k),p=e.sheetPaginator,q=za(a,n,o,b,c,l,m,p.horizontalZoomFactor(),p.verticalZoomFactor(),i),r=q.getContext(F),s=q.$va,t=q._va;g._l(a,r,new v(0,0,s,t),d,!l),g.Qma(a,r,new v(0,0,s,t),d),f=o.children[0],f.appendChild(i<=2?q:La(n,q,d,!0))},_l:function(a,b,c,d,e){a.bt=d,a.Us(c),a.invalidateLayout(),a.yl.LI(b,c,e)},Qma:function(a,b,c,d){var e,f,g,h=a.am();for(e=0;e<=2;e++)for(f=0;f<=2;f++)g=c.getIntersectRect(h.Ft(e,f)),g&&Ga(a,b,g,e,f,d)},processFooter:function(a,b,c,d,e,f,g,h){var i=this,j=i.c5,k=h.qualityFactor,l=sa(i.I3()),m=xa(l,a,b,k);i.Rma(m.getContext(F),a,b,c,d,e,f,g),j.appendChild(k<=2?m:La(l,m,f))},Rma:function(a,b,c,d,e,f,g,h){var i=Aa(h,d.footerLeft(),d.footerLeftImage(),e,f),j,k;Ba(a,i,0,0,b/3,c,0,2,g),j=Aa(h,d.footerCenter(),d.footerCenterImage(),e,f),Ba(a,j,b/3,0,b/3,c,1,2,g),k=Aa(h,d.footerRight(),d.footerRightImage(),e,f),Ba(a,k,b/3*2,0,b/3,c,2,2,g)},processBlackAndWhite:function(){var a,b,c,d,e=this.xo;if(x.mozilla)e.classList.add("grayscale");else for(a=r(I,e),b=0,c=a.length;b<c;b++)d=a[b],Fa(d.getContext(F),new v(0,0,d.width,d.height));
},clearAfterBuild:function(){var a=this,b=sa(a.I3()),c=b.body.lastChild;c&&r(c).removeClass(E)},build:function(){var a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,s,t,u,v,w,x,y,z,A,B,C,D,E=this,F=E.OC,G=E.H3;for(E.initBuild(),r[r.sd]({tc:F}),a=G.pageCount(),b=E.vu(),E.awa(),c=Ma(G,F),d=0;d<a;d++)e=G.getPage(d),f=F.getSheet(e.sheetIndex()),g=f.printInfo(),h=g.pageRange(),h&&(i=Z(h),$(i,a),i.indexOf(e.pageNumberInSheet())<0)||(j=E.J3(f,e),k=Y(g.margin()),l=k.left,m=k.right,n=k.top,o=k.bottom,p=k.header,q=k.footer,s=g.orientation(),t=X(f,g.paperSize(),s),u=t.width,v=t.height,w=u-l-m,x=v-K(p,n)-K(q,o),y=L(0,n-p),z=L(0,o-q),A=e.getPageSize(),B=A.width,C=A.height,D={qualityFactor:g.qualityFactor(),marginHeader:p,paperHeight:v,marginFooter:q,contentWidth:w,bodyContentHeight:x-y-z,showBorder:g.showBorder(),centering:g.centering(),marginLeft:l,marginTop:n,sheetPaginator:ta(G,d)},E.initContainer(t,k,s),y>0&&E.processHeader(w,y,g,e,a,b,c[d],D),E.processBody(f,B,C,b,D),z>0&&E.processFooter(w,z,g,e,a,b,c[d],D),g.blackAndWhite()&&E.processBlackAndWhite(),E.i_a(j),f.no(!1));E.clearAfterBuild(),b.mo()&&E.onBuildCompleted()},print:function(){var a=this.I3(),b,c;x.msie?(b=sa(a),b.execCommand("print")):(c=a.contentWindow,c.focus(),c.print()),window.focus()},dispose:function(){var a,b=this,c=b.K3,d=c&&c.parentElement;d&&(d.removeChild(c),b.K3=J),b.bt&&(b.bt.no(),b.bt=J),b.bwa&&(b.bwa.no(),b.bwa=J),a=b.OC,a&&(a.destroy(),b.OC=J),b.H3=J,b.xn={},b.c5=J,b.xo=J},I3:function(){var a,b,c;return this.K3||(a=document,b=V(a,"IFRAME"),c=b.style,c.position="absolute",c.left="-10px",c.width="0px",c.height="0px",a.body.insertBefore(b,J),this.K3=b),this.K3},J3:function(a,b){var c,d,e,f,g,h,i,j,k,l,m,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N=[];if(a.suspendPaint(),a.suspendEvent(),n.X3&&a.suspendCalcService(),a.showRowOutline&&(a.showRowOutline(!1),a.showColumnOutline(!1)),a.zoom(1),c=a.options,d=a.printInfo(),e=d.showGridLine(),f=a.options.gridline,f.showHorizontalGridline=e,f.showVerticalGridline=e,g=this,!g.xn[a.name()]){if(h=!1,d.bestFitColumns()){for(i=d.columnStart(),j=i===-1?0:i,k=ka(a,d),l=[],m=j;m<=k;m++)l.push({col:m});for(Ja(a,l,!1,1),o=[],p=a.getColumnCount(2),q=0;q<p;q++)o.push({col:q});Ja(a,o,!0,1),h=!0}if(d.bestFitRows()){for(r=d.rowStart(),s=r===-1?0:r,t=la(a,d),u=[],v=s;v<=t;v++)u.push({row:v});for(Ka(a,u,!1,1),w=[],x=a.getRowCount(1),y=0;y<x;y++)w.push({row:y});Ka(a,w,!0,1),h=!0}h&&da(a,function(a){a.Toa()}),g.xn[a.name()]=!0}if(z=d.showRowHeader(),0===z||(2===z||3===z&&0===b.columnPageIndexInSheet()?c.rowHeaderVisible=!0:c.rowHeaderVisible=!1),A=d.showColumnHeader(),0===A||(2===A||3===A&&0===b.rowPageIndexInSheet()?c.colHeaderVisible=!0:c.colHeaderVisible=!1),a.frozenRowCount(0),a.frozenColumnCount(0),a.frozenTrailingRowCount(0),a.frozenTrailingColumnCount(0),B=a.xr,B&&B.filterButtonVisible(!1),C=a.tables&&a.tables.all())for(D=0,E=C.length;D<E;D++)C[D].filterButtonVisible(!1);if(F=b.columnPage(),G=F.repeatItemStart(),H=F.repeatItemEnd(),G!==-1&&H!==-1)for(a.frozenColumnCount(H+1),D=0,E=G;D<E;D++)N.push({type:"colVisible",sheetName:a.name(),index:D,visible:a.getColumnVisible(D)}),a.setColumnVisible(D,!1),I=Ha(a,D),N.push.apply(N,I);if(J=b.rowPage(),K=J.repeatItemStart(),L=J.repeatItemEnd(),K!==-1&&L!==-1)for(a.frozenRowCount(L+1),D=0,E=K;D<E;D++)N.push({type:"rowVisible",sheetName:a.name(),index:D,visible:a.getRowVisible(D)}),a.setRowVisible(D,!1),M=Ia(a,D),N.push.apply(N,M);return a.showCell(J.itemStart(),F.itemStart(),0,0),N},i_a:function(a){var b,c,d,e,f,g,h,i,j=this.OC;for(b=a.length-1;b>=0;b--)c=a[b],d=c.type,e=c.index,f=c.visible,g=c.floatingObject,h=c.offset,i=j.getSheetFromName(c.sheetName),"colVisible"===d?i.setColumnVisible(e,f):"rowVisible"===d?i.setRowVisible(e,f):"startRowOffset"===d?g.startRowOffset(h):"startColumnOffset"===d&&g.startColumnOffset(h)},vu:function(){var a=this;return a.bt||(a.bt=new u(function(){a.bt&&a.build()})),a.bt},awa:function(){var a,b,c,d=this;d.bwa||(a=[],b=d.H3,c=b instanceof oa?[b.kj]:d.OC.sheets,c.forEach(function(b){b.charts&&b.charts.all().forEach(function(b){b.isVisible()&&b.canPrint()&&a.push(b)})}),a.length>0&&(d.bwa=new ra(a),d.bwa.Zva()))}},s(Na.prototype,l),y.tQa=Na;function Oa(){}m={print:function(a,b){var c,d,e=this;e.dispose(),c=e.prepareContext(a,b),d=new Na(c.workbook,c.paginator),e.M3=d,d.onBuildCompleted=function(){d.print()},d.build()},prepareContext:function(a,b){var c,d,e,f,g,h,i,j=this,k=new o;for(k.suspendPaint(),k.suspendEvent(),k.fromJSON(Q(P(a.toJSON({includeBindingSource:!0,ignoreFormula:!0})))),e=k.options,e.scrollbarMaxAlign=!1,e.scrollbarShowMax=!0,e.scrollIgnoreHidden=!1,f=k.getSheetCount(),h=0;h<f;h++)g=k.getSheet(h),g.printInfo().showBorder()?g.options.sheetAreaOffset={left:0,top:0}:g.options.sheetAreaOffset={left:2,top:2};do U(b)||b===J?c=new qa(k):(d={},i=k.getSheet(b),W(i,i.printInfo(),d),c=new oa(k,b,d.paperSize,d.pageImageableArea)),c.paginate();while(!j.L3(c,k));return{workbook:k,paginator:c}},dispose:function(){var a=this.M3;a&&a.dispose(),this.M3=J},L3:function(a,b){var c,d,e,f,g,h,i,j,k,l=a.pageCount();for(c=0;c<l;c++){if(d=a.getPage(c),h=d.rowPage(),h.contentOffset()>0&&c-1>=0&&(e=a.getPage(c-1),i=e.rowPage(),f=d.sheetIndex(),g=b.getSheet(f),f===e.sheetIndex()))return g.setRowHeight(i.itemEnd(),h.contentOffset(),3),!1;if(j=d.columnPage(),j.contentOffset()>0&&c-1>=0&&(e=a.getPage(c-1),k=e.columnPage(),f=d.sheetIndex(),g=b.getSheet(f),f===e.sheetIndex()))return g.setColumnWidth(k.itemEnd(),j.contentOffset(),3),!1}return!0}},s(Oa.prototype,m),y.uQa=Oa,a.exports=y}()},function(b,c){b.exports=a.Spread.Sheets}])});